/*!
 * Font Awesome Free 5.0.4 by @fontawesome - http://fontawesome.com
 * License - http://fontawesome.com/license (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 */
@import 'variables';

@font-face {
  font-family: 'Font Awesome 5 Free';
  font-style: normal;
  font-weight: 900;
  src: asset_url('#{$fa-font-path}/fa-solid-900.eot');
  src: asset_url('#{$fa-font-path}/fa-solid-900.eot?#iefix') format('embedded-opentype'),
  asset_url('#{$fa-font-path}/fa-solid-900.woff2') format('woff2'),
  asset_url('#{$fa-font-path}/fa-solid-900.woff') format('woff'),
  asset_url('#{$fa-font-path}/fa-solid-900.ttf') format('truetype'),
  asset_url('#{$fa-font-path}/fa-solid-900.svg#fontawesome') format('svg');
}

.fa,
.fas {
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}
