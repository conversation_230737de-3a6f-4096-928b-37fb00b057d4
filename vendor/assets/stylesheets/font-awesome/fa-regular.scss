/*!
 * Font Awesome Free 5.0.4 by @fontawesome - http://fontawesome.com
 * License - http://fontawesome.com/license (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 */
@import 'variables';

@font-face {
  font-family: 'Font Awesome 5 Free';
  font-style: normal;
  font-weight: 400;
  src: asset_url('#{$fa-font-path}/fa-regular-400.eot');
  src: asset_url('#{$fa-font-path}/fa-regular-400.eot?#iefix') format('embedded-opentype'),
  asset_url('#{$fa-font-path}/fa-regular-400.woff2') format('woff2'),
  asset_url('#{$fa-font-path}/fa-regular-400.woff') format('woff'),
  asset_url('#{$fa-font-path}/fa-regular-400.ttf') format('truetype'),
  asset_url('#{$fa-font-path}/fa-regular-400.svg#fontawesome') format('svg');
}

.far {
  font-family: 'Font Awesome 5 Free';
  font-weight: 400;
}
