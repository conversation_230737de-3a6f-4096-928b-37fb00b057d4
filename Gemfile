source 'https://rubygems.org'

gem 'rails', '~> 6.1.0'
gem 'composite_primary_keys', github: 'composite-primary-keys/composite_primary_keys'
gem 'pg', '~> 1.5', group: [:development, :production]
gem 'puma', '~> 3.0'
gem 'nokogiri', '>= 1.13'
gem 'psych', '~> 3.3.2'
gem 'globalid', '~> 1.0'
gem 'logger', '~> 1.4'
# config/network-related gems
gem 'net-smtp',   '~> 0.3.1', require: 'net/smtp'
gem 'net-http',   '~> 0.3.1', require: 'net/http'
gem 'net-pop', '~> 0.1.1', require: 'net/pop'
gem 'net-imap', '~> 0.2.3', require: 'net/imap'
gem 'net-ftp',       '~> 0.2.0', require: 'net/ftp'
gem 'net-protocol',  '~> 0.2.1', require: 'net/protocol'

# Configurations
gem 'settingslogic'

# REST
gem 'activeresource', '5.1.0' #, github: 'rails/activeresource'
gem 'activeresource_extensions', git: 'https://eon-seed.visualstudio.com/Flexibility%20Trading%20Technologies/_git/activeresource_extensions'

gem 'event_trail', #path: "~/dev/e.on/gitlab/event_trail"
    git: 'https://eon-seed.visualstudio.com/Flexibility%20Trading%20Technologies/_git/event_trail'

gem 'httparty'
gem 'oj'
gem 'net-sftp'

gem 'twilio-ruby'
gem 'authy'

gem 'nobspw'

gem 'mailjet'

# Asset Pipeline
gem 'sass-rails', '~> 5.0'
gem 'uglifier'
gem 'haml-rails'
gem 'compass-rails', '4.0.0'
gem 'jquery-rails', '~> 4.6.0'
gem 'bootstrap-sass', '~> 3.3.7'

# UI
gem 'simple_form'
gem 'select2-rails'
gem 'gon'
gem 'highcharts-rails'

gem 'draper', github: 'drapergem/draper'

# Controller
gem 'inherited_resources'

gem 'bcrypt', '~> 3.1.7'

# Authorization
gem 'authority'
gem 'vpp_authentication', #path: "~/dev/e.on/gitlab/vpp_authentication"
    git: 'https://eon-seed.visualstudio.com/Flexibility%20Trading%20Technologies/_git/vpp_authentication'

gem 'kaminari'
gem 'axlsx', git: 'https://eon-seed.visualstudio.com/Flexibility%20Trading%20Technologies/_git/axlsx'
gem 'rubyXL'
gem 'spreadsheet'

# Browser Detection
gem 'browser'

gem 'prometheus-client'

group :production, :staging, :integration, :testing do
  gem 'lograge', '~> 0.9.0'
  gem 'logstash-event'
end

group :development do
  gem 'dotenv-rails'
  gem 'byebug', platform: :mri
  gem 'better_errors'
  gem 'binding_of_caller'
end
