SECRET_KEY_BASE=7e01e4c429199e1ec5433298eca05fc6f9958d91ad6de8ed28dcf0bba64ee261c21018461724ecea375aca831ef9cd2e6fc0c437550922c63a4f7e80182fbf23

VPP_LOCALES=en-GB,de
VPP_TIME_ZONE=Europe/London

# de or en-GB
DEFAULT_LOCALE=en-GB

############################################################
# Integration
############################################################

EVENTS_APP_URL=http://int-events-1.vpp.internal:9000
VPP_REPORTING_URL=http://int-vppreporting-1.vpp.internal:9000
PORTFOLIO_MANAGEMENT_SERVICE_URL=http://localhost:58546
VPP_ROLLUPS_SERVICE_URL=http://int-vppreporting-1.vpp.internal:9010
VPP_TRADING_URL=http://int-services-1.vpp.internal:9020

PORT=3000

#SMTP_HOST=***********
#SMTP_PORT=25

# Sendgrid SMTP with TLS
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_TLS=true

VPP_MANAGEMENT_APPLICATION_URL=http://localhost:3000

# database
VPP_MANAGEMENT_DATABASE_HOST=vpp-nonprod-dev-pgsql-flex.postgres.database.azure.com
VPP_MANAGEMENT_DATABASE_NAME=vpp_db
VPP_MANAGEMENT_DATABASE_USERNAME=vpp
VPP_MANAGEMENT_DATABASE_PASSWORD=3hE81Wk8S6TwmR^2Y0qA

#VPP_MANAGEMENT_DATABASE_HOST=vpp-nonprod-prep-pgsql-flex.postgres.database.azure.com
#VPP_MANAGEMENT_DATABASE_NAME=vpp_db
#VPP_MANAGEMENT_DATABASE_USERNAME=vpp
#VPP_MANAGEMENT_DATABASE_PASSWORD=CVWO7nDfwViCfFqZyeOI

#reporting
VPP_MANAGEMENT_REPORTING_DATABASE_HOST=int-postgres-1.vpp.internal
VPP_MANAGEMENT_REPORTING_DATABASE_NAME=reporting_db
VPP_MANAGEMENT_REPORTING_DATABASE_USERNAME=reporting

# LOCAL OVERRIDE
PORTFOLIO_MANAGEMENT_SERVICE_URL=http://localhost:62260
# VPP_REPORTING_URL=http://localhost:3000
# VPP_TRADING_URL=http://localhost:9000

#VPP_MANAGEMENT_DATABASE_HOST=localhost
#VPP_MANAGEMENT_DATABASE_NAME=vpp_db
#VPP_MANAGEMENT_DATABASE_USERNAME=postgres
#VPP_MANAGEMENT_DATABASE_PASSWORD=postgres


VPP_MANAGEMENT_HOST=localhost
#VPP_ROLLUPS_SERVICE_URL=http://localhost:9000

ASSET_SCHEDULE_SERVICE_URL=http://int-services-1.vpp.internal:9003
VPP_TRADING_URL=http://localhost:52336

# comma separated list; currently only reports section can be disabled
# VPP_MANAGEMENT_DISABLED_SECTIONS=reports
VPP_MANAGEMENT_DISABLED_SECTIONS=

VPP_MANAGEMENT_SUPPORT_EMAIL=<EMAIL>

WS_TOKEN_KEEP_MONTHS=3
HEALTH_CHECKS_ENABLED=false

TWILIO_AUTHY_PUSH_AUTH_EXPIRE_SECONDS=300

PERF_DATA_SFTP_HOST=filedrop.dev.eon-orchestra.com
PERF_DATA_SFTP_PORT=2022
PERF_DATA_SFTP_USERNAME=VPPPerfDataDev

MARKET_DATA_API_URL=http://localhost:65476