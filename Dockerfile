# Build Image
# ------------------------------------------------------------------------------
ARG MIRRORED_REGISTRY=""
FROM ${MIRRORED_REGISTRY}library/node:16.20.2 as nodebuilder

COPY . /home/<USER>/webapp/
WORKDIR /home/<USER>/webapp/app/angular
RUN ["yarn", "install", "--ignore-engines"]
RUN ["yarn", "run", "dist"]

# ------------------------------------------------------------------------------
# Final Image
# ------------------------------------------------------------------------------
FROM ${MIRRORED_REGISTRY}library/ruby:3.1.2

COPY --from=nodebuilder /home/<USER>/webapp/ /opt/docker
RUN apt-get update -qq && apt-get install -y build-essential libpq-dev tzdata nodejs
ARG AZURE_ACCESS_TOKEN

# Set an environment variable where the Rails app is installed to inside of Docker image
ENV RAILS_ROOT /opt/docker

# Set working directory
RUN mkdir -p $RAILS_ROOT && chown -R daemon:daemon $RAILS_ROOT
WORKDIR $RAILS_ROOT

# Setting env up
ENV RAILS_ENV='production'
ENV RACK_ENV='production'
ENV RAILS_RELATIVE_URL_ROOT=/
ENV ENABLE_RACK_RELATIVE_URL='true'

# Adding gems
COPY Gemfile Gemfile
COPY Gemfile.lock Gemfile.lock

RUN bundle config set deployment 'true' \
    && bundle config set without 'development test' \
    && bundle config set eon-seed.visualstudio.com "azure-devops-ci-token:$AZURE_ACCESS_TOKEN" \
    && bundle install --jobs 20 --retry 5

USER daemon

# Adding project files
COPY --chown=daemon:daemon . .

# precompile javascript and CSS, for web portals only
RUN bundle exec rake assets:precompile

EXPOSE 8080
CMD ["bundle", "exec", "puma", "-C", "config/puma.rb"]
