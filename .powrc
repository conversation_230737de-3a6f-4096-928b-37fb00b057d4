if [ -f "$rvm_path/scripts/rvm" ]; then
  source "$rvm_path/scripts/rvm"

  if [ -f ".rvmrc" ]; then
    source ".rvmrc"
  fi

  if [ -f ".ruby-version" ]; then
    rvm use `cat .ruby-version`
  fi

  if [ -f ".ruby-gemset" ]; then
    rvm gemset use --create `cat .ruby-gemset`
  fi
fi

# Run in production mode:
# export RAILS_ENV=production
# export RAILS_RELATIVE_URL_ROOT=/vpp-management
# export ENABLE_RACK_RELATIVE_URL=true
# export RAILS_SERVE_STATIC_ASSETS=true
