class NominationBidsBroadcastJob < ApplicationJob

  queue_as :default_src_encoding

  def self.notify_bid_deleted(market_name:, delivery_date:)
    NominationBidsBroadcastJob.perform_now({market_name: market_name, delivery_date: delivery_date, action: 'delete'});
  end

  def self.notify_bid_uploaded(market_name:, delivery_date:)
    NominationBidsBroadcastJob.perform_now({market_name: market_name, delivery_date: delivery_date, action: 'upload'});
  end

  def perform(params)
    topic = NominationBidsChannel::topic_name(market_name: params[:market_name], delivery_date: params[:delivery_date])
    payload = {
      market_name: params[:market_name],
      action: params[:action],
      delivery_date: params[:delivery_date].strftime('%d_%m_%Y')
    }
    ActionCable.server.broadcast(topic, payload)
  end
end