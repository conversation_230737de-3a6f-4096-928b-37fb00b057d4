class DatePickerInput < SimpleForm::Inputs::StringInput
  def input
    input_html_options[:data] ||= {}
    input_html_options[:data][:behaviour] = 'datepicker'
    if input_html_options[:data][:time_zone]
      input_html_options[:value] = value.nil?? nil : value.in_time_zone(input_html_options[:data][:time_zone]).strftime("%d/%m/%Y")
    else
      input_html_options[:value] = value.nil?? nil : value.strftime("%d/%m/%Y")
    end
    input_html_options[:type] = 'text'

     super +
     @builder.hidden_field(attribute_name, { :class => attribute_name.to_s + "-alt"})
   end

   private
     def value
       object.send(attribute_name) if object
     end
end