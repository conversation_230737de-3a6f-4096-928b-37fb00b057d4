#
module ApplicationHelper
  def localize_report_feedback(messages, scope = 'reports.messages')
    Array(messages.presence).collect do |m|
      case m
      when Array
        if m.last.present? && m.last[:date].present?
          m.last[:date] = I18n.l(Date.parse(m.last[:date]))
        end
        if m.last.present? && m.last[:time].present?
          m.last[:time] = I18n.l(Time.parse(m.last[:time]))
        end
        I18n.t(m.first, m.last.merge(scope: scope))
      else
        m
      end
    end
  end

  def localize_report_parameters(params)
    params.collect do |k, v|
      case k
      when 'tso'
        {name: k, value: Tso.find(v).name}
      when 'market'
        {name: k, value: Market.find(v).name}
      when 'date'
        {name: k, value: (v.nil? ? '' : (v.kind_of?(Date) ? I18n.l(v) : I18n.l(Date.parse(v))))}
      else
        {name: k, value: v}
      end
    end.reject{|k, v| k.blank?}
  end

  def css_hidden(flag)
    flag ? "display: none" : ""
  end

  def to_minutes(seconds)
    seconds.blank? ? seconds : seconds / 60
  end

  def tech_support_email
    ENV['VPP_MANAGEMENT_SUPPORT_EMAIL'] || "<EMAIL>"
  end

  def pretty_phone_number(str_phone_no)
    if str_phone_no.blank?
      str_phone_no
    else
      str_phone_no = str_phone_no.gsub!(/\s+/, '') || str_phone_no
      if str_phone_no[0] == "+"
        country = str_phone_no[0..2]
        no = str_phone_no[3..str_phone_no.size] || ''
        country + " " + number_to_phone(no, delimiter: " ")
      else
        number_to_phone(str_phone_no, delimiter: " ")
      end
    end
  end

end
