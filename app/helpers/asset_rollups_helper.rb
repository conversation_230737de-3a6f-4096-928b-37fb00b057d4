module AssetRollupsHelper

  def rollup_for(rollup_type, rollup_family)
    rollup = rollup_type.rollups.find{|r| r.owner_id == rollup_family.id && r.owner_type == RollupFamily.model_name.to_s}
    unless rollup
      rollup = Rollup.new(rollup_type_id: rollup_type.id,
                           owner_type: RollupFamily.model_name.to_s,
                           owner_id: rollup_family.id)
    end
    rollup
  end
end