module DispatchGroupRollupsHelper

  def rollup_for_dg(rollup_type, dispatch_group)
    rollup = rollup_type.rollups.find{|r| r.owner_id == dispatch_group.id && r.owner_type == DispatchGroup.model_name.to_s}
    unless rollup
      rollup = Rollup.new(rollup_type_id: rollup_type.id,
                           owner_type: DispatchGroup.model_name.to_s,
                           owner_id: dispatch_group.id)
    end
    rollup
  end
end