Vpp Management Ui
========================

##### Important

BEFORE PUSHING: do a ng build --aot --prod to confirm that your changes work.
Code changes working while gulp is running (not AOT, not PROD mode) doesn't mean
the changes will work with AOT and PROD flag enabled.

 ng build --aot --prod on docker will fail silently if there are errors.
 So please check before pushing



## Initial Setup

1. To get a setup as close as possible to remote environment you need to install nginx
`brew install nginx`

2. run nginx server
`sudo nginx`

3. check if is working on
`http://localhost`

4. Create a nginx config file. On Mac `/usr/local/etc/nginx/servers`

```
server {
    listen 80;

    location /vpp-management {
        rewrite ^/vpp-management/(.*) /$1 break;
        client_max_body_size 20M;
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}
```
5. Must have installed node.js version 8
`brew update`
`brew install node@8`

If you have older version installed must remove it (brew uninstall node) and
clear the caches (remove folders `~/.node` and `~/npm`)


6. go to app/angular/ and install dependencies
`npm install`

## Development

1. Angular CLI is required
`npm install -g @angular/cli`

2. Run gulp
`gulp`

#Build and commit
`gulp prod`

3. Start rails app
`rails s`

4. check is app in running on
`http://localhost/vpp-management/`


#### Angular 8 - Upgrade
## Upgrade node to v10

1. `sudo n 10 `
2. in app/angular run `ng update @angular/cli @angular/core`



#### Routing

# All routing differences between localhost/* and vpp-management/* tries to be handled by src/environments files see how they are used in routing files eg: `${environment.routingPath}automatic-report-emailings`

if you run app through nginx and load the app using localhost/vpp-management then environment int needs to be identical to prod.
```
  export const environment = {
    production: true,
    apiPath:'/vpp-management',
    routingPath:'vpp-management/'
  };
```