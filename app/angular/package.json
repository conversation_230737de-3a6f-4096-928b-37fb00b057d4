{"name": "vpp-management", "version": "0.0.0", "license": "MIT", "scripts": {"ng": "ng", "start": "gulp", "build": "ng build --output-path ../../public/angular/dist", "build-prod": "ng build --prod --aot --output-path ../../public/angular/dist", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "dist": "gulp prod"}, "private": true, "dependencies": {"@angular/animations": "^8.2.10", "@angular/cdk": "^7.3.7", "@angular/common": "^8.2.10", "@angular/compiler": "^8.2.10", "@angular/core": "^8.2.10", "@angular/forms": "^8.2.10", "@angular/http": "^7.2.15", "@angular/material": "^7.3.7", "@angular/platform-browser": "^8.2.10", "@angular/platform-browser-dynamic": "^8.2.10", "@angular/platform-server": "8.2.10", "@angular/router": "^8.2.10", "@ng-bootstrap/ng-bootstrap": "^4.2.2", "@ngx-translate/core": "^11.0.1", "@ngx-translate/http-loader": "^4.0.0", "ag-grid-angular": "^23.2.1", "ag-grid-community": "^23.2.1", "angular-highcharts": "^8.0.3", "angular2-uuid": "^1.1.1", "bootstrap": "^4.3.1", "classlist.js": "^1.1.20150312", "core-js": "^2.6.10", "font-awesome": "^4.7.0", "hamburgers": "^1.1.3", "highcharts": "^7.2.0", "http-server": "^0.12.3", "ie-shim": "^0.1.0", "lodash": "^4.17.14", "moment": "^2.30.1", "moment-timezone": "^0.5.45", "ng-multiselect-dropdown": "^0.2.10", "ng-pick-datetime": "^7.0.0", "ng2-file-upload": "^1.3.0", "ng2-slim-loading-bar": "^4.0.0", "ngx-datetime-range-picker": "^1.1.8", "owl-ng": "^1.0.0-beta.19", "reflect-metadata": "^0.1.12", "rxjs": "^6.5.3", "rxjs-compat": "^6.5.3", "typescript": "3.5.3", "zone.js": "^0.9.1"}, "devDependencies": {"@angular-devkit/build-angular": "^0.803.29", "@angular-devkit/build-optimizer": "0.6.8", "@angular/cli": "8.3.9", "@angular/compiler-cli": "8.2.10", "@angular/language-service": "~8.2.10", "@types/hammerjs": "^2.0.35", "@types/jasmine": "^2.8.8", "@types/node": "^10.14.21", "@types/uglify-js": "^3.0.2", "browser-sync": "^2.26.7", "gulp": "^5.0.1", "gulp-autoprefixer": "^3.1.1", "gulp-cli": "^3.1.0", "gulp-concat": "^2.6.1", "gulp-exec": "^2.1.3", "gulp-sass": "^6.0.1", "gulp-sass-lint": "^1.4.0", "gulp-sourcemaps": "^2.6.5", "gulp-strip-debug": "^1.1.0", "jasmine-core": "^2.99.1", "jasmine-spec-reporter": "^3.3.0", "karma": "^6.2.0", "karma-chrome-launcher": "^2.2.0", "karma-cli": "^2.0.0", "karma-coverage-istanbul-reporter": "^0.2.3", "karma-jasmine": "^1.1.2", "karma-jasmine-html-reporter": "^0.2.2", "protractor": "^5.4.2", "sass": "^1.89.2", "ts-node": "^2.1.2"}, "resolutions": {"minimatch": "^3.1.2"}}