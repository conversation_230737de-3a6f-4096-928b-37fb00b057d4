import {Component, Renderer2, ViewChild} from "@angular/core";
import { GlobalService } from "./../services/global.service";
import {ConfirmService, ConfirmState} from "../services/confirm.service";
import { NotificationService } from './../services/notification.service';
import { SlideRightPageAnimation } from './../animations/slide-right-page';
import { TranslateService } from '@ngx-translate/core';
import { angularData } from "./../../global.export";
import { DateTimeAdapter } from 'ng-pick-datetime';

@Component({
  selector: "vpp-management",
  template: `
    <ng-template #confirmTemplate>
      <confirm-modal-component></confirm-modal-component>
    </ng-template>  
    <vpp-management-header></vpp-management-header>
    <vpp-management-tech-support></vpp-management-tech-support>
    <a [routerLink]="['/']"></a>
    <div [@slideRightPageAnimation]="o.isActivated ? o.activatedRoute : ''">
      <router-outlet #o="outlet"></router-outlet>
    </div>
    <ng2-slim-loading-bar [color]="'#D51607'" [height]="'8px'"></ng2-slim-loading-bar>
    <div class="alerts-wrapper">
      <ng-container *ngFor="let message of notifications; let ix = index;">
        <ngb-alert
          [dismissible]="true"
          (close)="cancelNotification(message)"
          [type]="message.type"> {{ message.text | translate }}
        </ngb-alert>
      </ng-container>
    </div>
  `,
  animations: [ SlideRightPageAnimation ]
})

export class AppComponent {
  className = [];
  sideMenu: any = null;
  isSideMenuOpen: boolean = false;
  languageMenu: any = null;
  isLanguageMenuOpen: boolean = false;
  userMenu: any = null;
  isUserMenuOpen: boolean = false;
  hamburgerMenu: any;
  pageTitle: any;
  notifications = [];

  @ViewChild('confirmTemplate', {static: true})
  confirmTemplate;

  constructor(
    private _Global: GlobalService,
    private renderer: Renderer2,
    public _NotificationService: NotificationService,
    public translate: TranslateService,
    private dateTimeAdapter: DateTimeAdapter<any>,
    private _confirmState: ConfirmState) {

    translate.setDefaultLang('en-GB');
    translate.use(angularData.railsExports.locale || 'en-GB');
    this.dateTimeAdapter.setLocale(angularData.railsExports.locale || 'en-GB');

    this.sideMenu = document.getElementById("side-menu");
    this.languageMenu = document.getElementById("languageMenu");
    this.userMenu = document.getElementById("userMenu");
    this.hamburgerMenu = document.getElementById("hamburgerMenu");
    this.pageTitle = document.getElementById('pageTitle')

    this._Global.class$.subscribe(className => {
      let classNames = className.split(" ");

      for (let i in this.className) {
        let classNameToRemove = this.className[i];
        this.renderer.removeClass(document.body, classNameToRemove);
      }

      for (let i in classNames) {
        let classNameToAdd = classNames[i];
        this.renderer.addClass(document.body, classNameToAdd);
      }
      this.className = classNames;
    });
  }

  ngOnInit() {}

  ngAfterContentInit() {
    this._NotificationService.messages$.subscribe(notifications => {
      this.notifications = notifications;
    });
    this._confirmState.template = this.confirmTemplate;
  }

  cancelNotification(message) {
    this._NotificationService.popMessage(message.id)
  }

  prepareRoute(outlet) {
    return outlet && outlet.activatedRouteData && outlet.activatedRouteData['animation'];
  }
}
