import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef, Output, EventEmitter
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { UrlProvider } from "../../../providers/url.provider";
import { AssetProvider } from "../../../providers/asset.provider";
import { HttpClientService } from "../../../services/httpclient.service";
import { NotificationService } from "../../../services/notification.service";
import { environment } from "../../../../environments/environment";
import { TranslateService } from '@ngx-translate/core';
import {angularData} from "../../../../global.export";

@Component({
  selector: "vpp-upload-declaration-of-unavailability",
  templateUrl: "./upload_declaration_of_unavailability.component.html",
  styleUrls: ["./upload_declaration_of_unavailability.component.scss"],
  encapsulation: ViewEncapsulation.None
})

export class UploadDeclarationOfUnavailabilityComponent implements OnInit {
  environment = environment;
  page: number = 1;
  pageSize: number = 25;
  collectionSize: number = 0;
  params = {
    page: this.page,
    per_page: this.pageSize
  };
  showFileFormat: boolean = false;
  asset;
  assets;
  fileToUpload;
  fileUploaderReference;
  uploaderOptions ={
    url: this.UrlProvider.getUploadDeclarationOfUnavailabilityUrl()
  };
  csr_token: string = "";
  disableButton: boolean = true;
  pleaseWait = false;
  response: any = null;

  @ViewChild("fileUploader", { static: true }) fileUploader: any;

  constructor(
    private _HttpClientService: HttpClientService,
    private UrlProvider: UrlProvider,
    private _Asset: AssetProvider,
    public _NotificationService: NotificationService,
    public translate: TranslateService
  ) {
    this.uploaderOptions.url = this.UrlProvider.getUploadDeclarationOfUnavailabilityUrl();
    this.csr_token = this._HttpClientService.getAuthToken();
  }

  ngOnInit() {
    this.getAssets();
  }

  fileChangedCallback(file) {
    this.fileToUpload = file;
    this.disableButton = false;
    this.pleaseWait = false
  }

  uploaderChangedCallback(uploader) {
    this.fileUploaderReference = uploader;
  }

  uploadFile(event) {
    this.response = null;
    event.preventDefault();
    event.stopPropagation();
    this.fileUploaderReference.url = this.UrlProvider.getUploadDeclarationOfUnavailabilityUrl();
    this.fileUploaderReference.onBuildItemForm = (
      fileItem: any,
      form: any
    ) => {
      form.append("authenticity_token", this.csr_token);
      form.append("asset_id", this.asset[0].id);
    };
    this.disableButton = true
    this.pleaseWait = true
    this.fileToUpload.upload();

    this.fileUploaderReference.onCompleteItem = (item:any, response:any, status:any, headers:any) => {
      if (status == 200) {
        this.response = JSON.parse(response);;
      }
      else {
        this.response = {"success":false,"result":{"validation_result":{"validationSuccess":false}},"error":null,"messages":["Unexpected Error - HTTP status " + status]}
      }
      this.disableButton = false;
      this.pleaseWait = false
      this.fileToUpload = null;
      this.fileUploader.clearFileUploaderQueue();
    };
  }

  getAssets() {
    this._Asset.findAllWithBmu().subscribe(result => {
      this.assets = result.assets;
    });
  }

  dropdownSettings(singleSelection, allowSearchFilter) {
      return {
        idField: 'id',
        textField: 'id_and_name',
        singleSelection: singleSelection,
        selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
        unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
        searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
        noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
        itemsShowLimit: 10,
        allowSearchFilter: allowSearchFilter
      }
    };

}
