<div class="row">
  <div class="col-md-12">
    <div class="tab-content light-grey">
      <h2>{{ 'NOMINATIONS.UPLOAD_PERF_DATA_FILE.TITLE' | translate }}</h2>
      <vpp-file-upload-results [response]="response"></vpp-file-upload-results>

      <a (click)="showFileFormat = !showFileFormat" class="view-more-link" *ngIf="showFileFormat">{{ 'NOMINATIONS.UPLOAD_PERF_DATA_FILE.HIDE' | translate }}</a>
      <a (click)="showFileFormat = !showFileFormat" class="view-more-link" *ngIf="!showFileFormat">{{ 'NOMINATIONS.UPLOAD_PERF_DATA_FILE.SHOW' | translate }}</a>
      <div class="file-format-box" *ngIf="showFileFormat">
        <p>{{ 'NOMINATIONS.UPLOAD_PERF_DATA_FILE.FILE_FORMAT_DETAIL' | translate }}</p>
        <ul>
          <li>{{ 'NOMINATIONS.UPLOAD_PERF_DATA_FILE.FILE_FORMAT_DETAIL_1' | translate }}</li>
          <li>{{ 'NOMINATIONS.UPLOAD_PERF_DATA_FILE.FILE_FORMAT_DETAIL_2' | translate }}</li>
          <li>{{ 'NOMINATIONS.UPLOAD_PERF_DATA_FILE.FILE_FORMAT_DETAIL_3' | translate }}</li>
        </ul>
      </div>
      <div class="form-row">
        <div class="form-group col-md-4">
          <label>{{ 'NOMINATIONS.UPLOAD_PERF_DATA_FILE.ASSET' | translate }}</label>
          <ng-multiselect-dropdown
              [placeholder]="('COMPONENTS.SELECT' | translate )"
              [data]="assets"
              [(ngModel)]="asset"
              [settings]="dropdownSettings(true, false)">
          </ng-multiselect-dropdown>
        </div>
        <div class="form-group col-md-6">
            <label>&nbsp;</label>
            <vpp-management-file-uploader
              [options]="uploaderOptions"
              (uploader)="uploaderChangedCallback($event)"
              (propagateFile)="fileChangedCallback($event)"
              #fileUploader>>
            </vpp-management-file-uploader>
        </div>
        <div class="form-group col-md-12">
          <button [disabled]="!fileToUpload || !(asset && asset.length > 0) || disableButton" (click)="uploadFile($event)" class="eon-button bg-eon-red bg-eon-white-disabled">
            <span><i *ngIf="pleaseWait" class="fa fa-spinner"></i> {{ 'NOMINATIONS.UPLOAD_PERF_DATA_FILE.UPLOAD_FILE' | translate }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>