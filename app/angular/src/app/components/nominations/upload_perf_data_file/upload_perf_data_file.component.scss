@import "./../../../styles/colors";
vpp-upload-perf-data-file {

  .tab-content.light-grey {
    position: relative;
    &:before{
      content:"";
      position: absolute;
      width: 0;
      height: 0;
      border-left: 15px solid transparent;
      border-right: 15px solid transparent;
      border-bottom: 15px solid $eon-darkgrey-25;
      top: -15px;
      left: 850px;
    }
  }

  .alerts-wrapper-static {
    margin-bottom: 15px;
  }

  .file-format-box{
    padding: 30px;
    background: #fff;
    border: 1px solid $eon-darkgrey;
  }

  .multiselect-dropdown {
    .dropdown-btn {
      border: 2px solid $eon-turquoise !important;
      background: #fff !important;
      border-radius: 3px !important;
      min-height: 52px !important;
    }
  }

}