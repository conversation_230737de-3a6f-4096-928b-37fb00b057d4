@import "./../../../styles/colors";
vpp-management-nominations-list {
  display: block;

  .form-check {
    padding-top: 36px;
  }

  .input-group-append {
    border: 2px solid #bfbfbf;
    border-left: 0;
    border-radius: 0 4px 4px 0;
    color: #343a40;
  }

  table.table-auction-results {
    tr:first-child > th:first-child {
      border-left:8px solid $eon-turquoise-dark;
    }

    tr.accepted > td:first-child {
      border-left:8px solid #08B002;
    }

    tr.pending > td:first-child {
      border-left:8px solid #c3c3c3;
    }

    tr.partial > td:first-child {
      border-left:8px solid #FFCC00;
    }

    tr.rejected > td:first-child {
      border-left:8px solid #EA1C0A;
    }

    td, th {
      &.text-right {
        text-align: right;
      }

      &.actions-column {
        li {
          cursor: pointer;
        }
      }

      vertical-align: middle;

      label {
        &.eon-checkbox-label {
          display: inline;
        }
      }

      ul {
        display: inline-flex;
        text-align: center;
        width: fit-content;
        margin: 0;

        li {
          display: inline-block;
        }
      }
    }

    td.actions-column {
      i {
        cursor: pointer;
      }
    }
  }

  .custom-day {
    text-align: center;
    padding: 0.185rem 0.25rem;
    display: inline-block;
    height: 2rem;
    width: 2rem;
  }
  .custom-day.focused {
    background-color: #e6e6e6;
  }
  .custom-day.range, .custom-day:hover {
    background-color: rgb(2, 117, 216);
    color: white;
  }
  .custom-day.faded {
    background-color: rgba(2, 117, 216, 0.5);
  }

}
