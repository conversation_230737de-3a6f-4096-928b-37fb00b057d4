<div class="container-fluid" *ngIf="permissions.can_see_nominations">
    <div class="row">
        <div class="col">
            <h2>{{ 'NOMINATIONS.LIST.TITLE' | translate }}</h2>
            <div class="table-filter d-flex">
              <div class="col-md-3">
                  <label>{{ 'NOMINATIONS.LIST.FILTER.START_DATE' | translate }}</label>
                  <div class="input-group">
                      <input
                        type="text"
                        class="form-control"
                        [owlDateTime]="dt1"
                        [selectMode]="'range'"
                        [(ngModel)]="selectedDatesModel"
                        [owlDateTimeTrigger]="dt1"
                        (dateTimeChange)="startDateSelectedCallback($event)"
                        placeholder="">
                      <owl-date-time
                        #dt1
                        (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
                        [pickerMode]="'popup'"></owl-date-time>
                      <div class="input-group-append">
                          <button class="btn btn-default" (click)="clearDateValue()">
                              <i class="fa fa-times"></i>
                          </button>
                      </div>
                  </div>
              </div>
              <div class="form-group">
                 <label for="inputState">{{ 'NOMINATIONS.LIST.FILTER.ENERGY_DIRECTION' | translate }}</label>
                 <select (ngModelChange)="changeEnergyCallback($event)" [(ngModel)]="params.energy_direction" id="inputState" class="form-control">
                    <option *ngFor="let d of energyDirections" [ngValue]="d"> {{ d | translate }} </option>
                 </select>
              </div>
              <div class="form-group">
                <label for="inputState">{{ 'NOMINATIONS.LIST.FILTER.DISPATCH_GROUP' | translate }}</label>
                <input
                  id="dispatchGroup"
                  type="text"
                  class="form-control"
                  name="dispatchGroup"
                  [(ngModel)]="search.dispatchGroup"
                  #dgSearch="ngbTypeahead"
                  [ngbTypeahead]="dispatchGroupSearch"
                  [resultTemplate]="rt"
                  (focus)="focusD$.next($event.target.value)"
                  (click)="clickD$.next($event.target.value)"
                  (blur)="dispatchGroupBlurCallback($event.target.value)"
                  [inputFormatter]="dispatchGroupformatter"/>
                <ng-template #rt let-r="result" let-t="term">
                  <ngb-highlight [result]="'#' + r.id + ' ' + r.name" [term]="t"></ngb-highlight>
                </ng-template>
              </div>
              <div class="form-check">
                <label class="eon-checkbox-label bg-eon-red" [ngClass]="params.deleted == true ? 'checked' : ''" (click)="toggleCheckbox($event)">
                  {{ 'NOMINATIONS.LIST.FILTER.SHOW_DELETED' | translate }}
                </label>
              </div>
            </div>
            <table class="table table-auction-results table-striped table-bg-turquoise">
                <tr>
                    <th class="text-center">{{ 'NOMINATIONS.LIST.TABLE.DISPATCH_GROUP' | translate }}</th>
                    <th class="text-center">{{ 'NOMINATIONS.LIST.TABLE.START' | translate }}</th>
                    <th class="text-center">{{ 'NOMINATIONS.LIST.TABLE.END' | translate }}</th>
                    <th class="text-center">{{ 'NOMINATIONS.LIST.TABLE.ENERGY_DIRECTION' | translate }}</th>
                    <th class="text-center">{{ 'NOMINATIONS.LIST.TABLE.FLEX_CAPACITY' | translate }}<br>(MW)</th>
                    <th class="text-center">{{ 'NOMINATIONS.LIST.TABLE.USE_FOR_ASSET_PRICE' | translate }}<br>{{ 'NOMINATIONS.LIST.TABLE.ENERGY_PRICE' | translate }} (¤ / MWh)<br>{{ 'NOMINATIONS.LIST.TABLE.CAPACITY_PRICE' | translate }} (¤ / MWh)</th>
                    <th></th>
                </tr>
                <tr *ngIf="showLoader">
                  <td colspan="7" class="text-center vertical-center"><vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
                  <td>
                </tr>
                <ng-container *ngFor="let n of nominations">
                  <tr>
                      <input type="hidden" value="{{ n.id }}">
                      <td class="text-center">{{ n.dispatch_group.name }}</td>
                      <td class="text-center">{{ n.start_time | localDate:'DD MMM HH:mm' }}</td>
                      <td class="text-center">{{ n.end_time | localDate:'DD MMM HH:mm' }}</td>
                      <td class="text-center">{{ ('NOMINATIONS.LIST.TABLE.' + n.product.energy_direction) | translate }}</td>
                      <td class="text-center">{{ (n.flex_volume | megawatt) | number: '1.3-3' }}</td>
                      <td class="text-center">
                        <label
                          class="eon-checkbox-label non-clickable bg-eon-red"
                          [ngClass]="n.used_for_asset_price == true ? 'checked' : ''"
                          (click)="useForAssetPrice(n)">
                        </label>&nbsp;
                        <span>{{ n.energy_price | number: '1.1-1' }}</span>&nbsp;
                        <span *ngIf="n.capacity_price">{{ n.capacity_price | number: '1.2-2' }}</span>
                        <span *ngIf="!n.capacity_price">n/a</span>
                        <ng-container *ngIf="n.assets.length">
                          <ul>
                            <li *ngFor="let asset of n.assets">(#{{ asset.id }}) {{ asset.name }}</li>
                          </ul>
                        </ng-container>
                      </td>
                      <td class="text-center actions-column">
                        <ul class="list-inline list-unstyled" *ngIf="!n.deleted">
                          <li *ngIf="permissions.can_update_nominations"><span (click)="editNomination(n)"  style="display: inline-block"><i class="fa fa-pencil"></i></span></li>
                          <li *ngIf="permissions.can_delete_nominations"><span (click)="deleteNomination(n)"><i class="fa fa-trash"></i></span></li>
                        </ul>
                        <span *ngIf="n.deleted">{{ 'NOMINATIONS.LIST.TABLE.DELETED' | translate }}</span>
                      </td>
                  </tr>
                </ng-container>
            </table>
            <div class="d-flex justify-content-between">
              <ngb-pagination
                [collectionSize]="collectionSize"
                [(page)]="page"
                [ellipses]="true"
                [maxSize]="5"
                (pageChange)="pageChangedCallback($event)"
                [pageSize]="pageSize">
              </ngb-pagination>

              <select
                class="custom-select"
                style="width: auto"
                [(ngModel)]="pageSize"
                (ngModelChange)="pageSizeChangedCallback($event)">
                <option [ngValue]="25">25 {{ 'NOMINATIONS.LIST.TABLE.PER_PAGE' | translate }}</option>
                <option [ngValue]="50">50 {{ 'NOMINATIONS.LIST.TABLE.PER_PAGE' | translate }}</option>
                <option [ngValue]="75">75 {{ 'NOMINATIONS.LIST.TABLE.PER_PAGE' | translate }}</option>
              </select>
            </div>
        </div>
    </div>
</div>
<ng-template #edit>
  <vpp-enter-nomination [distributedUnit]="editedNomination" (done)="closeNominationModal()"></vpp-enter-nomination>
</ng-template>