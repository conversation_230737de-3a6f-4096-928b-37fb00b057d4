import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import {NgbDate, NgbCalendar} from '@ng-bootstrap/ng-bootstrap';
import * as moment from "moment-timezone";
import { Observable, Subject, merge } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map } from 'rxjs/operators';
import { angularData } from "./../../../../global.export";
import { NominationProvider } from "./../../../providers/nomination.provider";
import { DispatchGroupProvider } from "./../../../providers/dispatch_group.provider";
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { NotificationService } from "./../../../services/notification.service";
import { NgbTypeahead } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { GlobalService } from "./../../../services/global.service";
import {ConfirmService} from "../../../services/confirm.service";

@Component({
  selector: "vpp-management-nominations-list",
  templateUrl: "./nominations_list.component.html",
  styleUrls: ["./nominations_list.component.scss"],
  encapsulation: ViewEncapsulation.None
})

export class NominationsListComponent implements OnInit {
  bidsList = [];
  directions = ["Select Direction"];
  params = {
    page: 1,
    per_page: 25,
    energy_direction: '',
    dispatch_group_id: null,
    deleted: false,
    start_date: '',
    end_date: ''
  };
  search = {
    dispatchGroup: null
  };
  page = 1;
  pageSize = 25;
  collectionSize = 0;
  dispatchGroups = [];
  hoveredDate: NgbDate;
  fromDate: NgbDate;
  toDate: NgbDate;
  nominations: any = [];
  editedNomination: any;
  bsModalRef;
  energyDirections = ['Select Direction', 'negative', 'positive'];
  showLoader: boolean = false;
  focusD$ = new Subject<string>();
  clickD$ = new Subject<string>();
  confirmDeleteMessage = '';

  selectedDate = {
    startDateTime: null,
    endDateTime: null
  };
  selectedDatesModel = [null, null];
  permissions: any;

  @ViewChild('edit', { static: true }) editModal;
  @ViewChild('dgSearch', { static: false }) dgSearch: NgbTypeahead;

  dispatchGroupSearch = (text$: Observable<string>) => {
    const debouncedText$ = text$.pipe(debounceTime(200), distinctUntilChanged());
    const clicksWithClosedPopup$ = this.clickD$.pipe(filter(() => !this.dgSearch.isPopupOpen()));
    const inputFocus$ = this.focusD$;

    return merge(debouncedText$, inputFocus$, clicksWithClosedPopup$).pipe(
      map(term => (term === '' ? this.dispatchGroups
        : this.dispatchGroups.filter(v => (v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 || (v.id + '').indexOf(term.toLowerCase()) > -1 ) )))
    );
  };

  dispatchGroupformatter = (result: any) => {
    this.dispatchGroupSelectedCallback(result);
    return `#${result.id} ${result.name}`;
  };

  constructor(
    private _DispatchGroup: DispatchGroupProvider,
    private _NominationProvider: NominationProvider,
    private calendar: NgbCalendar,
    private _ModalService: NgbModal,
    private _NotificationService: NotificationService,
    public translate: TranslateService,
    public globalService: GlobalService,
    private _confirmService: ConfirmService
  ) {
    this.permissions = angularData.permissions;
    this.fromDate = calendar.getToday();
    this.toDate = calendar.getNext(calendar.getToday(), 'd', 10);
    this.translate.get('NOMINATIONS.LIST.FILTER.SELECT_ENERGY_DIRECTION').subscribe((res: string) => {
      this.energyDirections = [res, 'negative', 'positive'];
    });
    this.translate.get('CONFIRM.MESSAGE.DELETE_ENTRY').subscribe((x) => {
      this.confirmDeleteMessage = x;
    });
  }

  ngOnInit() {
    this.getNominationData();
    this.getDispatchGroups();
  }

  getDispatchGroups() {
    this._DispatchGroup.findAllWithPagination().subscribe(result => {
      this.dispatchGroups = result.dispatch_groups;
    });
  }

  getNominationData() {
    this.nominations = [];
    this.showLoader = true;
    this._NominationProvider.findBy(this.params).subscribe(
      results => {
        this.showLoader = false;
        this.nominations = results.data.distributed_units;
        this.collectionSize = results.total_entries;
      },
      error => {
        this.showLoader = false;
      }
    );
  }

  pageChangedCallback(page) {
    this.params.page = page;
    this.getNominationData();
  }

  dispatchGroupSelectedCallback(dg) {
    if (dg) {
      this.params.dispatch_group_id = dg.id;
    } else {
       this.params.dispatch_group_id = '';
    }
    this.getNominationData();
  }

  useForAssetPrice(nomination) {
  }

  editNomination(nomination) {
    this.editedNomination = nomination;
    this.bsModalRef = this._ModalService.open(this.editModal, { size: 'lg' });
  }

  closeNominationModal() {
    this.bsModalRef.close();
    this.getNominationData();
  }

  startDateSelectedCallback(date) {
    console.log('date selected', date);
    if (date.value[0] && date.value[1]) {
      this.selectedDate = {
        startDateTime: date.value[0].startOf('minute').toISOString(),
        endDateTime: date.value[1].startOf('minute').toISOString()
      };
      this.params.start_date = this.selectedDate.startDateTime;
      this.params.end_date = this.selectedDate.endDateTime;
    } else {
      this.params.start_date = '';
      this.params.end_date = '';
      this.selectedDatesModel = [null, null];
    }
    this.getNominationData();
  }

  clearDateValue() {
    this.params.start_date = '';
    this.params.end_date = '';
    this.selectedDatesModel = [null, null];
    this.getNominationData();
  }

  deleteNomination(nomination) {
    this._confirmService.confirm({
      message: this.confirmDeleteMessage,
    }).then(() => {
      this._deleteConfirmedNomination(nomination);
    }, () => {
    });
  }

  private _deleteConfirmedNomination(nomination) {
    this._NominationProvider
        .deleteById(nomination.id)
        .subscribe(result => {
          if (result.success == true) {
            this._NotificationService.success({ text: 'SUCCESS'});
          } else {
            this._NotificationService.error({ text: JSON.stringify(result.error)});
          }
          this.getNominationData();
        });
  }

  toggleCheckbox(event) {
    this.params.deleted = !this.params.deleted;
    this.getNominationData();
  }

  changeEnergyCallback(direction) {
    this.params.energy_direction = direction;
    this.getNominationData();
  }

  pageSizeChangedCallback(pageSize) {
    this.params.per_page = pageSize;
    this.params.page = 1;
    this.getNominationData();
  }

  dispatchGroupBlurCallback(value) {
    if (!value) {
       this.params.dispatch_group_id = '';
      this.getNominationData();
    }
  }
}
