<div class="container-fluid">
  <div class="row">
    <div class="col-md-8">
      <div class="page-actions">
        <ul class="list-unstyled d-flex">
          <li *ngIf="permissions.can_create_nominations">
            <a href="javascript:void(0)" (click)="toggleSection($event, 'enter-nominations')" class="eon-button bg-eon-turquoise">{{ 'NOMINATIONS.TABS.ENTER_NOMINATIONS' | translate }}</a>
          </li>
          <li *ngIf="permissions.can_create_auction_results">
            <a href="javascript:void(0)" (click)="toggleSection($event, 'upload-market-result')" class="eon-button bg-eon-bordeaux">{{ 'NOMINATIONS.TABS.UPLOAD_MARKET_RESULTS' | translate }}</a>
          </li>
          <li *ngIf="permissions.can_create_nominations">
            <a href="javascript:void(0)" (click)="toggleSection($event, 'upload-nomination-file')" class="eon-button bg-eon-limeyellow">{{ 'NOMINATIONS.TABS.UPLOAD_NOMINATIONS_FILE' | translate }}</a>
          </li>
        </ul>
        <br/>
        <ul class="list-unstyled d-flex">
          <li *ngIf="permissions.can_create_nominations">
            <a href="javascript:void(0)" (click)="toggleSection($event, 'upload-opti-results-file')" class="eon-button bg-eon-darkgray">{{ 'NOMINATIONS.TABS.UPLOAD_OPTI_RESULTS_FILE' | translate }}</a>
          </li>
          <li *ngIf="permissions.can_create_nominations">
            <a href="javascript:void(0)" (click)="toggleSection($event, 'download-opti-results')" class="eon-button bg-eon-darkgray">{{ 'NOMINATIONS.TABS.DOWNLOAD_OPTI_RESULTS' | translate }}</a>
          </li>
          <li *ngIf="permissions.can_create_nominations">
            <a href="javascript:void(0)" (click)="toggleSection($event, 'upload-perf-data-file')" class="eon-button bg-eon-darkgray">{{ 'NOMINATIONS.TABS.UPLOAD_PERF_DATA_FILE' | translate }}</a>
          </li>
        </ul>
        <br/>
        <ul class="list-unstyled d-flex">
          <li *ngIf="permissions.can_create_nominations">
            <a href="javascript:void(0)" (click)="toggleSection($event, 'upload-bod-file')" class="eon-button bg-eon-darkgray">{{ 'NOMINATIONS.TABS.UPLOAD_BOD_FILE' | translate }}</a>
          </li>
          <li *ngIf="permissions.can_create_nominations">
            <a href="javascript:void(0)" (click)="toggleSection($event, 'upload-margin-data-file')" class="eon-button bg-eon-darkgray">{{ 'NOMINATIONS.TABS.UPLOAD_MARGIN_DATA_FILE' | translate }}</a>
          </li>
            <li *ngIf="permissions.can_create_nominations">
            <a href="javascript:void(0)" (click)="toggleSection($event, 'upload_declaration_of_unavailability')" class="eon-button bg-eon-darkgray">{{ 'NOMINATIONS.TABS.UPLOAD_DECLARATION_OF_UNAVAILABILITY' | translate }}</a>
          </li>

        </ul>  
      </div>
    </div>
  </div>
  <div class="overlay-on-top-of-list" [@slideRightContentAnimation]="selectedSelection == 'enter-nominations' ? 'in' : 'out'">
    <vpp-enter-nomination *ngIf="selectedSelection == 'enter-nominations'" (done)="nominationCreated()"></vpp-enter-nomination>
  </div>
  <div class="overlay-on-top-of-list" [@slideRightContentAnimation]="selectedSelection == 'upload-nomination-file' ? 'in' : 'out'">
    <vpp-upload-nomination-file *ngIf="selectedSelection == 'upload-nomination-file'"></vpp-upload-nomination-file>
  </div>
  <div class="overlay-on-top-of-list" [@slideRightContentAnimation]="selectedSelection == 'upload-market-result' ? 'in' : 'out'">
    <vpp-upload-market-result *ngIf="selectedSelection == 'upload-market-result'"></vpp-upload-market-result>
  </div>
  <div class="overlay-on-top-of-list" [@slideRightContentAnimation]="selectedSelection == 'upload-opti-results-file' ? 'in' : 'out'">
    <vpp-upload-opti-results-file *ngIf="selectedSelection == 'upload-opti-results-file'"></vpp-upload-opti-results-file>
  </div>
  <div class="overlay-on-top-of-list" [@slideRightContentAnimation]="selectedSelection == 'download-opti-results' ? 'in' : 'out'">
    <vpp-download-opti-results *ngIf="selectedSelection == 'download-opti-results'"></vpp-download-opti-results>
  </div>
  <div class="overlay-on-top-of-list" [@slideRightContentAnimation]="selectedSelection == 'upload-perf-data-file' ? 'in' : 'out'">
    <vpp-upload-perf-data-file *ngIf="selectedSelection == 'upload-perf-data-file'"></vpp-upload-perf-data-file>
  </div>
  <div class="overlay-on-top-of-list" [@slideRightContentAnimation]="selectedSelection == 'upload-bod-file' ? 'in' : 'out'">
    <vpp-upload-bod-file *ngIf="selectedSelection == 'upload-bod-file'"></vpp-upload-bod-file>
  </div>
  <div class="overlay-on-top-of-list" [@slideRightContentAnimation]="selectedSelection == 'upload-margin-data-file' ? 'in' : 'out'">
    <vpp-upload-margin-data-file *ngIf="selectedSelection == 'upload-margin-data-file'"></vpp-upload-margin-data-file>
  </div>
    <div class="overlay-on-top-of-list" [@slideRightContentAnimation]="selectedSelection == 'upload_declaration_of_unavailability' ? 'in' : 'out'">
    <vpp-upload-declaration-of-unavailability *ngIf="selectedSelection == 'upload_declaration_of_unavailability'"></vpp-upload-declaration-of-unavailability>
  </div>
  <section>
    <div class="row">
      <div class="col-md-12">
        <vpp-management-nominations-list *ngIf="loadNominationList"></vpp-management-nominations-list>
      </div>
    </div>
  </section>
</div>
