<div class="row">
  <div class="col-md-8">
    <div class="tab-content light-yellow">
      <h2>{{ 'NOMINATIONS.UPLOAD_NOMINATIONS_FILE.TITLE' | translate }}</h2>
      <vpp-file-upload-results [response]="response"></vpp-file-upload-results>
      <div class="alerts-wrapper-static" *ngIf="sourceId">
        <ng-container *ngIf="sourceId">
          <a href="{{environment.apiPath}}/nominations/nomination_detail/{{ sourceId }}" target="_blank">
            <i class="fas fa-eye"></i> {{ 'NOMINATIONS.ENTER_NOMINATIONS.VIEW_DETAILS' | translate }}
          </a>
        </ng-container>
      </div>
      <a (click)="showFileFormat = !showFileFormat" class="view-more-link" *ngIf="showFileFormat">{{ 'NOMINATIONS.UPLOAD_NOMINATIONS_FILE.HIDE' | translate }}</a>
      <a (click)="showFileFormat = !showFileFormat" class="view-more-link" *ngIf="!showFileFormat">{{ 'NOMINATIONS.UPLOAD_NOMINATIONS_FILE.SHOW' | translate }}</a>
      <div class="file-format-box" *ngIf="showFileFormat">
        <p>{{ 'NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_FORMAT_DETAIL' | translate }}</p>
        <code [innerHtml]="'NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_FORMAT' | translate"></code>

        <p>{{ 'NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_LIST_TITLE' | translate }}</p>
        <ul>
          <li>{{ 'NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_1' | translate }}</li>
          <li>{{ 'NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_2' | translate }}</li>
          <li>{{ 'NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_3' | translate }}</li>
          <li>{{ 'NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_4' | translate }}</li>
        </ul>
      </div>
      <div class="form-row">
        <div class="form-group col-md-6">
            <vpp-management-file-uploader
              [options]="uploaderOptions"
              (uploader)="uploaderChangedCallback($event)"
              (propagateFile)="fileChangedCallback($event)"
              #fileUploader>>
            </vpp-management-file-uploader>
        </div>
        <div class="form-group col-md-6"></div>
        <div class="form-group col-md-12">
          <div class="form-check">
            <label
              class="eon-checkbox-label bg-eon-red"
              (click)="toggleCheckbox()"
              [ngClass]="checkbox ? 'checked': ''">{{ 'NOMINATIONS.UPLOAD_NOMINATIONS_FILE.ALLOW_MODIFICATIONS_WITH_IMMEDIATE_EFFECT' | translate }}</label>
          </div>
        </div>
        <div class="form-group col-md-12">
          <button [disabled]="!fileToUpload" (click)="uploadFile($event)" class="eon-button bg-eon-red">
            <span>{{ 'NOMINATIONS.UPLOAD_NOMINATIONS_FILE.UPLOAD_NOMINATIONS_FILE' | translate }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>