<div class="row">
  <div class="col-md-12">
    <div class="tab-content light-grey">
      <h2>{{ 'NOMINATIONS.UPLOAD_BOD_FILE.TITLE' | translate }}</h2>
      <vpp-file-upload-results [response]="response"></vpp-file-upload-results>

      <a (click)="showFileFormat = !showFileFormat" class="view-more-link" *ngIf="showFileFormat">{{ 'NOMINATIONS.UPLOAD_BOD_FILE.HIDE' | translate }}</a>
      <a (click)="showFileFormat = !showFileFormat" class="view-more-link" *ngIf="!showFileFormat">{{ 'NOMINATIONS.UPLOAD_BOD_FILE.SHOW' | translate }}</a>
      <div class="file-format-box" *ngIf="showFileFormat">
        <p>{{ 'NOMINATIONS.UPLOAD_BOD_FILE.CSV_FORMAT_DETAIL' | translate }}</p>
        <code [innerHtml]="'NOMINATIONS.UPLOAD_BOD_FILE.CSV_FORMAT' | translate"></code>
        <ul>
          <li>{{ 'NOMINATIONS.UPLOAD_BOD_FILE.CSV_1' | translate }}</li>
          <li>{{ 'NOMINATIONS.UPLOAD_BOD_FILE.CSV_2' | translate }}</li>
          <li>{{ 'NOMINATIONS.UPLOAD_BOD_FILE.CSV_3' | translate }}</li>
          <li>{{ 'NOMINATIONS.UPLOAD_BOD_FILE.CSV_4' | translate }}</li>
          <li>{{ 'NOMINATIONS.UPLOAD_BOD_FILE.CSV_5' | translate }}</li>
          <li>{{ 'NOMINATIONS.UPLOAD_BOD_FILE.CSV_6' | translate }}</li>
        </ul>
      </div>
      <div class="form-row">
        <div class="form-group col-md-6">
            <label>&nbsp;</label>
            <vpp-management-file-uploader
              [options]="uploaderOptions"
              (uploader)="uploaderChangedCallback($event)"
              (propagateFile)="fileChangedCallback($event)"
              #fileUploader>>
            </vpp-management-file-uploader>
        </div>
        <div class="form-group col-md-12">
          <button [disabled]="!fileToUpload" (click)="uploadFile($event)" class="eon-button bg-eon-red bg-eon-white-disabled">
            <span>{{ 'NOMINATIONS.UPLOAD_BOD_FILE.UPLOAD_BOD_FILE' | translate }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>