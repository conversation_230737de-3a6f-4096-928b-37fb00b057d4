<div class="row">
  <div class="col-md-10">
    <div class="tab-content light-red">
      <h2 id="upload-market-results-form">{{ 'NOMINATIONS.UPLOAD_MARKET_RESULTS.TITLE' | translate }}</h2>

      <vpp-file-upload-results [response]="response"></vpp-file-upload-results>

      <div class="alerts-wrapper-static" *ngIf="sourceId">
        <ng-container *ngIf="sourceId">
          <ng-container *ngIf="sourceType == 'DistributedUnitSource'">
            <a href="{{environment.apiPath}}/nominations/nomination_detail/{{ sourceId }}" target="_blank">
              <i class="fas fa-eye"></i> {{ 'NOMINATIONS.UPLOAD_MARKET_RESULTS.VIEW_DETAILS' | translate }}
            </a>
          </ng-container>
          <ng-container *ngIf="sourceType == 'NominationBidSource'">
            <a href="{{environment.apiPath}}/history/bid_detail/{{ sourceId }}" target="_blank">
              <i class="fas fa-eye"></i> {{ 'NOMINATIONS.UPLOAD_MARKET_RESULTS.VIEW_DETAILS' | translate }}
            </a>
          </ng-container>
        </ng-container>
      </div>
      <div class="form-row">
        <div class="form-group col-md-4">
          <label>{{ 'NOMINATIONS.UPLOAD_MARKET_RESULTS.AUCTION' | translate }}</label>
          <ng-multiselect-dropdown
              [placeholder]="('COMPONENTS.SELECT' | translate )"
              [data]="auctionConfigs"
              [(ngModel)]="auctionConfig"
              [settings]="dropdownSettings(true, false)">
          </ng-multiselect-dropdown>
        </div>
        <div class="form-group col-md-5">
          <label>&nbsp;</label>
          <vpp-management-file-uploader
            [options]="uploaderOptions"
            (uploader)="uploaderChangedCallback($event)"
            (propagateFile)="fileChangedCallback($event)"
            #fileUploader>>
          </vpp-management-file-uploader>
        </div>
        <div class="form-group col-md-3">
          <label>&nbsp;</label>
        </div>
        <div class="form-group col-md-12">
          <div *ngIf="pleaseWait" class="pb-2">
            <span class="alert-warning">{{ 'NOMINATIONS.UPLOAD_MARKET_RESULTS.UPLOAD_INFO'  | translate }}</span>
          </div>
          <button [disabled]="!fileToUpload || !permissions.can_create_auction_results || !(auctionConfig && auctionConfig.length > 0)" (click)="uploadFile($event)" href="#" class="eon-button bg-eon-red">
            <span><i *ngIf="pleaseWait" class="fa fa-spinner"></i> {{ 'NOMINATIONS.UPLOAD_MARKET_RESULTS.UPLOAD' | translate }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>