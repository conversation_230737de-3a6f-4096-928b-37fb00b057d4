import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { UrlProvider } from "./../../../providers/url.provider";
import { HttpClientService } from "./../../../services/httpclient.service";
import { NotificationService } from "./../../../services/notification.service";
import { environment } from "../../../../environments/environment";
import { TranslateService } from '@ngx-translate/core';
import {AuctionsProvider} from "../../../providers/auctions.provider";
import { angularData } from "./../../../../global.export";

@Component({
  selector: "vpp-upload-market-result",
  templateUrl: "./upload_market_result.component.html",
  styleUrls: ["./upload_market_result.component.scss"],
  encapsulation: ViewEncapsulation.None
})

export class UploadMarketResultComponent implements OnInit {
  page = 1;
  pageSize = 25;
  collectionSize = 0;
  params = {
    page: this.page,
    per_page: this.pageSize
  };
  fileToUpload;
  fileUploaderReference;
  uploaderOptions ={
    url: this._UrlProvider.getUploadMarketResultsUrl()
  };
  auctionConfig;
  auctionConfigs;
  csr_token: string = "";
  disableButton: boolean = true;
  environment = environment;
  response: any = null;
  sourceId: number = null;
  sourceType: string = null;
  permissions: any;
  pleaseWait = false;

  @ViewChild("fileUploader", { static: true }) fileUploader: any;

  constructor(
    private _HttpClientService: HttpClientService,
    private _UrlProvider: UrlProvider,
    private _NotificationService: NotificationService,
    public translate: TranslateService,
    private _auctionsProvider: AuctionsProvider,
  ) {
    this.uploaderOptions.url = this._UrlProvider.getUploadMarketResultsUrl();
    this.csr_token = this._HttpClientService.getAuthToken();
    this.permissions = angularData.permissions;
  }

  ngOnInit() {
    this.getAuctionConfigs();
  }

  fileChangedCallback(file) {
    console.log('file')
    this.fileToUpload = file;

    console.log('fileToUpload', this.fileToUpload)
  }

  uploaderChangedCallback(uploader) {
    this.fileUploaderReference = uploader;
  }

  uploadFile(event) {
    this.response = null;
    event.preventDefault();
    event.stopPropagation();
    this.fileToUpload.url = this._UrlProvider.getUploadMarketResultsUrl();

    this.fileUploaderReference.onBuildItemForm = (
      fileItem: any,
      form: any
    ) => {
      form.append("authenticity_token", this.csr_token);
      form.append("auction_config_id", this.auctionConfig[0].id);
    };
    this.pleaseWait = true;
    this.disableButton = true;
    this.fileToUpload.upload();

    this.fileUploaderReference.onCompleteItem = (item:any, response:any, status:any, headers:any) => {
      try {
        if (status == 200) {
          let data = JSON.parse(response);
          this.response = data;
          if (data.success) {
            let source = data.result;
            this.sourceId = source.id;
            this.sourceType = data.result_type
            this.disableButton = false;
            this.pleaseWait = false;
          } else {
            document.getElementById("upload-market-results-form").scrollIntoView();
          }
        } else {
          this.response = {
            error: "unexpected error HTTP status " +  status,
            messages: [response]
          }
        }
      } catch (e) {
        console.log('caught', e);
        this.response = {
          error: e.name,
          messages: [e.message]
        }
      }
      this.pleaseWait = false
      this.fileToUpload = null;
      this.fileUploader.clearFileUploaderQueue();
    };
  }

  getAuctionConfigs() {
    this._auctionsProvider.findAll({active: true}).subscribe(
      res => {
        this.auctionConfigs = res.auctions;
        console.log("LOADED AUCTION CONFIGS", this.auctionConfigs)
      }, err => {
        console.log('Failed to load auction configs', err);
      }
    );
  }

  dropdownSettings(singleSelection, allowSearchFilter) {
    return {
      idField: 'id',
      textField: 'name',
      singleSelection: singleSelection,
      selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
      unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
      searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
      noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
      itemsShowLimit: 10,
      allowSearchFilter: allowSearchFilter
    }
  };
}
