import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef,
  Input,
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { NominationProvider } from "./../../../providers/nomination.provider";
import { TranslateService } from '@ngx-translate/core';
import { environment } from "../../../../environments/environment";
import { angularData } from "./../../../../global.export";
import { GlobalService } from "./../../../services/global.service";
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'vpp-management-nomination-detail',
  styleUrls: ['./nomination_detail.component.scss'],
  templateUrl: "./nomination_detail.component.html",
  encapsulation: ViewEncapsulation.None,
  animations: []
})

export class NominationDetailComponent {
  environment = environment;
  nominationSource: any = {
    validation_result: {
      errors: [],
      warnings: [],
      validationSuccess: null
    },
    content: {
      fileName: null
    },
    content_type: null,
    user_account: {
      name: null,
      email: null
    },
    distributed_units: []
  };
  editedNomination: any;
  bsModalRef;
  permissions: any;

  @ViewChild('edit', { static: true }) editModal;
  @Input('id') nominationId;

  constructor(
    private _Global: GlobalService,
    private _Nomination: NominationProvider,
    private _Route: ActivatedRoute,
    public translate: TranslateService,
    private _ModalService: NgbModal
  ) {
    this.permissions = angularData.permissions;
    this.translate.get('HISTORY.NOMINATIONS.DETAIL.TITLE').subscribe((res: string) => {
        this._Global.changeTitle(res);
    });
  }

  ngOnInit() {
    if (this.nominationId) {
      this.getNomination();
    }
  }

  ngAfterViewInit() {
    this._Route.params.forEach((params: Params) => {
      if (params["id"]) {
        this.nominationId = params["id"];
        this.getNomination();
      }
    });
  }

  getNomination() {
    this._Nomination.findById(this.nominationId).subscribe(
      (data) => {
        this.nominationSource = data;
        if (this.nominationSource && this.nominationSource.validation_result) {
          if (this.nominationSource.validation_result.errors) {
            this.nominationSource.validation_result.errors.map((e) => {
              e['error_id'] = e.name.replace(/(?:^|\.?)([A-Z])/g, function (x, y) {
                return "_" + y.toLowerCase()
              }).replace(/^_/, "").toUpperCase();
              return e;
            });
          }
          if (this.nominationSource.validation_result.warnings) {
            this.nominationSource.validation_result.warnings.map((w) => {
              w['warning_id'] = w.name.replace(/(?:^|\.?)([A-Z])/g, function (x, y) {
                return "_" + y.toLowerCase()
              }).replace(/^_/, "").toUpperCase();
              return w;
            });
          }
        }
        console.log('this.nominationSource', this.nominationSource);
      }
    )
  }

  editNomination(nomination) {
    this.editedNomination = nomination;
    this.bsModalRef = this._ModalService.open(this.editModal, { size: 'lg' });
  }

  closeNominationModal() {
    this.bsModalRef.close();
    this.getNomination();
  }
}