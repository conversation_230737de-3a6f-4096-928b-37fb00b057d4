<div class="container-fluid">
  <section>
    <div class="row">
      <div class="col-md-2">
        <a class="back-button" [routerLink]="[environment.routingPath + '/history/nominations']"><i class="fa fa-chevron-left"></i> {{ 'BACK' | translate }}</a>
        <br>
      </div>
    </div>
    <div class="row">
      <div class="alerts-wrapper-static col-md-8 col-lg-6" *ngIf="nominationSource.id">
        <ngb-alert
          *ngIf="nominationSource.validation_result.validationSuccess"
          [dismissible]="true"
          [type]="'success'"> {{ 'SUCCESS' | translate }}
        </ngb-alert>
        <ngb-alert
          *ngIf="!nominationSource.validation_result.validationSuccess"
          [dismissible]="false"
          [type]="'error'"> {{ 'FAILURE' | translate }}
        </ngb-alert>
      </div>
    </div>
    <div class="row">
      <div class="col-md-8 col-lg-6">
        <div class="card card-allocated">
          <div class="card-body">
            <div class="stats-group">
              <label>{{ 'HISTORY.NOMINATIONS.DETAIL.VALIDATION' | translate }}</label>
              <span class="value">
                {{ 'HISTORY.NOMINATIONS.DETAIL.ERRORS' | translate }}: {{ nominationSource?.validation_result?.errors?.length }}
                <br>
                {{ 'HISTORY.NOMINATIONS.DETAIL.WARNINGS' | translate }}: {{ nominationSource?.validation_result?.warnings?.length }}
              </span>
            </div>
            <div class="stats-group">
              <label>{{ 'HISTORY.NOMINATIONS.DETAIL.TYPE' | translate }}</label>
              <span class="value">
                {{ ('HISTORY.NOMINATIONS.DETAIL.CONTENT_TYPE.' + (nominationSource.content_type | nominationSourceType)) | translate }}
                <span *ngIf="nominationSource.content_type === 'file_import'">
                  <br>
                  <a href="{{environment.routingPath}}/asset_dg_allocation_sources/{{ nominationSource.id }}/download" target="_blank">{{ nominationSource.content.fileName }}</a>
                </span>
              </span>
            </div>
             <div class="stats-group">
              <label>{{ 'HISTORY.NOMINATIONS.DETAIL.PERFORMED_ON' | translate }}</label>
              <span class="value">{{ nominationSource.created | localDate:'ddd, DD MMM YYYY HH:mm' }}</span>
            </div>
            <div class="stats-group">
              <label>{{ 'HISTORY.NOMINATIONS.DETAIL.USER' | translate }}</label>
              <span class="value">{{ nominationSource.user_account.name }} {{ '<' + nominationSource.user_account.email + '>' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <table class="table table-bg-turquoise table-striped" *ngIf="nominationSource && nominationSource?.validation_result?.errors?.length">
    <thead>
      <tr>
        <th colspan="5">{{ 'HISTORY.NOMINATIONS.DETAIL.TABLE.ERROR_DESCRIPTION' | translate }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let w of nominationSource.validation_result.errors">
        <td>{{ w.name }}</td>
        <td>
          <table style="width: 100%">
            <tr *ngFor="let e of w.entries">
              <td>row {{ e.index }}</td>
              <td>{{ e.entry.startTime | localDate:'DD MMM HH:mm' }} / {{ e.entry.endTime | localDate:'DD MMM HH:mm' }}</td>
              <td>{{ 'HISTORY.NOMINATIONS.DETAIL.TABLE.DG' | translate }} {{ e.entry.dgId }}</td>
              <td>{{ e.entry.energyDirection }}</td>
              <td>{{ e.entry.flexVolume }}</td>
              <td>{{ e.entry.energyPrice }}</td>
              <td></td>
            </tr>
          </table>
          {{ 'HISTORY.NOMINATIONS.DETAIL.TABLE.DETAILS' | translate }}: {{ w.details }}
        </td>
      </tr>
    </tbody>
  </table>
  <table class="table table-bg-turquoise table-striped" *ngIf="nominationSource && nominationSource?.validation_result?.warnings?.length">
    <thead>
      <tr>
        <th colspan="5">{{ 'HISTORY.NOMINATIONS.DETAIL.TABLE.WARNING_DESCRIPTION' | translate }} ({{ nominationSource.validation_result.warnings.length }})</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let w of nominationSource.validation_result.warnings">
        <td>{{ w.name }}</td>
        <td>
          <table style="width:100%">
            <tr *ngFor="let e of w.entries">
              <td>row {{ e.index }}</td>
              <td>{{ e.entry.startTime | localDate:'DD MMM HH:mm' }} / {{ e.entry.endTime | localDate:'DD MMM HH:mm' }}</td>
              <td>{{ 'HISTORY.NOMINATIONS.DETAIL.TABLE.DG' | translate }} {{ e.entry.dgId }}</td>
              <td>{{ e.entry.energyDirection }}</td>
              <td>{{ e.entry.flexVolume }}</td>
              <td>{{ e.entry.energyPrice }}</td>
              <td></td>
            </tr>
          </table>
          {{ 'HISTORY.NOMINATIONS.DETAIL.TABLE.DETAILS' | translate }}: {{ w.details }}
        </td>
      </tr>
    </tbody>
  </table>
  <table class="table table-bg-turquoise table-striped" *ngIf="nominationSource.distributed_units.length">
    <thead>
      <tr>
        <th colspan="5">{{ 'HISTORY.NOMINATIONS.DETAIL.TABLE.DISTRIBUTED_UNITS' | translate }} ({{ nominationSource.distributed_units.length }})</th>
      </tr>
      <tr>
        <th>{{ 'HISTORY.NOMINATIONS.DETAIL.TABLE.DISPATCH_GROUP' | translate }}</th>
        <th>{{ 'HISTORY.NOMINATIONS.DETAIL.TABLE.START' | translate }}</th>
        <th>{{ 'HISTORY.NOMINATIONS.DETAIL.TABLE.END' | translate }}</th>
        <th>{{ 'HISTORY.NOMINATIONS.DETAIL.TABLE.ENERGY_DIRECTION' | translate }}</th>
        <th>{{ 'HISTORY.NOMINATIONS.DETAIL.TABLE.FLEX_CAPACITY' | translate }}</th>
        <th align="center">
          {{ 'HISTORY.NOMINATIONS.DETAIL.TABLE.USED_FOR_ASSET_PRICE' | translate }}<br>
          {{ 'HISTORY.NOMINATIONS.DETAIL.TABLE.ENERGY_PRICE' | translate }}<br>
          {{ 'HISTORY.NOMINATIONS.DETAIL.TABLE.CAPACITY_PRICE' | translate }}
        </th>
        <th></th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let n of nominationSource.distributed_units">
        <td>{{ n.dispatch_group.name }}</td>
        <td>{{ n.start_time | localDate:'ddd, DD MMM HH:mm:ss' }}</td>
        <td>{{ n.end_time | localDate:'ddd, DD MMM HH:mm:ss' }}</td>
        <td>{{ n.energy_direction | translate }}</td>
        <td>{{ n.flex_volume }}</td>
        <td align="center">
          <label
            style="display: inline"
            class="eon-checkbox-label bg-eon-red"
            [ngClass]="n.used_for_asset_price == true ? 'checked' : ''">
          </label>&nbsp;
          {{ n.energy_price }} (¤ / MWh) {{ n.capacity_price }} (¤ / MWh)
        </td>
        <td class="text-center actions-column">
          <ul class="list-inline list-unstyled" *ngIf="!n.deleted && permissions.can_create_nominations">
            <li><span (click)="editNomination(n)"  style="display: inline-block"><i class="fa fa-pencil"></i></span></li>
          </ul>
          <span *ngIf="n.deleted">{{ 'NOMINATIONS.LIST.TABLE.DELETED' | translate }}</span>
        </td>
      </tr>
    </tbody>
  </table>
</div>
<ng-template #edit>
  <vpp-enter-nomination [distributedUnit]="editedNomination" (done)="closeNominationModal()"></vpp-enter-nomination>
</ng-template>