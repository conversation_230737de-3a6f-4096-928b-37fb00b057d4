import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef, Output, EventEmitter
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { UrlProvider } from "./../../../providers/url.provider";
import { AssetProvider } from "./../../../providers/asset.provider";
import { HttpClientService } from "./../../../services/httpclient.service";
import { NotificationService } from "./../../../services/notification.service";
import { environment } from "../../../../environments/environment";
import { GlobalService } from "./../../../services/global.service";
import { TranslateService } from '@ngx-translate/core';
import {angularData} from "../../../../global.export";
import {Moment} from "moment-timezone/moment-timezone";
import * as moment from "moment-timezone";


@Component({
  selector: "vpp-download-opti-results",
  templateUrl: "./download_opti_results.component.html",
  styleUrls: ["./download_opti_results.component.scss"],
  encapsulation: ViewEncapsulation.None
})

export class DownloadOptiResultsComponent implements OnInit {
  environment = environment;
  asset;
  assets;
  maxDate: Moment = moment();
  selectedDate: Moment = moment();
  optiResultLinesCount: number = 0;
  optiResultLinesGaps: boolean = false;
  params = {
    asset_id: null,
    selected_date: moment().startOf('day').format('YYYY/MM/DD'),
  };
  csr_token: string;
  downloadOptiResultsUrl = this.UrlProvider.getDownloadOptiResultsUrl();
  @ViewChild("downloadOptiResults", { static: true }) downloadOptiResults: any;

  constructor(
    private _HttpClientService: HttpClientService,
    private UrlProvider: UrlProvider,
    private _Asset: AssetProvider,
    public _NotificationService: NotificationService,
    public globalService: GlobalService,
    public translate: TranslateService
  ) {
    this.csr_token = this._HttpClientService.getAuthToken();
    this.downloadOptiResultsUrl = this.UrlProvider.getDownloadOptiResultsUrl();
  }

  ngOnInit() {
    this.getAssets();
  }

  getAssets() {
    this._Asset.findAllWithPagination().subscribe(result => {
      this.assets = result.assets;
    });
  }

  downloadOptiResultsAction() {
    setTimeout(() => {
      this.downloadOptiResults.nativeElement.submit();
    });
  }

  selectedAssetChanged(asset) {
    console.log("#### selectedAssetChanged", asset)
    if (asset) {
      this.params.asset_id = asset.id;
    } else {
      this.params.asset_id = null;
    }
    this.getOptiResultsCount();
  }

  selectedDateChanged(date) {
    if (this.selectedDate != null ) {
      this.params.selected_date = this.selectedDate.startOf('day').format('YYYY/MM/DD')
    } else {
      this.params.selected_date = null
    }
    this.getOptiResultsCount();
  }

  getOptiResultsCount() {
    console.log("#### getOptiResultsCount", this.params)
    if (this.params.asset_id != null && this.params.selected_date != null) {
      this._Asset.assetOptiResultCount(this.params).subscribe(result => {
        console.log("#### getOptiResultsCount result", result)
        this.optiResultLinesCount = result.count;
        this.optiResultLinesGaps = result.has_gaps;
      });
    }
    else {
      this.optiResultLinesCount = 0;
      this.optiResultLinesGaps = false;
    }
  }

  dropdownSettings(singleSelection, allowSearchFilter) {
      return {
        idField: 'id',
        textField: 'id_and_name',
        singleSelection: singleSelection,
        selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
        unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
        searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
        noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
        itemsShowLimit: 10,
        allowSearchFilter: allowSearchFilter
      }
    };

}
