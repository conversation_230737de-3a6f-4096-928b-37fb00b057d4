@import "./../../../styles/colors";
vpp-download-opti-results {

  .hidden {
    display: none;
  }

  .tab-content.light-grey {
    position: relative;
    &:before{
      content:"";
      position: absolute;
      width: 0;
      height: 0;
      border-left: 15px solid transparent;
      border-right: 15px solid transparent;
      border-bottom: 15px solid $eon-darkgrey-25;
      top: -15px;
      left: 510px;
    }
  }

  .alerts-wrapper-static {
    padding: 0px;
    margin-left: 0px;
    margin-bottom: 15px;
  }

  .multiselect-dropdown {
    .dropdown-btn {
      border: 2px solid $eon-turquoise !important;
      background: #fff !important;
      border-radius: 3px !important;
      min-height: 52px !important;
    }
  }

}