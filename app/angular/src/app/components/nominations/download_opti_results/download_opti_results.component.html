<div class="row">
  <div class="col-md-12">
    <div class="tab-content light-grey">
      <h2>{{ 'NOMINATIONS.DOWNLOAD_OPTI_RESULTS.TITLE' | translate }}</h2>
      <div class="form-row">
        <div class="form-group col-md-4">
          <label>{{ 'NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.ASSET' | translate }}</label>
          <ng-multiselect-dropdown
              [placeholder]="('COMPONENTS.SELECT' | translate )"
              [data]="assets"
              [(ngModel)]="asset"
              (onSelect)="selectedAssetChanged($event)"
              (onDeSelect)="selectedAssetChanged($event)"
              [settings]="dropdownSettings(true, true)">
          </ng-multiselect-dropdown>
        </div>
        <div class="form-group col-md-3">
          <div class="form-group">
            <label class="required">{{ "NOMINATIONS.DOWNLOAD_OPTI_RESULTS_FILE.DATE" | translate }}</label>
            <input
                type="text"
                class="form-control"
                [owlDateTime]="dtSelectedDate"
                [selectMode]="'single'"
                [owlDateTimeTrigger]="dtSelectedDate"
                [(ngModel)]="selectedDate"
                (dateTimeChange)="selectedDateChanged($event)"
                [max]="maxDate"
                readonly="true"
                placeholder=""
            >
            <owl-date-time
                #dtSelectedDate
                (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
                [pickerType]="'calendar'"
                [pickerMode]="'popup'"
            ></owl-date-time>
          </div>
        </div>
        <div class="form-group col-md-12">
          <div class="alerts-wrapper-static col-md-7 col-lg-7">
            <ngb-alert  *ngIf="asset && asset.length > 0 && selectedDate && optiResultLinesCount == 0"
              [dismissible]="false"
              [type]="'error'"> {{ 'NOMINATIONS.DOWNLOAD_OPTI_RESULTS.NO_FILE' | translate }}
            </ngb-alert>
            <ngb-alert *ngIf="asset && asset.length > 0 && selectedDate && optiResultLinesCount > 0 && optiResultLinesGaps"
              [dismissible]="false"
              [type]="'warning'"> {{ 'NOMINATIONS.DOWNLOAD_OPTI_RESULTS.FILE_WITH_GAPS' | translate }}
            </ngb-alert>
          </div>
          <button [disabled]="!(asset && asset.length > 0 && selectedDate && optiResultLinesCount > 0)" (click)="downloadOptiResultsAction()" class="eon-button bg-eon-red bg-eon-white-disabled">
            <span>{{ 'NOMINATIONS.DOWNLOAD_OPTI_RESULTS.DOWNLOAD' | translate }}</span>
          </button>
          <div class="hidden">
            <form
              name="downloadOptiResults"
              #downloadOptiResults
              action="{{ downloadOptiResultsUrl }}"
              method="get"
              target="_blank">
              <input type="hidden" name="authenticity_token" value="{{ csr_token }}">
              <input type="hidden" name="asset_id" value="{{params.asset_id}}"/>
              <input type="hidden" name="selected_date" value="{{params.selected_date}}"/>
              <button type="submit">{{ 'NOMINATIONS.DOWNLOAD_OPTI_RESULTS.DOWNLOAD' | translate }}</button>
            </form>
          </div>

        </div>
      </div>
    </div>
  </div>
</div>