import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef, Output, EventEmitter
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { UrlProvider } from "./../../../providers/url.provider";
import { HttpClientService } from "./../../../services/httpclient.service";
import { NotificationService } from "./../../../services/notification.service";
import { environment } from "../../../../environments/environment";
import { TranslateService } from '@ngx-translate/core';
import {angularData} from "../../../../global.export";

@Component({
  selector: "vpp-upload-margin-data-file",
  templateUrl: "./upload_margin_data_file.component.html",
  styleUrls: ["./upload_margin_data_file.component.scss"],
  encapsulation: ViewEncapsulation.None
})

export class UploadMarginDataFileComponent implements OnInit {
  environment = environment;
  page: number = 1;
  pageSize: number = 25;
  collectionSize: number = 0;
  params = {
    page: this.page,
    per_page: this.pageSize
  };
  showFileFormat: boolean = false;
  fileToUpload;
  fileUploaderReference;
  uploaderOptions ={
    url: this.UrlProvider.getUploadMarginDataFileUrl()
  };
  csr_token: string = "";
  disableButton: boolean = true;
  response: any = null;

  @ViewChild("fileUploader", { static: true }) fileUploader: any;
  @Output() uploaded: any = new EventEmitter();

  constructor(
    private _HttpClientService: HttpClientService,
    private UrlProvider: UrlProvider,
    public _NotificationService: NotificationService,
    public translate: TranslateService
  ) {
    this.uploaderOptions.url = this.UrlProvider.getUploadMarginDataFileUrl();
    this.csr_token = this._HttpClientService.getAuthToken();
  }

  ngOnInit() {

  }

  fileChangedCallback(file) {
    this.fileToUpload = file;
    this.disableButton = false;
  }

  uploaderChangedCallback(uploader) {
    this.fileUploaderReference = uploader;
  }

  uploadFile(event) {
    this.response = null;
    event.preventDefault();
    event.stopPropagation();
    this.fileUploaderReference.url = this.UrlProvider.getUploadBodFileUrl();
    this.fileUploaderReference.onBuildItemForm = (
      fileItem: any,
      form: any
    ) => {
      form.append("authenticity_token", this.csr_token);
    };
    this.fileToUpload.upload();

    this.fileUploaderReference.onCompleteItem = (item:any, response:any, status:any, headers:any) => {
      let data = JSON.parse(response);
      this.response = data;
      if (data.success) {
        console.log("UPLOAD SUCCESS")
        this.uploaded.emit({deliveryDate: this.response.delivery_date, auctionConfigId: this.response.auction_config_id})
      }
      this.fileToUpload = null;
      this.fileUploader.clearFileUploaderQueue();
    };
  }

}
