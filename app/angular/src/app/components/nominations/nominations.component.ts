import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { Location } from "@angular/common";
import { GlobalService } from "./../../services/global.service";
import { SlideRightContentAnimation } from './../../animations/slide-right-content';
import { environment } from "../../../environments/environment";
import { angularData } from "./../../../global.export";
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: "vpp-management-nominations",
  templateUrl: "./nominations.component.html",
  styleUrls: ["./nominations.component.scss"],
  encapsulation: ViewEncapsulation.None,
  animations: [ SlideRightContentAnimation ],
})

export class NominationsComponent implements OnInit {
  selectedSelection = "";
  loadNominationList: boolean = true;
  permissions: any;

  constructor(
    private _Global: GlobalService,
    private route: ActivatedRoute,
    private _Location: Location,
    public translate: TranslateService
  ) {
    this.translate.get('PAGE.CAPTIONS.NOMINATIONS').subscribe((res: string) => {
      this._Global.changeTitle(res);
    });
    this.permissions = angularData.permissions;
  }

  ngOnInit() {}

  ngAfterViewInit() {
    this.route.params.forEach((params: Params) => {
      console.log(params);
      if (params["tab"]) {
        setTimeout(() => {
          this.toggleSection(null, params["tab"]);
        })
      }
    });
  }

  toggleSection(event, section) {
    if (event) {
      //event.preventDefault();
      //event.stopPropagation();
    }
    if (this.selectedSelection == section) {
      this.selectedSelection = '';
      this._Location.replaceState(`${environment.routingPath}/nominations/`);
    } else {
      this.selectedSelection = section;
      this._Location.replaceState(`${environment.routingPath}/nominations/${section}`);
    }
  }

  nominationCreated() {
    this.loadNominationList = false;
    // we need to toggle the list to trigger a list reload without additional logic
    setTimeout(() => {
      this.loadNominationList = true;
    });
  }
}
