<div class="row">
  <div class="col-md-8" id="full-width-in-modal">
    <div class="tab-content light-blue">
      <span *ngIf="distributedUnit.id" class="pull-right" (click)="close($event)">
        <i class="fa fa-times cursor-hand"></i>
      </span>
      <h2>{{ 'NOMINATIONS.ENTER_NOMINATIONS.TITLE' | translate }}</h2>
      <vpp-file-upload-results [response]="response"></vpp-file-upload-results>
      <div class="alerts-wrapper-static" *ngIf="sourceId">
        <ng-container *ngIf="sourceId">
          <a href="{{environment.apiPath}}/nominations/nomination_detail/{{ sourceId }}" target="_blank">
            <i class="fas fa-eye"></i> {{ 'NOMINATIONS.ENTER_NOMINATIONS.VIEW_DETAILS' | translate }}
          </a>
        </ng-container>
      </div>
      <div class="form-row">
        <div class="col-md-6">
          <label class="required">{{ 'NOMINATIONS.ENTER_NOMINATIONS.DISPATCH_GROUP' | translate }}</label>
          <ng-multiselect-dropdown
                  [placeholder]="' '"
                  [data]="dispatchGroups"
                  [(ngModel)]="selectedDispatchGroups"
                  [settings]="dgDropdownSettings"
          >
          </ng-multiselect-dropdown>
        </div>
        <div class="col-md-6">
          <label class="required">{{ 'NOMINATIONS.ENTER_NOMINATIONS.INTERVAL' | translate }}</label>
          <input
            type="text"
            class="form-control"
            [owlDateTime]="dt1"
            [selectMode]="'range'"
            [(ngModel)]="startRange"
            [owlDateTimeTrigger]="dt1"
            (dateTimeChange)="startDateSelectedCallback($event)"
            placeholder="">
          <owl-date-time
            #dt1
            (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
            [pickerMode]="'popup'"></owl-date-time>
        </div>
      </div>
      <div class="form-row">
        <div class="form-group col-md-3">
           <label class="required" for="inputState">{{ 'NOMINATIONS.ENTER_NOMINATIONS.ENERGY_DIRECTION' | translate }}</label>
           <select [(ngModel)]="distributedUnit.energy_direction" id="inputState" class="form-control">
              <option *ngFor="let d of energyDirections" [ngValue]="d"> {{ d | humanize: {titleize: true} }} </option>
           </select>
        </div>
        <div class="form-group col-md-3">
           <label class="required">{{ 'NOMINATIONS.ENTER_NOMINATIONS.FLEX_CAPACITY' | translate }} (MW)</label>
           <input
            [(ngModel)]="distributedUnit.flex_volume_mw"
            autocomplete="inputFlexCapacity"
            type="text"
            class="form-control"
            id="inputFlexCapacity">
        </div>
        <div class="form-group col-md-3">
          <label class="required" for="inputState">{{ 'NOMINATIONS.ENTER_NOMINATIONS.ENERGY_PRICE' | translate }} (¤/MWh)</label>
          <input
            [(ngModel)]="distributedUnit.energy_price"
            autocomplete="inputEnergyPrice"
            type="text"
            class="form-control"
            id="inputEnergyPrice">
        </div>
        <div class="form-group col-md-3">
          <label>{{ 'NOMINATIONS.ENTER_NOMINATIONS.CAPACITY_PRICE' | translate }} (¤/MW)</label>
          <input
            [(ngModel)]="distributedUnit.capacity_price"
            autocomplete="inputCapacityPrice"
            type="text"
            class="form-control"
            id="inputCapacityPrice">
        </div>
      </div>
      <div class="form-row">
        <div class="form-group col-md-12">
          <label>{{ 'NOMINATIONS.ENTER_NOMINATIONS.ASSETS' | translate }}</label>
          <ng-multiselect-dropdown
            [placeholder]="('COMPONENTS.SELECT' | translate )"
            [data]="assets"
            [(ngModel)]="selectedAssets"
            [settings]="assetsDropdownSettings"
            (onSelect)="itemSelectCallback($event)"
            (onSelectAll)="selectAllCallback($event)"
          >
          </ng-multiselect-dropdown>
        </div>
      </div>
      <div class="form-group">
        <div class="form-check">
          <label
            class="eon-checkbox-label bg-eon-red"
            (click)="toggleImmediateEffectCheckbox()"
            [ngClass]="immediateEffect ? 'checked': ''">{{ 'NOMINATIONS.ENTER_NOMINATIONS.ALLOW_MODIFICATIONS_WITH_IMMEDIATE_EFFECT' | translate }}</label>
        </div>
      </div>
      <div class="form-group">
          <div class="form-check">
            <label
              class="eon-checkbox-label bg-eon-red"
              (click)="togglePriceCheckbox()"
              [ngClass]="useForAssetPriceCheckbox ? 'checked': ''">{{ 'NOMINATIONS.ENTER_NOMINATIONS.USE_FOR_ASSET_PRICE' | translate }}</label>
          </div>
      </div>
      <button
        [disabled]="submitDisabled"
        (click)="submit()"
        class="eon-button bg-eon-red">
        <span>{{ (distributedUnit.id ? 'NOMINATIONS.ENTER_NOMINATIONS.SAVE_NOMINATION' : 'NOMINATIONS.ENTER_NOMINATIONS.CREATE_NOMINATION')| translate }}</span>
      </button>
    </div>
  </div>
</div>