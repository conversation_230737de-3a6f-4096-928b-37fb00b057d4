import {
  Component,
  OnInit,
  Input,
  Output,
  ViewEncapsulation,
  ViewChild,
  EventEmitter
} from "@angular/core";
import { DispatchGroupProvider } from "./../../../providers/dispatch_group.provider";
import { AssetProvider } from "./../../../providers/asset.provider";
import { NominationProvider } from "./../../../providers/nomination.provider";
import { NotificationService } from "./../../../services/notification.service";
import { Moment } from 'moment';
import * as moment from "moment-timezone";
import { angularData } from "./../../../../global.export";
import { environment } from "../../../../environments/environment";
import { TranslateService } from '@ngx-translate/core';
import { GlobalService } from "./../../../services/global.service";

@Component({
  selector: "vpp-enter-nomination",
  templateUrl: "./enter_nomination.component.html",
  styleUrls: ["./enter_nomination.component.scss"],
  encapsulation: ViewEncapsulation.None
})

export class EnterNominationComponent implements OnInit {
  @Output('done') done: any = new EventEmitter();
  @Input('distributedUnit') distributedUnit: any = {
    dispatch_group_id: null,
    energy_price: null,
    capacity_price: null,
    interval: null,
    energy_direction: null,
    flex_volume_mw: null,
    used_for_asset_price: null,
    asset_ids: [],
    id: null
  };
  page = 1;
  pageSize = 25;
  collectionSize = 0;
  dispatchGroups = [];
  assets = [];
  useForAssetPriceCheckbox: boolean = false;
  immediateEffect: boolean = false;
  selectedDispatchGroups: any = [];
  dgDropdownSettings = {
    singleSelection: true,
    idField: 'id',
    textField: 'ps_name',
    itemsShowLimit: 10,
    allowSearchFilter: true
  };
  assetsDropdownSettings = {
    singleSelection: false,
    idField: 'id',
    textField: 'ps_name',
    selectAllText: angularData.railsExports.locale  == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
    unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
    searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
    noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
    itemsShowLimit: 10,
    allowSearchFilter: true
  };
  selectedAssets: any = [];
  energyDirections = ['negative', 'positive'];
  submitDisabled: boolean = false;

  selectedDate = {
    startDateTime: null,
    endDateTime: null
  };
  response: any = null;
  sourceId: number = null;
  environment = environment;
  startRange: Moment[] = [];

  @ViewChild('dt1', { static: true }) dt1;


  constructor(
    private _DispatchGroup: DispatchGroupProvider,
    private _Asset: AssetProvider,
    private _NominationProvider: NominationProvider,
    private _NotificationService: NotificationService,
    public translate: TranslateService,
    public globalService: GlobalService,
  ) {}

  ngOnInit() {
    this.getDispatchGroups();
    this.getAssets();

    if (this.distributedUnit && this.distributedUnit.id) {
      this.processInputData();
    }
  }

  processInputData() {
    const start_time = moment(this.distributedUnit.start_time).tz(angularData.railsExports.timeZone);
    const end_time = moment(this.distributedUnit.end_time).tz(angularData.railsExports.timeZone);
    this.distributedUnit.flex_volume_mw = (this.distributedUnit.flex_volume / 1000);
    this.useForAssetPriceCheckbox = this.distributedUnit.used_for_asset_price;
    if (!this.distributedUnit.energy_direction && this.distributedUnit.product.energy_direction) {
      this.distributedUnit.energy_direction = this.distributedUnit.product.energy_direction;
    }
    this.startRange = [start_time, end_time];

    this.selectedDate.startDateTime = start_time.startOf('minute').toISOString();
    this.selectedDate.endDateTime = end_time.startOf('minute').toISOString();
    this.selectedAssets = this.mapAssetsAddingDisplayName(this.distributedUnit.assets);
    this.distributedUnit.dispatch_group['ps_name'] = this.getDispatchGroupDisplayName(this.distributedUnit.dispatch_group);
    this.selectedDispatchGroups = [this.distributedUnit.dispatch_group];
  }

  getDispatchGroupDisplayName(x) {
    let dgName = `#${x.id} ${x.name}`;
    return x.tso ? `${dgName} (${x.tso.name})` : dgName;
  }

  mapDispatchGroupsAddingDisplayName(dgs) {
    return dgs.map((x) => {
      x['ps_name'] = this.getDispatchGroupDisplayName(x);
      return x;
    });
  }

  getAssetDisplayName(a) {
    return `#${a.id} ${a.name} (${a.customer_name})`;
  }

  mapAssetsAddingDisplayName(dgs) {
    return dgs.map((x) => {
      x['ps_name'] = this.getAssetDisplayName(x);
      return x;
    });
  }

  getDispatchGroups() {
    this._DispatchGroup.findAllWithPagination().subscribe(result => {
      this.dispatchGroups = result.dispatch_groups;

      this.dispatchGroups = this.dispatchGroups.map((x) => {
        x['ps_name'] = this.getDispatchGroupDisplayName(x);

        if (this.distributedUnit && this.distributedUnit.id && x.id == this.distributedUnit.dispatch_group_id) {
          this.selectedDispatchGroups = [x];
          console.log('Mark selected Dispatch group', x);
        }

        return x;
      });
    });
  }

    getAssets() {
        const noData = angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar';
        const loading = angularData.railsExports.locale == 'en-GB' ? 'Loading...' : 'Lädt...';
        this.assetsDropdownSettings.noDataAvailablePlaceholderText = loading;
        this._Asset.findAllWithPagination().subscribe(
            result => {
                this.assets = result.assets;

                this.assets = this.mapAssetsAddingDisplayName(this.assets);

                if (this.distributedUnit && this.distributedUnit.id) {
                    let assetIds = this.distributedUnit.assets.map((a) => a.id);
                    this.selectedAssets = this.assets.filter((a) => assetIds.indexOf(a.id) > -1);
                }

                this.assetsDropdownSettings.noDataAvailablePlaceholderText = noData;
            }, err => {
                console.error('Failed to load asset list', err);
                this.assetsDropdownSettings.noDataAvailablePlaceholderText = noData;
            });
    }

  assetSelectedCallback(asset) {
  }

  itemSelectCallback(items) {
  }

  togglePriceCheckbox() {
    this.useForAssetPriceCheckbox = !this.useForAssetPriceCheckbox;
  }

  toggleImmediateEffectCheckbox() {
    this.immediateEffect = !this.immediateEffect;
  }

  startDateSelectedCallback(date) {
    if (date) {
      this.selectedDate = {
        startDateTime: date.value[0].startOf('minute').toISOString(),
        endDateTime: date.value[1].startOf('minute').toISOString()
      };
    } else {
      this.selectedDate = {
        startDateTime: null,
        endDateTime: null
      };
    }
  }

  close(event) {
     this.done.emit();
  }

  submit() {
    this.response = null;
    if (this.validateNomination(true)) {
      let isNew = !this.distributedUnit.id;

      this.submitDisabled = true;
      this.distributedUnit.dispatch_group_id = this.selectedDispatchGroups[0].id;
      this.distributedUnit.interval = `${this.selectedDate.startDateTime} - ${this.selectedDate.endDateTime}`;
      this.distributedUnit.asset_ids = this.selectedAssets.map((a) => a.id);
      this.distributedUnit.used_for_asset_price = this.useForAssetPriceCheckbox;

      // always positive
      this.distributedUnit.flex_volume_mw = Math.abs(this.distributedUnit.flex_volume_mw);

      if (this.distributedUnit.energy_direction == 'negative') {
        this.distributedUnit.flex_volume_mw = -1 * this.distributedUnit.flex_volume_mw;
      }

      let params = {
        distributed_unit: this.distributedUnit,
        skip_validations: this.immediateEffect
      };

      let fn = isNew ? this._NominationProvider.create : this._NominationProvider.edit;
      fn.call(this._NominationProvider, params).subscribe(
        (data) => {
          this.response = data;
          this.submitDisabled = false;
          if (isNew) {
            let source = data.result;
            this.sourceId = source.id;
          }

          if (this.isValidationResponseSuccess() && this.done) {
            this.done.emit();
          }
        },
        (err) => {
          this.submitDisabled = false;
          this.response = {messages: [JSON.stringify(err)]};
        }
      )
    }
  }

  isValidationResponseSuccess() {
    let response = this.response;
    if (response && response.success) {
      let source = response.result;
      let validationResults = source && source.validation_result ? (typeof (source.validation_result) == 'string' ? JSON.parse(source.validation_result) : source.validation_result) : null;
      if (validationResults && validationResults.validationSuccess == true) {
        return true;
      }
    }
    return false;
  }

  selectAllCallback(event) {
  }

  validateNomination(show: boolean = false) {
    let errors = [];

    if (!this.selectedDispatchGroups || !(this.selectedDispatchGroups.length > 0)) {
      errors.push('MISSING_DISPATCH_GROUP');
    }

    if (!this.selectedDate.startDateTime) {
      errors.push('MISSING_START_DATE');
    }
    if (!this.selectedDate.endDateTime) {
      errors.push('MISSING_END_DATE');
    }

    if (this.selectedDate.startDateTime == "Invalid date") {
      errors.push('INVALID_START_DATE');
    }

    if (this.selectedDate.endDateTime == "Invalid date") {
      errors.push('INVALID_END_DATE');
    }

    if (moment(this.selectedDate.endDateTime).isBefore(this.selectedDate.startDateTime)) {
      errors.push('END_DATE_BEFORE_START_DATE');
    }

    if (!this.distributedUnit.flex_volume_mw) {
      errors.push('MISSING_FLEX_VOLUME');
    }

    if (!this.distributedUnit.energy_price) {
      errors.push('MISSING_ENERGY_PRICE');
    }

    if (!this.distributedUnit.energy_direction) {
      errors.push('MISSING_ENERGY_DIRECTION');
    }

    if (show && errors.length) {
      this.response = {messages: errors};
    }

    return !errors.length;
  }
}
