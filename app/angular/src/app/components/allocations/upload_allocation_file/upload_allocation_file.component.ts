import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { Location } from "@angular/common";
import { UrlProvider } from "./../../../providers/url.provider";
import { HttpClientService } from "./../../../services/httpclient.service";
import { NotificationService } from "./../../../services/notification.service";
import { environment } from "../../../../environments/environment";
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: "vpp-upload-allocation-file",
  templateUrl: "./upload_allocation_file.component.html",
  styleUrls: ["./upload_allocation_file.component.scss"],
  encapsulation: ViewEncapsulation.None,
  animations: []
})

export class UploadAllocationFileComponent implements OnInit {
  environment = environment;
  page: number = 1;
  pageSize: number = 25;
  collectionSize: number = 0;
  params = {
    page: this.page,
    per_page: this.pageSize
  };
  showFileFormat: boolean = false;
  fileToUpload;
  fileUploaderReference;
  uploaderOptions ={
    url: ''
  };
  csr_token: string = "";
  immediateEffect: boolean = false;
  disableButton: boolean = true;
  response: any = null;
  sourceId: number = null;

  @ViewChild("fileUploader", { static: true }) fileUploader: any;

  constructor(
    private _HttpClientService: HttpClientService,
    private _UrlProvider: UrlProvider,
    private _NotificationService: NotificationService,
    private route: ActivatedRoute,
    public _router: Router,
    private _Location: Location,
    public translate: TranslateService
  ) {
    this.uploaderOptions.url = this._UrlProvider.getUploadAllocationUrl();
    this.csr_token = this._HttpClientService.getAuthToken();
  }

  ngOnInit() {
  }

  fileChangedCallback(file) {
    this.fileToUpload = file;
    this.disableButton = false;
  }

  uploaderChangedCallback(uploader) {
    this.fileUploaderReference = uploader;
  }

  uploadFile(event) {
    this.disableButton = true;
    this.response = null;
    event.preventDefault();
    event.stopPropagation();
    this.fileToUpload.url = this._UrlProvider.getUploadAllocationUrl();
    this.fileUploaderReference.onBuildItemForm = (
      fileItem: any,
      form: any
    ) => {
      form.append("authenticity_token", this.csr_token);
      form.append("skip_validations", this.immediateEffect);
    };
    this.fileToUpload.upload();

    this.fileUploaderReference.onCompleteItem = (item:any, response:any, status:any, headers:any) => {
      let data = JSON.parse(response);
      this.response = data;
      if (data.success) {
        let source = data.result;
        this.sourceId = source.id;

        this.fileToUpload = null;
      }

      this.fileUploader.clearFileUploaderQueue();
      this.disableButton = false;
    };
  }

  toggleCheckbox() {
    this.immediateEffect = !this.immediateEffect;
  }
}
