<div class="row">
  <div class="col-md-8">
    <div class="tab-content light-yellow ml_30">
      <h2>{{ 'ALLOCATIONS.UPLOAD_ALLOCATION.TITLE' | translate }}</h2>

      <vpp-file-upload-results [response]="response"></vpp-file-upload-results>

      <div class="alerts-wrapper-static" *ngIf="sourceId">
        <ng-container *ngIf="sourceId">
          <a [routerLink]="[environment.apiPath + '/allocations/manual-allocation/' + sourceId]" target="_blank">
            <i class="fas fa-eye"></i>{{ 'ALLOCATIONS.UPLOAD_ALLOCATION.VIEW_DETAILS' | translate }}</a>
        </ng-container>
      </div>

      <a (click)="showFileFormat = !showFileFormat" *ngIf="showFileFormat" class="view-more-link">{{ 'ALLOCATIONS.UPLOAD_ALLOCATION.HIDE' | translate }}</a>
      <a (click)="showFileFormat = !showFileFormat" *ngIf="!showFileFormat" class="view-more-link">{{ 'ALLOCATIONS.UPLOAD_ALLOCATION.SHOW' | translate }}</a>
      <div class="file-format-box" *ngIf="showFileFormat">
        <p>{{ 'ALLOCATIONS.UPLOAD_ALLOCATION.CSV_FORMAT' | translate }}</p>
        <code [innerHtml]="'ALLOCATIONS.UPLOAD_ALLOCATION_FILE.CSV_FORMAT_DETAIL' | translate"></code>
        <br>
        <br>
        <p>{{ 'ALLOCATIONS.UPLOAD_ALLOCATION_FILE.CSV_LIST_TITLE' | translate }}</p>
        <ul>
          <li>{{ 'ALLOCATIONS.UPLOAD_ALLOCATION_FILE.CSV_1' | translate }}</li>
        </ul>
      </div>
      <br>
      <div class="form-row">
        <div class="form-group col-md-6">
          <vpp-management-file-uploader
            [options]="uploaderOptions"
            (uploader)="uploaderChangedCallback($event)"
            (propagateFile)="fileChangedCallback($event)"
            #fileUploader>>
          </vpp-management-file-uploader>
        </div>
        <div class="form-group col-md-6">
          <div class="upload-meta">
            <i class="fa fa-file"></i>
            <br>
            <a href="{{environment.apiPath}}/api/v1/allocation/allocation_sample" target="_blank"><i class="fas fa-download"></i>{{ 'ALLOCATIONS.UPLOAD_ALLOCATION_FILE.EXAMPLE' | translate }}</a>
          </div>
        </div>
        <div class="form-group col-md-12">
          <div class="form-check">
            <label
              class="eon-checkbox-label bg-eon-red"
              [ngClass]="immediateEffect ? 'checked': ''"
              (click)="toggleCheckbox()">{{ 'ALLOCATIONS.UPLOAD_ALLOCATION_FILE.ALLOW_MODIFICATIONS_WITH_IMMEDIATE_EFFECT' | translate }}</label>
          </div>
        </div>
        <div class="form-group col-md-6">
          <button [disabled]="disableButton" (click)="uploadFile($event)" href="#" class="eon-button bg-eon-red">
            <span>{{ 'ALLOCATIONS.UPLOAD_ALLOCATION_FILE.UPLOAD_ALLOCATION_FILE' | translate }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
