<div class="container-fluid">
  <div class="row">
    <div class="col-md-8">
      <div class="page-actions">
        <ul class="list-unstyled d-flex">
          <li *ngIf="permissions.can_create_allocations">
            <a href="javascript:void(0)"  (click)="toggleSection($event, 'enter-allocations')" class="eon-button bg-eon-turquoise">{{ 'ALLOCATIONS.TABS.ENTER_ALLOCATION' | translate }}</a>
          </li>
          <li *ngIf="permissions.can_create_allocations">
            <a href="javascript:void(0)"  (click)="toggleSection($event, 'upload-allocation-file')" class="eon-button bg-eon-limeyellow">{{ 'ALLOCATIONS.TABS.UPLOAD_ALLOCATION_FILE' | translate }}</a>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <div [@slideRightContentAnimation]="selectedSelection == 'enter-allocations' ? 'in' : 'out'" class="z999">
    <vpp-enter-allocation *ngIf="selectedSelection == 'enter-allocations'"></vpp-enter-allocation>
  </div>
  <div [@slideRightContentAnimation]="selectedSelection == 'upload-allocation-file' ? 'in' : 'out'">
    <vpp-upload-allocation-file *ngIf="selectedSelection == 'upload-allocation-file'"></vpp-upload-allocation-file>
  </div>
  <section>
    <div class="row">
      <div class="col-md-12">
        <vpp-visualize-allocations></vpp-visualize-allocations>
      </div>
    </div>
  </section>
</div>
