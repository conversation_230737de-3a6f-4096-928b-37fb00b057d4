import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef,
  Input,
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { AllocationProvider } from "./../../../providers/allocation.provider";
import { TranslateService } from '@ngx-translate/core';
import { environment } from "../../../../environments/environment";
import { GlobalService } from "./../../../services/global.service";

@Component({
  selector: 'vpp-management-automatic-allocation-detail',
  styleUrls: ['./automatic_allocation.component.scss'],
  templateUrl: "./automatic_allocation.component.html",
  encapsulation: ViewEncapsulation.None
})

export class AutomaticAllocationComponent {
  automaticAllocation: any = {};
  automaticAllocationSource: any = {};
  environment: any = environment;

  @Input('id') allocationId;

  constructor(
    private _Global: GlobalService,
    private _Allocation: AllocationProvider,
    private _Route: ActivatedRoute,
    public translate: TranslateService
    ) {
    this.translate.get('HISTORY.MANUAL_ALLOCATIONS.DETAIL.TITLE').subscribe((res: string) => {
        this._Global.changeTitle(res);
    });
  }

  ngOnInit() {
    if (this.allocationId) {
      this.getAllocation();
    }
  }

  ngAfterViewInit() {
    this._Route.params.forEach((params: Params) => {
      if (params["id"]) {
        this.allocationId = params["id"];
        this.getAllocation();
      }
    });
  }

  getAllocation() {
    this._Allocation.findAutomaticAllocationById(this.allocationId).subscribe(
      (data) => {
        this.automaticAllocation = data.automatic_allocation;
        this.automaticAllocationSource = data.asset_dg_allocation_source;
      }
    )
  }
}