<div class="container-fluid">
  <section>
    <div class="row">
      <div class="col-md-2">
        <a class="back-button" [routerLink]="[environment.routingPath + '/history/automatic-allocations']"><i class="fa fa-chevron-left"></i> {{ 'BACK' | translate }}</a>
        <br>
      </div>
    </div>
    <div class="row">
      <div class="alerts-wrapper-static col-md-8 col-lg-6" *ngIf="automaticAllocation._expiresAt">
        <ngb-alert
          *ngIf="automaticAllocation.status.type == 'Success'"
          [dismissible]="true"
          [type]="'success'"> {{ 'SUCCESS' | translate }}
        </ngb-alert>
        <ngb-alert
          *ngIf="!automaticAllocation.status.type == 'Success'"
          [dismissible]="false"
          [type]="'error'"> {{ 'FAILURE' | translate }}
        </ngb-alert>
      </div>
    </div>
    <div class="row">
      <div class="col-md-8 col-lg-6">
        <div class="card card-allocated">
          <div class="card-body">
            <div class="stats-group">
              <label>{{ 'ALLOCATIONS.AUTOMATIC_ALLOCATIONS.START' | translate }}</label>
              <span class="value">{{ automaticAllocation.startedAt | localDate:'ddd, DD MMM YYYY HH:mm'}}</span>
            </div>
            <div class="stats-group">
              <label>{{ 'ALLOCATIONS.AUTOMATIC_ALLOCATIONS.END' | translate }}</label>
              <span class="value">{{ automaticAllocation.endTime | localDate:'ddd, DD MMM YYYY HH:mm'}}</span>
            </div>
            <div class="stats-group">
              <label>{{ 'ALLOCATIONS.AUTOMATIC_ALLOCATIONS.FALLBACK' | translate }}</label>
              <span class="value" *ngIf="automaticAllocation.fallbackStart">{{ automaticAllocation.fallbackStart | localDate:'ddd, DD MMM YYYY HH:mm'}}</span>
              <span class="value" *ngIf="!automaticAllocation.fallbackStart">-</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <table class="table table-bg-turquoise table-striped">
    <thead>
      <th colspan="6">{{ 'ALLOCATIONS.AUTOMATIC_ALLOCATIONS.DISPATCH_GROUPS_TITLE' | translate }}</th>
    </thead>
    <tbody>
      <tr>
        <td>
          <span *ngFor="let dg of automaticAllocation.dispatch_groups; let idx = index">
            <a href="/dispatch_groups/{{dg.id}}" target="_blank">{{ dg.name }}</a>
            <span *ngIf="idx < automaticAllocation.dispatch_groups.length-1">, </span>
          </span>
        </td>
      </tr>
    </tbody>
  </table>
  <table class="table table-bg-turquoise table-striped">
    <thead>
      <th colspan="6">{{ 'ALLOCATIONS.AUTOMATIC_ALLOCATIONS.TRIGGERS_TITLE' | translate }}</th>
    </thead>
    <tbody>
      <tr>
        <td>
          <span *ngFor="let tg of automaticAllocation.trigger_events; let idx = index">
            {{ 'ALLOCATIONS.AUTOMATIC_ALLOCATIONS.TRIGGERS.TYPES.' + tg.triggerType | translate }}
            <span *ngIf="idx < automaticAllocation.trigger_events.length -1">, </span>
          </span>
        </td>
      </tr>
    </tbody>
  </table>
  <table class="table table-bg-turquoise table-striped">
    <thead>
      <th colspan="6">{{ 'ALLOCATIONS.AUTOMATIC_ALLOCATIONS.IMPORT' | translate }}</th>
    </thead>
    <tbody>
      <tr *ngIf="!automaticAllocationSource">
        <td>{{ 'ALLOCATIONS.AUTOMATIC_ALLOCATIONS.NOT_FOUND' | translate }}</td>
      </tr>
      <tr *ngIf="automaticAllocationSource">
        <td>
        </td>
      </tr>
    </tbody>
  </table>
</div>
