<div class="row">
  <div class="col-md-12">
    <div class="form-row">
      <div class="form-group col-md-8">
        <label>{{ 'ALLOCATIONS.VISUALIZE_ALLOCATIONS.INTERVAL' | translate }}</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            [owlDateTime]="dt1"
            [selectMode]="'range'"
            [(ngModel)]="selectedDatesModel"
            [owlDateTimeTrigger]="dt1"
            (dateTimeChange)="startDateSelectedCallback($event)"
            placeholder="">
          <div class="input-group-append">
            <button class="btn btn-default" (click)="clearSelectedDateValue()">
              <i class="fa fa-times"></i>
            </button>
          </div>
          <owl-date-time
            #dt1
            (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
            [pickerMode]="'popup'"></owl-date-time>
        </div>
      </div>
      </div>
      <div class="form-row">
      <div class="form-group col-md-6">
        <label>{{ 'ALLOCATIONS.VISUALIZE_ALLOCATIONS.DISPATCH_GROUP' | translate }}</label>
        <ng-multiselect-dropdown
            [placeholder]="('COMPONENTS.SELECT' | translate )"
            [data]="dispatchGroups"
            [(ngModel)]="selectedDispatchGroups"
            (ngModelChange)="selectedDispatchGroupsChanged($event)"
            [settings]="dropdownSettings"
            (onSelect)="itemDgSelectCallback($event)"
            (onDeSelect)="itemDgSelectCallback($event)"
            (onSelectAll)="selectAllDgCallback($event)"
            (onDeSelectAll)="selectAllDgCallback($event)"
          >
          </ng-multiselect-dropdown>
      </div>
      <div class="form-group col-md-4">
        <ul class="list-inline list-unstyled d-flex">
          <li>
            <button
            [disabled]="disableShowDg"
            (click)="showDgChart()"
            class="eon-button bg-eon-red">
            <span>{{ 'ALLOCATIONS.VISUALIZE_ALLOCATIONS.SHOW' | translate }}</span>
          </button></li>
          <li>
            <form method="post" class="inline" #download action="{{environment.routingPath}}/api/v1/charts/allocations/download" target="_blank">
              <input type="hidden" name="dispatchgroup_ids" value="{{ dispatchGroupIds }}"/>
              <input type="hidden" name="interval" value="{{intervalString}}"/>
              <input type="hidden" name="authenticity_token" value="{{csr_token}}"/>
              <button
                type="submit"
                [disabled]="disableShowDg"
                (click)="download.submit()"
                class="eon-button bg-eon-red">
                <span>{{ 'ALLOCATIONS.VISUALIZE_ALLOCATIONS.DOWNLOAD' | translate }}</span>
              </button>
            </form>
          </li>
        </ul>
      </div>

    </div>
    <div class="form-row text-centered" *ngIf="showLoaderDg">
      <div class="form-group col-md-12">
        <vpp-management-loader [showLoader]="showLoaderDg"></vpp-management-loader>
      </div>
    </div>
    <ng-container *ngFor="let chart of chartsDg">
     <div class="form-row">
        <div class="form-group col-md-12">
          <div [chart]="chart"></div>
        </div>
      </div>
    </ng-container>
  </div>
</div>
<div class="row">
  <div class="col-md-12">
    <div class="form-row">
      <div class="form-group col-md-2">
        <label>{{ 'ALLOCATIONS.VISUALIZE_ALLOCATIONS.ASSETS' | translate }}</label>
        <input
            id="asset"
            type="text"
            class="form-control"
            name="asset"
            [(ngModel)]="asset"
            #aSearch="ngbTypeahead"
            [ngbTypeahead]="assetsSearch"
            [resultTemplate]="rta"
            (focus)="focusA$.next($event.target.value)"
            (click)="clickA$.next($event.target.value)"
            [inputFormatter]="assetFormatter"
            (ngModelChange)="_updateControls()"
        />
          <ng-template #rta let-r="result" let-t="term">
            <ngb-highlight [result]="'#' + r.id + ' ' + r.name + '(' + r.customer_name + ')'" [term]="t"></ngb-highlight>
          </ng-template>
      </div>
      <div class="form-group col-md-4">
        <label>{{ 'ALLOCATIONS.VISUALIZE_ALLOCATIONS.DISPATCH_GROUP' | translate }}</label>
        <ng-multiselect-dropdown
            [placeholder]="('COMPONENTS.SELECT' | translate )"
            [data]="dispatchGroups"
            [(ngModel)]="selectedDispatchGroupsA"
            (ngModelChange)="_updateControls()"
            [settings]="dropdownSettings"
            (onSelect)="itemDGAssetSelectCallback($event)"
            (onDeSelect)="itemDGAssetSelectCallback($event)"
            (onSelectAll)="selectAllDGAssetCallback($event)"
            (onDeSelectAll)="selectAllDGAssetCallback($event)"
          >
          </ng-multiselect-dropdown>
      </div>
      <div class="form-group col-md-2">
        <button
          [disabled]="disableShowAsset"
          (click)="showAssetChart()"
          class="eon-button bg-eon-red">
          <span>{{ 'ALLOCATIONS.VISUALIZE_ALLOCATIONS.SHOW' | translate }}</span>
        </button>
      </div>
    </div>
     <div class="form-row text-centered" *ngIf="showLoaderAsset">
      <div class="form-group col-md-12">
        <vpp-management-loader [showLoader]="showLoaderAsset"></vpp-management-loader>
      </div>
    </div>
    <div class="form-row">
      <div class="form-group col-md-12">
        <div [chart]="chartsAsset"></div>
      </div>
    </div>
  </div>
</div>
