import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { Location } from "@angular/common";
import { UrlProvider } from "./../../../providers/url.provider";
import { HttpClientService } from "./../../../services/httpclient.service";
import { NotificationService } from "./../../../services/notification.service";
import { TranslateService } from '@ngx-translate/core';
import { Observable, Subject, merge } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map } from 'rxjs/operators';
import { environment } from "../../../../environments/environment";
import { NgbTypeahead } from '@ng-bootstrap/ng-bootstrap';
import { DispatchGroupProvider } from "./../../../providers/dispatch_group.provider";
import { AssetProvider } from "./../../../providers/asset.provider";
import { ChartProvider } from "./../../../providers/chart.provider";
import { ChartService } from "./../../../services/chart.service";
import * as moment from "moment-timezone";
import { GlobalService } from "./../../../services/global.service";
import { angularData } from "./../../../../global.export";

@Component({
  selector: "vpp-visualize-allocations",
  templateUrl: "./visualize_allocations.component.html",
  styleUrls: ["./visualize_allocations.component.scss"],
  encapsulation: ViewEncapsulation.None,
  animations: []
})

export class VisualizeAllocationsComponent implements OnInit {
  environment: any = environment;
  csr_token: string = '';
  dispatchGroups: any[] = [];
  assets: any = [];
  dispatchGroup: any = null;
  asset: any = null;
  focusA$ = new Subject<string>();
  clickA$ = new Subject<string>();

  selectedDate = {
    startDateTime: null,
    endDateTime: null
  };
  selectedDatesModel = [null, null];

  selectedDispatchGroups: any = [];
  selectedDispatchGroupsA: any = [];

  dropdownSettings = {
    singleSelection: false,
    idField: 'id',
    textField: 'ps_name',
    selectAllText: angularData.railsExports.locale  == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
    unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
    searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
    noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
    itemsShowLimit: 10,
    allowSearchFilter: true
  };
  showLoaderAsset: boolean = false;
  showLoaderDg: boolean = false;

  assetsSearch = (text$: Observable<string>) => {
    const debouncedText$ = text$.pipe(debounceTime(200), distinctUntilChanged());
    const clicksWithClosedPopup$ = this.clickA$.pipe(filter(() => !this.aSearch.isPopupOpen()));
    const inputFocus$ = this.focusA$;

    return merge(debouncedText$, inputFocus$, clicksWithClosedPopup$).pipe(
      map(term => (term === '' ? this.assets
        : this.assets.filter(v => (v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 || (v.id + '').indexOf(term.toLowerCase()) > -1 ) )))
    );
  };

  assetFormatter = (result: any) => {
    this.assetSelectedCallback(result);
    return `#${result.id} ${result.name}`;
  };

  @ViewChild('dgSearch', { static: false }) dgSearch: NgbTypeahead;
  @ViewChild('aSearch', { static: false }) aSearch: NgbTypeahead;

  loadAssetChart: boolean = false;
  loadDgChart: boolean = false;
  chartsDg: any = [];
  chartsAsset: any;
  disableShowDg: boolean = true;
  disableShowAsset: boolean = true;
  startDate;
  endDate;
  intervalString = '';
  dispatchGroupIds = '';

  constructor(
    private _HttpClientService: HttpClientService,
    private _UrlProvider: UrlProvider,
    private _NotificationService: NotificationService,
    private route: ActivatedRoute,
    public _router: Router,
    private _Location: Location,
    public translate: TranslateService,
    private _DispatchGroup: DispatchGroupProvider,
    private _Asset: AssetProvider,
    private _Chart: ChartProvider,
    private _ChartService: ChartService,
    public globalService: GlobalService
  ) {
    this.csr_token = this._HttpClientService.getAuthToken();
  }

  ngOnInit() {
    this.getDispatchGroups();
    this.getAssets();
  }

  assetSelectedCallback(asset) {
    this.asset = asset;

    if (this.selectedDate.startDateTime && this.selectedDate.endDateTime) {
      this.disableShowAsset = false;
    } else {
      this.disableShowAsset = true;
    }
  }

  getDispatchGroups() {
    this._DispatchGroup.findAllWithPagination().subscribe(result => {
      this.dispatchGroups = result.dispatch_groups;
      this.dispatchGroups = this.dispatchGroups.map((d) => {
        d['ps_name'] = `#${d.id} ${d.name} (${d.tso.name})`;
        return d;
      })
    });
  }

  getAssets() {
    this._Asset.findAllWithPagination().subscribe(result => {
      this.assets = result.assets;
    });
  }

  dateTimeInputCallback(what) {
  }

  startDateSelectedCallback(date) {
    if (date.value[0] && date.value[1]) {
      this.selectedDate = {
        startDateTime: date.value[0].startOf('minute').toISOString(),
        endDateTime: date.value[1].startOf('minute').toISOString()
      };
      this.intervalString = `${this.selectedDate.startDateTime} - ${this.selectedDate.endDateTime}`;
    } else {
      this.selectedDate = {
        startDateTime: null,
        endDateTime: null
      };
      this.intervalString = '';
      this.selectedDatesModel = [null, null];
    }

    this._updateControls();
  }

  clearSelectedDateValue() {
    this.selectedDate.startDateTime = null;
    this.selectedDate.endDateTime = null;
    this.selectedDatesModel = [null, null];
    this._updateControls();
  }

  monthSelected(date) {
  }

  showDgChart() {
    this.chartsDg = [];
    this.showLoaderDg = true;
    this.disableShowDg = true;
    this.loadDgChart = true;
    const params = {
      interval: `${this.selectedDate.startDateTime} - ${this.selectedDate.endDateTime}`,
      dispatchgroup_ids: this.selectedDispatchGroups.map(d => d.id )
    };

    this._Chart.getAllocationData(params).subscribe((result)=> {
      for (let i in this.selectedDispatchGroups) {
        const dg = this.selectedDispatchGroups[i];
        const chart = this._ChartService.getAllocationDgChart(
          dg.ps_name.replace(`#${dg.id}`, ''),
          moment(`${this.selectedDate.startDateTime}`).unix() * 1000,
          moment(`${this.selectedDate.endDateTime}`).unix() * 1000,
          result.allocated_flex_series[dg.id],
          result.nominated_flex_series[dg.id],
          this.translate
        );
        this.chartsDg.push(chart);
      }
      this.disableShowDg = false;
      this.showLoaderDg = false;
    })
  }

  itemDgSelectCallback(item) {
    this._updateControls();
  }

  selectAllDgCallback(event) {
    this._updateControls();
  }

  itemDGAssetSelectCallback(items) {
    this._updateControls();
  }

  selectAllDGAssetCallback(event) {
    this._updateControls();
  }

  _updateControls() {
    this.disableShowDg = !(this.selectedDate.startDateTime && this.selectedDate.endDateTime && this.selectedDispatchGroups.length);
    this.disableShowAsset = !(this.selectedDate.startDateTime && this.selectedDate.endDateTime && this.asset);
  }

  showAssetChart() {
    this.loadAssetChart = true;
    this.showLoaderAsset = true;
    this.disableShowAsset = true;
    this.loadDgChart = true;
    const params = {
      interval: `${this.selectedDate.startDateTime} - ${this.selectedDate.endDateTime}`,
      dispatchgroup_ids: this.selectedDispatchGroupsA.map(d => d.id ),
      asset_id: this.asset.id
    };

    this._Chart.getAllocationData(params).subscribe((result)=> {
      this.chartsAsset = this._ChartService.getAllocationAssetChart(
        this.asset.name,
        moment(`${this.selectedDate.startDateTime}`).unix() * 1000,
        moment(`${this.selectedDate.endDateTime}`).unix() * 1000,
        result.allocated_flex_series,
        this.translate
      );
      this.disableShowAsset = false;
      this.showLoaderAsset = false;
    })
  }

  selectedDispatchGroupsChanged(values) {
    if (values) {
      this.dispatchGroupIds = this.selectedDispatchGroups ? this.selectedDispatchGroups.map(d => d.id ).join(',') : '';
    } else {
      this.dispatchGroupIds = '';
    }
  }

}
