@import "./../../../styles/colors";
vpp-visualize-allocations {
  display: block;

  .input-group {
    .input-group-append {
      color: #343a40;
      border: 2px solid #bfbfbf;
      border-left: 0;
      border-radius: 0 4px 4px 0;

      button {
        margin-top: 0;
      }
    }
  }

  .multiselect-dropdown {
    .dropdown-btn {
      border: 2px solid $eon-darkgrey-25 !important;
      background: #fff !important;
      border-radius: 3px !important;
      min-height: 52px !important;
    }
  }

  .inline {
    display: inline;
  }

  button {
    margin-top: 39px;
  }

  ngb-typeahead-window.dropdown-menu {
    z-index: 9999;

    button {
      margin-top: inherit !important;
    }
  }
}
