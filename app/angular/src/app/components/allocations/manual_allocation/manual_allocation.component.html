<div class="container-fluid">
  <section>
    <div class="row">
      <div class="col-md-2">
        <a class="back-button" [routerLink]="[environment.routingPath + '/history/manual-allocations']"><i class="fa fa-chevron-left"></i> {{ 'BACK' | translate }}</a>
        <br>
      </div>
    </div>
    <div class="row">
      <div class="alerts-wrapper-static col-md-8 col-lg-6" *ngIf="allocationSource.id">
        <ngb-alert
          *ngIf="allocationSource.validation_result.validationSuccess"
          [dismissible]="true"
          [type]="'success'"> {{ 'SUCCESS' | translate }}
        </ngb-alert>
        <ngb-alert
          *ngIf="!allocationSource.validation_result.validationSuccess"
          [dismissible]="false"
          [type]="'error'"> {{ 'FAILURE' | translate }}
        </ngb-alert>
      </div>
    </div>
    <div class="row">
      <div class="col-md-8 col-lg-6">
        <div class="card card-allocated">
          <div class="card-body">

            <div class="stats-group">
              <label>{{ 'HISTORY.MANUAL_ALLOCATIONS.DETAIL.VALIDATION' | translate }}</label>
              <span class="value">
                {{ 'HISTORY.MANUAL_ALLOCATIONS.DETAIL.ERRORS' | translate }}: {{ allocationSource.validation_result.errors && allocationSource.validation_result.errors.length || 0 }}
                <br>
                {{ 'HISTORY.MANUAL_ALLOCATIONS.DETAIL.WARNINGS' | translate }}: {{ allocationSource.validation_result.errors && allocationSource.validation_result.errors.length || 0}}
              </span>
            </div>
            <div class="stats-group">
              <label>{{ 'HISTORY.MANUAL_ALLOCATIONS.DETAIL.TYPE' | translate }}</label>
              <span class="value">
                {{ ('HISTORY.MANUAL_ALLOCATIONS.CONTENT_TYPE.' + (allocationSource.content_type | bidSourceType)) | translate }}
                <span *ngIf="allocationSource.content_type === 'file_import'">
                  <br>
                  <a href="{{environment.routingPath}}/asset_dg_allocation_sources/{{ allocationSource.id }}/download" target="_blank">{{ allocationSource.content.fileName }}</a>
                </span>
              </span>
            </div>
             <div class="stats-group">
              <label>{{ 'HISTORY.MANUAL_ALLOCATIONS.DETAIL.PERFORMED_ON' | translate }}</label>
              <span class="value">{{ allocationSource.created | localDate: detailDateFormat }}</span>
            </div>
            <div class="stats-group">
              <label>{{ 'HISTORY.MANUAL_ALLOCATIONS.DETAIL.USER' | translate }}</label>
              <span class="value">{{ allocationSource.user_account.name }} <{{ allocationSource.user_account.email }}></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <table class="table table-bg-turquoise table-striped" *ngIf="allocationSource.validation_result.errors.length">
    <thead>
      <tr>
        <th colspan="5">{{ 'HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.ERROR_DESCRIPTION' | translate }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let e of allocationSource.validation_result.errors">
        <td>{{ e.error_id }}</td>
        <td><span *ngFor="let e of e.entries"> row {{ e.index }}</span></td>
        <td><span *ngFor="let e of e.entries"> {{ e.entry.startTime | localDate:'DD MMM HH:mm' }} / {{ e.entry.endTime | localDate:'DD MMM HH:mm' }}</span></td>
        <td><span *ngFor="let e of e.entries"> {{ 'HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.ASSET' | translate }} {{ e.entry.assetId }}</span></td>
        <td><span *ngFor="let e of e.entries"> {{ 'HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.DG' | translate }} {{ e.entry.dgId }}</span></td>
      </tr>
    </tbody>
  </table>
  <table class="table table-bg-turquoise table-striped" *ngIf="allocationSource.validation_result.warnings.length">
    <thead>
      <tr>
        <th colspan="5">{{ 'HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.WARNING_DESCRIPTION' | translate }} ({{ allocationSource.validation_result.warnings.length }})</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let w of allocationSource.validation_result.warnings">
        <td>{{ w.warning_id }}</td>
        <td><span *ngFor="let e of w.entries"> row {{ e.index }}</span></td>
        <td><span *ngFor="let e of w.entries"> {{ e.entry.startTime | localDate:'DD MMM HH:mm' }} / {{ e.entry.endTime | localDate:'DD MMM HH:mm' }}</span></td>
        <td><span *ngFor="let e of w.entries"> {{ 'HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.ASSET' | translate }} {{ e.entry.assetId }}</span></td>
        <td><span *ngFor="let e of w.entries"> {{ 'HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.DG' | translate }} {{ e.entry.dgId }}</span></td>
      </tr>
    </tbody>
  </table>
  <table class="table table-bg-turquoise table-striped" *ngIf="allocationSource.asset_dg_allocations.length">
    <thead>
      <tr>
        <th colspan="5">{{ 'HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.ALLOCATIONS' | translate }} ({{ allocationSource.asset_dg_allocations.length }})</th>
      </tr>
      <tr>
        <th>{{ 'HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.START' | translate }}</th>
        <th>{{ 'HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.END' | translate }}</th>
        <th>{{ 'HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.DISPATCH_GROUP' | translate }}</th>
        <th>{{ 'HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.ASSET' | translate }}</th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let a of allocationSource.asset_dg_allocations">
        <td>{{ a.start_time | localDate:'ddd, DD MMM HH:mm:ss' }}</td>
        <td>{{ a.end_time | localDate:'ddd, DD MMM HH:mm:ss' }}</td>
        <td>{{ a.dispatch_group_id }}</td>
        <td>{{ a.asset_id }}</td>
      </tr>
    </tbody>
  </table>
</div>
