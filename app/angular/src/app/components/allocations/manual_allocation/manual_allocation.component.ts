import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef,
  Input,
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";

import { AllocationProvider } from "./../../../providers/allocation.provider";
import { TranslateService } from '@ngx-translate/core';
import { environment } from "../../../../environments/environment";
import { GlobalService } from "./../../../services/global.service";

@Component({
  selector: 'vpp-management-manual-allocation-detail',
  styleUrls: ['./manual_allocation.component.scss'],
  templateUrl: "./manual_allocation.component.html",
  encapsulation: ViewEncapsulation.None,
  animations: []
})

export class ManualAllocationComponent {
  allocationSource: any = {
    id: null,
    content: {
      fileName: null
    },
    content_type: null,
    created: null,
    user_account: {
      name: null,
      email: null
    },
    validation_result: {
      errors: [],
      warnings: [],
      validationSuccess: null
    },
    asset_dg_allocations: []
  };
  environment: any = environment;
  @Input('id') allocationId;
  detailDateFormat = "ddd, DD MMM YYYY HH:mm";

  constructor(
    private _Global: GlobalService,
    private _Allocation: AllocationProvider,
    private _Route: ActivatedRoute,
    public translate: TranslateService
  ) {
    this.translate.get('PAGE.CAPTIONS.HISTORY.MANUAL_ALLOCATIONS').subscribe((res: string) => {
      this._Global.changeTitle(res);
    });
    this.translate.get('HISTORY.MANUAL_ALLOCATIONS.DETAIL.PERFORMED_ON_FORMAT').subscribe((res: string) => {
      this.detailDateFormat = res;
    });
  }

  ngOnInit() {
    if (this.allocationId) {
      this.getAllocation();
    }
  }

  ngAfterViewInit() {
    this._Route.params.forEach((params: Params) => {
      if (params["id"]) {
        this.allocationId = params["id"];
        this.getAllocation();
      }
    });
  }

  getAllocation() {
    this._Allocation.findManualAllocationById(this.allocationId).subscribe(
      (data) => {
        this.allocationSource = data;
        if (this.allocationSource && this.allocationSource.validation_result) {
          if (this.allocationSource.validation_result.errors) {
            this.allocationSource.validation_result.errors.map((e) => {
              e['error_id'] = e.name.replace(/(?:^|\.?)([A-Z])/g, function (x, y) {
                return "_" + y.toLowerCase();
              }).replace(/^_/, "").toUpperCase();
              return e;
            });
          }
          if (this.allocationSource.validation_result.warnings) {
            this.allocationSource.validation_result.warnings.map((w) => {
              w['warning_id'] = w.name.replace(/(?:^|\.?)([A-Z])/g, function (x, y) {
                return "_" + y.toLowerCase();
              }).replace(/^_/, "").toUpperCase();
              return w;
            });
          }
        }
        console.log('this.allocationSource', this.allocationSource);
      }
    )
  }
}