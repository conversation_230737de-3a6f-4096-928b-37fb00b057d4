<div class="row">
  <div class="col-md-8">
    <div class="tab-content light-blue ml_30">
      <h2>{{ 'ALLOCATIONS.ENTER_ALLOCATION.TITLE' | translate }}</h2>

      <vpp-file-upload-results [response]="response"></vpp-file-upload-results>

      <div class="alerts-wrapper-static" *ngIf="sourceId">
        <ng-container *ngIf="sourceId && (allocation['allocation-action'] == 'Allocate')">
          <a [routerLink]="[environment.apiPath + '/allocations/manual-allocation/' + sourceId]" target="_blank">
            <i class="fas fa-eye">{{ 'ALLOCATIONS.ENTER_ALLOCATION.VIEW_DETAILS' | translate }}</i>
          </a>
        </ng-container>
      </div>

       <div class="form-row">
          <div class="form-group col-md-6">
            <label class="required">{{ 'ALLOCATIONS.ENTER_ALLOCATION.INTERVAL' | translate }}</label>
            <input
              type="text"
              class="form-control"
              [owlDateTime]="dt1"
              [selectMode]="'range'"
              [owlDateTimeTrigger]="dt1"
              [(ngModel)]="selectedDatesModel"
              (dateTimeChange)="startDateSelectedCallback($event)"
              placeholder="">
            <owl-date-time
              #dt1
              (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
              [pickerMode]="'popup'"></owl-date-time>
          </div>
          <div class="form-group col-md-6"></div>
          <div class="form-group col-md-6">
            <label class="required">{{ 'ALLOCATIONS.ENTER_ALLOCATION.ASSET' | translate }}</label>
            <input
                id="asset"
                type="text"
                class="form-control"
                name="asset"
                [(ngModel)]="asset"
                #aSearch="ngbTypeahead"
                [ngbTypeahead]="assetsSearch"
                [resultTemplate]="rta"
                (focus)="focusA$.next($event.target.value)"
                (click)="clickA$.next($event.target.value)"
                [inputFormatter]="assetFormatter"/>
              <ng-template #rta let-r="result" let-t="term">
                <ngb-highlight [result]="'#' + r.id + ' ' + r.name + ' ' + '(' + r.customer_name + ')'" [term]="t"></ngb-highlight>
              </ng-template>
          </div>
          <div class="form-group col-md-6">
            <label class="required">{{ 'ALLOCATIONS.ENTER_ALLOCATION.DISPATCH_GROUP' | translate }}</label>
            <input
              id="dispatchGroup"
              type="text"
              class="form-control"
              name="dispatchGroup"
              [(ngModel)]="dispatchGroup"
              #dgSearch="ngbTypeahead"
              [ngbTypeahead]="dispatchGroupSearch"
              (focus)="focusD$.next($event.target.value)"
              (click)="clickD$.next($event.target.value)"
              [resultTemplate]="rtd"
              [inputFormatter]="dispatchGroupformatter"/>
            <ng-template #rtd let-r="result" let-t="term">
              <ngb-highlight [result]="'#' + r.id + ' ' + r.name + ' (' + r.tso.name + ') '" [term]="t"></ngb-highlight>
            </ng-template>
          </div>
       </div>
       <div class="form-group">
          <div class="form-check">
            <label
              class="eon-checkbox-label bg-eon-red"
              (click)="toggleCheckbox()"
              [ngClass]="immediateEffect ? 'checked': ''">{{ 'ALLOCATIONS.ENTER_ALLOCATION.ALLOW_MODIFICATIONS_WITH_IMMEDIATE_EFFECT' | translate }}</label>
          </div>
       </div>
      <div class="form-row">
        <div class="form-group">
          <button
            [disabled]="createDisabled"
            (click)="createAllocation($event)"
            class="eon-button bg-eon-red">
            <span>{{ 'ALLOCATIONS.ENTER_ALLOCATION.CREATE_ALLOCATION' | translate }}</span>
          </button>
        </div>
        <div class="form-group">
          <button
            [disabled]="deallocateDisabled"
            (click)="deallocateAllocation($event)"
            class="eon-button bg-eon-red">
            <span>{{ 'ALLOCATIONS.ENTER_ALLOCATION.DEALLOCATE' | translate }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
