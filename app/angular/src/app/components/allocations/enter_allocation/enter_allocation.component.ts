import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Para<PERSON>,
  PRIMARY_OUTLET
} from "@angular/router";
import { Location } from "@angular/common";
import { DispatchGroupProvider } from "./../../../providers/dispatch_group.provider";
import { AssetProvider } from "./../../../providers/asset.provider";
import { AllocationProvider } from "./../../../providers/allocation.provider";
import { NotificationService } from "./../../../services/notification.service";
import { Observable, Subject, merge } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map } from 'rxjs/operators';
import * as moment from "moment-timezone";
import { angularData } from "./../../../../global.export";
import { NgbTypeahead } from '@ng-bootstrap/ng-bootstrap';
import { environment } from "../../../../environments/environment";
import { TranslateService } from '@ngx-translate/core';
import { GlobalService } from "./../../../services/global.service";

@Component({
  selector: "vpp-enter-allocation",
  templateUrl: "./enter_allocation.component.html",
  styleUrls: ["./enter_allocation.component.scss"],
  encapsulation: ViewEncapsulation.None,
  animations: []
})

export class EnterAllocationComponent implements OnInit {
  page: number = 1;
  pageSize: number = 25;
  collectionSize: number = 0;
  dispatchGroups: any[] = [];
  assets: any[] = [];
  allocation: any = {
    interval: null,
    dispatch_group_id: null,
    asset_id: null,
  };
  dispatchGroup: any = null;
  asset: any = null;
  immediateEffect: boolean = false;
  createDisabled: boolean = false;
  deallocateDisabled: boolean = false;
  focusD$ = new Subject<string>();
  clickD$ = new Subject<string>();
  focusA$ = new Subject<string>();
  clickA$ = new Subject<string>();
  errors:any = [];
  environment = environment;
  selectedDate = {
    startDateTime: null,
    endDateTime: null
  };
  selectedDatesModel = [null, null];
  response: any = null;
  sourceId: number = null;

  @ViewChild('dgSearch', { static: false }) dgSearch: NgbTypeahead;
  @ViewChild('aSearch', { static: false }) aSearch: NgbTypeahead;

  dispatchGroupSearch = (text$: Observable<string>) => {
    const debouncedText$ = text$.pipe(debounceTime(200), distinctUntilChanged());
    const clicksWithClosedPopup$ = this.clickD$.pipe(filter(() => !this.dgSearch.isPopupOpen()));
    const inputFocus$ = this.focusD$;

    return merge(debouncedText$, inputFocus$, clicksWithClosedPopup$).pipe(
      map(term => (term === '' ? this.dispatchGroups
        : this.dispatchGroups.filter(v => (v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 || (v.id + '').indexOf(term.toLowerCase()) > -1 ) )))
    );
  };

  dispatchGroupformatter = (result: any) => {
    this.dispatchGroupSelectedCallback(result);
    return `#${result.id} ${result.name}`;
  };

  assetsSearch = (text$: Observable<string>) => {
    const debouncedText$ = text$.pipe(debounceTime(200), distinctUntilChanged());
    const clicksWithClosedPopup$ = this.clickA$.pipe(filter(() => !this.aSearch.isPopupOpen()));
    const inputFocus$ = this.focusA$;

    return merge(debouncedText$, inputFocus$, clicksWithClosedPopup$).pipe(
      map(term => (term === '' ? this.assets
        : this.assets.filter(v => (v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 || (v.id + '').indexOf(term.toLowerCase()) > -1 ) )))
    );
  };

  assetFormatter = (result: any) => {
    this.assetSelectedCallback(result);
    return `#${result.id} ${result.name}`;
  };

  constructor(
    private _DispatchGroup: DispatchGroupProvider,
    private _Asset: AssetProvider,
    private _AllocationProvider: AllocationProvider,
    private _NotificationService: NotificationService,
    private _Location: Location,
    public _router: Router,
    public translate: TranslateService,
    public globalService: GlobalService,
  ) {}

  ngOnInit() {
    this.getDispatchGroups();
    this.getAssets();
  }

  getDispatchGroups() {
    this._DispatchGroup.findAllWithPagination().subscribe(result => {
      this.dispatchGroups = result.dispatch_groups;
    });
  }

  dispatchGroupSelectedCallback(dg) {
    this.dispatchGroup = dg;
  }

  getAssets() {
    this._Asset.findAllWithPagination().subscribe(result => {
      this.assets = result.assets;
    });
  }

  assetSelectedCallback(asset) {
    this.asset = asset;
  }

  toggleCheckbox() {
    this.immediateEffect = !this.immediateEffect;
  }

  startDateSelectedCallback(date) {
    if (date.value[0] && date.value[1]) {
      this.selectedDate = {
        startDateTime: date.value[0].startOf('minute').toISOString(),
        endDateTime: date.value[1].startOf('minute').toISOString()
      };
    } else {
      this.selectedDate = {
        startDateTime: null,
        endDateTime: null
      };
      this.selectedDatesModel = [null, null];
    }
  }

  createAllocation(event) {
    event.preventDefault();
    event.stopPropagation();
    this.response = null;

    if (this.allocationValid(true)) {
      this.createDisabled = true;
      this.allocation.interval = `${this.selectedDate.startDateTime} - ${this.selectedDate.endDateTime}`;
      this.allocation.dispatch_group_id = this.dispatchGroup.id;
      this.allocation.asset_id = this.asset.id;
      this.allocation['allocation-action'] = 'Allocate';

      let params = {
        asset_dg_allocation: this.allocation,
        skip_validations: this.immediateEffect
      };

      this._AllocationProvider.create(params).subscribe(
        (data) => {
          this.response = data;
          if (data.result) {
            let source = data.result;
            let validationResults = source && source.validation_result ? source.validation_result : null;
            this.sourceId = source.id;
            if (validationResults && validationResults.validationSuccess == true) {
              this.createDisabled = false;
              this.asset = null;
              this.dispatchGroup = null;
            } else {
              this.createDisabled = false;
            }
          } else {
            this.createDisabled = false;
          }
        },
        (err) => {
          this.createDisabled = false;
          this.response = {messages: [JSON.stringify(err)]};
        }
      )
    }
  }

  deallocateAllocation(event) {
    this.response = null;
    event.preventDefault();
    event.stopPropagation();

    if (this.allocationValid(true)) {
      this.deallocateDisabled = true;
      this.allocation.interval = `${this.selectedDate.startDateTime} - ${this.selectedDate.endDateTime}`;
      this.allocation.dispatch_group_id = this.dispatchGroup.id;
      this.allocation.asset_id = this.asset.id;
      this.allocation['allocation-action'] = 'Deallocate';

      let params = {
        asset_dg_allocation: this.allocation,
        skip_validations: this.immediateEffect
      };

      this._AllocationProvider.create(params).subscribe(
        (data) => {
          this.response = data;
          let source = data.result;
          let validationResults = source && source.validation_result ? source.validation_result : null;
          if (validationResults && validationResults.validationSuccess == true) {
            this.deallocateDisabled = false;
            this.asset = null;
            this.dispatchGroup = null;
          } else {
            this.deallocateDisabled = false;

          }
        },
        (err) => {
          this.deallocateDisabled = false;
          this.response = {messages: [JSON.stringify(err)]};
        }
      )
    }
  }

  allocationValid(show:boolean = false) {
    let errors = [];

    console.log('this.selectedDate', this.selectedDate);

    if (!this.selectedDate.startDateTime) {
      errors.push('MISSING_START_DATE');
    }

    if (!this.selectedDate.endDateTime) {
      errors.push('MISSING_END_DATE');
    }

    if (this.selectedDate.startDateTime == "Invalid date") {
      errors.push('INVALID_START_DATE');
    }

    if (this.selectedDate.endDateTime == "Invalid date") {
      errors.push('INVALID_END_DATE');
    }

    if (moment(this.selectedDate.endDateTime).isBefore(this.selectedDate.startDateTime)) {
      errors.push('END_DATE_BEFORE_START_DATE');
    }

    if (!this.dispatchGroup) {
      errors.push('MISSING_DISPATCH_GROUP');
    }

    if (!this.asset) {
      errors.push('MISSING_ASSET');
    }

    if (show && errors.length) {
      this.response = {messages: errors};
    }

    return !errors.length;
  }
}
