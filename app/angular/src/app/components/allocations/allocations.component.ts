import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { Location } from "@angular/common";
import { GlobalService } from "./../../services/global.service";
import { SlideRightContentAnimation } from './../../animations/slide-right-content';
import { environment } from "../../../environments/environment";
import { TranslateService } from '@ngx-translate/core';
import { angularData } from "./../../../global.export";

@Component({
  selector: "vpp-management-allocations",
  templateUrl: "./allocations.component.html",
  styleUrls: ["./allocations.component.scss"],
  encapsulation: ViewEncapsulation.None,
  animations: [ SlideRightContentAnimation ],
})
export class AllocationsComponent implements OnInit {
  selectedSelection = "";
  permissions: any;

  constructor(
    private _Global: GlobalService,
    private route: ActivatedRoute,
    private _Location: Location,
    public translate: TranslateService
  ) {
    this.translate.get('PAGE.CAPTIONS.ALLOCATIONS').subscribe((res: string) => {
      this._Global.changeTitle(res);
    });
    this.permissions = angularData.permissions;
  }

  ngOnInit() {}

  ngAfterViewInit() {
    this.route.params.forEach((params: Params) => {
      if (params["tab"]) {
        setTimeout(() => {
          this.toggleSection(null, params["tab"]);
        })
      }
    });
  }

  toggleSection(event, section) {
    if (event) {
      event.preventDefault();
      //event.stopPropagation();
    }
    if (this.selectedSelection == section) {
      this.selectedSelection = '';
      this._Location.replaceState(`${environment.routingPath}/allocations/`);
    } else {
      this.selectedSelection = section;
      this._Location.replaceState(`${environment.routingPath}/allocations/${section}`);
    }
  }
}
