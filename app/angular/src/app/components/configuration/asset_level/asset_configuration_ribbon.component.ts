import {
    Component,
    OnInit,
    ViewEncapsulation
} from "@angular/core";
import { GlobalService } from "../../../services/global.service";
import { SlideRightContentAnimation } from '../../../animations/slide-right-content';
import { TranslateService } from '@ngx-translate/core';
import {ActivatedRoute, Params} from "@angular/router";
import {environment} from "../../../../environments/environment";
import {Location} from "@angular/common";
import {angularData} from "../../../../global.export";

@Component({
    selector: "vpp-management-asset-configuration-ribbon",
    templateUrl: "./asset_configuration_ribbon.component.html",
    styleUrls: ["./asset_configuration_ribbon.component.scss"],
    encapsulation: ViewEncapsulation.None,
    animations: [ SlideRightContentAnimation ],
})
export class AssetConfigurationRibbonComponent implements OnInit {
    selectedTab: string;
    permittedTabs = { boxTypes: false, genericSteeringTypes: false, signalLists: false};

    constructor(
        public translate: TranslateService,
        private _globalService: GlobalService,
        private _route: ActivatedRoute,
        private _location: Location
    ) {
        this._globalService.changeTitle('Configuration - Asset Level');
        this.translate.get('PAGE.CAPTIONS.CONFIGURATION.ASSET_LEVEL').subscribe((res: string) => {
            this._globalService.changeTitle(res);
        });
        let permissions = angularData.permissions;
        this.permittedTabs = {
            boxTypes: permissions.can_see_asset_configurations || permissions.can_create_asset_configurations,
            genericSteeringTypes: permissions.can_see_generic_steering_config || permissions.can_create_generic_steering_config,
            signalLists: permissions.can_see_asset_configurations || permissions.can_create_asset_configurations
        };
    }

    ngOnInit() {
    }

    ngAfterViewInit() {
        this._route.params.forEach((params: Params) => {
            if (params['tab']) {
                this.selectTab(null, params['tab']);
            } else {
                if (this.permittedTabs.boxTypes) {
                    this.selectedTab = 'box-types';
                } else if (this.permittedTabs.genericSteeringTypes) {
                    this.selectedTab = 'generic-steering-types';
                } else if (this.permittedTabs.signalLists) {
                    this.selectedTab = 'signal-lists';
                }
                this.selectTab(null, this.selectedTab);
            }
        });
    }

    selectTab(event, tab) {
        this.selectedTab = tab;
        this._location.replaceState(`${environment.routingPath}/asset_configuration_ribbon/${tab}`);
    }

}
