<section> <!-- Asset Level Configuration -->
    <div class="container-fluid">
        <div id="ngb-tabset" class="ngb-tabset">
            <ul role="tablist" class="nav nav-tabs justify-content-start">

                <!-- Box Types -->
                <li class="nav-item"
                    *ngIf="permittedTabs.boxTypes">
                    <a (click)="selectTab($event, 'box-types')"
                       [ngClass]="selectedTab == 'box-types' ? 'active' : ''"
                       class="nav-link"
                       href="javascript:void(0)"
                       role="tab"
                       id="box-types" >{{'CONFIGURATION.ASSET_LEVEL.TAB.BOX_TYPES' | translate}}</a>
                </li>

                <!-- Generic Steering Types -->
                <li class="nav-item"
                    *ngIf="permittedTabs.genericSteeringTypes"
                >
                    <a (click)="selectTab($event, 'generic-steering-types')"
                       [ngClass]="selectedTab == 'generic-steering-types' ? 'active' : ''"
                       class="nav-link"
                       href="javascript:void(0)"
                       role="tab"
                       id="generic-steering-types" >{{'CONFIGURATION.ASSET_LEVEL.TAB.GENERIC_STEERING_TYPES' | translate}}</a>
                </li>

                <!-- Signal Lists -->
                <li class="nav-item"
                    *ngIf="permittedTabs.signalLists"
                >
                    <a (click)="selectTab($event, 'signal-lists')"
                       [ngClass]="selectedTab == 'signal-lists' ? 'active' : ''"
                       class="nav-link"
                       href="javascript:void(0)"
                       role="tab"
                       id="signal-lists" >{{'CONFIGURATION.ASSET_LEVEL.TAB.SIGNAL_LISTS' | translate}}</a>
                </li>
            </ul>

            <div class="tab-content">
                <div [@slideRightContentAnimation]="selectedTab == 'box-types' ? 'in' : 'out'"
                     *ngIf="permittedTabs.boxTypes">
                    <vpp-box-types-configuration *ngIf="selectedTab == 'box-types'"></vpp-box-types-configuration>
                </div>

                <div [@slideRightContentAnimation]="selectedTab == 'generic-steering-types' ? 'in' : 'out'"
                     *ngIf="permittedTabs.genericSteeringTypes">
                    <vpp-steering-types *ngIf="selectedTab == 'generic-steering-types'"></vpp-steering-types>
                </div>

                <div [@slideRightContentAnimation]="selectedTab == 'signal-lists' ? 'in' : 'out'"
                     *ngIf="permittedTabs.signalLists">
                    <vpp-signal-lists *ngIf="selectedTab == 'signal-lists'"></vpp-signal-lists>
                </div>
            </div>
        </div>
    </div>
</section>
