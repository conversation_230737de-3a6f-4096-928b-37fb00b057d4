<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="page-actions">
                <ul class="list-unstyled d-flex">
                    <li *ngIf="permissions.can_create_generic_steering_config">
                        <a href="javascript:void(0)"
                           (click)="toggleSection($event, 'enter-steering-type')"
                           class="eon-button bg-eon-turquoise">
                            {{ 'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.ENTER_STEERING_TYPE' | translate }}
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <div class="overlay-on-top-of-list"
         [@slideRightContentAnimation]="selectedSection == 'enter-steering-type' ? 'in' : 'out'">
        <vpp-steering-type-form
                class="embedded"
                *ngIf="selectedSection == 'enter-steering-type'"
                [embedded]="true"
                [openInEditMode]="true"
                (done)="reloadList()"
                (canceled)="reloadList()">
        </vpp-steering-type-form>
    </div>

    <section>
        <div class="row">
            <div class="col-md-12">
                <vpp-steering-types-list *ngIf="loadList"></vpp-steering-types-list>
            </div>
        </div>
    </section>
</div>