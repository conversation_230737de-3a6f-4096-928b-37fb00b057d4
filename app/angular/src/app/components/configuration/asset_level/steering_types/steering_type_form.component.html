<div class="row">
    <div class="col-md-8" id="full-width-in-modal">
        <div class="tab-content light-blue" style="border: none" id="enter-steering-type-tab">
            <span *ngIf="!embedded" class="pull-right" (click)="cancel($event)">
                <i class="fa fa-times"></i>
            </span>

            <h2 *ngIf="isEditing">{{ 'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.ENTER_STEERING_TYPE' | translate }}</h2>
            <h2 *ngIf="!isEditing">{{ 'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SHOW_STEERING_TYPE' | translate }}</h2>

            <div class="alerts-wrapper-static" *ngIf="errors.length">
                <ng-container *ngFor="let errmsg of errors; let ix = index;">
                    <ngb-alert
                            [dismissible]="false"
                            [type]="'error'"> {{ errmsg }}
                    </ngb-alert>
                </ng-container>
            </div>

            <div class="form-row">
                <div class="form-group col-md-4">
                    <label>Adapter Name</label>
                    <input
                            *ngIf="isEditing"
                            [(ngModel)]="steeringType.name"
                            type="text"
                            class="form-control"
                            id="inputName">
                    <span
                            *ngIf="!isEditing"
                            class="value">
                        {{ steeringType.name }}
                    </span>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group col-md-4">
                    <label>Basepoint</label>
                    <select
                            *ngIf="isEditing"
                            [(ngModel)]="steeringType.hasBasepoint">
                        <option
                                *ngFor="let b of BASEPOINT_OPTS"
                                [ngValue]="b">
                            {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.BASEPOINTS.' + b) | translate }}
                        </option>
                    </select>
                    <span
                            *ngIf="!isEditing"
                            class="value">
                        {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.BASEPOINTS.' + steeringType.hasBasepoint) | translate }}
                    </span>
                </div>
                <div class="form-group col-md-4">
                    <label>Schedule</label>
                    <select
                            *ngIf="isEditing"
                            [(ngModel)]="steeringType.scheduleType">
                        <option
                                *ngFor="let st of SCHEDULE_TYPE_OPTS"
                                [ngValue]="st">
                            {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SCHEDULES.' + st) | translate }}
                        </option>
                    </select>
                    <span
                            *ngIf="!isEditing"
                            class="value">
                        {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SCHEDULES.' + steeringType.scheduleType) | translate }}
                    </span>
                </div>
                <div class="form-group col-md-4">
                    <label>Flex</label>
                    <select
                            *ngIf="isEditing"
                            [(ngModel)]="steeringType.flexType">
                        <option
                                *ngFor="let ft of FLEX_TYPE_OPTS"
                                [ngValue]="ft">
                            {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.FLEXES.' + ft) | translate }}
                        </option>
                    </select>
                    <span
                            *ngIf="!isEditing"
                            class="value">
                        {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.FLEXES.' + steeringType.flexType) | translate }}
                    </span>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group col-md-4">
                    <label>Setpoint</label><!-- setpoint_relativity -->
                    <select
                            *ngIf="isEditing"
                            [(ngModel)]="steeringType.setpointValueType">
                        <option
                                *ngFor="let st of SETPOINT_VALUE_TYPE_OPTS"
                                [ngValue]="st">
                            {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_RELATIVITIES.' + st) | translate }}
                        </option>
                    </select>
                    <span
                            *ngIf="!isEditing"
                            class="value">
                        {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_RELATIVITIES.' + steeringType.setpointValueType) | translate }}
                    </span>
                </div>
                <div class="form-group col-md-4">
                    <label>Default Setpoint</label><!-- asset_sp -->
                    <span
                            id="asset_sp_notice"
                            *ngIf="isRelativeSteering()"
                            class="value">
                        {{ isOnOff() ? 'OFF' : '0' }}
                    </span>
                    <select
                            *ngIf="isEditing && !isRelativeSteering()"
                            [(ngModel)]="steeringType.defaultAbsoluteSetpoint">
                        <option
                                *ngFor="let x of DEFAULT_ABSOLUTE_SETPOINT_OPTS"
                                [ngValue]="x">
                            {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.DEFAULT_ABSOLUTE_SETPOINTS.' + x) | translate }}
                        </option>
                    </select>
                    <span
                            *ngIf="!isEditing && !isRelativeSteering()"
                            class="value">
                        {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.DEFAULT_ABSOLUTE_SETPOINTS.' + steeringType.defaultAbsoluteSetpoint) | translate }}
                    </span>
                </div>
                <div class="form-group col-md-4">
                    <label>Default Setpoint</label><!-- has_setpoint_feedback -->
                    <select
                            *ngIf="isEditing"
                            [(ngModel)]="steeringType.hasSetpointFeedback">
                        <option
                                *ngFor="let x of BOOLEANS"
                                [ngValue]="x">
                            {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.YES_NOS.' + x) | translate }}
                        </option>
                    </select>
                    <span
                            *ngIf="!isEditing"
                            class="value">
                        {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.YES_NOS.' + steeringType.hasSetpointFeedback) | translate }}
                    </span>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group col-md-4">
                    <label>Setpoint unit</label>
                    <select
                            *ngIf="isEditing"
                            [(ngModel)]="steeringType.setpointType">
                        <option
                                *ngFor="let x of SETPOINT_TYPE_OPTS"
                                [ngValue]="x">
                            {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_UNITS.' + x) | translate }}
                        </option>
                    </select>
                    <span
                            *ngIf="!isEditing"
                            class="value">
                        {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_UNITS.' + steeringType.setpointType) | translate }}
                    </span>
                </div>
                <div class="form-group col-md-4">
                    <!-- empty -->
                </div>
                <div class="form-group col-md-4">
                    <label>Setpoint Frequency (s)</label>
                    <input
                            *ngIf="isEditing"
                            type="number" min="1"
                            [(ngModel)]="steeringType.setpointFrequencySeconds">
                    <span
                            *ngIf="!isEditing"
                            class="value">
                        {{ steeringType.setpointFrequencySeconds }}
                    </span>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group col-md-4">
                    <select
                            *ngIf="isEditing"
                            [(ngModel)]="steeringType.hasLock">
                        <option
                                *ngFor="let x of BOOLEANS"
                                [ngValue]="x">
                            {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_LOCKS.' + x) | translate }}
                        </option>
                    </select>
                    <span
                            *ngIf="!isEditing"
                            class="value">
                        {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_LOCKS.' + steeringType.hasLock) | translate }}
                    </span>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group col-md-4">
                    <select
                            *ngIf="isEditing"
                            [(ngModel)]="steeringType.setpointIntervalType">
                        <option
                                *ngFor="let x of SETPOINT_INTERVAL_TYPE_OPTS"
                                [ngValue]="x">
                            {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_INTERVALS.' + x) | translate }}
                        </option>
                    </select>
                    <span
                            *ngIf="!isEditing"
                            class="value">
                        {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_INTERVALS.' + steeringType.setpointIntervalType) | translate }}
                    </span>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group col-md-4">
                    <label>Dispatch Deviated</label>
                    <select
                            *ngIf="isEditing"
                            [(ngModel)]="steeringType.hasDispatchedDeviated">
                        <option
                                *ngFor="let x of BOOLEANS"
                                [ngValue]="x">
                            {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.YES_NOS.' + x) | translate }}
                        </option>
                    </select>
                    <span
                            *ngIf="!isEditing"
                            class="value">
                        {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.YES_NOS.' + steeringType.hasDispatchedDeviated) | translate }}
                    </span>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group col-md-4">
                    <label>Heartbeat VPP Comm Alive</label>
                    <select
                            *ngIf="isEditing"
                            [(ngModel)]="steeringType.hasHeartbeatVPP">
                        <option
                                *ngFor="let x of BOOLEANS"
                                [ngValue]="x">
                            {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.YES_NOS.' + x) | translate }}
                        </option>
                    </select>
                    <span
                            *ngIf="!isEditing"
                            class="value">
                        {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.YES_NOS.' + steeringType.hasHeartbeatVPP) | translate }}
                    </span>
                </div>
                <div class="form-group col-md-4">
                    <label>Frequency (s)</label>
                    <input
                            *ngIf="isEditing"
                            type="number"
                            min="0"
                            [(ngModel)]="steeringType.heartbeatVPPFrequencySeconds"
                            [disabled]="!steeringType.hasHeartbeatVPP"
                    >
                    <span
                            *ngIf="!isEditing"
                            class="value">
                        {{ steeringType.heartbeatVPPFrequencySeconds }}
                    </span>
                </div>
                <div class="form-group col-md-4">
                    <label>Read / Write Authorization</label>
                    <select
                            *ngIf="isEditing"
                            [(ngModel)]="steeringType.heartbeatVPPType"
                            [disabled]="!steeringType.hasHeartbeatVPP"
                    >
                        <option
                                *ngFor="let x of HEARTBEAT_TYPE_OPTS"
                                [ngValue]="x">
                            {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.HEARTBEAT_AUTHORIZATIONS.' + x) | translate }}
                        </option>
                    </select>
                    <span
                            *ngIf="!isEditing"
                            class="value">
                        {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.HEARTBEAT_AUTHORIZATIONS.' + steeringType.heartbeatVPPType) | translate }}
                    </span>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group col-md-4">
                    <label>Heartbeat Asset Comm Alive</label>
                    <select
                            *ngIf="isEditing"
                            [(ngModel)]="steeringType.hasHeartbeatAsset">
                        <option
                                *ngFor="let x of BOOLEANS"
                                [ngValue]="x">
                            {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.YES_NOS.' + x) | translate }}
                        </option>
                    </select>
                    <span
                            *ngIf="!isEditing"
                            class="value">
                        {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.YES_NOS.' + steeringType.hasHeartbeatAsset) | translate }}
                    </span>
                </div>
                <div class="form-group col-md-4">
                    <label>Frequency (s)</label>
                    <input
                            *ngIf="isEditing"
                            type="number"
                            min="0"
                            [(ngModel)]="steeringType.heartbeatAssetFrequencySeconds"
                            [disabled]="!steeringType.hasHeartbeatAsset">
                    <span
                            *ngIf="!isEditing"
                            class="value">
                        {{ steeringType.heartbeatAssetFrequencySeconds }}
                    </span>
                </div>
                <div class="form-group col-md-4">
                    <label>Read / Write Authorization</label>
                    <select
                            *ngIf="isEditing"
                            [(ngModel)]="steeringType.heartbeatAssetType"
                            [disabled]="!steeringType.hasHeartbeatAsset">
                        <option
                                *ngFor="let x of HEARTBEAT_TYPE_OPTS"
                                [ngValue]="x">
                            {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.HEARTBEAT_AUTHORIZATIONS.' + x) | translate }}
                        </option>
                    </select>
                    <span
                            *ngIf="!isEditing"
                            class="value">
                        {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.HEARTBEAT_AUTHORIZATIONS.' + steeringType.heartbeatAssetType) | translate }}
                    </span>
                </div>
            </div>

            <ng-template [ngIf]="isEditing">
                <ul class="list-inline list-unstyled d-flex form-actions">
                    <li>
                        <button
                                (click)="submit()"
                                class="eon-button bg-eon-red">
                            <span>{{ (embedded ? 'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.CREATE_STEERING_TYPE'
                                : 'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SAVE_STEERING_TYPE') | translate }}</span>
                        </button>
                    </li>
                    <li *ngIf="isEditing && !openInEditMode">
                        <button
                                (click)="cancelEditMode()"
                                class="eon-button bg-eon-red">
                            <span>{{ 'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.CANCEL_EDIT' | translate }}</span>
                        </button>
                    </li>
                </ul>
            </ng-template>

            <ng-template [ngIf]="!isEditing && permissions.can_create_generic_steering_config">
                <ul class="list-inline list-unstyled d-flex form-actions">
                    <li>
                        <button
                                (click)="enterEditMode()"
                                class="eon-button bg-eon-red">
                            <span>{{ 'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.EDIT' | translate }}</span>
                        </button>
                    </li>
                    <li>
                        <button
                                (click)="delete()"
                                class="eon-button bg-eon-red">
                            <span>{{ 'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.DELETE' | translate }}</span>
                        </button>
                    </li>
                </ul>
            </ng-template>

        </div>
    </div>
</div>