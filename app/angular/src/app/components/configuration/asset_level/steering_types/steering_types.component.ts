import {Component, OnInit, ViewEncapsulation} from "@angular/core";
import {SlideRightContentAnimation} from "../../../../animations/slide-right-content";
import {TranslateService} from "@ngx-translate/core";
import {GlobalService} from "../../../../services/global.service";
import {ActivatedRoute} from "@angular/router";
import {Location} from "@angular/common";
import {angularData} from "../../../../../global.export";

@Component({
    selector: "vpp-steering-types",
    templateUrl: "./steering_types.component.html",
    styleUrls: ["./steering_types.component.scss"],
    encapsulation: ViewEncapsulation.None,
    animations: [ SlideRightContentAnimation ]
})
export class SteeringTypesComponent implements OnInit {
    selectedSection = "";
    loadList : boolean = true;
    permissions : any;

    constructor(
        public translate: TranslateService,
        private _global: GlobalService,
        private _route: ActivatedRoute,
        private _location: Location
    ) {
        this.permissions = angularData.permissions;
        this.translate.get('PAGE.CAPTIONS.CONFIGURATION.ASSET_LEVEL.GENERIC_STEERING_TYPES').subscribe((res: string) => {
            this._global.changeTitle(res);
        });
    }

    ngOnInit() {}

    ngAfterViewInit() {
    }

    toggleSection(event, section) {
        if (event) {
            //event.preventDefault();
            //event.stopPropagation();
        }

        if (this.selectedSection == section) {
            this.selectedSection = '';
        } else {
            this.selectedSection = section;
        }
    }

    reloadList() {
        this.toggleSection(null, '');
        // reload the list in the next refresh cycle
        this.loadList = false;
        setTimeout(() => {
            this.loadList = true;
        });
    }

}