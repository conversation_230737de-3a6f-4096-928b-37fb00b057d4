<div class="container-fluid">
    <div class="row">
        <div class="col">
            <h2>{{'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.LIST.TITLE' | translate}}</h2>

            <div class="table-filter d-flex"></div>

            <table class="table table-steering-types table-auction-results table-striped table-bg-turquoise">
                <tr>
                    <th class="name">{{'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.NAME' | translate}}</th>
                    <th class="basepoint">{{'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.BASEPOINT' | translate}}</th>
                    <th class="schedule">{{'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.SCHEDULE' | translate}}</th>
                    <th class="flex">{{'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.FLEX' | translate}}</th>
                    <th class="setpoint">{{'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.SETPOINT' | translate}}</th>
                    <th class="dispatch-deviated">{{'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.DISPATCH_DEVIATED' | translate}}</th>
                    <th class="heartbeat-vpp">{{'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.HEARTBEAT_VPP' | translate}}</th>
                    <th class="heartbeat-asset">{{'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.HEARTBEAT_ASSET' | translate}}</th>
                    <th class="actions"></th>
                </tr>
                <tr *ngIf="showLoader">
                    <td colspan="9" class="text-center vertical-center">
                        <vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
                    <td>
                </tr>
                <ng-container *ngFor="let x of steeringTypes">
                    <tr>
                        <input type="hidden" value="{{ x.id }}">
                        <td class="name">{{ x.name }}</td>
                        <td class="basepoint">{{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.BASEPOINTS.' + x.hasBasepoint) | translate }}</td>
                        <td class="schedule">{{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SCHEDULES.' + x.scheduleType) | translate }}</td>
                        <td class="flex">{{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.FLEXES.' + x.flexType) | translate }}</td>
                        <td class="setpoint">
                            <ul>
                                <li>{{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_RELATIVITIES.' + x.setpointValueType) | translate }}</li>
                                <li>{{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_UNITS.' + x.setpointType) | translate }}</li>
                                <li>{{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_LOCKS.' + x.hasLock) | translate }}</li>
                                <li>{{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_INTERVALS.' + x.setpointIntervalType) | translate }}</li>
                                <li>{{ 'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.FEEDBACK' | translate }}: {{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.Y_NS.' + x.hasSetpointFeedback) | translate }}</li>
                                <li>{{ 'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.FREQUENCY_S' | translate }}: {{ x.setpointFrequencySeconds }}</li>
                            </ul>
                        </td>
                        <td class="dispatch-deviated">{{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.Y_NS.' + x.hasDispatchedDeviated) | translate }}</td>
                        <td class="heartbeat-vpp">
                            <ul>
                                <li>{{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.Y_NS.' + x.hasHeartbeatVPP) | translate }}</li>
                                <ng-template [ngIf]="x.hasHeartbeatVPP">
                                    <li>{{ 'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.FEEDBACK' | translate }}: {{ x.heartbeatVPPFrequencySeconds }}</li>
                                    <li>{{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.HEARTBEAT_AUTHORIZATIONS.' + x.heartbeatVPPType) | translate }}</li>
                                </ng-template>
                            </ul>
                        </td>
                        <td class="heartbeat-asset">
                            <ul>
                                <li>{{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.Y_NS.' + x.hasHeartbeatAsset) | translate }}</li>
                                <ng-template [ngIf]="x.hasHeartbeatAsset">
                                    <li>{{ 'CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.FEEDBACK' | translate }}: {{ x.heartbeatAssetFrequencySeconds }}</li>
                                    <li>{{ ('CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.HEARTBEAT_AUTHORIZATIONS.' + x.heartbeatAssetType) | translate }}</li>
                                </ng-template>
                            </ul>
                        </td>
                        <td class="actions">
                            <ul class="list-inline list-unstyled">
                                <li *ngIf="permissions.can_see_generic_steering_config"><span (click)="showSteeringType(x)" style="display: inline-block"><i class="fa fa-eye"></i></span></li>
                                <li *ngIf="permissions.can_create_generic_steering_config"><span (click)="editSteeringType(x)" style="display: inline-block"><i class="fa fa-pencil"></i></span></li>
                                <li *ngIf="permissions.can_create_generic_steering_config"><span (click)="deleteSteeringType(x)" style="display: inline-block"><i class="fa fa-trash"></i></span></li>
                            </ul>
                        </td>
                    </tr>
                </ng-container>
            </table>
        </div>
    </div>
</div>

<ng-template #edit>
    <vpp-steering-type-form
            [steeringType]="editedSteeringType"
            [openInEditMode]="openInEditMode"
            [embedded]="false"
            (done)="closeModalForm(true)"
            (canceled)="closeModalForm(false)">
    </vpp-steering-type-form>
</ng-template>