import {Component, OnInit, ViewChild, ViewEncapsulation} from "@angular/core";
import {TranslateService} from "@ngx-translate/core";
import {GlobalService} from "../../../../services/global.service";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {NotificationService} from "../../../../services/notification.service";
import {ConfirmService} from "../../../../services/confirm.service";
import {AssetConfigurationProvider} from "../../../../providers/asset_configuration.provider";
import {angularData} from "../../../../../global.export";

@Component({
    selector: 'vpp-steering-types-list',
    templateUrl: './steering_types_list.component.html',
    styleUrls: ['./steering_types_list.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class SteeringTypesListComponent implements OnInit {
    showLoader : boolean = true;
    steeringTypes = [];
    params = {
        page: 1,
        per_page: 25,
    };
    page = 1;
    pageSize = 25;
    collectionSize = 0;
    editedSteeringType: any;
    openInEditMode = true;
    confirmDeleteMessage = '';
    bsModalRef;
    permissions: any;

    @ViewChild('edit', { static: true }) editModal;

    constructor(
        public translate: TranslateService,
        public globalService: GlobalService,
        private _modalService: NgbModal,
        private _notificationService: NotificationService,
        private _confirmService: ConfirmService,
        private _assetConfiguration: AssetConfigurationProvider
    ) {
        this.translate.get('CONFIRM.MESSAGE.DELETE_ENTRY').subscribe((x) => {
            this.confirmDeleteMessage = x;
        });
        this.permissions = angularData.permissions;
    }

    ngOnInit(): void {
        this.getSteeringTypes();
    }

    getSteeringTypes() {
        this.showLoader = true;
        this.steeringTypes = [];

        this._assetConfiguration.getGenericSteeringConfigs(this.params).subscribe(
            res => {
                this.steeringTypes = res.configs;
                //this.collectionSize = res.total_entries;
                this.showLoader = false;
            }, err => {
                this.showLoader = false;
                console.log('Failed to load steering type configurations', err);
            }
        );
    }

    pageChangedCallback(page) {
        this.params.page = page;
        this.getSteeringTypes();
    }

    pageSizeChangedCallback(pageSize) {
        this.params.per_page = pageSize;
        this.params.page = 1;
        this.getSteeringTypes();
    }

    showSteeringType(x) {
        this.editedSteeringType = { ...x};
        this.openInEditMode = false;
        this.bsModalRef = this._modalService.open(this.editModal, { size: 'lg' });
    }

    editSteeringType(x) {
        this.editedSteeringType = { ...x};
        this.openInEditMode = true;
        this.bsModalRef = this._modalService.open(this.editModal, { size: 'lg' });
    }

    closeModalForm(reload: boolean = true) {
        this.bsModalRef.close();
        if (reload) {
            this.getSteeringTypes();
        }
    }

    deleteSteeringType(x) {
        this._confirmService.confirm({
            message: this.confirmDeleteMessage,
        }).then(() => {
            this._deleteConfirmedSteeringType(x);
        }, () => {
        });
    }

    _deleteConfirmedSteeringType(x) {
        this._assetConfiguration
            .deleteGenericSteeringConfig(x.id)
            .subscribe(
                (res) => {
                    if (res.success == true) {
                        this._notificationService.success({ text: 'SUCCESS'});
                    } else {
                        this._notificationService.error({ text: JSON.stringify(res.error)});
                    }
                    this.getSteeringTypes();
                },
                (err) => {
                    console.log('Failed to delete', err);
                    this._notificationService.error({ text: JSON.stringify(err)});
                });
    }
}