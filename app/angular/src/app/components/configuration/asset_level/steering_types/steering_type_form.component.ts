import {Component, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewEncapsulation} from "@angular/core";
import {TranslateService} from "@ngx-translate/core";
import {NotificationService} from "../../../../services/notification.service";
import {AssetConfigurationProvider} from "../../../../providers/asset_configuration.provider";
import {ConfirmService} from "../../../../services/confirm.service";
import {angularData} from "../../../../../global.export";

@Component({
    selector: 'vpp-steering-type-form',
    templateUrl: './steering_type_form.component.html',
    styleUrls: ['./steering_type_form.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class SteeringTypeFormComponent implements OnInit {
    @Output('done') done: any = new EventEmitter();
    @Output('canceled') canceled: any = new EventEmitter();
    @Input('steeringType') steeringType: any = {
        id: null,
        name: '',
        hasBasepoint: "NoBasepoint",
        scheduleType: "Flexibility",
        flexType: "Both",
        setpointValueType: "Relative",
        hasSetpointFeedback: true,
        setpointType: "Kilowatts",
        setpointIntervalType: "Continuous",
        setpointFrequencySeconds: 3,
        hasLock: false,
        hasDispatchedDeviated: false,
        hasHeartbeatVPP: false,
        heartbeatVPPFrequencySeconds: 15,
        heartbeatVPPType: "ReadWrite",
        hasHeartbeatAsset: false,
        heartbeatAssetFrequencySeconds: 15,
        heartbeatAssetType: "ReadWrite",
        defaultAbsoluteSetpoint: "Default"
    };
    @Input() openInEditMode: boolean;
    @Input() embedded: boolean = false;

    BASEPOINT_OPTS = ['NoBasepoint', 'HasBasepoint', 'HasBasepointAndPbp'];
    SETPOINT_TYPE_OPTS = ['Boolean', 'Kilowatts'];
    SETPOINT_VALUE_TYPE_OPTS = ['Relative', 'Absolute'];
    SETPOINT_INTERVAL_TYPE_OPTS = ['Continuous', 'OnOff', 'Steps'];
    SCHEDULE_TYPE_OPTS = ['Production', 'Flexibility'];
    HEARTBEAT_TYPE_OPTS = ['ReadWrite', 'Read', 'Write'];
    FLEX_TYPE_OPTS = ['Positive', 'Negative', 'Both'];
    DEFAULT_ABSOLUTE_SETPOINT_OPTS = ['ImposedPMax', 'ImposedPMin', 'PNorm'];
    BOOLEANS = [true, false];

    errors: any = [];
    canSubmit: boolean = true;
    isEditing: boolean;
    confirmDeleteMessage = '';
    permissions: any;

    steeringTypeCopy = null;

    constructor(
        public translate: TranslateService,
        private _notificationService: NotificationService,
        private _assetConfiguration: AssetConfigurationProvider,
        private _confirmService: ConfirmService
    ) {
        this.translate.get('CONFIRM.MESSAGE.DELETE_ENTRY').subscribe((x) => {
            this.confirmDeleteMessage = x;
        });
        this.permissions = angularData.permissions;
    }

    ngOnInit(): void {
        this.isEditing = this.openInEditMode;
        console.log('edit steering type', this.steeringType, 'in mode', this.isEditing);
    }

    ngOnChanges(changes: SimpleChanges) {
        if (changes['steeringType']) {
            this.steeringTypeCopy = {...this.steeringType}
        }
    }

    close(event) {
        this.done.emit();
    }

    cancel(event) {
        this.canceled.emit();
    }

    enterEditMode() {
        this.isEditing = true;
    }

    cancelEditMode() {
        this.isEditing = false;
        this.steeringType = {...this.steeringTypeCopy}
    }

    submit() {
        console.log('Submit', this.steeringType);

        this.errors = [];


        let isNew = !this.steeringType.id;

        let params = {
            steering_type: this.steeringType
        };

        this.canSubmit = false;
        let fn = isNew ? this._assetConfiguration.createGenericSteeringConfig : this._assetConfiguration.updateGenericSteeringConfig;
        fn.call(this._assetConfiguration, params).subscribe(
            (res) => {
                this.canSubmit = true;
                if (res.success == true) {
                    this._notificationService.success({ text: 'SUCCESS'});
                    this.done.emit();
                } else {
                    this.errors = res.messages;
                    this._notificationService.errors(this.errors);
                }
            },
            (err) => {
                console.log('submit failed', err);
                this.errors = [JSON.stringify(err)];
                this._notificationService.errors(this.errors);
                this.canSubmit = true;
            }
        );
    }

    delete() {
        this._confirmService.confirm({
            message: this.confirmDeleteMessage,
        }).then(() => {
            this._deleteConfirmed();
        }, () => {
        });
    }

    _deleteConfirmed() {
        this._assetConfiguration
            .deleteGenericSteeringConfig(this.steeringType.id)
            .subscribe(
                (res) => {
                    if (res.success == true) {
                        this._notificationService.success({ text: 'SUCCESS'});
                        this.done.emit();
                    } else {
                        this._notificationService.error({ text: JSON.stringify(res.error)});
                    }
                },
                (err) => {
                    console.log('Failed to delete', err);
                    this._notificationService.error({ text: JSON.stringify(err)});
                });
    }

    isRelativeSteering(): boolean {
        return this.steeringType.setpointValueType == 'Relative';
    }

    isOnOff() {
        return this.steeringType.setpointIntervalType == 'OnOff';
    }

    isBasepointFromAsset() {
        return this.steeringType.hasBasepoint == 'HasBasepoint';
    }
}