<div class="row">
    <div class="col-md-8" id="full-width-in-modal">
        <div class="tab-content light-blue" style="border: none" id="enter-signal-list-tab">
            <span *ngIf="!embedded" class="pull-right" (click)="cancel($event)">
                <i class="fa fa-times"></i>
            </span>

            <h2 *ngIf="isEditing">{{ 'CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.ENTER_SIGNAL_LIST' | translate }}</h2>

            <div class="alerts-wrapper-static" *ngIf="errors.length">
                <ng-container *ngFor="let errmsg of errors; let ix = index;">
                    <ngb-alert
                            [dismissible]="false"
                            [type]="'error'"> {{ errmsg }}
                    </ngb-alert>
                </ng-container>
            </div>

            <div class="form-row">
                <div class="form-group col-md-8">
                    <label>Name</label>
                    <input
                            *ngIf="isEditing"
                            [(ngModel)]="signalList.name"
                            type="text"
                            class="form-control"
                            id="inputName">
                    <span
                            *ngIf="!isEditing"
                            class="value">
                        {{ signalList.name }}
                    </span>
                </div>
            </div>

            <div class="row">
                <div class="form-group col-md-3">
                    <label>{{ 'CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.DIRECTION' | translate }}</label>
                </div>
                <div class="form-group col-md-5">
                    <label>{{ 'CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.NAME' | translate }}</label>
                </div>
            </div>

            <div class="row" *ngFor="let s of signalList.vpp_signals; let last = last">
                <div class="form-group col-md-3">
                    <select
                        class="form-control"
                        [(ngModel)]="s.direction">
                        <option
                            *ngFor="let d of directions"
                            [ngValue]="d">
                            {{ d }}
                        </option>
                    </select>
                </div>
                <div class="form-group col-md-5">
                    <input
                            *ngIf="!last"
                            class="form-control"
                            type="text"
                            [(ngModel)]="s.vpp_name"
                    >
                    <input
                            *ngIf="last"
                            #lastSignalName
                            class="form-control"
                            type="text"
                            [(ngModel)]="s.vpp_name"
                    >
                </div>
                <div class="form-group">
                    <button (click)="onDeleteSignal(s)"
                            class="btn btn-link form-control"
                            style="border: none; padding: 0">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
            </div>

            <div class="row">
                <div class="col-md-2 form-group">
                    <button (click)="onNewSignal($event)"
                            class="btn btn-link form-control"
                            style="border: none; padding: 0">

                        <span>
                            <i class="fa fa-plus"></i>
                            Add Signal
                        </span>
                    </button>
                </div>
            </div>

            <!--
            <div class="row">
                <div class="form-group col-md-3">
                    <select
                            class="form-control"
                            [(ngModel)]="newSignal.direction">
                        <option
                                *ngFor="let d of directions"
                                [ngValue]="d">
                            {{ d }}
                        </option>
                    </select>
                </div>
                <div class="form-group col-md-5">
                    <input
                            class="form-control"
                            type="text"
                            [(ngModel)]="newSignal.vpp_name"
                            (change)="onNewSignal($event)"
                    >
                </div>
            </div>
            -->

            <!-- Action button -->
            <ul class="list-inline list-unstyled d-flex form-actions">
                <li>
                    <button
                            (click)="submit()"
                            class="eon-button bg-eon-red">
                        <span>{{ 'CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.SAVE' | translate }}</span>
                    </button>
                </li>
            </ul>

        </div>
    </div>
</div>