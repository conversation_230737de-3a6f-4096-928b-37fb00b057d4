import {Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild, ViewEncapsulation} from "@angular/core";
import {TranslateService} from "@ngx-translate/core";
import {NotificationService} from "../../../../services/notification.service";
import {AssetConfigurationProvider} from "../../../../providers/asset_configuration.provider";
import {ConfirmService} from "../../../../services/confirm.service";
import {angularData} from "../../../../../global.export";

@Component({
    selector: 'vpp-signal-list-form',
    templateUrl: './signal_list_form.component.html',
    styleUrls: ['./signal_list_form.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class SignalListFormComponent implements OnInit {
    @Output('done') done: any = new EventEmitter();
    @Output('canceled') canceled: any = new EventEmitter();
    @Input('signalList') signalList: any = {
        id: null,
        name: '',
        read_signals: [],
        write_signals: [],
        vpp_signals_attributes: [],
        vpp_signals: []
    };
    @Input() openInEditMode: boolean;
    @Input() embedded: boolean = false;

    @ViewChild('lastSignalName', {read: ElementRef, static: false}) lastSignalNameInputElement: ElementRef;

    directions = ['read', 'write'];

    newSignalTemplate = {
        id: null,
        direction: 'read',
        vpp_name: ''
    };

    newSignal = {...this.newSignalTemplate};

    errors: any = [];
    canSubmit: boolean = true;
    isEditing: boolean;
    confirmDeleteMessage = '';
    permissions: any;

    constructor(
        public translate: TranslateService,
        private _notificationService: NotificationService,
        private _assetConfiguration: AssetConfigurationProvider,
        private _confirmService: ConfirmService
    ) {
        this.translate.get('CONFIRM.MESSAGE.DELETE_ENTRY').subscribe((x) => {
            this.confirmDeleteMessage = x;
        });
        this.permissions = angularData.permissions;
    }

    ngOnInit(): void {
        this.isEditing = this.openInEditMode;
        console.log('edit signal list', this.signalList, 'in mode', this.isEditing);
    }

    close(event) {
        this.done.emit();
    }

    cancel(event) {
        this.canceled.emit();
    }

    enterEditMode() {
        this.isEditing = true;
    }

    cancelEditMode() {
        this.isEditing = false;
    }

    onNewSignal(event) {
        this.signalList.vpp_signals.push(this.newSignal);
        this.newSignal = {...this.newSignalTemplate};

        setTimeout(() => {
            let el = this.lastSignalNameInputElement.nativeElement;
            el.focus();
        });
    }

    onDeleteSignal(x) {
        this.signalList.vpp_signals = this.signalList.vpp_signals.filter(i => i != x);
    }

    submit() {
        console.log('Submit', this.signalList);

        this.errors = [];

        let isNew = !this.signalList.id;
        this.signalList.vpp_signals_attributes =
            this.signalList.vpp_signals.filter(x => x.vpp_name && x.vpp_name.length > 0);

        let params = {
            signal_list: this.signalList
        };

        this.canSubmit = false;
        let fn = isNew ? this._assetConfiguration.createSignalList : this._assetConfiguration.updateSignalList;
        fn.call(this._assetConfiguration, params).subscribe(
            (res) => {
                this.canSubmit = true;
                if (res.success == true) {
                    this._notificationService.success({text: 'SUCCESS'});
                    this.done.emit();
                } else {
                    this.errors = res.messages;
                    this._notificationService.errors(this.errors);
                }
            },
            (err) => {
                console.log('submit failed', err);
                this.errors = [JSON.stringify(err)];
                this._notificationService.errors(this.errors);
                this.canSubmit = true;
            }
        );
    }

    delete() {
        this._confirmService.confirm({
            message: this.confirmDeleteMessage,
        }).then(() => {
            this._deleteConfirmed();
        }, () => {
        });
    }

    _deleteConfirmed() {
        this._assetConfiguration
            .deleteSignalList(this.signalList.id)
            .subscribe(
                (res) => {
                    if (res.success == true) {
                        this._notificationService.success({text: 'SUCCESS'});
                        this.done.emit();
                    } else {
                        this._notificationService.error({text: JSON.stringify(res.error)});
                    }
                },
                (err) => {
                    console.log('Failed to delete', err);
                    this._notificationService.error({text: JSON.stringify(err)});
                });
    }

}