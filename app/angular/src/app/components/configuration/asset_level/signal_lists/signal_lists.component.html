<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="page-actions">
                <ul class="list-unstyled d-flex">
                    <li *ngIf="permissions.can_create_asset_configurations">
                        <a href="javascript:void(0)"
                           (click)="toggleSection($event, 'enter-signal-list')"
                           class="eon-button bg-eon-turquoise">
                            {{ 'CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.ENTER_SIGNAL_LIST' | translate }}
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <div class="overlay-on-top-of-list"
         [@slideRightContentAnimation]="selectedSection == 'enter-signal-list' ? 'in' : 'out'">
        <vpp-signal-list-form
                class="embedded"
                *ngIf="selectedSection == 'enter-signal-list'"
                [embedded]="true"
                [openInEditMode]="true"
                (done)="reloadList()"
                (canceled)="reloadList()">
        </vpp-signal-list-form>
    </div>

    <section>
        <div class="row">
            <div class="col-md-12">
                <vpp-signal-lists-list *ngIf="loadList"></vpp-signal-lists-list>
            </div>
        </div>
    </section>
</div>