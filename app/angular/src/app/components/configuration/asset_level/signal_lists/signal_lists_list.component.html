<div class="container-fluid">
    <div class="row">
        <div class="col">
            <h2>{{ 'CONFIGURATION.ASSET_LEVEL.TAB.SIGNAL_LISTS' | translate }}</h2>

            <div class="table-filter d-flex"></div>

            <table class="table table-signal-lists table-auction-results table-striped table-bg-turquoise">
                <tr>
                    <th class="name">{{ 'CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.NAME' | translate }}</th>
                    <th class="read-signals">{{ 'CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.SIGNAL_DIRECTION_READ' | translate }}</th>
                    <th class="write-signals">{{ 'CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.SIGNAL_DIRECTION_WRITE' | translate }}</th>
                    <th class="actions text-center"></th>
                </tr>

                <tr *ngIf="showLoader">
                    <td colspan="4" class="text-center vertical-center">
                        <vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
                    <td>
                </tr>

                <ng-container *ngFor="let list of signalLists">
                    <tr>
                        <input type="hidden" value="{{ list.id }}">
                        <td class="name">{{ list.name }}</td>
                        <td class="read-signals">
                            <ul>
                                <li *ngFor="let s of list.read_signals">
                                    {{ s.vpp_name }}
                                </li>
                            </ul>
                        </td>
                        <td class="write-signals">
                            <ul>
                                <li *ngFor="let s of list.write_signals">
                                    {{ s.vpp_name }}
                                </li>
                            </ul>
                        </td>
                        <td class="actions text-center">
                            <ul class="list-inline list-unstyled">
                                <li *ngIf="permissions.can_create_asset_configurations"><span (click)="editSignalList(list)" style="display: inline-block"><i class="fa fa-pencil"></i></span> </li>
                                <li *ngIf="permissions.can_create_asset_configurations"><span (click)="deleteSignalList(list)" style="display: inline-block"><i class="fa fa-trash"></i></span></li>
                            </ul>
                        </td>
                    </tr>
                </ng-container>
            </table>

        </div>
    </div>
</div>

<ng-template #edit>
    <vpp-signal-list-form
            [signalList]="editedSignalList"
            [openInEditMode]="openInEditMode"
            [embedded]="false"
            (done)="closeModalForm(true)"
            (canceled)="closeModalForm(false)">
    </vpp-signal-list-form>
</ng-template>