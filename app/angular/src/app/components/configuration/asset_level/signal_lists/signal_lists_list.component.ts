import {Component, OnInit, ViewChild, ViewEncapsulation} from "@angular/core";
import {TranslateService} from "@ngx-translate/core";
import {GlobalService} from "../../../../services/global.service";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {NotificationService} from "../../../../services/notification.service";
import {ConfirmService} from "../../../../services/confirm.service";
import {AssetConfigurationProvider} from "../../../../providers/asset_configuration.provider";
import {angularData} from "../../../../../global.export";


@Component({
    selector: 'vpp-signal-lists-list',
    templateUrl: './signal_lists_list.component.html',
    styleUrls: ['./signal_lists_list.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class SignalListsListComponent implements OnInit {
    showLoader = true;
    signalLists = [];
    params = {
        page: 1,
        per_page: 25,
    };
    page = 1;
    pageSize = 25;
    collectionSize = 0;
    editedSignalList: any;
    openInEditMode = true;
    confirmDeleteMessage = '';
    bsModalRef;
    permissions: any;

    @ViewChild('edit', { static: true }) editModal;

    constructor(
        public translate: TranslateService,
        public globalService: GlobalService,
        private _modalService: NgbModal,
        private _notificationService: NotificationService,
        private _confirmService: ConfirmService,
        private _assetConfiguration: AssetConfigurationProvider
    ) {
        this.translate.get('CONFIRM.MESSAGE.DELETE_ENTRY').subscribe((x) => {
            this.confirmDeleteMessage = x;
        });
        this.permissions = angularData.permissions;
    }

    ngOnInit(): void {
        this.getSignalLists();
    }

    getSignalLists() {
        this.showLoader = true;
        this.signalLists = [];
        this._assetConfiguration.getSignalLists(this.params).subscribe(
            res => {
                this.signalLists = res.signal_lists;
                this.collectionSize = res.total_entries;
                this.showLoader = false;
            }, err => {
                this.showLoader = false;
                console.error('Failed to load signal lists', err);
            }
        );
    }

    editSignalList(x) {
        this.editedSignalList = { ...x};
        this.openInEditMode = true;
        this.bsModalRef = this._modalService.open(this.editModal, { size: 'lg' });
    }

    closeModalForm(reload: boolean = true) {
        this.bsModalRef.close();
        if (reload) {
            this.getSignalLists();
        }
    }

    deleteSignalList(x) {
        this._confirmService.confirm({
            message: this.confirmDeleteMessage,
        }).then(() => {
            this._deleteConfirmedSignalList(x);
        }, () => {
        });
    }

    _deleteConfirmedSignalList(x) {
        this._assetConfiguration
            .deleteSignalList(x.id)
            .subscribe(
                (res) => {
                    if (res.success == true) {
                        this._notificationService.success({ text: 'SUCCESS'});
                    } else {
                        this._notificationService.error({ text: JSON.stringify(res.error)});
                    }
                    this.getSignalLists();
                },
                (err) => {
                    console.log('Failed to delete', err);
                    this._notificationService.error({ text: JSON.stringify(err)});
                });
    }

}