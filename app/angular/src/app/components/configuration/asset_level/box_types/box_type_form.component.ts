import {Component, EventEmitter, Input, OnInit, Output, SimpleChanges, ViewEncapsulation} from "@angular/core";
import {AssetConfigurationProvider} from "../../../../providers/asset_configuration.provider";
import {NotificationService} from "../../../../services/notification.service";
import {ConfirmService} from "../../../../services/confirm.service";
import {TranslateService} from "@ngx-translate/core";
import {angularData} from "../../../../../global.export";

@Component({
    selector: 'vpp-box-type-form',
    templateUrl: './box_type_form.component.html',
    styleUrls: ['./box_type_form.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class BoxTypeFormComponent implements OnInit {
    @Output('done') done: any = new EventEmitter();
    @Output('canceled') canceled: any = new EventEmitter();
    @Input('box') box: any = {
        id: null,
        box_type: '',
        box_protocol: '',
        box_port: '',
        box_vpn_type: '',
        box_contact_person_email: null,
        box_contact_person_first_name: null,
        box_contact_person_last_name: null,
        box_contact_person_phone_number: null,
        asset_type: null
    };
    @Input() openInEditMode: boolean;
    @Input() inline: boolean = false;

    types = ['direct_marketing', 'power_regulation'];
    errors: any = [];
    canSubmit: boolean = true;
    isEditing: boolean;
    confirmDeleteMessage = '';
    permissions: any;

    boxCopy = null;


    constructor(
        public translate: TranslateService,
        private _notificationService: NotificationService,
        private _assetConfiguration: AssetConfigurationProvider,
        private _confirmService: ConfirmService
    ) {
        this.translate.get('CONFIRM.MESSAGE.DELETE_ENTRY').subscribe((x) => {
            this.confirmDeleteMessage = x;
        });
        this.permissions = angularData.permissions;
    }

    ngOnInit(): void {
        this.isEditing = this.openInEditMode;
        console.log('edit box', this.box, 'in mode', this.isEditing);
    }

    ngOnChanges(changes: SimpleChanges) {
        if (changes['box']) {
            this.boxCopy = {...this.box}
        }
    }

    close(event) {
        this.done.emit();
    }

    cancel(event) {
        this.canceled.emit();
    }

    enterEditMode() {
        this.isEditing = true;
    }

    cancelEditMode() {
        this.isEditing = false;
        this.box = {...this.boxCopy}
    }

    delete() {
        this._confirmService.confirm({
            message: this.confirmDeleteMessage,
        }).then(() => {
            this._deleteConfirmedBox(this.box);
        }, () => {
        });
    }

    _deleteConfirmedBox(box) {
        this._assetConfiguration
            .deleteBoxTypeConfiguration(box.id)
            .subscribe(
                (res) => {
                    if (res.success == true) {
                        this._notificationService.success({ text: 'SUCCESS'});
                        this.done.emit();
                    } else {
                        this._notificationService.error({ text: JSON.stringify(res.error)});
                    }
                },
                (err) => {
                    console.log('Failed to delete', err);
                    this._notificationService.error({ text: JSON.stringify(err)});
                });
    }

    submit() {
        this.errors = [];


        let isNew = !this.box.id;

        let params = {
            box_type: this.box
        };

        this.canSubmit = false;
        let fn = isNew ? this._assetConfiguration.createBoxTypeConfiguration : this._assetConfiguration.updateBoxTypeConfiguration;
        fn.call(this._assetConfiguration, params).subscribe(
            (res) => {
                this.canSubmit = true;
                if (res.success == true) {
                    this._notificationService.success({ text: 'SUCCESS'});
                    this.done.emit();
                } else {
                    this.errors = res.messages;
                    this._notificationService.errors(this.errors);
                }
            },
            (err) => {
                console.log('submit failed', err);
                this.errors = [JSON.stringify(err)];
                this._notificationService.errors(this.errors);
                this.canSubmit = true;
            }
        );
    }



}