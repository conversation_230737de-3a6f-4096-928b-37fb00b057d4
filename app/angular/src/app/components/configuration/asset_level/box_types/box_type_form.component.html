<div class="row">
    <div class="col-md-8" id="full-width-in-modal">
        <div class="tab-content light-blue" style="border: none" id="enter-box-type-tab">
            <span *ngIf="box.id" class="pull-right" (click)="cancel($event)">
                <i class="fa fa-times"></i>
            </span>
            <h2 *ngIf="isEditing">{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.ENTER_BOX_TYPE' | translate }}</h2>
            <h2 *ngIf="!isEditing">{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.SHOW_BOX_TYPE' | translate }}</h2>

            <div class="alerts-wrapper-static" *ngIf="errors.length">
                <ng-container *ngFor="let errmsg of errors; let ix = index;">
                    <ngb-alert
                            [dismissible]="false"
                            [type]="'error'"> {{ errmsg }}
                    </ngb-alert>
                </ng-container>
            </div>

            <div class="form-row">
                <div class="form-group col-md-4">
                    <label>{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.BOX_TYPE' | translate }}</label>
                    <input
                            *ngIf="isEditing"
                            [(ngModel)]="box.box_type"
                            type="text"
                            class="form-control"
                            id="inputBoxType"
                    >
                    <span *ngIf="!isEditing" class="value">{{ box.box_type }}</span>
                </div>
                <div class="form-group col-md-4">
                    <label>{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.PROTOCOL' | translate }}</label>
                    <input
                            *ngIf="isEditing"
                            [(ngModel)]="box.box_protocol"
                            type="text"
                            class="form-control"
                            id="inputProtocol"
                    >
                    <span *ngIf="!isEditing" class="value">{{ box.box_protocol }}</span>
                </div>
                <div class="form-group col-md-4">
                    <label>{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.PORT' | translate }}</label>
                    <input
                            *ngIf="isEditing"
                            [(ngModel)]="box.box_port"
                            type="number"
                            step="1"
                            autocomplete="false"
                            class="form-control"
                            id="inputPort"
                    >
                    <span *ngIf="!isEditing" class="value">{{ box.box_port }}</span>
                </div>
                <div class="form-group col-md-4">
                    <label>{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.VPN_TYPE' | translate }}</label>
                    <input
                            *ngIf="isEditing"
                            [(ngModel)]="box.box_vpn_type"
                            type="text"
                            class="form-control"
                            id="inputVpnType"
                    >
                    <span *ngIf="!isEditing" class="value">{{ box.box_vpn_type }}</span>
                </div>
                <div class="form-group col-md-4">
                    <label>{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.TYPE' | translate }}</label>
                    <select
                            *ngIf="isEditing"
                            [(ngModel)]="box.asset_type"
                            id="inputType"
                            class="form-control">
                        <option
                                *ngFor="let t of types" [ngValue]="t">
                            {{ t | humanize: {titleize: true} }}
                        </option>
                    </select>
                    <span *ngIf="!isEditing" class="value">{{ box.asset_type | humanize: {titleize: true} }}</span>
                </div>
                <div class="form-group col-md-4">
                    <label>{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.EMAIL' | translate }}</label>
                    <input
                            *ngIf="isEditing"
                            [(ngModel)]="box.box_contact_person_email"
                            type="text"
                            class="form-control"
                            id="inputContactPersonEmail"
                    >
                    <span *ngIf="!isEditing" class="value">{{ box.box_contact_person_email }}</span>
                </div>
                <div class="form-group col-md-4">
                    <label>{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.FIRST_NAME' | translate }}</label>
                    <input
                            *ngIf="isEditing"
                            [(ngModel)]="box.box_contact_person_first_name"
                            type="text"
                            class="form-control"
                            id="inputContactPersonFirstName"
                    >
                    <span *ngIf="!isEditing" class="value">{{ box.box_contact_person_first_name }}</span>
                </div>
                <div class="form-group col-md-4">
                    <label>{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.LASE_NAME' | translate }}</label>
                    <input
                            *ngIf="isEditing"
                            [(ngModel)]="box.box_contact_person_last_name"
                            type="text"
                            class="form-control"
                            id="inputContactPersonLastName"
                    >
                    <span *ngIf="!isEditing" class="value">{{ box.box_contact_person_last_name }}</span>
                </div>
                <div class="form-group col-md-4">
                    <label>{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.PHONE' | translate }}</label>
                    <input
                            *ngIf="isEditing"
                            [(ngModel)]="box.box_contact_person_phone_number"
                            type="text"
                            class="form-control"
                            id="inputContactPersonPhone"
                    >
                    <span *ngIf="!isEditing" class="value">{{ box.box_contact_person_phone_number }}</span>
                </div>

            </div>

            <ng-template [ngIf]="isEditing">
                <ul class="list-inline list-unstyled d-flex form-actions">
                    <li>
                        <button
                                (click)="submit()"
                                class="eon-button bg-eon-red">
                            <span>{{ (box.id ? 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.SAVE_BOX_TYPE'
                                : 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.CREATE_BOX_TYPE') | translate }}</span>
                        </button>
                    </li>
                    <li *ngIf="isEditing && !openInEditMode">
                        <button
                                (click)="cancelEditMode()"
                                class="eon-button bg-eon-red">
                            <span>{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.CANCEL_EDIT' | translate }}</span>
                        </button>
                    </li>
                </ul>
            </ng-template>

            <ng-template [ngIf]="!isEditing && permissions.can_create_asset_configurations">
                <ul class="list-inline list-unstyled d-flex form-actions">
                    <li>
                        <button
                                (click)="enterEditMode()"
                                class="eon-button bg-eon-red">
                            <span>{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.EDIT' | translate }}</span>
                        </button>
                    </li>
                    <li>
                        <button
                                (click)="delete()"
                                class="eon-button bg-eon-red">
                            <span>{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.DELETE' | translate }}</span>
                        </button>
                    </li>
                </ul>
            </ng-template>

        </div>
    </div>
</div>