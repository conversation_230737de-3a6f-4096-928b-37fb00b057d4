<div class="container-fluid">
    <div class="row">
        <div class="col">
            <h2>{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LIST.TITLE' | translate }}</h2>

            <div class="table-filter d-flex"></div>

            <table class="table table-auction-results table-striped table-bg-turquoise">
                <tr>
                    <th class="">{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.BOX_TYPE' | translate }}</th>
                    <th class="">{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.PROTOCOL' | translate }}</th>
                    <th class="">{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.PORT' | translate }}</th>
                    <th class="">{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.VPN_TYPE' | translate }}</th>
                    <th class="">{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.EMAIL' | translate }}</th>
                    <th class="">{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.NAME' | translate }}</th>
                    <th class="">{{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.PHONE' | translate }}</th>
                    <th class="text-center"></th>
                </tr>
                <tr *ngIf="showLoader">
                    <td colspan="8" class="text-center vertical-center">
                        <vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
                    <td>
                </tr>
                <ng-container *ngFor="let b of boxTypeConfigurations">
                    <tr>
                        <input type="hidden" value="{{ b.id }}">
                        <td class="">{{ b.box_type }}</td>
                        <td class="">{{ b.box_protocol }}</td>
                        <td class="">{{ b.box_port }}</td>
                        <td class="">{{ b.box_vpn_type }}</td>
                        <td class="">{{ b.box_contact_person_email }}</td>
                        <td class="">{{ (b.box_contact_person_first_name || '') + ' ' + (b.box_contact_person_last_name || '') }}</td>
                        <td class="">{{ b.box_contact_person_pretty_phone_number }}</td>
                        <td class="text-center actions-column">
                            <ul class="list-inline list-unstyled">
                                <li *ngIf="permissions.can_see_asset_configurations"><span (click)="showBox(b)" style="display: inline-block"><i class="fa fa-eye"></i></span> </li>
                                <li *ngIf="permissions.can_create_asset_configurations"><span (click)="editBox(b)" style="display: inline-block"><i class="fa fa-pencil"></i></span> </li>
                                <li *ngIf="permissions.can_create_asset_configurations"><span (click)="deleteBox(b)" style="display: inline-block"><i class="fa fa-trash"></i></span></li>
                            </ul>
                        </td>
                    </tr>
                </ng-container>
            </table>
            <div class="d-flex justify-content-between">
                <ngb-pagination
                        [collectionSize]="collectionSize"
                        [(page)]="page"
                        [ellipses]="true"
                        [maxSize]="5"
                        (pageChange)="pageChangedCallback($event)"
                        [pageSize]="pageSize">
                </ngb-pagination>

                <select
                        class="custom-select"
                        style="width: auto"
                        [(ngModel)]="pageSize"
                        (ngModelChange)="pageSizeChangedCallback($event)">
                    <option [ngValue]="25">25 {{ 'PAGINATION.PER_PAGE' | translate }}</option>
                    <option [ngValue]="50">50 {{ 'PAGINATION.PER_PAGE' | translate }}</option>
                    <option [ngValue]="75">75 {{ 'PAGINATION.PER_PAGE' | translate }}</option>
                </select>
            </div>


        </div>
    </div>
</div>

<ng-template #edit>
    <vpp-box-type-form
            [box]="editedBox"
            [openInEditMode]="openInEditMode"
            (done)="closeBoxModal(true)"
            (canceled)="closeBoxModal(false)">
    </vpp-box-type-form>
</ng-template>