<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="page-actions">
                <ul class="list-unstyled d-flex">
                    <li *ngIf="permissions.can_create_asset_configurations">
                        <a href="javascript:void(0)"
                           (click)="toggleSection($event, 'enter-box-type')"
                           class="eon-button bg-eon-turquoise">
                            {{ 'CONFIGURATION.ASSET_LEVEL.BOX_TYPES.ENTER_BOX_TYPE' | translate }}
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <div class="overlay-on-top-of-list"
         [@slideRightContentAnimation]="selectedSelection == 'enter-box-type' ? 'in' : 'out'">
        <vpp-box-type-form
                class="embedded"
                *ngIf="selectedSelection == 'enter-box-type'"
                [openInEditMode]="true"
                (done)="reloadList()"
                (canceled)="reloadList()">

        </vpp-box-type-form>
    </div>

    <section>
        <div class="row">
            <div class="col-md-12">
                <vpp-box-types-list *ngIf="loadList"></vpp-box-types-list>
            </div>
        </div>
    </section>
</div>