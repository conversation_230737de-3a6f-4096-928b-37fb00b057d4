import {Component, OnInit, ViewChild, ViewEncapsulation} from "@angular/core";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {NotificationService} from "../../../../services/notification.service";
import {TranslateService} from "@ngx-translate/core";
import {GlobalService} from "../../../../services/global.service";
import {ConfirmService} from "../../../../services/confirm.service";
import {AssetConfigurationProvider} from "../../../../providers/asset_configuration.provider";
import {angularData} from "../../../../../global.export";

@Component({
    selector: 'vpp-box-types-list',
    templateUrl: './box_types_list.component.html',
    styleUrls: ['./box_types_list.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class BoxTypesListComponent implements OnInit {
    showLoader = true;
    boxTypeConfigurations = [];
    params = {
        page: 1,
        per_page: 25,
    };
    page = 1;
    pageSize = 25;
    collectionSize = 0;
    editedBox: any;
    openInEditMode = true;
    confirmDeleteMessage = '';
    bsModalRef;
    permissions: any;

    @ViewChild('edit', { static: true }) editModal;


    constructor(
        public translate: TranslateService,
        public globalService: GlobalService,
        private _modalService: NgbModal,
        private _notificationService: NotificationService,
        private _confirmService: ConfirmService,
        private _assetConfiguration: AssetConfigurationProvider
    ) {
        this.translate.get('CONFIRM.MESSAGE.DELETE_ENTRY').subscribe((x) => {
            this.confirmDeleteMessage = x;
        });
        this.permissions = angularData.permissions;
    }

    ngOnInit(): void {
        this.getBoxTypeConfigurations();
    }

    getBoxTypeConfigurations() {
        this.showLoader = true;
        this.boxTypeConfigurations = [];
        this._assetConfiguration.getBoxTypeConfigurations(this.params).subscribe(
            res => {
                this.boxTypeConfigurations = res.boxes;
                this.collectionSize = res.total_entries;
                this.showLoader = false;
            }, err => {
                this.showLoader = false;
                console.log('Failed to load box type configurations', err);
            }
        );
    }

    pageChangedCallback(page) {
        this.params.page = page;
        this.getBoxTypeConfigurations();
    }

    pageSizeChangedCallback(pageSize) {
        this.params.per_page = pageSize;
        this.params.page = 1;
        this.getBoxTypeConfigurations();
    }

    showBox(box) {
        this.editedBox = { ...box};
        this.openInEditMode = false;
        this.bsModalRef = this._modalService.open(this.editModal, { size: 'lg' });
    }

    editBox(box) {
        this.editedBox = { ...box};
        this.openInEditMode = true;
        this.bsModalRef = this._modalService.open(this.editModal, { size: 'lg' });
    }

    closeBoxModal(reload: boolean = true) {
        this.bsModalRef.close();
        if (reload) {
            this.getBoxTypeConfigurations();
        }
    }

    deleteBox(box) {
        this._confirmService.confirm({
            message: this.confirmDeleteMessage,
        }).then(() => {
            this._deleteConfirmedBox(box);
        }, () => {
        });
    }

    _deleteConfirmedBox(box) {
        this._assetConfiguration
            .deleteBoxTypeConfiguration(box.id)
            .subscribe(
                (res) => {
                    if (res.success == true) {
                        this._notificationService.success({ text: 'SUCCESS'});
                    } else {
                        this._notificationService.error({ text: JSON.stringify(res.error)});
                    }
                    this.getBoxTypeConfigurations();
                },
                (err) => {
                    console.log('Failed to delete', err);
                    this._notificationService.error({ text: JSON.stringify(err)});
                });
    }

}