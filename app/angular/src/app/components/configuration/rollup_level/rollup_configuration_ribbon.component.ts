import {
    Component,
    OnInit,
    ViewEncapsulation
} from "@angular/core";
import { GlobalService } from "../../../services/global.service";
import { SlideRightContentAnimation } from '../../../animations/slide-right-content';
import { TranslateService } from '@ngx-translate/core';
import {ActivatedRoute, Params} from "@angular/router";
import {environment} from "../../../../environments/environment";
import {Location} from "@angular/common";
import {angularData} from "../../../../global.export";

@Component({
    selector: "vpp-management-rollup-configuration-ribbon",
    templateUrl: "./rollup_configuration_ribbon.component.html",
    styleUrls: ["./rollup_configuration_ribbon.component.scss"],
    encapsulation: ViewEncapsulation.None,
    animations: [ SlideRightContentAnimation ],
})
export class RollupConfigurationRibbonComponent implements OnInit {
    selectedTab: string = 'asset-rollups';
    permittedTabs = { assetRollups: false, dispatchGroupRollups: false };

    constructor(
        public translate: TranslateService,
        private _globalService: GlobalService,
        private _route: ActivatedRoute,
        private _location: Location
    ) {
        this._globalService.changeTitle('Configuration - Rollups');
        //this.translate.get('PAGE.CAPTIONS.CONFIGURATION.ASSET_LEVEL').subscribe((res: string) => {
        //    this._globalService.changeTitle(res);
        //});
        let permissions = angularData.permissions;
        this.permittedTabs = {
            assetRollups: permissions.can_see_rollups || permissions.can_create_rollups,
            dispatchGroupRollups: permissions.can_see_rollups || permissions.can_create_rollups
        }
    }

    ngOnInit() {
    }

    ngAfterViewInit() {
        this._route.params.forEach((params: Params) => {
            if (params['tab']) {
                this.selectTab(null, params['tab']);
            } else {
                this.selectTab(null, this.selectedTab);
            }
        });
    }

    selectTab(event, tab) {
        this.selectedTab = tab;
        this._location.replaceState(`${environment.routingPath}/rollup_configuration_ribbon/${tab}`);
    }

}
