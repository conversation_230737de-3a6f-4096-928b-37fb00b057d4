import {Component, Input, OnChanges, OnInit, SimpleChanges, ViewChild, ViewEncapsulation} from "@angular/core";
import {angularData} from "../../../../../global.export";
import {AssetProvider} from "../../../../providers/asset.provider";
import {GlobalService} from "../../../../services/global.service";
import {Moment} from 'moment';
import * as moment from "moment-timezone";
import {NotificationService} from "../../../../services/notification.service";
import {RollupConfigProvider} from "../../../../providers/rollup_config.provider";


@Component({
    selector: 'vpp-asset-rollup-regeneration',
    templateUrl: './asset_rollup_regeneration.component.html',
    styleUrls: ['./asset_rollup_regeneration.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class AssetRollupRegenerationComponent implements OnInit, OnChanges {

    canSubmit = false;
    errors = [];

    @Input() rollupTypes: any;
    selectedRollupTypes: any = [];
    rollupTypesDropdownSettings = {
        singleSelection: false,
        idField: 'id',
        textField: 'name',
        itemsShowLimit: 10,
        allowSearchFilter: true,
        selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
        unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
        searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
        noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
    };

    selectedTargetType: any;
    targetTypes = [
        {val: 'Asset', label: 'Asset'},
        {val: 'RollupFamily', label: 'Rollup Grouping'}
    ];
    targetTypeDropdownSettings = {
        singleSelection: true,
        idField: 'val',
        textField: 'label',
        allowSearchFilter: false,
        selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
        unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
        searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
        noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
    };

    selectedAssets: any = [];
    assets: any = [];
    assetsDropdownSettings = {
        singleSelection: false,
        idField: 'id',
        textField: 'ps_name',
        allowSearchFilter: true,
        itemsShowLimit: 10,
        selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
        unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
        searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
        noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar'
    };

    selectedRollupFamilies: any = [];
    @Input() rollupFamilies: any;
    rollupFamiliesDropdownSettings = {
        singleSelection: false,
        idField: 'id',
        textField: 'name',
        allowSearchFilter: true,
        selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
        unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
        searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
        noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
    };

    selectedInterval: Moment[] = [];
    @ViewChild('dt1', {static: false}) dt1;


    constructor(
        private _assetProvider: AssetProvider,
        public globalService: GlobalService,
        private _notificationService: NotificationService,
        private _rollupConfigProvider: RollupConfigProvider
    ) {
        this.selectedTargetType = [this.targetTypes[0]];
    }

    ngOnInit() {
        this.getAssets();
        this.selectedInterval = [moment().startOf('day'), moment().endOf('day')];
    }

    ngOnChanges(changes: SimpleChanges) {
    }

    getAssetDisplayName(a) {
        return `#${a.id} ${a.name} (${a.customer_name})`;
    }

    mapAssetsAddingDisplayName(dgs) {
        return dgs.map((x) => {
            x['ps_name'] = this.getAssetDisplayName(x);
            return x;
        });
    }

    getAssets() {
        const noData = angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar';
        const loading = angularData.railsExports.locale == 'en-GB' ? 'Loading...' : 'Lädt...';
        this.assetsDropdownSettings.noDataAvailablePlaceholderText = loading;
        this._assetProvider.findAllWithPagination().subscribe(
            result => {
                this.assets = result.assets;

                this.assets = this.mapAssetsAddingDisplayName(this.assets);

                this.assetsDropdownSettings.noDataAvailablePlaceholderText = noData;
            }, err => {
                console.error('Failed to load asset list', err);
                this.assetsDropdownSettings.noDataAvailablePlaceholderText = noData;
            });
    }

    onSelectedRollupTypesChanged() {
        this.updateControls();
    }

    onSelectedTargetTypeChanged() {
        this.updateControls();
    }

    onSelectedAssetsChanged() {
        this.updateControls();
    }

    onSelectedRollupRollupFamiliesChanged() {
        this.updateControls();
    }

    onSelectedIntervalChanged(event: any) {
        if (!event.value[0] || !event.value[1]) {
            this.selectedInterval = [null, null];
        }
        this.updateControls();
    }

    updateControls() {
        this.canSubmit =
            this.selectedRollupTypes && this.selectedRollupTypes.length > 0 &&
            this.selectedTargetType && this.selectedTargetType.length > 0 &&
            (this.selectedTargetType[0].val == this.targetTypes[0].val
                ? this.selectedAssets && this.selectedAssets.length > 0
                : this.selectedRollupFamilies && this.selectedRollupFamilies.length > 0) &&
            this.selectedInterval &&
            this.selectedInterval.length == 2 &&
            !!this.selectedInterval[0] &&
            !!this.selectedInterval[1];
    }

    maxRangeFilter(date) {
        // todo range limit 1 month
        //console.log('max range filter', date, 'for selection', this.selectedInterval);
        return true;
    }

    submit() {
        this.errors = [];

        let param: any = {
            rollup_type_ids: this.selectedRollupTypes.map(x => x.id),
            from_time: this.selectedInterval[0].startOf('minute').toISOString(),
            to_time: this.selectedInterval[1].endOf('minute').toISOString(),
            item_type: this.selectedTargetType[0].val,

        };
        if (this.selectedTargetType[0].val == this.targetTypes[0].val) {
            param.asset_ids = this.selectedAssets.map(x => x.id);
        } else {
            param.rollup_family_ids = this.selectedRollupFamilies.map(x => x.id);
        }

        console.log('regenerate', param);

        this.canSubmit = false;

        this._rollupConfigProvider.generateRollup(param).subscribe(
            res => {
                this.canSubmit = true;
                if (res.success == true) {
                    this._notificationService.success({text: 'SUCCESS'});
                } else {
                    this.errors = [res.error];
                    this._notificationService.errors(this.errors);
                }
            },
            err => {
                this.canSubmit = true;
                console.error('submit failed', err);

                this.errors = [JSON.stringify(err)];
                this._notificationService.errors(this.errors);
                this.canSubmit = true;
            }
        );
    }
}