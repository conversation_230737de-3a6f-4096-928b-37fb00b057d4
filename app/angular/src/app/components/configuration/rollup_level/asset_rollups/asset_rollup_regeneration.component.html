<div class="alerts-wrapper-static" *ngIf="errors.length">
    <ng-container *ngFor="let errmsg of errors; let ix = index;">
        <div class="row">
            <div class="col-md-12">
                <ngb-alert
                        [dismissible]="false"
                        [type]="'error'"> {{ errmsg }}
                </ngb-alert>
            </div>
        </div>
    </ng-container>
</div>

<div class="row">
    <div class="form-group col-md-3">
        <label>{{'CONFIGURATION.ASSET_ROLLUPS.SELECT_ROLLUP_TYPE'|translate}}</label>
        <ng-multiselect-dropdown
                [placeholder]="('COMPONENTS.SELECT'|translate)"
                [data]="rollupTypes"
                [(ngModel)]="selectedRollupTypes"
                (ngModelChange)="onSelectedRollupTypesChanged()"
                [settings]="rollupTypesDropdownSettings"
        >
        </ng-multiselect-dropdown>
    </div>

    <div class="form-group col-md-2">
        <label>{{'CONFIGURATION.ASSET_ROLLUPS.SELECT_ITEM_TYPE'|translate}}</label>
        <ng-multiselect-dropdown
                [placeholder]="('COMPONENTS.SELECT'|translate)"
                [data]="targetTypes"
                [(ngModel)]="selectedTargetType"
                (ngModelChange)="onSelectedTargetTypeChanged()"
                [settings]="targetTypeDropdownSettings"
        >
        </ng-multiselect-dropdown>
    </div>

    <div class="form-group col-md-3">
        <ng-template
                [ngIf]="(selectedTargetType.length == 0 || selectedTargetType[0].val == 'Asset')"
                [ngIfElse]="familySelector">
            <label>{{'CONFIGURATION.ASSET_ROLLUPS.SELECT_ASSET'|translate}}</label>
            <ng-multiselect-dropdown
                    [placeholder]="('COMPONENTS.SELECT'|translate)"
                    [data]="assets"
                    [(ngModel)]="selectedAssets"
                    (ngModelChange)="onSelectedAssetsChanged()"
                    [settings]="assetsDropdownSettings"
            >
            </ng-multiselect-dropdown>
        </ng-template>
        <ng-template #familySelector>
            <label>{{'CONFIGURATION.ASSET_ROLLUPS.SELECT_ROLLUP_GROUP'|translate}}</label>
            <ng-multiselect-dropdown
                    [placeholder]="('COMPONENTS.SELECT'|translate)"
                    [data]="rollupFamilies"
                    [(ngModel)]="selectedRollupFamilies"
                    (ngModelChange)="onSelectedRollupRollupFamiliesChanged()"
                    [settings]="rollupFamiliesDropdownSettings"
            >
            </ng-multiselect-dropdown>
        </ng-template>
    </div>

    <div class="form-group col-md-4">
        <label>{{'CONFIGURATION.ASSET_ROLLUPS.SELECT_INTERVAL'|translate}}</label>
        <input
                type="text"
                class="form-control"
                [owlDateTime]="dt1"
                [selectMode]="'range'"
                [(ngModel)]="selectedInterval"
                [owlDateTimeTrigger]="dt1"
                (dateTimeChange)="onSelectedIntervalChanged($event)"
                [owlDateTimeFilter]="maxRangeFilter"
                readonly="true"
        > <!-- todo limit range to 1 month -->
        <owl-date-time
            #dt1
            (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
            [pickerMode]="'popup'"></owl-date-time>
    </div>

    <div class="col-md-10"><!-- empty space --></div>
    <div class="form-group col-md-2">
        <button class="eon-button bg-eon-red pull-right"
                [disabled]="!canSubmit"
                (click)="submit()"
        >
            <span>{{'CONFIGURATION.ASSET_ROLLUPS.BTN_REGENERATE'|translate}}</span>
        </button>
    </div>
</div>
