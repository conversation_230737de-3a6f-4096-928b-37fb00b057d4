import {Component, ViewEncapsulation} from "@angular/core";
import {IHeaderAngularComp} from "ag-grid-angular";
import {IHeaderParams} from "ag-grid-community";


@Component({
    selector: 'rollup-family-header',
    templateUrl: './rollup_family_header.component.html',
    styleUrls: ['./rollup_family_header.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class RollupFamilyHeaderComponent implements IHeaderAngularComp {

    private params: any;

    agInit(params: IHeaderParams) {
        console.log('my header init', params);

        this.params = params;
    }
}