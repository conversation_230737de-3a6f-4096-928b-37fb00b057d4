<div class="container-fluid" *ngIf="permissions.can_see_rollups">

    <div class="row" *ngIf="permissions.can_create_rollups">
        <div class="col-md-8">
            <div class="page-actions">
                <ul class="list-unstyled d-flex">
                    <li>
                        <a href="javascript:void(0)"
                           class="eon-button bg-eon-turquoise"
                           (click)="editRollupFamilies()">
                            {{'CONFIGURATION.ASSET_ROLLUPS.EDIT_ROLLUP_FAMILIES'|translate}}
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>


    <ng-template [ngIf]="permissions.can_create_rollups">
        <h2>{{'CONFIGURATION.ASSET_ROLLUPS.TITLE_ROLLUP_REGENERATION'|translate}}</h2>
        <vpp-asset-rollup-regeneration
                [rollupTypes]="rollupTypes"
                [rollupFamilies]="rollupFamilies"
        >
        </vpp-asset-rollup-regeneration>
    </ng-template>


    <h2>{{'CONFIGURATION.ASSET_ROLLUPS.TITLE_ROLLUP_MANAGEMENT'|translate}}</h2>
    <div class="row">
        <div class="col-md-12">
            <ag-grid-angular
                    xstylex="width: 500px; height: 500px;"
                    class="ag-theme-alpine"
                    [rowData]="rowData"
                    [columnDefs]="columnDefs"
                    [gridOptions]="gridOptions"
                    (gridReady)="onGridReady($event)"
                    [domLayout]="'autoHeight'"
                    [frameworkComponents]="frameworkComponents"
            >
            </ag-grid-angular>
        </div>
    </div>

</div>

<ng-template #edit>
    <vpp-rollup-families-form
            [rollupFamilies]="rollupFamilies"
            (done)="closeRollupFamiliesModal(true)"
            (canceled)="closeRollupFamiliesModal(false)"
    >
    </vpp-rollup-families-form>
</ng-template>