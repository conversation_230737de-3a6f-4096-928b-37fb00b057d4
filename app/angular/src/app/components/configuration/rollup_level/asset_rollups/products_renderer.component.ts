import {Component, ViewEncapsulation} from "@angular/core";
import {ICellRendererAngularComp} from "ag-grid-angular";
import {ICellRendererParams} from "ag-grid-community";

@Component({
    selector: 'products-renderer-component',
    templateUrl: './products_renderer.component.html',
    styleUrls: ['./products_renderer.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class ProductsRendererComponent implements ICellRendererAngularComp {

    private params: any;
    private products;

    productNames;

    agInit(params: ICellRendererParams) {
        // console.log('init product renderer', params);

        this.setParams(params);
    }

    refresh(params: any): boolean {
        this.setParams(params);

        return true;
    }

    private setParams(params) {
        this.params = params;

        //this.products = params.context.products;
        this.products = params.products;

        // console.log('render products', this.params.value, 'of', this.products, 'from params', params);

        let selectedProducts = this.params.value.map(x => {
            return this.products.find(p => p.id == x);
        });
        this.productNames = selectedProducts.map(p => p.name).join(',');
        // if (this.productNames.length > 0) {
        //     console.log('productNames', this.productNames);
        // }
    }

}