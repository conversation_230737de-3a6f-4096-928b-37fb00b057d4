<div class="row">
    <div class="col-md-8" id="full-width-in-modal">
        <div class="tab-content light-blue" style="border: none" id="enter-rollup-families-tab">
            <span class="pull-right" (click)="cancel($event)">
                <i class="fa fa-times"></i>
            </span>

            <h2>{{'CONFIGURATION.ASSET_ROLLUPS.EDIT_ROLLUP_FAMILIES'|translate}}</h2>

            <div class="alerts-wrapper-static" *ngIf="errors.length">
                <ng-container *ngFor="let errmsg of errors; let ix = index;">
                    <ngb-alert
                            [dismissible]="false"
                            [type]="'error'"> {{ errmsg }}
                    </ngb-alert>
                </ng-container>
            </div>

            <div class="row" *ngFor="let rf of rollupFamilies; let last = last">
                <div class="form-group col-md-7" *ngIf="!rf.deleted">
                    <input
                            *ngIf="!last"
                            class="form-control"
                            type="text"
                            [(ngModel)]="rf.name">
                    <input
                            *ngIf="last"
                            #lastNameInput
                            class="form-control"
                            [(ngModel)]="rf.name">
                </div>
                <div class="form-group" *ngIf="!rf.deleted">
                    <button (click)="onDeleteEntry(rf)"
                            class="btn btn-link form-control"
                            style="border: none; padding: 0">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
            </div>

            <div class="row">
                <div class="col-md-3 form-group">
                    <button (click)="onNewEntry($event)"
                            class="btn btn-link form-control"
                            style="border: none; padding: 0">
                        <span>
                            <i class="fa fa-plus"></i>
                            {{'CONFIGURATION.ASSET_ROLLUPS.ADD'|translate}}
                        </span>
                    </button>
                </div>
            </div>

            <!-- Action button -->
            <ul class="list-inline list-unstyled d-flex form-actions">
                <li>
                    <button
                            (click)="submit()"
                            class="eon-button bg-eon-red">
                        <span>{{'CONFIGURATION.ASSET_ROLLUPS.SAVE'|translate}}</span>
                    </button>
                </li>
            </ul>


        </div>
    </div>
</div>