import {
    Component,
    OnInit,
    ViewEncapsulation,
    ViewChild,
    ElementRef
} from "@angular/core";
import {
    Router,
    ActivatedRoute,
    Para<PERSON>,
    PRIMARY_OUTLET
} from "@angular/router";
import {Location} from "@angular/common";
import {GlobalService} from "../../../../services/global.service";
import {SlideRightContentAnimation} from "../../../../animations/slide-right-content";
import {environment} from "../../../../../environments/environment";
import {angularData} from "../../../../../global.export";
import {TranslateService} from '@ngx-translate/core';
import {RollupConfigProvider} from "../../../../providers/rollup_config.provider";
import {NotificationService} from "../../../../services/notification.service";
import {GridOptions, RefreshCellsParams} from "ag-grid-community";
import {ProductsRendererComponent} from "./products_renderer.component";
import {ProductsEditorComponent} from "./products_editor.component";
import {RollupFamilyHeaderComponent} from "./rollup_family_header.component";
import {NgbModal, NgbModalOptions} from "@ng-bootstrap/ng-bootstrap";
import {RollupConfigGridCellHelper} from "../rollup_config_grid_cell_helper";


@Component({
    selector: "vpp-asset-rollups-configuration",
    templateUrl: "./asset_rollups_configuration.component.html",
    styleUrls: ["./asset_rollups_configuration.component.scss"],
    encapsulation: ViewEncapsulation.None,
    animations: [SlideRightContentAnimation],
})
export class AssetRollupsConfigurationComponent implements OnInit {
    selectedSelection = "";
    loadList: boolean = true;
    permissions: any;
    rollupsData: any;
    products: any;
    rollupFamilies: any;
    rollupTypes: any;
    columnDefs = [];
    rowData = [];
    gridOptions: GridOptions;
    grid: any;
    frameworkComponents;

    @ViewChild('edit', {static: true}) editModal;
    bsModalRef;

    constructor(
        public translate: TranslateService,
        private _global: GlobalService,
        private _route: ActivatedRoute,
        private _location: Location,
        public rollupConfigProvider: RollupConfigProvider,
        public notificationService: NotificationService,
        private _modalService: NgbModal,
    ) {
        this.frameworkComponents = {
            productsRendererComponent: ProductsRendererComponent,
            productsEditorComponent: ProductsEditorComponent,
            rollupFamilyHeaderComponent: RollupFamilyHeaderComponent
        };
        this.gridOptions = <GridOptions>{
            context: this
        };
        this.permissions = angularData.permissions;
        this.translate.get('PAGE.CAPTIONS.CONFIGURATION.ROLLUPS_LEVEL.ASSET_ROLLUPS').subscribe((res: string) => {
            this._global.changeTitle(res);
        });
    }

    ngOnInit() {
        this.loadRollupConfig();
    }

    ngAfterViewInit() {
    }

    toggleSection(event, section) {
        if (event) {
            //event.preventDefault();
            //event.stopPropagation();
        }

        if (this.selectedSelection == section) {
            this.selectedSelection = '';
        } else {
            this.selectedSelection = section;
        }
    }

    reloadList() {
        this.toggleSection(null, '');
        // reload the list in the next refresh cycle
        this.loadList = false;
        setTimeout(() => {
            this.loadList = true;
        });
    }

    editRollupFamilies() {
        if (this.rollupsData && this.rollupsData.rollup_families) {
            this.rollupFamilies = JSON.parse(JSON.stringify(this.rollupsData.rollup_families));

            let options = <NgbModalOptions>{
                //size: 'sm'
            };
            this.bsModalRef = this._modalService.open(this.editModal, options);
        }
    }

    closeRollupFamiliesModal(reload: boolean = true) {
        this.bsModalRef.close();
        if (reload) {
            this.loadRollupConfig();
        }
    }

    onProductsCellValueChanged(event) {
        console.log('products cell value changed', event);

        let dis = event.context;

        let params = {
            val: event.newValue,
            rollup_type_code: event.data.code
        }

        let cellHelper = new RollupConfigGridCellHelper(event);

        cellHelper.toggleError(false);

        dis.rollupConfigProvider.saveAssetRollupProducts(params).subscribe(
            res => {

                if (res.success) {
                    if (res.val) {
                        cellHelper.setCellValue(res.val);
                    }
                } else {
                    dis.notificationService.error({text: res.error});

                    cellHelper.toggleError(true);
                }
            },
            err => {
                console.error('failed to save cell data', err);
                dis.notificationService.error(err);
                cellHelper.toggleError(true);
            });
    }

    onIntervalCellValueChanged(event) {
        let dis = event.context;

        let cellHelper = new RollupConfigGridCellHelper(event);


        // if (event.newValue !== event.oldValue) {
        let params = {
            val: event.newValue,
            rollup_type_code: event.data.code,
            rollup_family_id: event.colDef.field,
            rollup_family_name: event.colDef.headerName
        };

        cellHelper.toggleError(false);

        dis.rollupConfigProvider.saveAssetRollupInterval(params).subscribe(
            res => {
                if (res.success) {
                    console.log('saved cell data', res);

                    cellHelper.setCellValue(res.val);
                } else {
                    console.log('cell data rejected', res);

                    dis.notificationService.error({text: res.error});
                    cellHelper.toggleError(true);
                }
            },
            err => {
                console.error('failed to save cell data', err);

                dis.notificationService.error(err);
                cellHelper.toggleError(true);
            });
        // } else {
        //     console.log('data was unchanged');
        // }
    }

    onGridReady(grid) {
        console.log('grid is ready');

        this.grid = grid;
        this.sizeToFit();
    }

    sizeToFit() {
        let grid = this.grid;
        if (grid) {
            grid.api.sizeColumnsToFit();
            grid.api.resetRowHeights();
        }
    }

    loadRollupConfig() {
        this.rollupConfigProvider.getAssetRollupConfig().subscribe(
            res => {
                console.log('Got rollups config', res);

                this.rollupsData = res;
                this.products = this.rollupsData.products;
                this.rollupFamilies = this.rollupsData.rollup_families;
                this.rollupTypes = this.rollupsData.rollup_row_data.map(x => {return {name: x.name, code: x.code, id: x.rollup_type_id};})

                let cellClassRules = RollupConfigGridCellHelper.cellClassRules;

                this.columnDefs = [
                    {
                        headerName: this.translate.instant('CONFIGURATION.ASSET_ROLLUPS.ROLLUP_METRIC'),
                        headerTooltip: this.translate.instant('CONFIGURATION.ASSET_ROLLUPS.ROLLUP_METRIC'),
                        field: 'name',
                        cellStyle: {
                            'white-space': 'normal',
                            'width': '150px',
                            'overflow-wrap': 'break-word',
                            'line-height': 'normal',
                            'vertical-align': 'middle',
                            'display': 'flex',
                            'align-items': 'center'
                        },
                        autoHeight: true
                    },
                    {
                        headerName: this.translate.instant('CONFIGURATION.ASSET_ROLLUPS.PRODUCT'),
                        headerTooltip: this.translate.instant('CONFIGURATION.ASSET_ROLLUPS.PRODUCT'),
                        field: 'products',
                        cellRenderer: 'productsRendererComponent',
                        cellRendererParams: {
                            myCellRendererParam: 'myParam',
                            products: this.products
                        },
                        cellStyle: {"overflow": 'visible'},
                        editable: !!this.permissions.can_create_rollups,
                        cellEditor: 'productsEditorComponent',
                        cellEditorParams: {
                            myCellEditorParam: 'myParam',
                            products: this.products
                        },
                        onCellValueChanged: this.onProductsCellValueChanged,
                        cellClassRules: cellClassRules
                    }
                ];

                this.columnDefs = this.columnDefs.concat(res.rollup_families.map(x => {
                    return {
                        //headerComponent: 'rollupFamilyHeaderComponent',
                        //headerComponentParams: { raulsHeaderParam: 'raulsHeaderParamVal' },
                        headerName: x.name,
                        headerTooltip: x.name,
                        field: '' + x.id,
                        editable: !!this.permissions.can_create_rollups,
                        onCellValueChanged: this.onIntervalCellValueChanged,
                        cellClass: 'grid-cell-centered',
                        // cellClassRules: {
                        //     'grid-cell-invalid': "!!data['err'][''+colDef.field]",
                        //     'grid-cell-valid': "!data['err'][''+colDef.field]"
                        // }
                        cellClassRules: cellClassRules
                    };
                }));

                this.rowData = res.rollup_row_data.map(x => {
                    x['err'] = {};
                    return x;
                });

                setTimeout(() => { this.sizeToFit(); }, 50);
            },
            err => {
                console.log('Failed to load rollups config', err);
            }
        )
    }

}
