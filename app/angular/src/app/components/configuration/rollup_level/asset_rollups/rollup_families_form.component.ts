import {Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild, ViewEncapsulation} from "@angular/core";
import {NotificationService} from "../../../../services/notification.service";
import {ConfirmService} from "../../../../services/confirm.service";
import {GlobalService} from "../../../../services/global.service";
import {RollupConfigProvider} from "../../../../providers/rollup_config.provider";


@Component({
    selector: 'vpp-rollup-families-form',
    templateUrl: './rollup_families_form.component.html',
    styleUrls: ['./rollup_families_form.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class RollupFamiliesFormComponent implements OnInit {

    @Output('done') done: any = new EventEmitter();
    @Output('canceled') canceled: any = new EventEmitter();

    @Input() rollupFamilies: Array<any> = [];

    @ViewChild('lastNameInput', {read: ElementRef, static: false}) lastNameInputElement: ElementRef;

    errors: any = [];

    newEntryTemplate = {
        id: null,
        name: ''
    };
    newEntry = {...this.newEntryTemplate};

    canSubmit: boolean = true;
    changed = false;


    constructor(
        private _notificationService: NotificationService,
        private _confirmService: ConfirmService,
        public globalService: GlobalService,
        private _rollupConfigProvider: RollupConfigProvider,
    ) {
    }

    ngOnInit() {
        console.log('edit rollup families', this.rollupFamilies);
    }

    close(event) {
        this.done.emit();
    }

    cancel(event) {
        if (this.changed) {
            this.done.emit();
        } else {
            this.canceled.emit();
        }
    }

    onNewEntry(event) {
        this.rollupFamilies.push(this.newEntry);
        this.newEntry = {...this.newEntryTemplate};

        setTimeout(() => {
            let el = this.lastNameInputElement.nativeElement;
            el.focus();
        });
    }

    onDeleteEntry(x) {
        //this.rollupFamilies = this.rollupFamilies.filter(i => i != x);
        x.deleted = true;
    }

    submit() {
        console.log('submit', this.rollupFamilies);

        this.errors = [];

        let params = {
            changed_families: this.rollupFamilies.filter(x => x.id),
            deleted_families: this.rollupFamilies.filter(x => x.id && x.deleted),
            new_families: this.rollupFamilies.filter(x => !x.id && x.name && x.name.length > 0)
        }

        console.log('submit', params);

        this.canSubmit = false;
        this.changed = true;
        this._rollupConfigProvider.updateAssetRollupFamilies(params).subscribe(
            res => {
                this.canSubmit = true;
                if (res.success == true) {
                    this._notificationService.success({text: 'SUCCESS'});
                    this.done.emit();
                } else {
                    this.errors = res.messages;
                    this._notificationService.errors(this.errors);
                }
                this.rollupFamilies = res.rollup_families;
            },
            err => {
                this.canSubmit = true;
                console.log('submit failed', err);
                this.errors = [JSON.stringify(err)];
                this._notificationService.errors(this.errors);
            });
    }
}