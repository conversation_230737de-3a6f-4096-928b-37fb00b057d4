import {ICellEditorAngularComp} from "ag-grid-angular";
import {AfterViewInit, Component, ViewChild, ViewContainerRef, ViewEncapsulation} from "@angular/core";
import {ICellEditorParams} from "ag-grid-community";


@Component({
    selector: 'products-editor-component',
    templateUrl: './products_editor.component.html',
    styleUrls: ['./products_editor.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class ProductsEditorComponent implements ICellEditorAngularComp, AfterViewInit {

    private params: any;
    private selectedProductIds; any;
    products: any;

    selectedProducts: any;

    // @ViewChild('container', { read: ViewContainerRef }) public container;

    dropdownSettings = {
        singleSelection: false,
        idField: 'id',
        textField: 'name',
        itemsShowLimit: 10,
        allowSearchFilter: true,
        // todo i18n
        defaultOpen: true
    };


    ngAfterViewInit() {
        // window.setTimeout(() => {
        //     this.container.element.nativeElement.focus();
        // });
    }

    agInit(params: ICellEditorParams): void {
        console.log('init products editor', params);

        this.setParams(params);
    }

    private setParams(params) {
        this.params = params;
        this.selectedProductIds = params.value;

        //this.products = params.context.products;
        this.products = params.products;

        console.log('set editor params', params);

        this.selectedProducts = this.params.value.map(x => {
            return this.products.find(p => p.id == x);
        });

    }

    isPopup(): boolean {
        return true;
    }

    getValue(): any {
        return this.selectedProducts.map(x => x.id);
    }


}