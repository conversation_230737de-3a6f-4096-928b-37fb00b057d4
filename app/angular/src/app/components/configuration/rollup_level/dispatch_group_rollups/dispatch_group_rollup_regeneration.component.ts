import {Component, Input, OnChanges, OnInit, SimpleChanges, ViewChild, ViewEncapsulation} from "@angular/core";
import {angularData} from "../../../../../global.export";
import {Moment} from "moment";
import {AssetProvider} from "../../../../providers/asset.provider";
import {GlobalService} from "../../../../services/global.service";
import {NotificationService} from "../../../../services/notification.service";
import {RollupConfigProvider} from "../../../../providers/rollup_config.provider";
import * as moment from "moment-timezone";
import {DispatchGroupProvider} from "../../../../providers/dispatch_group.provider";


@Component({
    selector: 'vpp-dispatch-group-rollup-regeneration',
    templateUrl: './dispatch_group_rollup_regeneration.component.html',
    styleUrls: ['./dispatch_group_rollup_regeneration.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class DispatchGroupRollupRegenerationComponent implements OnInit, OnChanges {
    canSubmit = false;
    errors = [];

    @Input() rollupTypes: any;
    selectedRollupTypes: any = [];
    rollupTypesDropdownSettings = {
        singleSelection: false,
        idField: 'id',
        textField: 'name',
        itemsShowLimit: 10,
        allowSearchFilter: true,
        selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
        unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
        searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
        noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
    };

    selectedDispatchGroups: any = [];
    @Input() dispatchGroups: any = [];
    dispatchGroupsDropdownSettings = {
        singleSelection: false,
        idField: 'id',
        // textField: 'ps_name',
        textField: 'label',
        allowSearchFilter: true,
        itemsShowLimit: 10,
        selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
        unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
        searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
        noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar'
    };

    selectedInterval: Moment[] = [];
    @ViewChild('dt1', {static: false}) dt1;


    constructor(
        public globalService: GlobalService,
        private _notificationService: NotificationService,
        private _rollupConfigProvider: RollupConfigProvider
    ) {
    }

    ngOnInit() {
        this.selectedInterval = [moment().startOf('day'), moment().endOf('day')];
    }

    ngOnChanges(changes: SimpleChanges) {
    }

    onSelectedRollupTypesChanged() {
        this.updateControls();
    }

    onSelectedDispatchGroupsChanged() {
        this.updateControls();
    }

    maxRangeFilter(date) {
        return true;
    }

    onSelectedIntervalChanged(event: any) {
        console.log('interval changed', this.selectedInterval);

        this.updateControls();
    }

    updateControls() {
        this.canSubmit = this.selectedRollupTypes && this.selectedRollupTypes.length > 0 &&
            this.selectedDispatchGroups && this.selectedDispatchGroups.length > 0 &&
            this.selectedInterval && this.selectedInterval.length == 2;
    }

    submit() {
        this.errors = [];

        let param: any = {
            rollup_type_ids: this.selectedRollupTypes.map(x => x.id),
            from_time: this.selectedInterval[0].startOf('minute').toISOString(),
            to_time: this.selectedInterval[1].endOf('minute').toISOString(),
            item_type: 'DispatchGroup',
            dispatch_group_ids: this.selectedDispatchGroups.map(x => x.id)
        };

        console.log('regenerate', param);

        this.canSubmit = false;

        this._rollupConfigProvider.generateRollup(param).subscribe(
            res => {
                this.canSubmit = true;
                if (res.success == true) {
                    this._notificationService.success({text: 'SUCCESS'});
                } else {
                    this.errors = [res.error];
                    this._notificationService.errors(this.errors);
                }
            },
            err => {
                this.canSubmit = true;
                console.error('submit failed', err);

                this.errors = [JSON.stringify(err)];
                this._notificationService.errors(this.errors);
                this.canSubmit = true;
            }
        );
    }
}