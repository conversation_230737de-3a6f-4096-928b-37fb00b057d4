<div class="container-fluid" *ngIf="permissions.can_see_rollups">

    <ng-template [ngIf]="permissions.can_create_rollups">
        <h2>{{'CONFIGURATION.DG_ROLLUPS.TITLE_ROLLUP_REGENERATION'|translate}}</h2>
        <vpp-dispatch-group-rollup-regeneration
                [rollupTypes]="rollupTypes"
                [dispatchGroups]="dispatchGroups"
        >
        </vpp-dispatch-group-rollup-regeneration>
    </ng-template>

    <h2>{{'CONFIGURATION.DG_ROLLUPS.TITLE_ROLLUP_MANAGEMENT'|translate}}</h2>
    <div class="row">
        <div class="col-md-12">
            <ag-grid-angular
                    xstylex="width: 500px; height: 500px;"
                    class="ag-theme-alpine"
                    [rowData]="rowData"
                    [columnDefs]="columnDefs"
                    [gridOptions]="gridOptions"
                    (gridReady)="onGridReady($event)"
                    [domLayout]="'autoHeight'"
                    [frameworkComponents]="frameworkComponents"
            >
            </ag-grid-angular>
        </div>
    </div>

</div>