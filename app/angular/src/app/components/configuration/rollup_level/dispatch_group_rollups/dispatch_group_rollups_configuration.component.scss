@import "./../../../../styles/colors";

vpp-dispatch-group-rollups-configuration {
  display: block;


  .tab-content {
    padding: 60px;
    margin-left: -15px;

    select.form-control, .form-control, input[type=text].form-control {
      border-color: $eon-turquoise;
    }

  }

  .page-actions {
    padding: 30px 0;

    ul {
      margin-bottom: 0;
    }

    ul > li {
      margin-right: 20px;
    }
  }

  .overlay-on-top-of-list {
    position: relative;
    z-index: 999;
  }

  .grid-cell-centered {
    text-align: center;
  }

  .ag-header-cell-label {
    justify-content: center;
  }

  .grid-cell-invalid {
    border: 1px solid red !important;
  }

  .grid-cell-valid {
    border: none !important;
  }

  .ag-root,
  .ag-body-viewport,
  .ag-body-viewport-wrapper {
    overflow: visible !important;
  }
}