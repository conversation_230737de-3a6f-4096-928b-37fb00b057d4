import {
    Component,
    OnInit,
    ViewEncapsulation,
    ViewChild,
    ElementRef
} from "@angular/core";
import {
    Router,
    ActivatedRoute,
    Para<PERSON>,
    PRIMARY_OUTLET
} from "@angular/router";
import {Location} from "@angular/common";
import {GlobalService} from "../../../../services/global.service";
import {SlideRightContentAnimation} from "../../../../animations/slide-right-content";
import {environment} from "../../../../../environments/environment";
import {angularData} from "../../../../../global.export";
import {TranslateService} from '@ngx-translate/core';
import {RollupConfigProvider} from "../../../../providers/rollup_config.provider";
import {GridOptions} from "ag-grid-community";
import {RollupConfigGridCellHelper} from "../rollup_config_grid_cell_helper";
import {ProductsRendererComponent} from "../asset_rollups/products_renderer.component";
import {ProductsEditorComponent} from "../asset_rollups/products_editor.component";
import {RollupFamilyHeaderComponent} from "../asset_rollups/rollup_family_header.component";
import {NotificationService} from "../../../../services/notification.service";

@Component({
    selector: "vpp-dispatch-group-rollups-configuration",
    templateUrl: "./dispatch_group_rollups_configuration.component.html",
    styleUrls: ["./dispatch_group_rollups_configuration.component.scss"],
    encapsulation: ViewEncapsulation.None,
    animations: [SlideRightContentAnimation],
})
export class DispatchGroupRollupsConfigurationComponent implements OnInit {
    selectedSelection = "";
    loadList: boolean = true;
    permissions: any;
    rollupsData: any;
    rollupTypes: Array<any>;
    dispatchGroups: Array<any>;
    columnDefs = [];
    rowData = [];
    gridOptions: GridOptions;
    grid: any;

    constructor(
        public translate: TranslateService,
        private _global: GlobalService,
        private _route: ActivatedRoute,
        private _location: Location,
        public rollupConfigProvider: RollupConfigProvider,
        public notificationService: NotificationService
    ) {
        this.gridOptions = <GridOptions>{
            context: this
        };
        this.permissions = angularData.permissions;
        this.translate.get('PAGE.CAPTIONS.CONFIGURATION.ROLLUPS_LEVEL.DISPATCH_GROUP_ROLLUPS').subscribe((res: string) => {
            this._global.changeTitle(res);
        });
    }

    ngOnInit() {
        this.loadRollupConfig();
    }

    ngAfterViewInit() {
    }

    toggleSection(event, section) {
        if (event) {
            //event.preventDefault();
            //event.stopPropagation();
        }

        if (this.selectedSelection == section) {
            this.selectedSelection = '';
        } else {
            this.selectedSelection = section;
        }
    }

    reloadList() {
        this.toggleSection(null, '');
        // reload the list in the next refresh cycle
        this.loadList = false;
        setTimeout(() => {
            this.loadList = true;
        });
    }

    onIntervalCellValueChanged(event) {
        console.log('interval changed', event);
        console.log('event.data', event.data);


        let self = event.context;

        let cellHelper = new RollupConfigGridCellHelper(event);

        // if (event.newValue !== event.oldValue) {
        let params = {
            rollup_type_code: event.data.rollup_type_code,
            dg_id: event.colDef.field,
            val: event.newValue
        };

        console.log('saving rollup interval', params);

        cellHelper.toggleError(false);

        self.rollupConfigProvider.saveDgRollupInterval(params).subscribe(
            res => {
                if (res.success) {
                    console.log('saved cell data', res);

                    cellHelper.setCellValue(res.val);
                } else {
                    console.log('cell data rejected', res);

                    self.notificationService.error({text: res.error});
                    cellHelper.toggleError(true);
                }
            },
            err => {
                console.error('failed to save cell data', err);

                self.notificationService.error(err);
                cellHelper.toggleError(true);
            }
        );
        // } else {
        //     console.log('data was unchanged');
        // }
    }

    onGridReady(grid) {
        console.log('grid is ready');

        this.grid = grid;
        this.sizeToFit();
    }

    sizeToFit() {
        let grid = this.grid;
        if (grid) {
            grid.api.sizeColumnsToFit();
            grid.api.resetRowHeights();
        }
    }

    loadRollupConfig() {
        this.rollupConfigProvider.getDgRollupConfig().subscribe(
            res => {
                console.log('Got rollups config', res);

                this.rollupsData = res;
                this.rollupTypes = res.rollup_row_data.map(x => {
                    return {
                        id: x.rollup_type_id,
                        name: x.rollup_type_name
                    };
                });
                this.dispatchGroups = res.dispatch_groups;

                let cellClassRules = RollupConfigGridCellHelper.cellClassRules;

                this.columnDefs = [
                    {
                        headerName: this.translate.instant('CONFIGURATION.ASSET_ROLLUPS.ROLLUP_METRIC'),
                        headerTooltip: this.translate.instant('CONFIGURATION.ASSET_ROLLUPS.ROLLUP_METRIC'),
                        field: 'rollup_type_name',
                        cellStyle: {
                            'white-space': 'normal',
                            'width': '150px',
                            'overflow-wrap': 'break-word',
                            'line-height': 'normal',
                            'vertical-align': 'middle',
                            'display': 'flex',
                            'align-items': 'center'
                        },
                        autoHeight: true
                    },
                ];

                this.columnDefs = this.columnDefs.concat(res.dispatch_groups.map(x => {
                    return {
                        headerName: x.name,
                        headerTooltip: x.name,
                        field: '' + x.id,
                        editable: !!this.permissions.can_create_rollups,
                        onCellValueChanged: this.onIntervalCellValueChanged,
                        cellClass: 'grid-cell-centered',
                        cellClassRules: cellClassRules
                    };
                }));

                this.rowData = res.rollup_row_data.map(x => {
                    x['err'] = {};
                    return x;
                });

                setTimeout(() => { this.sizeToFit(); }, 50);
            },
            err => {
                console.log('Failed to load rollups config', err);
            }
        );
    }

}
