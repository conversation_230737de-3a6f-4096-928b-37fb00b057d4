import {RefreshCellsParams} from "ag-grid-community";

export class RollupConfigGridCellHelper {

    static readonly cellClassRules = {
        'grid-cell-invalid': function (param) {
            return param.data['err']['' + param.colDef.field];
        },
        'grid-cell-valid': "!data['err'][''+colDef.field]"
    };

    constructor(private event: any) {
    }

    redrawCell() {
        this.event.api.refreshCells(<RefreshCellsParams>{
            rowNodes: [this.event.node],
            columns: ['' + this.event.colDef.field]
        });
    };

    toggleError(err: boolean) {
        this.event.data['err']['' + this.event.colDef.field] = err;
        this.redrawCell();
    };

    setCellValue(val) {
        this.event.data['' + this.event.colDef.field] = val;
        this.redrawCell();
    };
}