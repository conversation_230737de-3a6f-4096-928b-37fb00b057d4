<section> <!-- Rollups Level Configuration -->
    <div class="container-fluid">
        <div id="ngb-tabset" class="ngb-tabset">
            <ul role="tablist" class="nav nav-tabs justify-content-start">

                <!-- Asset Rollups -->
                <li class="nav-item"
                    *ngIf="permittedTabs.assetRollups">
                    <a (click)="selectTab($event, 'asset-rollups')"
                       [ngClass]="selectedTab == 'asset-rollups' ? 'active' : ''"
                       class="nav-link"
                       href="javascript:void(0)"
                       role="tab"
                       id="box-types" >{{'CONFIGURATION.ROLLUPS.TAB.ASSET_ROLLUPS' | translate}}</a>
                </li>

                <!-- Dispatch Group Rollups -->
                <li class="nav-item"
                    *ngIf="permittedTabs.dispatchGroupRollups">
                    <a (click)="selectTab($event, 'dispatch-group-rollups')"
                       [ngClass]="selectedTab == 'dispatch-group-rollups' ? 'active' : ''"
                       class="nav-link"
                       href="javascript:void(0)"
                       role="tab"
                       id="signal-lists" >{{'CONFIGURATION.ROLLUPS.TAB.DISPATCH_GROUP_ROLLUPS' | translate}}</a>
                </li>
            </ul>

            <div class="tab-content">
                <div [@slideRightContentAnimation]="selectedTab == 'asset-rollups' ? 'in' : 'out'"
                     *ngIf="permittedTabs.assetRollups"
                >
                    <vpp-asset-rollups-configuration *ngIf="selectedTab == 'asset-rollups'"></vpp-asset-rollups-configuration>
                </div>
                <div [@slideRightContentAnimation]="selectedTab == 'dispatch-group-rollups' ? 'in' : 'out'"
                     *ngIf="permittedTabs.dispatchGroupRollups"
                >
                    <vpp-dispatch-group-rollups-configuration *ngIf="selectedTab == 'dispatch-group-rollups'"></vpp-dispatch-group-rollups-configuration>
                </div>
            </div>
        </div>
    </div>
</section>