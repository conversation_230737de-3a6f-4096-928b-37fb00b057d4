<div class="row">
    <div class="col-md-8" id="full-width-in-modal">
        <div class="tab-content light-blue" style="border: none" id="enter-dispatch-group-tab">
            <span *ngIf="!embedded" class="pull-right cursor-hand" (click)="cancel($event)">
                <i class="fa fa-times"></i>
            </span>

            <h2 *ngIf="isEditing">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.ENTER_DISPATCH_GROUP'|translate}}</h2>
            <h2 *ngIf="!isEditing">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.ENTER_DISPATCH_GROUP'|translate}}</h2>

            <div class="alerts-wrapper-static" *ngIf="errors.length" #errorsDiv>
                <ng-container *ngFor="let errmsg of errors; let ix = index;">
                    <ngb-alert
                            [dismissible]="false"
                            [type]="'error'"> {{ errmsg }}
                    </ngb-alert>
                </ng-container>
            </div>

            <section>
                <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.GENERAL_DATA_TITLE'|translate}}</h6>

                <div class="row">
                    <div class="form-group col-sm-6">
                        <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.NAME'|translate}}</label>
                        <input
                                *ngIf="isEditing"
                                [(ngModel)]="dispatchGroup.name"
                                type="text"
                                class="form-control"
                                id="inputName">
                        <span *ngIf="!isEditing" class="value">{{ dispatchGroup.name }}</span>
                    </div>

                    <div class="form-group col-sm-6" *ngIf="dispatchGroup.generic_config_enabled">
                        <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.GENERIC_CONFIG_ENABLED'|translate}}</label>
                        <div class="form-check">
                            <label
                                    class="eon-checkbox-label bg-eon-red"
                                    (click)="toggleGenericConfigEnabled()"
                                    [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.has_generic_config}">
                            </label>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="form-group col-sm-6">
                        <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.TSO'|translate}}</label>
                        <ng-multiselect-dropdown
                                *ngIf="isEditing"
                                [placeholder]="('COMPONENTS.SELECT'|translate)"
                                [data]="tsos"
                                [(ngModel)]="selectedTsos"
                                [settings]="tsoDropdownSettings"
                                (onSelect)="onSelectedTsoChanged()"
                                (onDeSelect)="onSelectedTsoChanged()"
                                (onSelectAll)="onSelectedTsoChanged()"
                        >
                        </ng-multiselect-dropdown>
                        <span *ngIf="!isEditing" class="value">{{ selectedTsos[0].name }}</span>
                    </div>

                    <div class="form-group col-sm-6">
                        <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.MARKET'|translate}}</label>
                        <ng-multiselect-dropdown
                                *ngIf="isEditing"
                                [placeholder]="('COMPONENTS.SELECT'|translate)"
                                [data]="markets"
                                [(ngModel)]="selectedMarkets"
                                [settings]="marketDropdownSettings"
                                (onSelect)="onSelectedMarketChanged()"
                                (onDeSelect)="onSelectedMarketChanged()"
                                (onSelectAll)="onSelectedMarketChanged()"
                        >
                        </ng-multiselect-dropdown>
                        <span *ngIf="!isEditing" class="value">{{ selectedMarkets[0].name }}</span>
                    </div>
                </div>

                <div class="row" *ngIf="(isSelectedMarketSpot && !this.dispatchGroup.has_generic_config) ||
                    (isSelectedMarketIntraday && this.dispatchGroup.has_generic_config &&
                        (dispatchGroup.export_entrader || showParametersFor('dispatch_source', 'residualShape'))
                    ) ||
                    (this.dispatchGroup.has_generic_config && showParametersFor('dispatch_source', 'nlAfrr'))
                ">
                    <div class="form-group col-sm-6">
                        <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.PORTFOLIO_ID'|translate}}</label>
                        <input
                                *ngIf="isEditing"
                                type="text"
                                class="form-control"
                                [(ngModel)]="dispatchGroup.portfolio_id">
                        <span *ngIf="!isEditing" class="value">{{ dispatchGroup.portfolio_id || '-' }}</span>
                    </div>
                </div>

                <div class="row" *ngIf="!dispatchGroup.has_generic_config">
                    <div class="form-group col-sm-6">
                        <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.REACTION_TIME'|translate}}</label>
                        <input
                                *ngIf="isEditing"
                                type="number"
                                step="1"
                                class="form-control"
                                [(ngModel)]="dispatchGroup.reaction_time">
                        <span *ngIf="!isEditing" class="value">{{ dispatchGroup.reaction_time || '-' }}</span>
                    </div>

                    <div class="form-group col-sm-6">
                        <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.MINIMUM_DURATION_OF_DISPATCH'|translate}}</label>
                        <input
                                *ngIf="isEditing"
                                type="number"
                                step="1"
                                class="form-control"
                                [(ngModel)]="dispatchGroup.minimum_duration_of_dispatch">
                        <span *ngIf="!isEditing"
                              class="value">{{ dispatchGroup.minimum_duration_of_dispatch || '-' }}</span>
                    </div>
                </div>
            </section>

            <!-- Balancing Groups -->
            <section *ngIf="!isEditing">
                <h6>{{ 'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_TITLE'|translate }}</h6>

                <div class="row">
                    <div class="form-group col-sm-10">
                        <table
                                *ngIf="balancingGroups.length > 0"
                                class="table table-auction-results table striped table-bg-turquoise">
                            <tr>
                                <th>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_START_DATE'|translate}}</th>
                                <th>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_END_DATE'|translate}}</th>
                                <th>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_NAME'|translate}}</th>
                                <th></th>
                            </tr>
                            <ng-container *ngFor="let bg of balancingGroups">
                                <tr>
                                    <input type="hidden" value="bg.id">
                                    <td>{{ bg.start_date }}</td>
                                    <td>{{ bg.end_date }}</td>
                                    <td>{{ bg.name }}</td>
                                    <td class="text-center actions-column">
                                        <ul
                                                *ngIf="permissions.can_create_dispatch_groups || permissions.can_update_dispatch_groups"
                                                class="list-inline list-unstyled">
                                            <li>
                                                <span
                                                        (click)="editBalancingGroup(bg)"
                                                        style="display: inline-block">
                                                    <i class="fa fa-pencil"></i></span>
                                            </li>
                                            <li
                                                    *ngIf="bg.can_delete">
                                                <span
                                                        (click)="deleteBalancingGroup(bg)"
                                                        style="display: inline-block">
                                                    <i class="fa fa-trash"></i></span>
                                            </li>
                                        </ul>
                                    </td>
                                </tr>
                            </ng-container>
                        </table>


                    </div>
                </div>

                <!--
                <ul class="list-inline list-unstyled d-flex form-actions">
                    <li>
                        <button
                                (click)="addBalancingGroup()"
                                class="eon-button bg-eon-red">
                            <span>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_ADD'|translate}}</span>
                        </button>
                    </li>
                </ul>
                -->

                <div class="row" *ngIf="permissions.can_create_dispatch_groups || permissions.can_update_dispatch_groups">
                    <div class="form-group col-sm-6">
                        <div class="page-actions">
                            <ul class="list-unstyled d-flex">
                                <li>
                                    <a href="javascript:void(0)"
                                       (click)="addBalancingGroup()"
                                       class="eon-button bg-eon-turquoise">
                                        {{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_ADD'|translate}}
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>

            <div *ngIf="dispatchGroup.has_generic_config">
                <vpp-generic-dg-config [dispatchGroup]="dispatchGroup" [dsos]="dsos"
                                       [isSelectedTsoNationalGrid]="isSelectedTsoNationalGrid"
                                       [isSelectedMarketIntraday]="isSelectedMarketIntraday"
                                       [isSelectedMarketDynamicServices]="isSelectedMarketDynamicServices"
                                       [isEditing]="isEditing"></vpp-generic-dg-config>
            </div>

            <div *ngIf="!dispatchGroup.has_generic_config">
                <section *ngIf="isSelectedMarketFfrd">
                    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.PARAMETERS_TITLE'|translate}}
                        <ng-template #ffrdTooltip>
                            <div [innerHTML]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.PARAMETERS_INFO_HTML'|translate"
                            ></div>
                        </ng-template>
                        <i class="fa fa-question-circle"
                           [ngbTooltip]="ffrdTooltip"
                           containerClass="wider-tooltip"></i>
                    </h6>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DEAD_BAND_POS_MHZ'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="1" class="form-control"
                                    [(ngModel)]="dispatchGroup.dead_band_pos_mhz">
                            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.dead_band_pos_mhz || '-' }}</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DEAD_BAND_NEG_MHZ'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="1" class="form-control"
                                    [(ngModel)]="dispatchGroup.dead_band_neg_mhz">
                            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.dead_band_neg_mhz || '-' }}</span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.MAX_FREQUENCY_DEVIATION_POS_MHZ'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="1" class="form-control"
                                    [(ngModel)]="dispatchGroup.max_frequency_deviation_pos_mhz">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.max_frequency_deviation_pos_mhz || '-' }}</span>
                        </div>
                        <div class="form-group col-sm-6">
                            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.MAX_FREQUENCY_DEVIATION_NEG_MHZ'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="1" class="form-control"
                                    [(ngModel)]="dispatchGroup.max_frequency_deviation_neg_mhz">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.max_frequency_deviation_neg_mhz || '-' }}</span>
                        </div>
                    </div>
                </section>

                <section>
                    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SIGNALS_TITLE'|translate}}</h6>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SIGNALS'|translate}}</label>
                            <ng-multiselect-dropdown
                                    *ngIf="isEditing"
                                    [placeholder]="('COMPONENTS.SELECT'|translate)"
                                    [data]="signals"
                                    [(ngModel)]="dispatchGroup.signals_list"
                                    [settings]="signalsDropdownSettings"
                            >
                            </ng-multiselect-dropdown>
                            <ul *ngIf="!isEditing" class="list-unstyled">
                                <li *ngFor="let x of dispatchGroup.signals_list">
                                    <span class="value">{{ x }}</span>
                                </li>
                            </ul>
                            <span *ngIf="!isEditing && !(dispatchGroup.signals_list && dispatchGroup.signals_list.length > 0)">-</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DSOS'|translate}}</label>
                            <ng-multiselect-dropdown
                                    *ngIf="isEditing"
                                    [placeholder]="('COMPONENTS.SELECT'|translate)"
                                    [data]="dsos"
                                    [(ngModel)]="dispatchGroup.dsos"
                                    [settings]="dsoDropdownSettings"
                            >
                            </ng-multiselect-dropdown>
                            <ul *ngIf="!isEditing" class="list-unstyled">
                                <li *ngFor="let x of dispatchGroup.dsos">
                                    <span class="value">{{ x.name }}</span>
                                </li>
                            </ul>
                            <span *ngIf="!isEditing && !(dispatchGroup.dsos && dispatchGroup.dsos.length > 0)">-</span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SUBPOOLS_VALUE_ON'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    class="form-control"
                                    type="number"
                                    step="1"
                                    [(ngModel)]="dispatchGroup.subpools_value_on"
                            >
                            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.subpools_value_on || '-' }}</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SUBPOOLS_VALUE_OFF'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    class="form-control"
                                    type="number"
                                    step="1"
                                    [(ngModel)]="dispatchGroup.subpools_value_off"
                            >
                            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.subpools_value_off || '-' }}</span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ECHO_SIGNAL_ACTIVE_POWER'|translate}}</label>
                            <div class="form-check">
                                <label
                                        class="eon-checkbox-label bg-eon-red"
                                        (click)="toggleEchoSignalActivePower()"
                                        [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.echo_signal_active_power}">
                                </label>
                            </div>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ECHO_SIGNAL_HEARTBEAT'|translate}}</label>
                            <div class="form-check">
                                <label
                                        class="eon-checkbox-label bg-eon-red"
                                        (click)="toggleEchoSignalHeartbeat()"
                                        [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.echo_signal_heartbeat}">
                                </label>
                            </div>
                        </div>
                    </div>
                </section>

                <section>
                    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARMS_TITLE'|translate}}</h6>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_ASSETS_WITH_NOT_RECOVERABLE_FAULTS_ENABLED'|translate}}</label>
                            <div class="form-check">
                                <label
                                        class="eon-checkbox-label bg-eon-red"
                                        (click)="toggleAlarmAssetsWithNotRecoverableFaultsEnabled()"
                                        [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.alarm_assets_with_not_recoverable_faults_enabled}">
                                </label>
                            </div>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_ASSETS_WITH_RECOVERABLE_FAULTS_ENABLED'|translate}}</label>
                            <div class="form-check">
                                <label
                                        class="eon-checkbox-label bg-eon-red"
                                        (click)="toggleAlarmAssetsWithRecoverableFaultsEnabled()"
                                        [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.alarm_assets_with_recoverable_faults_enabled}">
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_SETPOINT_REACHABLE_ENABLED'|translate}}</label>
                            <div class="form-check">
                                <label
                                        class="eon-checkbox-label bg-eon-red"
                                        (click)="toggleAlarmSetpointReachableEnabled()"
                                        [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.alarm_setpoint_reachable_enabled}">
                                </label>
                            </div>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_AVAILABLE_FLEX_TOO_LOW_ENABLED'|translate}}</label>
                            <div class="form-check">
                                <label
                                        class="eon-checkbox-label bg-eon-red"
                                        (click)="toggleAlarmAvailableFlexTooLowEnabled()"
                                        [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.alarm_available_flex_too_low_enabled}">
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_ASSETS_FAULTS_PQ_THRESHOLD'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    class="form-control"
                                    type="number"
                                    step="1"
                                    [(ngModel)]="dispatchGroup.alarm_assets_faults_pq_threshold">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.alarm_assets_faults_pq_threshold || '-' }}</span>
                        </div>
                    </div>
                </section>

                <section>
                    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.SECTION.RCC_TITLE'|translate}}</h6>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARMS_ENABLED'|translate}}
                                <i class="fa fa-question-circle"
                                   [ngbTooltip]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TOOLTIP.INDIVIDUAL_ALARMS_ENABLED_INFO_HTML'|translate"
                                   containerClass="wider-tooltip"></i>
                            </label>
                            <div class="form-check">
                                <label
                                        class="eon-checkbox-label bg-eon-red"
                                        (click)="toggleIndividualAlarmsEnabled()"
                                        [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.individual_alarms_enabled}">
                                </label>
                            </div>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_FLEXIBILITY_THRESHOLD_FACTOR'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" class="form-control" step="1.0"
                                    [(ngModel)]="dispatchGroup.alarm_flexibility_threshold_factor">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.alarm_flexibility_threshold_factor || '-' }}</span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_MERLIN_ENABLED'|translate}}</label>
                            <div class="form-check">
                                <label
                                        class="eon-checkbox-label bg-eon-red"
                                        (click)="toggleIndividualAlarmMerlinEnabled()"
                                        [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.individual_alarm_merlin_enabled}">
                                </label>
                            </div>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_FLEXIBILITY_THRESHOLD_BUFFER'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    class="form-control" type="number" step="1.0"
                                    [(ngModel)]="dispatchGroup.alarm_flexibility_threshold_buffer">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.alarm_flexibility_threshold_buffer || '-' }}</span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_ECHO_SIGNAL_ACTIVE_POWER_ENABLED'|translate}}</label>
                            <div class="form-check">
                                <label
                                        class="eon-checkbox-label bg-eon-red"
                                        (click)="toggleIndividualAlarmEchoSignalActivePowerEnabled()"
                                        [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.individual_alarm_echo_signal_active_power_enabled}">
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <p class="col-sm-12"
                           [innerHTML]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.NOTICE.INDIVIDUAL_ALARMS_ENABLED_HTML'|translate"
                        >
                        </p>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_OVERDELIVERY_POS_ENABLED'|translate}}
                                <i class="fa fa-question-circle"
                                   [ngbTooltip]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TOOLTIP.INDIVIDUAL_ALARM_OVERDELIVERY_POS_ENABLED_INFO_HTML'|translate"
                                   containerClass="wider-tooltip"></i>
                            </label>
                            <div class="form-check">
                                <label
                                        class="eon-checkbox-label bg-eon-red"
                                        (click)="toggleIndividualAlarmOverdeliveryPosEnabled()"
                                        [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.individual_alarm_overdelivery_pos_enabled}">
                                </label>
                            </div>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_OVERDELIVERY_NEG_ENABLED'|translate}}
                                <i class="fa fa-question-circle"
                                   [ngbTooltip]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TOOLTIP.INDIVIDUAL_ALARM_OVERDELIVERY_NEG_ENABLED_INFO_HTML'|translate"
                                   containerClass="wider-tooltip"></i>
                            </label>
                            <div class="form-check">
                                <label
                                        class="eon-checkbox-label bg-eon-red"
                                        (click)="toggleIndividualAlarmOverdeliveryNegEnabled()"
                                        [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.individual_alarm_overdelivery_neg_enabled}">
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_OVERDELIVERY_POS_THRESHOLD'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="tex" class="form-control"
                                    [(ngModel)]="dispatchGroup.individual_alarm_overdelivery_pos_threshold_mw">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.individual_alarm_overdelivery_pos_threshold_mw || '-' }}</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_OVERDELIVERY_NEG_THRESHOLD'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="tex" class="form-control"
                                    [(ngModel)]="dispatchGroup.individual_alarm_overdelivery_neg_threshold_mw">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.individual_alarm_overdelivery_neg_threshold_mw || '-' }}</span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_OVERDELIVERY_POS_DELAY'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="1" class="form-control"
                                    [(ngModel)]="dispatchGroup.individual_alarm_overdelivery_pos_delay">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.individual_alarm_overdelivery_pos_delay || '-' }}</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_OVERDELIVERY_NEG_DELAY'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="1" class="form-control"
                                    [(ngModel)]="dispatchGroup.individual_alarm_overdelivery_neg_delay">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.individual_alarm_overdelivery_neg_delay || '-' }}</span>
                        </div>
                    </div>
                </section>

                <section>
                    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.PRL_TITLE'|translate}}</h6>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ACTIVATION_FACTOR'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    class="form-control" type="number" step="any"
                                    [(ngModel)]="dispatchGroup.activation_factor">
                            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.activation_factor || '-' }}</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.AVAILABILITY_DEVIATION_THRESHOLD_FACTOR'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    class="form-control" type="number" step="any"
                                    [(ngModel)]="dispatchGroup.availability_deviation_threshold_factor">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.availability_deviation_threshold_factor || '-' }}</span>
                        </div>
                    </div>
                </section>

                <section>
                    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SETPOINT_REACHED_TITLE'|translate}}</h6>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SETPOINT_REACHED_DETECTION_ROLLING_AVERAGE_DURATION'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="1" class="form-control"
                                    [(ngModel)]="dispatchGroup.setpoint_reached_detection_rolling_average_duration">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.setpoint_reached_detection_rolling_average_duration || '-' }}</span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SETPOINT_REACHED_DETECTION_UPPER_TOLERANCE_FACTOR'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" class="form-control"
                                    [(ngModel)]="dispatchGroup.setpoint_reached_detection_upper_tolerance_factor">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.setpoint_reached_detection_upper_tolerance_factor || '-' }}</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SETPOINT_REACHED_DETECTION_LOWER_TOLERANCE_FACTOR'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" class="form-control"
                                    [(ngModel)]="dispatchGroup.setpoint_reached_detection_lower_tolerance_factor">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.setpoint_reached_detection_lower_tolerance_factor || '-' }}</span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SETPOINT_REACHED_DETECTION_UPPER_TOLERANCE_MINIMUM'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" class="form-control"
                                    [(ngModel)]="dispatchGroup.setpoint_reached_detection_upper_tolerance_minimum">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.setpoint_reached_detection_upper_tolerance_minimum || '-' }}</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SETPOINT_REACHED_DETECTION_LOWER_TOLERANCE_MINIMUM'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" class="form-control"
                                    [(ngModel)]="dispatchGroup.setpoint_reached_detection_lower_tolerance_minimum">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.setpoint_reached_detection_lower_tolerance_minimum || '-' }}</span>
                        </div>
                    </div>
                </section>

                <section>
                    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.IS_AT_SETPOINT_DETECTION_TITLE'|translate}}</h6>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_FACTOR'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    [(ngModel)]="dispatchGroup.is_at_setpoint_detection_upper_tolerance_factor">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.is_at_setpoint_detection_upper_tolerance_factor || '-' }}</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_FACTOR'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    [(ngModel)]="dispatchGroup.is_at_setpoint_detection_lower_tolerance_factor">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.is_at_setpoint_detection_lower_tolerance_factor || '-' }}</span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_MINIMUM'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    [(ngModel)]="dispatchGroup.is_at_setpoint_detection_upper_tolerance_minimum">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.is_at_setpoint_detection_upper_tolerance_minimum || '-' }}</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_MINIMUM'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    [(ngModel)]="dispatchGroup.is_at_setpoint_detection_lower_tolerance_minimum">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.is_at_setpoint_detection_lower_tolerance_minimum || '-' }}</span>
                        </div>
                    </div>
                </section>

                <section>
                    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_TITLE'|translate}}</h6>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_CHECK_INTERVAL'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="1" class="form-control"
                                    [(ngModel)]="dispatchGroup.compensation_check_interval">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.compensation_check_interval || '-' }}</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_RESOLUTION'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="1" class="form-control"
                                    [(ngModel)]="dispatchGroup.compensation_resolution">
                            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.compensation_resolution || '-' }}</span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.OVER_DELIVERY_EXCESS_COMPENSATION_FACTOR'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    [(ngModel)]="dispatchGroup.over_delivery_excess_compensation_factor">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.over_delivery_excess_compensation_factor || '-' }}</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.OVER_DELIVERY_COMPENSATION_LIMIT_FACTOR'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    [(ngModel)]="dispatchGroup.over_delivery_compensation_limit_factor">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.over_delivery_compensation_limit_factor || '-' }}</span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.UNDER_DELIVERY_EXCESS_COMPENSATION_FACTOR'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    [(ngModel)]="dispatchGroup.under_delivery_excess_compensation_factor">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.under_delivery_excess_compensation_factor || '-' }}</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.UNDER_DELIVERY_COMPENSATION_LIMIT_FACTOR'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    [(ngModel)]="dispatchGroup.under_delivery_compensation_limit_factor">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.under_delivery_compensation_limit_factor || '-' }}</span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_FACTOR'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    [(ngModel)]="dispatchGroup.compensation_is_at_setpoint_detection_upper_tolerance_factor">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.compensation_is_at_setpoint_detection_upper_tolerance_factor || '-' }}</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_FACTOR'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    [(ngModel)]="dispatchGroup.compensation_is_at_setpoint_detection_lower_tolerance_factor">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.compensation_is_at_setpoint_detection_lower_tolerance_factor || '-' }}</span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_MINIMUM'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    [(ngModel)]="dispatchGroup.compensation_is_at_setpoint_detection_upper_tolerance_minimum">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.compensation_is_at_setpoint_detection_upper_tolerance_minimum || '-' }}</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_MINIMUM'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    [(ngModel)]="dispatchGroup.compensation_is_at_setpoint_detection_lower_tolerance_minimum">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.compensation_is_at_setpoint_detection_lower_tolerance_minimum || '-' }}</span>
                        </div>
                    </div>
                </section>

                <section>
                    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SALLY_SETPOINT_TITLE'|translate}}</h6>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SALLY_SETPOINT_READ_ENABLED'|translate}}</label>
                            <div class="form-check">
                                <label
                                        class="eon-checkbox-label bg-eon-red"
                                        (click)="toggleSallySetpointReadEnabled()"
                                        [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.sally_setpoint_read_enabled}">
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SALLY_SETPOINT_QUANTIZATION_RESOLUTION'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    [(ngModel)]="dispatchGroup.sally_setpoint_quantization_resolution">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.sally_setpoint_quantization_resolution || '-' }}</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SALLY_SETPOINT_QUANTIZATION_TIMEOUT'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    [(ngModel)]="dispatchGroup.sally_setpoint_quantization_timeout">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.sally_setpoint_quantization_timeout || '-' }}</span>
                        </div>
                    </div>
                </section>

                <section>
                    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.MERLIN_DISPATCH_TITLE'|translate}}</h6>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.MERLIN_DISPATCH_ENABLED'|translate}}</label>
                            <div class="form-check">
                                <label
                                        class="eon-checkbox-label bg-eon-red"
                                        (click)="toggleMerlinDispatchEnabled()"
                                        [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.merlin_dispatch_enabled}">
                                </label>
                            </div>
                        </div>
                    </div>
                </section>

                <section>
                    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SPOT_OPTIMIZATION_TITLE'|translate}}</h6>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.CAPACITY_PRICE_GROUPING_STEPS'|translate}}</label>
                            <ng-multiselect-dropdown
                                    *ngIf="isEditing"
                                    [placeholder]="('COMPONENTS.SELECT'|translate)"
                                    [data]="capacityPriceGroupingSteps"
                                    [(ngModel)]="dispatchGroup.capacity_price_grouping_steps_list"
                                    [settings]="capacityPriceGroupingStepsDropdownSettings"
                            >
                            </ng-multiselect-dropdown>
                            <ul *ngIf="!isEditing" class="list-unstyled">
                                <li *ngFor="let x of dispatchGroup.capacity_price_grouping_steps_list">
                                    <span class="value">{{ x }}</span>
                                </li>
                            </ul>
                            <span *ngIf="!isEditing && !(dispatchGroup.capacity_price_grouping_steps_list && dispatchGroup.capacity_price_grouping_steps_list.length > 0)">-</span>
                        </div>
                        <div class="form-group col-sm-6">
                            <label>&nbsp;</label>
                            <input
                                *ngIf="isEditing"
                                [(ngModel)]="newCapacityPriceGroupingStep"
                                [placeholder]="('CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.PLACEHOLDER.CAPACITY_PRICE_GROUPING_STEPS'|translate)"
                                (keydown.enter)="addNewCapacityPriceGroupingStep($event)"
                                type="number"
                                class="form-control"
                                id="newCapacityPriceGroupingSteps">

                        </div>
                    </div>
                </section>

                <section>
                    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.UI_ALERTS_TITLE'|translate}}</h6>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EVENT_TYPES_ACKNOWLEDGE_ALARM'|translate}}</label>
                            <ng-multiselect-dropdown
                                    *ngIf="isEditing"
                                    [placeholder]="('COMPONENTS.SELECT'|translate)"
                                    [data]="eventTypes"
                                    [(ngModel)]="dispatchGroup.event_types_acknowledge_alarm_list"
                                    [settings]="eventTypesDropdownSettings"
                            >
                            </ng-multiselect-dropdown>
                            <ul *ngIf="!isEditing" class="list-unstyled">
                                <li *ngFor="let x of dispatchGroup.event_types_acknowledge_alarm_list">
                                    <span class="value">{{ x }}</span>
                                </li>
                            </ul>
                            <span *ngIf="!isEditing && !(dispatchGroup.event_types_acknowledge_alarm_list && dispatchGroup.event_types_acknowledge_alarm_list.length > 0)">-</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EVENT_TYPES_ACKNOWLEDGE_AUDIO_ALARM'|translate}}</label>
                            <ng-multiselect-dropdown
                                    *ngIf="isEditing"
                                    [placeholder]="('COMPONENTS.SELECT'|translate)"
                                    [data]="eventTypes"
                                    [(ngModel)]="dispatchGroup.event_types_acknowledge_audio_alarm_list"
                                    [settings]="eventTypesDropdownSettings"
                            >
                            </ng-multiselect-dropdown>
                            <ul *ngIf="!isEditing" class="list-unstyled">
                                <li *ngFor="let x of dispatchGroup.event_types_acknowledge_audio_alarm_list">
                                    <span class="value">{{ x }}</span>
                                </li>
                            </ul>
                            <span *ngIf="!isEditing && !(dispatchGroup.event_types_acknowledge_audio_alarm_list && dispatchGroup.event_types_acknowledge_audio_alarm_list.length > 0)">-</span>
                        </div>
                    </div>
                </section>

                <section *ngIf="isSelectedMarketAllowsNominationToolEnablement">
                    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_TITLE'|translate}}</h6>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_ENABLED'|translate}}</label>
                            <div class="form-check">
                                <label
                                        class="eon-checkbox-label bg-eon-red"
                                        (click)="toggleNominationToolEnabled()"
                                        [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.nomination_tool_enabled}">
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_MAX_FAULT_SECONDS_PERCENTAGE'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    [(ngModel)]="dispatchGroup.nomination_tool_max_fault_seconds_percentage">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.nomination_tool_max_fault_seconds_percentage || '-' }}</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_MAX_INVALID_ROLLUPS_PERCENTAGE'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    [(ngModel)]="dispatchGroup.nomination_tool_max_invalid_rollups_percentage">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.nomination_tool_max_invalid_rollups_percentage || '-' }}</span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_PAST_AVAILABILITY_HOURS'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    [(ngModel)]="dispatchGroup.nomination_tool_past_availability_hours">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.nomination_tool_past_availability_hours || '-' }}</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_TARGET_BID_VOLUME_MW'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    [(ngModel)]="dispatchGroup.nomination_tool_target_bid_volume_mw">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.nomination_tool_target_bid_volume_mw || '-' }}</span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_ADDITIONAL_BUFFER_POS_MW'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    [(ngModel)]="dispatchGroup.nomination_tool_additional_buffer_pos_mw">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.nomination_tool_additional_buffer_pos_mw === null ? '-' : dispatchGroup.nomination_tool_additional_buffer_pos_mw }}</span>
                        </div>

                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_ADDITIONAL_BUFFER_NEG_MW'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    [(ngModel)]="dispatchGroup.nomination_tool_additional_buffer_neg_mw">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.nomination_tool_additional_buffer_neg_mw === null ? '-' : dispatchGroup.nomination_tool_additional_buffer_neg_mw }}</span>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group col-sm-6">
                            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_ENERGY_PRICE_TARGET_MARGIN'|translate}}</label>
                            <input
                                    *ngIf="isEditing"
                                    type="number" step="any" class="form-control"
                                    disabled="disabled"
                                    style="background: #6c757d"
                                    [(ngModel)]="dispatchGroup.nomination_tool_energy_price_target_margin">
                            <span *ngIf="!isEditing"
                                  class="value">{{ dispatchGroup.nomination_tool_energy_price_target_margin }}</span>
                        </div>

                    </div>
                </section>
            </div>

            <section>
                <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ROLLUPS_ENABLED_TITLE'|translate}}</h6>

                <div class="row">
                    <div class="form-group col-sm-6">
                        <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ROLLUPS_ENABLED'|translate}}</label>
                        <div class="form-check">
                            <label
                                    class="eon-checkbox-label bg-eon-red"
                                    (click)="toggleRollupsEnabled()"
                                    [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.rollups_enabled}">
                            </label>
                        </div>
                    </div>
                </div>
            </section>

            <section>
                <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.AUTOMATIC_ALLOCATION'|translate}}</h6>

                <div class="row">
                    <div class="form-group col-sm-6">
                        <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.AUTOMATIC_ALLOCATION_ENABLED'|translate}}</label>
                        <div class="form-check">
                            <label
                                    class="eon-checkbox-label bg-eon-red"
                                    (click)="toggleAutomaticAllocationEnabled()"
                                    [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.automatic_allocation_enabled}">
                            </label>
                        </div>
                    </div>

                    <div class="form-group col-sm-6">
                        <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.AUTOMATIC_ALLOCATION_WEIGHT'|translate}}</label>
                        <input
                                *ngIf="isEditing"
                                type="number" step="any" class="form-control"
                                [(ngModel)]="dispatchGroup.automatic_allocation_weight">
                        <span *ngIf="!isEditing"
                              class="value">{{ dispatchGroup.automatic_allocation_weight || '-' }}</span>
                    </div>
                </div>

                <div class="row">
                    <div class="form-group col-sm-6">
                        <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.AUTOMATIC_ALLOCATION_NOMINATION_BUFFER_PERCENTAGE'|translate}}</label>
                        <input
                                *ngIf="isEditing"
                                type="number" step="any" class="form-control"
                                [(ngModel)]="dispatchGroup.automatic_allocation_nomination_buffer_percentage">
                        <span *ngIf="!isEditing"
                              class="value">{{ dispatchGroup.automatic_allocation_nomination_buffer_percentage || '-' }}</span>
                    </div>

                    <div class="form-group col-sm-6">
                        <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.MAX_ASSET_REALLOCATION'|translate}}</label>
                        <input
                                *ngIf="isEditing"
                                type="number" step="1" class="form-control"
                                [(ngModel)]="dispatchGroup.max_asset_reallocation">
                        <span *ngIf="!isEditing" class="value">{{ dispatchGroup.max_asset_reallocation || '-' }}</span>
                    </div>
                </div>
            </section>

            <section>
                <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP'|translate}}
                    <ng-template #extBackupInfoHtml>
                        <div [innerHTML]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_INFO_HTML'|translate"
                        ></div>
                    </ng-template>
                    <i class="fa fa-question-circle"
                       [ngbTooltip]="extBackupInfoHtml"
                       containerClass="wider-tooltip"></i>
                </h6>

                <div class="row">
                    <div class="form-group col-sm-6">
                        <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_ACTIVE'|translate}}</label>
                        <div class="form-check">
                            <label
                                    class="eon-checkbox-label bg-eon-red"
                                    (click)="toggleExtBackupActive()"
                                    [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.ext_backup_active}">
                            </label>
                        </div>
                    </div>

                    <div class="form-group col-sm-6">
                        <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_BUFFER'|translate}}</label>
                        <input
                                *ngIf="isEditing"
                                type="number" step="any" class="form-group"
                                [(ngModel)]="dispatchGroup.ext_backup_buffer">
                        <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ext_backup_buffer || '-' }}</span>
                    </div>
                </div>

                <div class="row">
                    <div class="form-group col-sm-6">
                        <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_INCREMENT_POS'|translate}}</label>
                        <input
                                *ngIf="isEditing"
                                type="number" step="any" class="form-control"
                                [(ngModel)]="dispatchGroup.ext_backup_increment_pos_mw">
                        <span *ngIf="!isEditing"
                              class="value">{{ dispatchGroup.ext_backup_increment_pos_mw || '-' }}</span>
                    </div>

                    <div class="form-group col-sm-6">
                        <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_INCREMENT_NEG'|translate}}</label>
                        <input
                                *ngIf="isEditing"
                                type="number" step="any" class="form-control"
                                [(ngModel)]="dispatchGroup.ext_backup_increment_neg_mw">
                        <span *ngIf="!isEditing"
                              class="value">{{ dispatchGroup.ext_backup_increment_neg_mw || '-' }}</span>
                    </div>
                </div>

                <div class="row">
                    <div class="form-group col-sm-6">
                        <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_THRESHOLD_POS'|translate}}</label>
                        <input
                                *ngIf="isEditing"
                                type="number" step="any" class="form-control"
                                [(ngModel)]="dispatchGroup.ext_backup_threshold_pos_mw">
                        <span *ngIf="!isEditing"
                              class="value">{{ dispatchGroup.ext_backup_threshold_pos_mw || '-' }}</span>
                    </div>

                    <div class="form-group col-sm-6">
                        <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_THRESHOLD_NEG'|translate}}</label>
                        <input
                                *ngIf="isEditing"
                                type="number" step="any" class="form-control"
                                [(ngModel)]="dispatchGroup.ext_backup_threshold_neg_mw">
                        <span *ngIf="!isEditing"
                              class="value">{{ dispatchGroup.ext_backup_threshold_neg_mw || '-' }}</span>
                    </div>
                </div>

                <div class="row">
                    <div class="form-group col-sm-6">
                        <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_NOMINATION_THRESHOLD_POS'|translate}}</label>
                        <input
                                *ngIf="isEditing"
                                type="number" step="any" class="form-control"
                                [(ngModel)]="dispatchGroup.ext_backup_nomination_threshold_pos">
                        <span *ngIf="!isEditing"
                              class="value">{{ dispatchGroup.ext_backup_nomination_threshold_pos || '-' }}</span>
                    </div>

                    <div class="form-group col-sm-6">
                        <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_NOMINATION_THRESHOLD_NEG'|translate}}</label>
                        <input
                                *ngIf="isEditing"
                                type="number" step="any" class="form-control"
                                [(ngModel)]="dispatchGroup.ext_backup_nomination_threshold_neg">
                        <span *ngIf="!isEditing"
                              class="value">{{ dispatchGroup.ext_backup_nomination_threshold_neg || '-' }}</span>
                    </div>
                </div>
            </section>

            <div *ngIf="isSelectedMarketDlm">
                <vpp-dg-dlm-params [dispatchGroup]="dispatchGroup" [isEditing]="isEditing"></vpp-dg-dlm-params>
            </div>

            <!-- Action button -->
            <ng-template [ngIf]="isEditing">
                <ul class="list-inline list-unstyled d-flex form-actions">
                    <li>
                        <button
                                (click)="submit()"
                                class="eon-button bg-eon-red">
                            <span>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.SAVE'|translate}}</span>
                        </button>
                    </li>
                    <li *ngIf="isEditing && !openInEditMode">
                        <button
                                (click)="cancelEditMode()"
                                class="eon-button bg-eon-red">
                            <span>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.CANCEL_EDIT'|translate}}</span>
                        </button>
                    </li>
                </ul>
            </ng-template>

            <ng-template [ngIf]="!isEditing && (permissions.can_create_dispatch_group || permissions.can_update_dispatch_groups)">
                <ul class="list-inline list-unstyled d-flex form-actions">
                    <li>
                        <button
                                (click)="enterEditMode()"
                                class="eon-button bg-eon-red">
                            <span>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.EDIT'|translate}}</span>
                        </button>
                    </li>
                    <li>
                        <button
                                (click)="delete()"
                                class="eon-button bg-eon-red">
                            <span>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.DELETE'|translate}}</span>
                        </button>
                    </li>
                </ul>
            </ng-template>

        </div>
    </div>
</div>

<ng-template #editBg>
    <vpp-dg-bg-form
            [availableBgNames]="availableBgNames"
            [dispatchGroupId]="dispatchGroup.id"
            [balancingGroup]="editedBalancingGroup"
            (done)="closeDispatchGroupModal(true)"
            (canceled)="closeDispatchGroupModal(false)">
    </vpp-dg-bg-form>
</ng-template>