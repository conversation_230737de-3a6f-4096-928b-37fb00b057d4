<div class="row">
    <div class="col-md-8" id="full-width-in-modal">
        <div class="tab-content light-blue" style="border: none" id="enter-dg-bg-tab">
            <span *ngIf="!embedded" class="pull-right cursor-hand" (click)="cancel($event)">
                <i class="fa fa-times"></i>
            </span>

            <h2>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_DIALOG_TITLE'|translate}}</h2>

            <div class="alerts-wrapper-static" *ngIf="errors.length">
                <ng-container *ngFor="let errmsg of errors; let ix = index;">
                    <ngb-alert
                            [dismissible]="false"
                            [type]="'error'"> {{ errmsg }}
                    </ngb-alert>
                </ng-container>
            </div>

            <div class="row">
                <div class="form-group col-sm-6">
                    <label class="required">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_START_DATE'|translate}}</label>
                    <input
                            class="form-control"
                            type="text"
                            [owlDateTime]="dt1"
                            [selectMode]="'single'"
                            [(ngModel)]="balancingGroup.start_date"
                            [owlDateTimeTrigger]="dt1"
                            (dateTimeChange)="startDateSelectedCallback($event)"
                    >
                    <owl-date-time
                        #dt1
                        (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
                        [pickerType]="'calendar'"
                        [pickerMode]="'popup'"></owl-date-time>
                </div>

                <div class="form-group col-sm-6">
                    <label class="required">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_END_DATE'|translate}}</label>
                    <input
                            class="form-control"
                            type="text"
                            [owlDateTime]="dt2"
                            [selectMode]="'single'"
                            [(ngModel)]="balancingGroup.end_date"
                            [owlDateTimeTrigger]="dt2"
                            (dateTimeChange)="endDateSelectedCallback($event)"
                    >
                    <owl-date-time
                        #dt2
                        (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
                        [pickerType]="'calendar'"
                        [pickerMode]="'popup'"></owl-date-time>
                </div>
            </div>

            <div class="row">
                <div class="form-group col-sm-12">
                    <label class="required">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_NAME'|translate}}</label>
                    <select class="form-control"
                            [(ngModel)]="balancingGroup.name"
                            id="selectBgName">
                        <option *ngFor="let n of availableBgNames" [(ngValue)]="n">{{n}}</option>
                    </select>
                </div>
            </div>

            <!-- Action button -->
            <ul class="list-inline list-unstyled d-flex form-actions">
                <li>
                    <button
                        (click)="submit()"
                        class="eon-button bg-eon-red">
                        <span>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_SAVE'|translate}}</span>
                    </button>
                </li>
            </ul>

        </div>
    </div>
</div>