import {Component, Input, OnInit, ViewChild, ViewEncapsulation} from "@angular/core";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {NotificationService} from "../../../../services/notification.service";
import {TranslateService} from "@ngx-translate/core";
import {GlobalService} from "../../../../services/global.service";
import {ConfirmService} from "../../../../services/confirm.service";
import {angularData} from "../../../../../global.export";
import {SubpoolProvider} from "../../../../providers/subpool.provider";
import {DispatchGroupProvider} from "../../../../providers/dispatch_group.provider";

@Component({
    selector: 'vpp-dispatch-groups-list',
    templateUrl: './dispatch_groups_list.component.html',
    styleUrls: ['./dispatch_groups_list.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class DispatchGroupsListComponent implements OnInit {
    showLoader = true;
    dispatchGroups = [];
    params = {
        page: 1,
        per_page: 25
    };
    page = 1;
    pageSize = 25;
    collectionSize = 0;
    editedDispatchGroup: any;
    openInEditMode = true;
    confirmDeleteMessage = '';
    bsModalRef;
    permissions: any;
    genericConfigEnabled = false

    @ViewChild('edit', { static: true }) editModal;
    @Input() selectedDgId;


    constructor(
        public translate: TranslateService,
        public globalService: GlobalService,
        private _modalService: NgbModal,
        private _notificationService: NotificationService,
        private _confirmService: ConfirmService,
        private _dispatchGroupProvider: DispatchGroupProvider
    ) {
        this.translate.get('CONFIRM.MESSAGE.DELETE_ENTRY').subscribe((x) => {
            this.confirmDeleteMessage = x;
        });
        this.permissions = angularData.permissions;
    }

    ngOnInit(): void {
        this.getDispatchGroups();
    }

    getDispatchGroups() {
        this.showLoader = true;
        this.dispatchGroups = [];
        let params = this.selectedDgId ? {dg_id : this.selectedDgId, ...this.params} : {...this.params};
        this._dispatchGroupProvider.getDispatchGroupsList(params).subscribe(
            res => {
                this.dispatchGroups = res.dispatch_groups;
                if (this.dispatchGroups.length > 0) {
                    this.genericConfigEnabled = this.dispatchGroups[0].generic_config_enabled;
                }
                this.collectionSize = res.total_entries;
                if (""+res.page !== ""+this.page) {
                    console.log('changing page from', this.page, 'to', res.page);
                    this.params.page = res.page;
                    this.page = res.page;
                }
                this.showLoader = false;

                this.openSelectedDg();
            }, err => {
                this.showLoader = false;
                console.log('Failed to load dispatch groups', err);
            }
        );
    }

    openSelectedDg() {
        if (this.selectedDgId) {
            let dg = this.dispatchGroups.find(x => x.id == this.selectedDgId);
            if (dg) {
                this.showDispatchGroup(dg);
            } else {
                console.log('dg', this.selectedDgId, 'not found');
            }
            this.selectedDgId = null;
        }
    }

    pageChangedCallback(page) {
        console.log('page changed from', this.params.page, 'to', page);
        this.params.page = page;
        this.selectedDgId = null;
        this.getDispatchGroups();
    }

    pageSizeChangedCallback(pageSize) {
        this.params.per_page = pageSize;
        this.params.page = 1;
        this.selectedDgId = null;
        this.getDispatchGroups();
    }

    showDispatchGroup(x) {
        this.editedDispatchGroup = { ...x};
        this.openInEditMode = false;
        this.bsModalRef = this._modalService.open(this.editModal, { size: 'lg' });
    }

    editDispatchGroup(x) {
        this.editedDispatchGroup = { ...x};
        this.openInEditMode = true;
        this.bsModalRef = this._modalService.open(this.editModal, { size: 'lg' });
    }

    closeDispatchGroupModal(reload: boolean = true) {
        this.bsModalRef.close();
        if (reload) {
            this.getDispatchGroups();
        }
    }

    deleteDispatchGroup(x) {
        this._confirmService.confirm({
            message: this.confirmDeleteMessage,
        }).then(() => {
            this._deleteConfirmedDispatchGroup(x);
        }, () => {
        });
    }

    _deleteConfirmedDispatchGroup(x) {
        this._dispatchGroupProvider
            .deleteDispatchGroup(x.id)
            .subscribe(
                (res) => {
                    if (res.success == true) {
                        this._notificationService.success({ text: 'SUCCESS'});
                    } else {
                        this._notificationService.error({ text: JSON.stringify(res.error)});
                    }
                    this.getDispatchGroups();
                },
                (err) => {
                    console.log('Failed to delete', err);
                    this._notificationService.error({ text: JSON.stringify(err)});
                });
    }

}