import {Component, Input, OnInit, SimpleChanges, ViewEncapsulation} from '@angular/core';
import {TranslateService} from "@ngx-translate/core";
import {GlobalService} from "../../../../services/global.service";
import * as moment from "moment-timezone";
import {DispatchGroupProvider} from "../../../../providers/dispatch_group.provider";
import {angularData} from "../../../../../global.export";

@Component({
    selector: 'vpp-generic-dg-config-notifications',
    templateUrl: './generic_dg_config_notifications.component.html',
    styleUrls: ['./generic_dg_config_notifications.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class GenericDgConfigNotificationsComponent implements OnInit {
  @Input() dispatchGroup: any;
  @Input() isEditing: any;

  constructor(
      public translate: TranslateService,
      public globalService: GlobalService,
  ) {

  }

  ngOnInit() {
  }

  ngOnChanges(changes: SimpleChanges) {
    if(changes['dispatchGroup']) {
      if (this.dispatchGroup['has_generic_config']) {
        //set array objects for ng-multiselect
        this.setNotificationsOptions();
      }
    }
  }

  findOption(id, options) {
    return options.find(e => e.id == id);
  }

  notificationsOptions = [
    {id: 'flexTooLow', name: 'FlexTooLow'},
    {id: 'merlinState', name: 'MerlinState'},
    {id: 'overDeliveryNegative', name: 'OverDeliveryNegative'},
    {id: 'overDeliveryPositive', name: 'OverDeliveryPositive'},
    {id: 'setpointNotReached', name: 'SetpointNotReached'},
    {id: 'setpointReachable', name: 'SetpointReachable'},
    {id: 'marketNotice', name: 'MarketNotice'},
  ];
  setNotificationsOptions() {
    if (this.dispatchGroup.notifications && !this.isArrayWithIds(this.dispatchGroup.notifications)) {
      this.dispatchGroup.notifications = this.dispatchGroup.notifications.map((x) =>
          this.findOption(x, this.notificationsOptions));
    }
  }

  showParametersFor(attributeName, value) {
    if (this.dispatchGroup[attributeName]) {
      if (Array.isArray(this.dispatchGroup[attributeName])) {
        return this.dispatchGroup[attributeName].some(o => o.id === value);
      } else {
        return this.dispatchGroup[attributeName].id == value;
      }
    } else {
      return false;
    }
  }

  isArrayWithIds(value) {
    let isArray = value && Array.isArray(value)
    if (isArray) {
      if (value.length == 0) {
        return true
      } else {
        return value[0].hasOwnProperty("id")
      }
    } else {
      return false
    }
  }

  toggleCheckboxAttribute(dgAttrName) {
    if (this.isEditing) {
      this.dispatchGroup[dgAttrName] = !this.dispatchGroup[dgAttrName];
    }
  }

  dropdownSettings(singleSelection, allowSearchFilter) {
    return {
      idField: 'id',
      textField: 'name',
      singleSelection: singleSelection,
      selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
      unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
      searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
      noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
      itemsShowLimit: 10,
      allowSearchFilter: allowSearchFilter
    };
  };

  optionNames(optionsArray) {
    if (optionsArray) {
      return optionsArray.map((x) => x.name).join(", ");
    } else {
      return '';
    }
  }
}
