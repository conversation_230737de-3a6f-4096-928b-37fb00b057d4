import {Component, Input, OnInit, SimpleChanges, ViewEncapsulation} from '@angular/core';
import {TranslateService} from "@ngx-translate/core";
import {GlobalService} from "../../../../services/global.service";
import * as moment from "moment-timezone";
import {angularData} from "../../../../../global.export";

@Component({
    selector: 'vpp-dg-dlm-params',
    templateUrl: './dg_dlm_params.component.html',
    styleUrls: ['./dg_dlm_params.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class DgDlmParamsComponent implements OnInit {
  @Input() dispatchGroup: any;
  @Input() isEditing: any;

  constructor(
      public translate: TranslateService,
      public globalService: GlobalService,
  ) {

  }

  ngOnInit() {
    console.log("### DLM PARAMS INIT", this.dispatchGroup)
  }

  ngOnChanges(changes: SimpleChanges) {
    if(changes['dispatchGroup']) {
      if (!this.dispatchGroup['control_windows']) {
        this.dispatchGroup['control_windows'] = [{start_time: "", end_time: ""}]
      }
    }
  }

  changeGroupingRule(event, data) {
    // if (event) {
    //   event.preventDefault();
    //   event.stopPropagation();
    // }
    //TODO: add code here if needded
  }

  removeControlWindow(index) {
    this.dispatchGroup['control_windows'].splice(index, 1);
  }

  addControlWindow(index) {
    this.dispatchGroup['control_windows'].splice(index, 0, {start_time: "", end_time: ""})
  }

  groupingRuleIs(ruleName) {
    return this.dispatchGroup.grouping_rule == ruleName;
  }

  findOption(id, options) {
    return options.find(e => e.id == id)
  }

  dropdownSettings(singleSelection, allowSearchFilter) {
    return {
      idField: 'id',
      textField: 'name',
      singleSelection: singleSelection,
      selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
      unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
      searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
      noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
      itemsShowLimit: 10,
      allowSearchFilter: allowSearchFilter
    }
  };

  optionNames(optionsArray) {
    if (optionsArray)
      return optionsArray.map((x) => x.name).join(", ")
    else
      return ''
  }
}

