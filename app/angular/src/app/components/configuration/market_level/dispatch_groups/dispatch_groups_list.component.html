<div class="container-fluid">
    <div class="row">
        <div class="col">
            <h2>{{ 'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LIST.TITLE' | translate }}</h2>

            <div class="table-filter d-flex"></div>

            <table class="table table-dispatch-groups table-auction-results table-striped table-bg-turquoise">
                <tr>
                    <th class="name">
                        {{ 'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.NAME' | translate }}</th>
                    <th class="tso">
                        {{ 'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.TSO' | translate }}</th>
                    <th class="market">
                        {{ 'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.MARKET' | translate }}</th>
                    <th class="balancing-group">
                        {{ 'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.BALANCING_GROUP' | translate }}</th>
                    <th class="generic-config" *ngIf="genericConfigEnabled">
                        {{ 'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.GENERIC_CONFIG' | translate }}</th>
                    <th class="trading-nomination-enabled">
                        {{ 'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.TRADING_NOMINATION_ENABLED' | translate }}</th>
                    <th class="automatic-allocation-enabled">
                        {{ 'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.AUTOMATIC_ALLOCATION_ENABLED' | translate }}</th>
                    <th class="text-center actions-column">
                        {{ 'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.ACTIONS' | translate }}</th>
                </tr>
                <tr *ngIf="showLoader">
                    <td colspan="6" class="text-center vertical-center">
                        <vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
                    <td>
                </tr>
                <ng-container *ngFor="let x of dispatchGroups">
                    <tr>
                        <input type="hidden" value="{{ x.id }}">
                        <td class="name">{{ x.name }}</td>
                        <td class="tso">{{ x.tso.name }}</td>
                        <td class="market">{{ x.market.name }}</td>
                        <td class="balancing-group">{{ x.bg_label }}</td>
                        <td class="generic-config" *ngIf="genericConfigEnabled">
                            <span class="eon-checkbox-label non-clickable bg-eon-red"
                                  *ngIf="genericConfigEnabled"
                                  [ngClass]="x.has_generic_config ? 'checked' : ''"></span>
                        </td>
                        <td class="trading-nomination-enabled">
                            <span class="eon-checkbox-label non-clickable bg-eon-red"
                                  *ngIf="x.nomination_tool_enabled"
                                  [ngClass]="x.nomination_tool_enabled ? 'checked' : ''"></span>
                        </td>
                        <td class="automatic-allocation-enabled">
                            <span class="eon-checkbox-label non-clickable bg-eon-red"
                                  *ngIf="x.automatic_allocation_enabled"
                                  [ngClass]="x.automatic_allocation_enabled ? 'checked' : ''"></span>
                        </td>
                        <td class="text-center actions-column">
                            <ul class="list-inline list-unstyled">
                                <li *ngIf="permissions.can_see_dispatch_groups"><span (click)="showDispatchGroup(x)" style="display: inline-block"><i class="fa fa-eye"></i></span> </li>
                                <li *ngIf="permissions.can_update_dispatch_groups"><span (click)="editDispatchGroup(x)" style="display: inline-block"><i class="fa fa-pencil"></i></span> </li>
                                <li *ngIf="permissions.can_delete_dispatch_groups"><span (click)="deleteDispatchGroup(x)" style="display: inline-block"><i class="fa fa-trash"></i></span></li>
                            </ul>
                        </td>
                    </tr>
                </ng-container>
            </table>
            <div class="d-flex justify-content-between">
                <ngb-pagination
                        [collectionSize]="collectionSize"
                        [(page)]="page"
                        [ellipses]="true"
                        [maxSize]="5"
                        (pageChange)="pageChangedCallback($event)"
                        [pageSize]="pageSize">
                </ngb-pagination>

                <select
                        class="custom-select"
                        style="width: auto"
                        [(ngModel)]="pageSize"
                        (ngModelChange)="pageSizeChangedCallback($event)">
                    <option [ngValue]="25">25 {{ 'PAGINATION.PER_PAGE' | translate }}</option>
                    <option [ngValue]="50">50 {{ 'PAGINATION.PER_PAGE' | translate }}</option>
                    <option [ngValue]="75">75 {{ 'PAGINATION.PER_PAGE' | translate }}</option>
                </select>
            </div>
        </div>
    </div>
</div>

<ng-template #edit>
    <vpp-dispatch-group-form
            [dispatchGroup]="editedDispatchGroup"
            [openInEditMode]="openInEditMode"
            (done)="closeDispatchGroupModal(true)"
            (canceled)="closeDispatchGroupModal(false)">
    </vpp-dispatch-group-form>
</ng-template>
