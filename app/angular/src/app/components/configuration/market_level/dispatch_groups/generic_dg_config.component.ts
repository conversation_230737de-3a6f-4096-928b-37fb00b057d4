import {Component, Input, OnInit, SimpleChanges, ViewEncapsulation} from '@angular/core';
import {TranslateService} from "@ngx-translate/core";
import {GlobalService} from "../../../../services/global.service";
import * as moment from "moment-timezone";
import {DispatchGroupProvider} from "../../../../providers/dispatch_group.provider";
import {angularData} from "../../../../../global.export";

@Component({
    selector: 'vpp-generic-dg-config',
    templateUrl: './generic_dg_config.component.html',
    styleUrls: ['./generic_dg_config.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class GenericDgConfigComponent implements OnInit {
  @Input() dispatchGroup: any;
  @Input() dsos: any;
  @Input() isEditing: any;
  @Input() isSelectedTsoNationalGrid: any;
  @Input() isSelectedMarketIntraday: any;
  @Input() isSelectedMarketDynamicServices: any;


  constructor(
      public translate: TranslateService,
      public globalService: GlobalService,
  ) {

  }

  ngOnInit() {
    console.log("### GENERIC DG INIT", this.dispatchGroup);
  }

  ngOnChanges(changes: SimpleChanges) {
    if(changes['dsos']) {
      // this.signalsDsoForSignalsOptions = this.dsos.map((x) => ({id: x, name: x}));
      this.signalsDsoForSignalsOptions = this.dsos;
    }
    if(changes['dispatchGroup']) {
      console.log("### GENERIC DG CHANGES", this.dispatchGroup)
      if (this.dispatchGroup['has_generic_config']) {
        //set array objects for ng-multiselect
        this.setAssetActivationTypeOptions();
        this.setStateOfChargeManagementOptions();
        this.setAdsStrategyOptions();
        this.setAdsStrategyOnOffSignalOptions();
        this.setCrossDgLinksOptions();
        this.setDgActivationTypeOptions();
        this.setDispatchCommandsOptions();
        this.setDispatchSourceOptions();
        this.setRampAdjustmentStrategyOptions();
        this.setRedispatchTriggersOptions();
        this.setSetpointValidationNominationIntervalValidationOptions();
        this.setSignalsOutputSignalsOptions();
        this.setSignalsDsoForSignalsOptions();
      }
    }
  }

  findOption(id, options) {
    return options.find(e => e.id == id);
  }

  assetActivationTypeOptions = [
    {id: 'activationRange', name: 'ActivationRange'},
    {id: 'basepoint', name: 'Basepoint'},
    {id: 'setpoint', name: 'Setpoint'},
  ];
  setAssetActivationTypeOptions() {
    if (this.dispatchGroup.asset_activation_type && !this.isArrayWithIds(this.dispatchGroup.asset_activation_type)) {
      this.dispatchGroup.asset_activation_type = this.dispatchGroup.asset_activation_type.map((x) =>
          this.findOption(x, this.assetActivationTypeOptions));
    }
  }

  stateOfChargeManagementOptions = [
    {id: "dynamicContainment", name: "Dynamic Containment"},
    {id: "dynamicContainmentLow", name: "Dynamic Containment Low"},
    {id: "optimizedDc", name: "Variable SoE"},
    {id: "volumeBaseline", name: "Volume Baselining"},
    {id: "fcrNl", name: "FCR NL"},
    {id: "optimizationPassThrough", name: "Optimization Pass Through"}
  ]
  setStateOfChargeManagementOptions() {
    if (this.dispatchGroup.state_of_charge_management && !this.isArrayWithIds(this.dispatchGroup.state_of_charge_management)) {
      this.dispatchGroup.state_of_charge_management = this.dispatchGroup.state_of_charge_management.map((x) =>
          this.findOption(x, this.stateOfChargeManagementOptions));
    }
  }

  adsStrategyOptions = [
    {id: 'byPrice', name: 'By Price'},
    {id: 'byPriceAndSoC', name: 'By Price and State of Charge'},
    {id: 'byAvailableFlex', name: 'By Available Flex'},
    {id: 'proRata', name: 'Pro Rata'},
    {id: 'onOff', name: 'On Off'},
    {id: 'v2gExternalOptimizer', name: 'V2G External Optimizer'},
    {id: 'nominationPassThrough', name: 'Nomination Pass Through'},
  ];
  setAdsStrategyOptions() {
    if (this.dispatchGroup.ads_strategy && !this.isArrayWithIds(this.dispatchGroup.ads_strategy)) {
      this.dispatchGroup.ads_strategy = this.dispatchGroup.ads_strategy.map((x) =>
          this.findOption(x, this.adsStrategyOptions));
    }
  }

  crossDgLinksOptions = [
    {id:'crossPlanPropagation', name: 'CrossPlanPropagation'}
  ];
  setCrossDgLinksOptions() {
    if (this.dispatchGroup.cross_dg_links && !this.isArrayWithIds(this.dispatchGroup.cross_dg_links)) {
      this.dispatchGroup.cross_dg_links = this.dispatchGroup.cross_dg_links.map((x) =>
          this.findOption(x, this.crossDgLinksOptions));
    }
  }

  dgActivationTypeOptions = [
    {id: "activationRange", name: "ActivationRange"},
    {id: "onOff", name: "OnOff"},
    {id: "relative", name: "Relative"},
  ];
  setDgActivationTypeOptions() {
    if (this.dispatchGroup.dg_activation_type && !this.isArrayWithIds(this.dispatchGroup.dg_activation_type)) {
      this.dispatchGroup.dg_activation_type = this.dispatchGroup.dg_activation_type.map((x) =>
          this.findOption(x, this.dgActivationTypeOptions));
    }
  }

  dispatchCommandsOptions = [
    {id: 'dispatchNow', name: 'DispatchNow'},
    {id: 'plannedDispatch', name: 'PlannedDispatch'},
    {id: 'cancelPlannedDispatch', name: 'CancelPlannedDispatch'},
    {id: 'rtnAtEndOfNomination', name: 'RTNAtEndOfNomination'},
    {id: 'dispatchConfirmation', name: 'DispatchConfirmation'},
    {id: 'plannedDispatchWithEnd', name: 'PlannedDispatchWithEnd'},
  ];
  setDispatchCommandsOptions() {
    if (this.dispatchGroup.dispatch_commands && !this.isArrayWithIds(this.dispatchGroup.dispatch_commands)) {
      this.dispatchGroup.dispatch_commands = this.dispatchGroup.dispatch_commands.map((x) =>
          this.findOption(x, this.dispatchCommandsOptions));
    }
  }

  dispatchSourceOptions = [
    {id: 'followSchedule', name: 'FollowSchedule'},
    {id: 'merlin', name: 'Merlin'},
    {id: 'nominatedVolume', name: 'NominatedVolume'},
    {id: 'sally', name: 'Sally'},
    {id: 'ui', name: 'UI'},
    {id: 'residualShape', name: 'ResidualShape'},
    {id: 'priceTrigger', name: 'PriceTrigger'},
    {id: 'nlAfrr', name: 'NL-aFRR'},
  ];
  setDispatchSourceOptions() {
    if (this.dispatchGroup.dispatch_source && !this.isArrayWithIds(this.dispatchGroup.dispatch_source)) {
      this.dispatchGroup.dispatch_source = this.dispatchGroup.dispatch_source.map((x) =>
          this.findOption(x, this.dispatchSourceOptions));
    }
  }

  rampAdjustmentStrategyOptions = [
    {id: 'allAtOnceOnRampUp', name: 'AllAtOnceOnRampUp'},
    {id: 'allAtOnceOnRampUpWithIndirectSteeringAdjustments', name: 'AllAtOnceOnRampUp With IndirectSteeringAdjustments'},
    {id: 'individuallyOnRampUp', name: 'IndividuallyOnRampUp'},
    {id: 'noRampAdjustment', name: 'NoRampAdjustment'},
  ];
  setRampAdjustmentStrategyOptions() {
    if (this.dispatchGroup.ramp_adjustment_strategy && !this.isArrayWithIds(this.dispatchGroup.ramp_adjustment_strategy)) {
      this.dispatchGroup.ramp_adjustment_strategy = this.dispatchGroup.ramp_adjustment_strategy.map((x) =>
          this.findOption(x, this.rampAdjustmentStrategyOptions));
    }
  }

  redispatchTriggersOptions = [
    {id: 'allocationChange', name: 'AllocationChange'},
    {id: 'assetAvailableFlexChange', name: 'AssetAvailableFlexChange'},
    {id: 'assetAvailableFlexChangeSinceActivation', name: 'AssetAvailableFlexChangeSinceActivation'},
    {id: 'assetFaultChange', name: 'AssetFaultChange'},
    {id: 'backupAssetActivationChange', name: 'BackupAssetActivationChange'},
    {id: 'dgTotalDeviation', name: 'DGTotalDeviation'},
  ];
  setRedispatchTriggersOptions() {
    if (this.dispatchGroup.redispatch_triggers && !this.isArrayWithIds(this.dispatchGroup.redispatch_triggers)) {
      this.dispatchGroup.redispatch_triggers = this.dispatchGroup.redispatch_triggers.map((x) =>
          this.findOption(x, this.redispatchTriggersOptions));
    }
  }

  setpointValidationNominationIntervalValidationOptions = [
    {id: 'mrlSetpointValidation', name: 'MRLSetpointValidation'},
    {id: 'nominationExtendedIntervalValidation', name: 'NominationExtendedIntervalValidation'},
    {id: 'nominationIntervalOnlyValidation', name: 'NominationIntervalOnlyValidation'}
  ];
  setSetpointValidationNominationIntervalValidationOptions() {
    if (this.dispatchGroup.setpoint_validation_nomination_interval_validation && !this.isArrayWithIds(this.dispatchGroup.setpoint_validation_nomination_interval_validation)) {
      this.dispatchGroup.setpoint_validation_nomination_interval_validation = this.dispatchGroup.setpoint_validation_nomination_interval_validation.map((x) =>
          this.findOption(x, this.setpointValidationNominationIntervalValidationOptions));
    }
  }

  signalsOutputSignalsOptions = [
    {id: 'AssetTotalSetpoint', name: 'AssetTotalSetpoint'},
    {id: 'SCState', name: 'SCState'},
    {id: 'VPPTotalGeneration', name: 'VPPTotalGeneration'},
    {id: 'VPPTotalGeneration4S', name: 'VPPTotalGeneration4S'},
    {id: 'VPPDeliveredFlex', name: 'VPPDeliveredFlex'},
    {id: 'VPPDeliveredFlexPositive', name: 'VPPDeliveredFlexPositive'},
    {id: 'VPPDeliveredFlexNegative', name: 'VPPDeliveredFlexNegative'},
    {id: 'MRMinusBand', name: 'MRMinusBand'},
    {id: 'MRPlusBand', name: 'MRPlusBand'},
    {id: 'TradedFlexPositive', name: 'TradedFlexPositive'},
    {id: 'TradedFlexNegative', name: 'TradedFlexNegative'},
    {id: 'BasePoint', name: 'BasePoint'},
    {id: 'PrecedingBasePoint', name: 'PrecedingBasePoint'},
    {id: 'PrecedingBasePoint4S', name: 'PrecedingBasePoint4S'},
    {id: 'BasePointAverage', name: 'BasePointAverage'},
    {id: 'HeadroomResponse', name: 'HeadroomResponse'},

    {id: 'UpperPowerLimit', name: 'UpperPowerLimit'},
    {id: 'LowerPowerLimit', name: 'LowerPowerLimit'},

    {id: 'RampUpRate', name: 'RampUpRate'},
    {id: 'RampDownRate', name: 'RampDownRate'},

    {id: 'SetpointMirror', name: 'SetpointMirror'},

    {id: 'SubPoolIndex', name: 'SubPoolIndex'},

    {id: 'PRLUsableEnergyNegative', name: 'PRLUsableEnergyNegative'},
    {id: 'PRLUsableEnergyPositive', name: 'PRLUsableEnergyPositive'},
    {id: 'MinuteAverageActivePower', name: 'MinuteAverageActivePower'},
    {id: 'Frequency', name: 'Frequency'},
    {id: 'PrequalificationPositive', name: 'PrequalificationPositive'},
    {id: 'PrequalificationNegative', name: 'PrequalificationNegative'},
  ];
  setSignalsOutputSignalsOptions() {
    if (this.dispatchGroup.signals_output_signals && !this.isArrayWithIds(this.dispatchGroup.signals_output_signals)) {
      this.dispatchGroup.signals_output_signals = this.dispatchGroup.signals_output_signals.map((x) =>
          this.findOption(x, this.signalsOutputSignalsOptions));
    }
  }

  signalsDsoForSignalsOptions = []
  setSignalsDsoForSignalsOptions() {
    if (this.dispatchGroup.signals_dso_for_signals && !this.isArrayWithIds(this.dispatchGroup.signals_dso_for_signals)) {
      this.dispatchGroup.signals_dso_for_signals = this.dispatchGroup.signals_dso_for_signals.map((x) =>
          this.findOption(x, this.signalsDsoForSignalsOptions));
    }
  }

  adsStrategyOnOffSignalOptions = [
    {id: "availableFlex", name: "Available Flex"},
    {id: "installedPower", name: "Installed Power"},
  ];
  setAdsStrategyOnOffSignalOptions() {
    if (this.dispatchGroup.ads_strategy_on_off_signal && !this.isArrayWithIds(this.dispatchGroup.ads_strategy_on_off_signal)) {
      this.dispatchGroup.ads_strategy_on_off_signal = this.dispatchGroup.ads_strategy_on_off_signal.map((x) =>
          this.findOption(x, this.adsStrategyOnOffSignalOptions));
    }
  }

  showParametersFor(attributeName, value) {
    if (this.dispatchGroup[attributeName]) {
      if (Array.isArray(this.dispatchGroup[attributeName])) {
        return this.dispatchGroup[attributeName].some(o => o.id === value);
      } else {
        return this.dispatchGroup[attributeName].id == value;
      }
    } else {
      return false;
    }
  }

  isArrayWithIds(value) {
    let isArray = value && Array.isArray(value)
    if (isArray) {
      if (value.length == 0) {
        return true
      } else {
        return value[0].hasOwnProperty("id")
      }
    } else {
      return false
    }
  }

  toggleCheckboxAttribute(dgAttrName) {
    if (this.isEditing) {
      this.dispatchGroup[dgAttrName] = !this.dispatchGroup[dgAttrName];
    }
  }

  dropdownSettings(singleSelection, allowSearchFilter) {
    return {
      idField: 'id',
      textField: 'name',
      singleSelection: singleSelection,
      selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
      unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
      searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
      noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
      itemsShowLimit: 10,
      allowSearchFilter: allowSearchFilter
    };
  };

  optionNames(optionsArray) {
    if (optionsArray) {
      return optionsArray.map((x) => x.name).join(", ");
    } else {
      return '';
    }
  }
}
