<div class="row" *ngIf="isSelectedTsoNationalGrid && isSelectedMarketIntraday">
    <div class="form-group col-sm-6">
        <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.EXPORT_ENTRADER'|translate}}</label>
        <div class="form-check">
            <label
                class="eon-checkbox-label bg-eon-red"
                (click)="toggleCheckboxAttribute('export_entrader')"
                [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.export_entrader}">
            </label>
        </div>
    </div>
    <div class="form-group col-sm-6" *ngIf="dispatchGroup.export_entrader">
        <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ENTRADER_UPLOAD_FOLDER'|translate}}</label>
        <input
                *ngIf="isEditing"
                type="text"
                class="form-control"
                [(ngModel)]="dispatchGroup.entrader_upload_folder">
        <span *ngIf="!isEditing" class="value">{{ dispatchGroup.entrader_upload_folder || '-' }}</span>
    </div>
</div>
<div class="row" *ngIf="isSelectedTsoNationalGrid && isSelectedMarketIntraday && dispatchGroup.export_entrader">
    <div class="form-group col-sm-6">
        <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ENTRADER_TRESHOLD_UPDATE'|translate}}</label>
        <input
            *ngIf="isEditing"
            [(ngModel)]="dispatchGroup.entrader_treshold_update"
            type="number"
            class="form-control"
            id="entrader_treshold_update">
        <span *ngIf="!isEditing" class="value">{{ dispatchGroup.entrader_treshold_update}}</span>
    </div>
    <div class="form-group col-sm-6">
        <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ENTRADER_LEAD_TIME_UPDATE'|translate}}</label>
        <input
            *ngIf="isEditing"
            [(ngModel)]="dispatchGroup.entrader_lead_time_update"
            type="number"
            class="form-control"
            id="entrader_lead_time_update">
        <span *ngIf="!isEditing" class="value">{{ dispatchGroup.entrader_lead_time_update}}</span>
    </div>
</div>
<section>
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.BASIC_BEHAVIOR'|translate}}</h6>

    <div class="row">
        <div class="form-group col-sm-4">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.HAS_EXCLUSIVE_BEHAVIOUR'|translate}}</label>
            <div class="form-check">
                <label
                    class="eon-checkbox-label bg-eon-red"
                    (click)="toggleCheckboxAttribute('has_exclusive_behaviour')"
                    [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.has_exclusive_behaviour}">
                </label>
            </div>
        </div>
        <div class="form-group col-sm-4">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.HAS_NOMINATION'|translate}}</label>
            <div class="form-check">
                <label
                    class="eon-checkbox-label bg-eon-red"
                    (click)="toggleCheckboxAttribute('has_nomination')"
                    [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.has_nomination}">
                </label>
            </div>
        </div>
        <div class="form-group col-sm-4">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.HAS_PRECEDING_BASEPOINT'|translate}}</label>
            <div class="form-check">
                <label
                    class="eon-checkbox-label bg-eon-red"
                    (click)="toggleCheckboxAttribute('has_preceding_basepoint')"
                    [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.has_preceding_basepoint}">
                </label>
            </div>
        </div>
    </div>
</section>

<section>
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ASSET_ACTIVATION'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ASSET_ACTIVATION_TYPE'|translate}}</label>
            <ng-multiselect-dropdown
                *ngIf="isEditing"
                [placeholder]="('COMPONENTS.SELECT'|translate)"
                [data]="assetActivationTypeOptions"
                [(ngModel)]="dispatchGroup.asset_activation_type"
                [settings]="dropdownSettings(true, false)">
            </ng-multiselect-dropdown>
            <span *ngIf="!isEditing" class="value">{{ optionNames(dispatchGroup.asset_activation_type) }}</span>
        </div>
    </div>
    <section *ngIf="!isSelectedMarketDynamicServices && showParametersFor('asset_activation_type', 'activationRange')">
        <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.FREQUENCY_LOCAL_STEERING_PARAMETERS'|translate}}</h6>
        <div class="row">
            <div class="form-group col-sm-6" *ngIf="!isSelectedMarketDynamicServices && showParametersFor('asset_activation_type', 'activationRange')">
                <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.AAT_AR_FLSP_DEAD_BAND_POS'|translate}}</label>
                <input
                    *ngIf="isEditing"
                    [(ngModel)]="dispatchGroup.aat_ar_flsp_dead_band_pos"
                    type="number"
                    class="form-control"
                    id="aat_ar_flsp_dead_band_pos">
                <span *ngIf="!isEditing" class="value">{{ dispatchGroup.aat_ar_flsp_dead_band_pos }}</span>
            </div>
            <div class="form-group col-sm-6" *ngIf="!isSelectedMarketDynamicServices && showParametersFor('asset_activation_type', 'activationRange')">
                <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.AAT_AR_FLSP_DEAD_BAND_NEG'|translate}}</label>
                <input
                    *ngIf="isEditing"
                    [(ngModel)]="dispatchGroup.aat_ar_flsp_dead_band_neg"
                    type="number"
                    class="form-control"
                    id="aat_ar_flsp_dead_band_neg">
                <span *ngIf="!isEditing" class="value">{{ dispatchGroup.aat_ar_flsp_dead_band_neg }}</span>
            </div>
        </div>
        <div class="row">
            <div class="form-group col-sm-6" *ngIf="!isSelectedMarketDynamicServices && showParametersFor('asset_activation_type', 'activationRange')">
                <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.AAT_AR_FLSP_MAX_FREQUENCY_DEVIATION_POS'|translate}}</label>
                <input
                    *ngIf="isEditing"
                    [(ngModel)]="dispatchGroup.aat_ar_flsp_max_frequency_deviation_pos"
                    type="number"
                    class="form-control"
                    id="aat_ar_flsp_max_frequency_deviation_pos">
                <span *ngIf="!isEditing" class="value">{{ dispatchGroup.aat_ar_flsp_max_frequency_deviation_pos }}</span>
            </div>
            <div class="form-group col-sm-6" *ngIf="!isSelectedMarketDynamicServices && showParametersFor('asset_activation_type', 'activationRange')">
                <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.AAT_AR_FLSP_MAX_FREQUENCY_DEVIATION_NEG'|translate}}</label>
                <input
                    *ngIf="isEditing"
                    [(ngModel)]="dispatchGroup.aat_ar_flsp_max_frequency_deviation_neg"
                    type="number"
                    class="form-control"
                    id="aat_ar_flsp_max_frequency_deviation_neg">
                <span *ngIf="!isEditing" class="value">{{ dispatchGroup.aat_ar_flsp_max_frequency_deviation_neg }}</span>
            </div>
        </div>
        <div class="row">
            <div class="form-group col-sm-6" *ngIf="!isSelectedMarketDynamicServices && showParametersFor('asset_activation_type', 'activationRange')">
                <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.AAT_AR_FLSP_HIGH_KNEE_JOINT'|translate}}</label>
                <input
                    *ngIf="isEditing"
                    [(ngModel)]="dispatchGroup.aat_ar_flsp_high_knee_joint"
                    type="number"
                    class="form-control"
                    id="aat_ar_flsp_high_knee_joint">
                <span *ngIf="!isEditing" class="value">{{ dispatchGroup.aat_ar_flsp_high_knee_joint }}</span>
            </div>
            <div class="form-group col-sm-6" *ngIf="!isSelectedMarketDynamicServices && showParametersFor('asset_activation_type', 'activationRange')">
                <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.AAT_AR_FLSP_LOW_KNEE_JOINT'|translate}}</label>
                <input
                    *ngIf="isEditing"
                    [(ngModel)]="dispatchGroup.aat_ar_flsp_low_knee_joint"
                    type="number"
                    class="form-control"
                    id="aat_ar_flsp_low_knee_joint">
                <span *ngIf="!isEditing" class="value">{{ dispatchGroup.aat_ar_flsp_low_knee_joint }}</span>
            </div>
        </div>
    </section>
    <section>
        <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.STATE_OF_CHARGE_MANAGEMENT'|translate}}</h6>
        <div class="row">
            <div class="form-group col-sm-6">
                <label [ngClass]="isEditing ? '' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.STATE_OF_CHARGE_MANAGEMENT'|translate}}</label>
                <ng-multiselect-dropdown
                    *ngIf="isEditing"
                    [placeholder]="('COMPONENTS.SELECT'|translate)"
                    [data]="stateOfChargeManagementOptions"
                    [(ngModel)]="dispatchGroup.state_of_charge_management"
                    [settings]="dropdownSettings(true, false)">
                </ng-multiselect-dropdown>
                <span *ngIf="!isEditing" class="value">{{ optionNames(dispatchGroup.state_of_charge_management) || '-'}}</span>
            </div>
        </div>
        <section *ngIf="showParametersFor('state_of_charge_management', 'dynamicContainment') || showParametersFor('state_of_charge_management', 'dynamicContainmentLow') || showParametersFor('state_of_charge_management', 'optimizedDc')">
            <h6 *ngIf="showParametersFor('state_of_charge_management', 'dynamicContainmentLow')">
                {{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DYNAMIC_CONTAINMENT_LOW'|translate}}
            </h6>
            <h6 *ngIf="showParametersFor('state_of_charge_management', 'dynamicContainment')">
                {{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DYNAMIC_CONTAINMENT'|translate}}
            </h6>
            <h6 *ngIf="showParametersFor('state_of_charge_management', 'optimizedDc')">
                {{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.VARIABLE_SOE'|translate}}
            </h6>
            <div class="row">
                <div class="form-group col-sm-6" *ngIf="showParametersFor('state_of_charge_management', 'dynamicContainment') || showParametersFor('state_of_charge_management', 'dynamicContainmentLow')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_TARGET_STATE_OF_CHARGE_LOW'|translate}}</label>
                    <input
                        *ngIf="isEditing"
                        [(ngModel)]="dispatchGroup.scm_dc_target_state_of_charge_low"
                        type="number"
                        class="form-control"
                        id="scm_dc_target_state_of_charge_low">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_dc_target_state_of_charge_low }}</span>
                </div>
                <div class="form-group col-sm-6" *ngIf="showParametersFor('state_of_charge_management', 'dynamicContainment')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_TARGET_STATE_OF_CHARGE_HIGH'|translate}}</label>
                    <input
                        *ngIf="isEditing"
                        [(ngModel)]="dispatchGroup.scm_dc_target_state_of_charge_high"
                        type="number"
                        class="form-control"
                        id="scm_dc_target_state_of_charge_high">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_dc_target_state_of_charge_high }}</span>
                </div>
                <div class="form-group col-sm-6" *ngIf="showParametersFor('state_of_charge_management', 'dynamicContainmentLow')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_FOOT_ROOM'|translate}}</label>
                    <input
                        *ngIf="isEditing"
                        [(ngModel)]="dispatchGroup.scm_dc_foot_room"
                        type="number"
                        class="form-control"
                        id="scm_dc_foot_room">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_dc_foot_room }}</span>
                </div>
            </div>
            <div class="row">
                <div class="form-group col-sm-6" *ngIf="showParametersFor('state_of_charge_management', 'dynamicContainment')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_TARGET_STATE_OF_CHARGE_BOTH'|translate}}</label>
                    <input
                        *ngIf="isEditing"
                        [(ngModel)]="dispatchGroup.scm_dc_target_state_of_charge_both"
                        type="number"
                        class="form-control"
                        id="scm_dc_target_state_of_charge_both">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_dc_target_state_of_charge_both }}</span>
                </div>
            </div>
            <div class="row">
                <div class="form-group col-sm-6" *ngIf="showParametersFor('state_of_charge_management', 'dynamicContainment') || showParametersFor('state_of_charge_management', 'optimizedDc')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_FOOT_ROOM'|translate}}</label>
                    <input
                        *ngIf="isEditing"
                        [(ngModel)]="dispatchGroup.scm_dc_foot_room"
                        type="number"
                        class="form-control"
                        id="scm_dc_foot_room">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_dc_foot_room }}</span>
                </div>
                <div class="form-group col-sm-6" *ngIf="showParametersFor('state_of_charge_management', 'dynamicContainment') || showParametersFor('state_of_charge_management', 'optimizedDc')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_HEAD_ROOM'|translate}}</label>
                    <input
                        *ngIf="isEditing"
                        [(ngModel)]="dispatchGroup.scm_dc_head_room"
                        type="number"
                        class="form-control"
                        id="scm_dc_head_room">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_dc_head_room }}</span>
                </div>
            </div>
            <div class="row">
                <div class="form-group col-sm-6" *ngIf="showParametersFor('state_of_charge_management', 'dynamicContainment') || showParametersFor('state_of_charge_management', 'dynamicContainmentLow') || showParametersFor('state_of_charge_management', 'optimizedDc')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_RAMP_RATE_LIMIT'|translate}}</label>
                    <input
                        *ngIf="isEditing"
                        [(ngModel)]="dispatchGroup.scm_dc_ramp_rate_limit"
                        type="number"
                        class="form-control"
                        id="scm_dc_ramp_rate_limit">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_dc_ramp_rate_limit }}</span>
                </div>
                <div class="form-group col-sm-6" *ngIf="showParametersFor('state_of_charge_management', 'dynamicContainment') || showParametersFor('state_of_charge_management', 'dynamicContainmentLow') || showParametersFor('state_of_charge_management', 'optimizedDc')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_ENERGY_RESERVE'|translate}}</label>
                    <input
                        *ngIf="isEditing"
                        [(ngModel)]="dispatchGroup.scm_dc_energy_reserve"
                        type="number"
                        class="form-control"
                        id="scm_dc_energy_reserve">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_dc_energy_reserve }}</span>
                </div>
            </div>
            <div class="row">
                <div class="form-group col-sm-6" *ngIf="showParametersFor('state_of_charge_management', 'dynamicContainment') || showParametersFor('state_of_charge_management', 'dynamicContainmentLow') || showParametersFor('state_of_charge_management', 'optimizedDc')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_MIN_ENERGY_RECOVERY'|translate}}</label>
                    <input
                        *ngIf="isEditing"
                        [(ngModel)]="dispatchGroup.scm_dc_min_energy_recovery"
                        type="number"
                        class="form-control"
                        id="scm_dc_min_energy_recovery">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_dc_min_energy_recovery }}</span>
                </div>
                <div class="form-group col-sm-6" *ngIf="showParametersFor('state_of_charge_management', 'dynamicContainment') || showParametersFor('state_of_charge_management', 'dynamicContainmentLow')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_DEAD_BAND_FACTOR'|translate}}</label>
                    <input
                        *ngIf="isEditing"
                        [(ngModel)]="dispatchGroup.scm_dc_dead_band_factor"
                        type="number"
                        class="form-control"
                        id="scm_dc_dead_band_factor">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_dc_dead_band_factor }}</span>
                </div>
            </div>
        </section>
        <section *ngIf="showParametersFor('state_of_charge_management', 'volumeBaseline')">
            <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.VOLUME_BASELINE_DC'|translate}}</h6>
            <div class="row">
                <div class="form-group col-sm-4" *ngIf="showParametersFor('state_of_charge_management', 'volumeBaseline')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DC_DELIVERY_DURATION'|translate}}</label>
                    <input
                            *ngIf="isEditing"
                            [(ngModel)]="dispatchGroup.scm_vb_dc_delivery_duration"
                            type="number"
                            class="form-control"
                            id="scm_vb_dc_delivery_duration">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_vb_dc_delivery_duration }}</span>
                </div>
                <div class="form-group col-sm-4" *ngIf="showParametersFor('state_of_charge_management', 'volumeBaseline')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DC_DELIVERY_DURATION_BUFFER'|translate}}</label>
                    <input
                            *ngIf="isEditing"
                            [(ngModel)]="dispatchGroup.scm_vb_dc_delivery_duration_buffer"
                            type="number"
                            class="form-control"
                            id="scm_vb_dc_delivery_duration_buffer">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_vb_dc_delivery_duration_buffer }}</span>
                </div>
                <div class="form-group col-sm-4" *ngIf="showParametersFor('state_of_charge_management', 'volumeBaseline')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DC_MIN_ENERGY_RECOVERY'|translate}}</label>
                    <input
                            *ngIf="isEditing"
                            [(ngModel)]="dispatchGroup.scm_vb_dc_min_energy_recovery"
                            type="number"
                            class="form-control"
                            id="scm_vb_dc_min_energy_recovery">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_vb_dc_min_energy_recovery }}</span>
                </div>
            </div>
        </section>
        <section *ngIf="showParametersFor('state_of_charge_management', 'volumeBaseline')">
            <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.VOLUME_BASELINE_DM'|translate}}</h6>
            <div class="row">
                <div class="form-group col-sm-4" *ngIf="showParametersFor('state_of_charge_management', 'volumeBaseline')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DM_DELIVERY_DURATION'|translate}}</label>
                    <input
                            *ngIf="isEditing"
                            [(ngModel)]="dispatchGroup.scm_vb_dm_delivery_duration"
                            type="number"
                            class="form-control"
                            id="scm_vb_dm_delivery_duration">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_vb_dm_delivery_duration }}</span>
                </div>
                <div class="form-group col-sm-4" *ngIf="showParametersFor('state_of_charge_management', 'volumeBaseline')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DM_DELIVERY_DURATION_BUFFER'|translate}}</label>
                    <input
                            *ngIf="isEditing"
                            [(ngModel)]="dispatchGroup.scm_vb_dm_delivery_duration_buffer"
                            type="number"
                            class="form-control"
                            id="scm_vb_dm_delivery_duration_buffer">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_vb_dm_delivery_duration_buffer }}</span>
                </div>
                <div class="form-group col-sm-4" *ngIf="showParametersFor('state_of_charge_management', 'volumeBaseline')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DM_MIN_ENERGY_RECOVERY'|translate}}</label>
                    <input
                            *ngIf="isEditing"
                            [(ngModel)]="dispatchGroup.scm_vb_dm_min_energy_recovery"
                            type="number"
                            class="form-control"
                            id="scm_vb_dm_min_energy_recovery">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_vb_dm_min_energy_recovery }}</span>
                </div>
            </div>
        </section>
        <section *ngIf="showParametersFor('state_of_charge_management', 'volumeBaseline')">
            <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.VOLUME_BASELINE_DR'|translate}}</h6>
            <div class="row">
                <div class="form-group col-sm-4" *ngIf="showParametersFor('state_of_charge_management', 'volumeBaseline')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DR_DELIVERY_DURATION'|translate}}</label>
                    <input
                            *ngIf="isEditing"
                            [(ngModel)]="dispatchGroup.scm_vb_dr_delivery_duration"
                            type="number"
                            class="form-control"
                            id="scm_vb_dr_delivery_duration">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_vb_dr_delivery_duration }}</span>
                </div>
                <div class="form-group col-sm-4" *ngIf="showParametersFor('state_of_charge_management', 'volumeBaseline')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DR_DELIVERY_DURATION_BUFFER'|translate}}</label>
                    <input
                            *ngIf="isEditing"
                            [(ngModel)]="dispatchGroup.scm_vb_dr_delivery_duration_buffer"
                            type="number"
                            class="form-control"
                            id="scm_vb_dr_delivery_duration_buffer">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_vb_dr_delivery_duration_buffer }}</span>
                </div>
                <div class="form-group col-sm-4" *ngIf="showParametersFor('state_of_charge_management', 'volumeBaseline')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DR_MIN_ENERGY_RECOVERY'|translate}}</label>
                    <input
                            *ngIf="isEditing"
                            [(ngModel)]="dispatchGroup.scm_vb_dr_min_energy_recovery"
                            type="number"
                            class="form-control"
                            id="scm_vb_dr_min_energy_recovery">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_vb_dr_min_energy_recovery }}</span>
                </div>
            </div>
        </section>
        <section *ngIf="showParametersFor('state_of_charge_management', 'fcrNl')">
            <h6>
                {{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.FCR_NL'|translate}}
            </h6>
            <div class="row">
                <div class="form-group col-sm-6" *ngIf="showParametersFor('state_of_charge_management', 'fcrNl')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_FCR_LOWER_SOC_LIMIT'|translate}}</label>
                    <input
                        *ngIf="isEditing"
                        [(ngModel)]="dispatchGroup.scm_fcr_lower_soc_limit"
                        type="number"
                        class="form-control"
                        id="scm_fcr_lower_soc_limit">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_fcr_lower_soc_limit }}</span>
                </div>
                <div class="form-group col-sm-6" *ngIf="showParametersFor('state_of_charge_management', 'fcrNl')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_FCR_UPPER_SOC_LIMIT'|translate}}</label>
                    <input
                        *ngIf="isEditing"
                        [(ngModel)]="dispatchGroup.scm_fcr_upper_soc_limit"
                        type="number"
                        class="form-control"
                        id="scm_fcr_upper_soc_limit">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_fcr_upper_soc_limit }}</span>
                </div>
            </div>
        </section>
        <section *ngIf="showParametersFor('state_of_charge_management', 'optimizationPassThrough')">
            <h6>
                {{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.OPTIMIZATION_PASS_THROUGH'|translate}}
            </h6>
            <div class="row">
                <div class="form-group col-sm-6" *ngIf="showParametersFor('state_of_charge_management', 'optimizationPassThrough')">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_FCR_SCM_OPT_SP_DURATION_MINUTES'|translate}}</label>
                    <select *ngIf="isEditing"
                        [(ngModel)]="dispatchGroup.scm_opt_sp_duration_minutes"
                        class="form-control"
                        id="scm_opt_sp_duration_minutes">
                        <option [value]="15">15</option>
                        <option [value]="30">30</option>
                    </select>
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.scm_opt_sp_duration_minutes }}</span>
                </div>
            </div>
        </section>
    </section>
</section>

<section>
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ASSET_DISPATCH_STRATEGY'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_START_USING_ASSETS_ONLY_WHEN_AT_BASEPOINT'|translate}}</label>
            <div class="form-check">
                <label
                    class="eon-checkbox-label bg-eon-red"
                    (click)="toggleCheckboxAttribute('ads_start_using_assets_only_when_at_basepoint')"
                    [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.ads_start_using_assets_only_when_at_basepoint}">
                </label>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_STRATEGY'|translate}}</label>
            <ng-multiselect-dropdown
                *ngIf="isEditing"
                [placeholder]="('COMPONENTS.SELECT'|translate)"
                [data]="adsStrategyOptions"
                [(ngModel)]="dispatchGroup.ads_strategy"
                [settings]="dropdownSettings(true, false)">
            </ng-multiselect-dropdown>
            <span *ngIf="!isEditing" class="value">{{ optionNames(dispatchGroup.ads_strategy) }}</span>
        </div>
        <div class="form-group col-sm-6" *ngIf="showParametersFor('ads_strategy', 'byPrice')">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_STRATEGY_BY_PRICE_PRESERVE_CURRENT_DISPATCHES'|translate}}</label>
            <div class="form-check">
                <label
                    class="eon-checkbox-label bg-eon-red"
                    (click)="toggleCheckboxAttribute('ads_strategy_by_price_preserve_current_dispatches')"
                    [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.ads_strategy_by_price_preserve_current_dispatches}">
                </label>
            </div>
        </div>
        <div class="form-group col-sm-6" *ngIf="showParametersFor('ads_strategy', 'byPriceAndSoC')">
            <ng-template #limitBatteryPowerWindowInfoHtml>
                <div [innerHTML]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_STRATEGY_BY_PRICE_AND_SOC_LIMIT_BATTERY_POWER_WINDOW_INFO_HTML'|translate"></div>
            </ng-template>

            <label>
                {{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_STRATEGY_BY_PRICE_AND_SOC_LIMIT_BATTERY_POWER_WINDOW'|translate}}
                <i class="fa fa-question-circle"
                    [ngbTooltip]="limitBatteryPowerWindowInfoHtml"
                    containerClass="wider-tooltip"></i>
            </label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ads_strategy_by_price_and_soc_limit_battery_power_window"
                type="number"
                class="form-control"
                id="ads_strategy_by_price_and_soc_limit_battery_power_window">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ads_strategy_by_price_and_soc_limit_battery_power_window || '-' }}</span>
        </div>
        <div class="form-group col-sm-6" *ngIf="showParametersFor('ads_strategy', 'proRata')">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_STRATEGY_PRO_RATA_SYMMETRIC'|translate}}</label>
            <div class="form-check">
                <label
                    class="eon-checkbox-label bg-eon-red"
                    (click)="toggleCheckboxAttribute('ads_strategy_pro_rata_symmetric')"
                    [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.ads_strategy_pro_rata_symmetric}">
                </label>
            </div>
        </div>
        <div class="form-group col-sm-6" *ngIf="showParametersFor('ads_strategy', 'onOff')">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_STRATEGY_ON_OFF_SIGNAL'|translate}}</label>
            <ng-multiselect-dropdown
                *ngIf="isEditing"
                [placeholder]="('COMPONENTS.SELECT'|translate)"
                [data]="adsStrategyOnOffSignalOptions"
                [(ngModel)]="dispatchGroup.ads_strategy_on_off_signal"
                [settings]="dropdownSettings(true, false)">
            </ng-multiselect-dropdown>
            <span *ngIf="!isEditing" class="value">{{ optionNames(dispatchGroup.ads_strategy_on_off_signal) }}</span>
        </div>
    </div>
</section>

<section>
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.CROSS_DG_LINKS'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.CROSS_DG_LINKS'|translate}}</label>
            <ng-multiselect-dropdown
                *ngIf="isEditing"
                [placeholder]="('COMPONENTS.SELECT'|translate)"
                [data]="crossDgLinksOptions"
                [(ngModel)]="dispatchGroup.cross_dg_links"
                [settings]="dropdownSettings(false, false)">
            </ng-multiselect-dropdown>
            <span *ngIf="!isEditing" class="value">{{ optionNames(dispatchGroup.cross_dg_links) }}</span>
        </div>
        <div class="form-group col-sm-6" *ngIf="showParametersFor('cross_dg_links', 'crossPlanPropagation')">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.CDL_CROSS_PLAN_PROPAGATION_FREQUENCY'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.cdl_cross_plan_propagation_frequency"
                type="number"
                class="form-control"
                id="cdl_cross_plan_propagation_frequency">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.cdl_cross_plan_propagation_frequency }}</span>
        </div>
    </div>
</section>

<section>
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DEVIATIONS_COMPENSATION'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_CHECK_INTERVAL_SECONDS'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.dc_check_interval_seconds"
                type="number"
                class="form-control"
                id="dc_check_interval_seconds">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.dc_check_interval_seconds }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_COMPENSATION_RESOLUTION_KW'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.dc_compensation_resolution_kw"
                type="number"
                class="form-control"
                id="dc_compensation_resolution_kw">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.dc_compensation_resolution_kw }}</span>
        </div>
    </div>
    <div class="row">
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_OVER_DELIVERY_EXCESS_COMPENSATION_FACTOR'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.dc_over_delivery_excess_compensation_factor"
                type="number"
                class="form-control"
                id="dc_over_delivery_excess_compensation_factor">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.dc_over_delivery_excess_compensation_factor }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_OVER_DELIVERY_COMPENSATION_LIMIT_FACTOR'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.dc_over_delivery_compensation_limit_factor"
                type="number"
                class="form-control"
                id="dc_over_delivery_compensation_limit_factor">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.dc_over_delivery_compensation_limit_factor }}</span>
        </div>
    </div>
    <div class="row">
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_UNDER_DELIVERY_EXCESS_COMPENSATION_FACTOR'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.dc_under_delivery_excess_compensation_factor"
                type="number"
                class="form-control"
                id="dc_under_delivery_excess_compensation_factor">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.dc_under_delivery_excess_compensation_factor }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_UNDER_DELIVERY_COMPENSATION_LIMIT_FACTOR'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.dc_under_delivery_compensation_limit_factor"
                type="number"
                class="form-control"
                id="dc_under_delivery_compensation_limit_factor">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.dc_under_delivery_compensation_limit_factor }}</span>
        </div>
    </div>
    <div class="row">
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_IS_AT_SETPOINT_UPPER_TOLERANCE_FACTOR'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.dc_is_at_setpoint_upper_tolerance_factor"
                type="number"
                class="form-control"
                id="dc_is_at_setpoint_upper_tolerance_factor">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.dc_is_at_setpoint_upper_tolerance_factor }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_IS_AT_SETPOINT_UPPER_TOLERANCE_MINIMUM_KW'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.dc_is_at_setpoint_upper_tolerance_minimum_kw"
                type="number"
                class="form-control"
                id="dc_is_at_setpoint_upper_tolerance_minimum_kw">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.dc_is_at_setpoint_upper_tolerance_minimum_kw }}</span>
        </div>
    </div>
    <div class="row">
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_IS_AT_SETPOINT_LOWER_TOLERANCE_FACTOR'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.dc_is_at_setpoint_lower_tolerance_factor"
                type="number"
                class="form-control"
                id="dc_is_at_setpoint_lower_tolerance_factor">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.dc_is_at_setpoint_lower_tolerance_factor }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_IS_AT_SETPOINT_LOWER_TOLERANCE_MINIMUM_KW'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.dc_is_at_setpoint_lower_tolerance_minimum_kw"
                type="number"
                class="form-control"
                id="dc_is_at_setpoint_lower_tolerance_minimum_kw">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.dc_is_at_setpoint_lower_tolerance_minimum_kw }}</span>
        </div>
    </div>
</section>

<section>
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DG_ACTIVATION'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DG_ACTIVATION_TYPE'|translate}}</label>
            <ng-multiselect-dropdown
                *ngIf="isEditing"
                [placeholder]="('COMPONENTS.SELECT'|translate)"
                [data]="dgActivationTypeOptions"
                [(ngModel)]="dispatchGroup.dg_activation_type"
                [settings]="dropdownSettings(true, false)">
            </ng-multiselect-dropdown>
            <span *ngIf="!isEditing" class="value">{{ optionNames(dispatchGroup.dg_activation_type) }}</span>
        </div>
    </div>
</section>

<section>
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DISPATCH'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DISPATCH_COMMANDS'|translate}}</label>
            <ng-multiselect-dropdown
                *ngIf="isEditing"
                [placeholder]="('COMPONENTS.SELECT'|translate)"
                [data]="dispatchCommandsOptions"
                [(ngModel)]="dispatchGroup.dispatch_commands"
                [settings]="dropdownSettings(false, false)">
            </ng-multiselect-dropdown>
            <span *ngIf="!isEditing" class="value">{{ optionNames(dispatchGroup.dispatch_commands) }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DISPATCH_SOURCE'|translate}}</label>
            <ng-multiselect-dropdown
                *ngIf="isEditing"
                [placeholder]="('COMPONENTS.SELECT'|translate)"
                [data]="dispatchSourceOptions"
                [(ngModel)]="dispatchGroup.dispatch_source"
                [settings]="dropdownSettings(false, false)">
            </ng-multiselect-dropdown>
            <span *ngIf="!isEditing" class="value">{{ optionNames(dispatchGroup.dispatch_source) }}</span>
        </div>
    </div>
    <div class="row" *ngIf="showParametersFor('dispatch_source', 'nominatedVolume')">
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_NOMINATED_VOLUME_ACTIVATION_FACTOR'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ds_nominated_volume_activation_factor"
                type="number"
                class="form-control"
                id="ds_nominated_volume_activation_factor">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ds_nominated_volume_activation_factor }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_NOMINATED_VOLUME_SYMMETRIC_ACTIVATION'|translate}}</label>
            <div class="form-check">
                <label
                    class="eon-checkbox-label bg-eon-red"
                    (click)="toggleCheckboxAttribute('ds_nominated_volume_symmetric_activation')"
                    [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.ds_nominated_volume_symmetric_activation}">
                </label>
            </div>
        </div>
    </div>
    <div class="row" *ngIf="showParametersFor('dispatch_source', 'ui') && showParametersFor('dg_activation_type', 'relative')">
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_UI_EDG_SCHEDULE'|translate}}</label>
            <div class="form-check">
                <label
                    class="eon-checkbox-label bg-eon-red"
                    (click)="toggleCheckboxAttribute('ds_ui_edg_schedule')"
                    [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.ds_ui_edg_schedule}">
                </label>
            </div>
        </div>
    </div>
    <div class="row" *ngIf="showParametersFor('dispatch_source', 'residualShape')">
        <div class="form-group col-sm-4">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_RESIDUAL_SHAPE_POSITIVE_THRESHOLD'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ds_residual_shape_positive_threshold"
                type="number"
                class="form-control"
                id="ds_residual_shape_positive_threshold">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ds_residual_shape_positive_threshold }}</span>
        </div>
        <div class="form-group col-sm-4">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_RESIDUAL_SHAPE_NEGATIVE_THRESHOLD'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ds_residual_shape_negative_threshold"
                type="number"
                class="form-control"
                id="ds_residual_shape_negative_threshold">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ds_residual_shape_negative_threshold }}</span>
        </div>
        <div class="form-group col-sm-4">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_RESIDUAL_SHAPE_WINDOW'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ds_residual_shape_window"
                type="number"
                class="form-control"
                id="ds_residual_shape_window">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ds_residual_shape_window }}</span>
        </div>
    </div>
    <section *ngIf="showParametersFor('dispatch_source', 'priceTrigger')">
        <div class="row">
            <div class="form-group col-sm-4">
                <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_PRICE_TRIGGER_WINDOW'|translate}}</label>
                <input
                    *ngIf="isEditing"
                    [(ngModel)]="dispatchGroup.ds_price_trigger_window"
                    type="number"
                    class="form-control"
                    id="ds_price_trigger_window">
                <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ds_price_trigger_window }}</span>
            </div>
            <div class="form-group col-sm-4">
                <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_PRICE_TRIGGER_PRICE_TYPE'|translate}}</label>
                <input
                    *ngIf="isEditing"
                    [(ngModel)]="dispatchGroup.ds_price_trigger_price_type"
                    type="text"
                    class="form-control"
                    id="ds_price_trigger_price_type">
                <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ds_price_trigger_price_type }}</span>
            </div>
            <div class="form-group col-sm-4">
                <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_PRICE_TRIGGER_SETTLEMENT_PERIOD'|translate}}</label>
                <input
                    *ngIf="isEditing"
                    [(ngModel)]="dispatchGroup.ds_price_trigger_settlement_period"
                    type="number"
                    class="form-control"
                    id="ds_price_trigger_settlement_period">
                <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ds_price_trigger_settlement_period }}</span>
            </div>
        </div>
        <div class="row">
            <div class="form-group col-sm-4">
                <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_PRICE_TRIGGER_PRICE_THRESHOLD_NEG'|translate}}</label>
                <input
                    *ngIf="isEditing"
                    [(ngModel)]="dispatchGroup.ds_price_trigger_price_threshold_neg"
                    type="number"
                    class="form-control"
                    id="ds_price_trigger_price_threshold_neg">
                <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ds_price_trigger_price_threshold_neg }}</span>
            </div>
            <div class="form-group col-sm-4">
                <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_PRICE_TRIGGER_PRICE_THRESHOLD_POS'|translate}}</label>
                <input
                    *ngIf="isEditing"
                    [(ngModel)]="dispatchGroup.ds_price_trigger_price_threshold_pos"
                    type="number"
                    class="form-control"
                    id="ds_price_trigger_price_threshold_pos">
                <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ds_price_trigger_price_threshold_pos }}</span>
            </div>
            <div class="form-group col-sm-4">
                <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_PRICE_TRIGGER_PRICE_EXPIRATION_SECONDS'|translate}}</label>
                <input
                    *ngIf="isEditing"
                    [(ngModel)]="dispatchGroup.ds_price_trigger_price_expiration_seconds"
                    type="number"
                    class="form-control"
                    id="ds_price_trigger_price_expiration_seconds">
                <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ds_price_trigger_price_expiration_seconds }}</span>
            </div>
        </div>
    </section>
    <div class="row" *ngIf="showParametersFor('dispatch_source', 'nlAfrr')">
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_NLAFRR_BLEEDING_TIME_SECONDS'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ds_nlafrr_bleeding_time_seconds"
                type="number"
                class="form-control"
                id="ds_nlafrr_bleeding_time_seconds">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ds_nlafrr_bleeding_time_seconds }}</span>
        </div>
    </div>
    <div class="row">
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.EXECUTION_PLAN_WINDOW_SECONDS'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.execution_plan_window_seconds"
                type="number"
                class="form-control"
                id="execution_plan_window_seconds">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.execution_plan_window_seconds }}</span>
        </div>
        <div class="form-group col-sm-6">
          <ng-template #execPlanFrequencyInfoHtml>
            <div [innerHTML]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.EXECUTION_PLAN_FREQUENCY_INFO_HTML'|translate"></div>
          </ng-template>
          <label>
            {{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.EXECUTION_PLAN_FREQUENCY_SECONDS'|translate}}
            <i class="fa fa-question-circle"
                [ngbTooltip]="execPlanFrequencyInfoHtml"
                containerClass="wider-tooltip"></i>
          </label>
          <input
              *ngIf="isEditing"
              [(ngModel)]="dispatchGroup.execution_plan_frequency_seconds"
              type="number"
              class="form-control"
              id="execution_plan_frequency_seconds">
          <span *ngIf="!isEditing" class="value">{{ dispatchGroup.execution_plan_frequency_seconds }}</span>
      </div>
    </div>
</section>

<section>
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NOMINATION_EXTENSION_TIMES'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NET_EXTEND_BEFORE_NOMINATION_SECONDS'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.net_extend_before_nomination_seconds"
                type="number"
                class="form-control"
                id="net_extend_before_nomination_seconds">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.net_extend_before_nomination_seconds }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NET_OVERLAP_NOMINATIONS_SECONDS'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.net_overlap_nominations_seconds"
                type="number"
                class="form-control"
                id="net_overlap_nominations_seconds">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.net_overlap_nominations_seconds }}</span>
        </div>
    </div>
    <div class="row">
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NET_STOP_BEFORE_END_OF_NOMINATION_SECONDS'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.net_stop_before_end_of_nomination_seconds"
                type="number"
                class="form-control"
                id="net_stop_before_end_of_nomination_seconds">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.net_stop_before_end_of_nomination_seconds }}</span>
        </div>
    </div>
</section>

<vpp-generic-dg-config-notifications [dispatchGroup]="dispatchGroup" [isEditing]="isEditing"></vpp-generic-dg-config-notifications>

<section>
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.PERIODICITY'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.PERIODICITY_DG_AGGREGATIONS_SECONDS'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.periodicity_dg_aggregations_seconds"
                type="number"
                class="form-control"
                id="periodicity_dg_aggregations_seconds">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.periodicity_dg_aggregations_seconds }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.PERIODICITY_OUTPUT_SIGNAL_WRITE_SECONDS'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.periodicity_output_signal_write_seconds"
                type="number"
                class="form-control"
                id="periodicity_output_signal_write_seconds">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.periodicity_output_signal_write_seconds }}</span>
        </div>
    </div>
</section>

<section>
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.RAMP_ADJUSTMENT_STRATEGY'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.RAMP_ADJUSTMENT_STRATEGY'|translate}}</label>
            <ng-multiselect-dropdown
                *ngIf="isEditing"
                [placeholder]="('COMPONENTS.SELECT'|translate)"
                [data]="rampAdjustmentStrategyOptions"
                [(ngModel)]="dispatchGroup.ramp_adjustment_strategy"
                [settings]="dropdownSettings(true, false)">
            </ng-multiselect-dropdown>
            <span *ngIf="!isEditing" class="value">{{ optionNames(dispatchGroup.ramp_adjustment_strategy) }}</span>
        </div>
    </div>
</section>

<section>
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.REDISPATCH_TRIGGERS'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.REDISPATCH_TRIGGERS'|translate}}</label>
            <ng-multiselect-dropdown
                *ngIf="isEditing"
                [placeholder]="('COMPONENTS.SELECT'|translate)"
                [data]="redispatchTriggersOptions"
                [(ngModel)]="dispatchGroup.redispatch_triggers"
                [settings]="dropdownSettings(false, true)">
            </ng-multiselect-dropdown>
            <span *ngIf="!isEditing" class="value">{{ optionNames(dispatchGroup.redispatch_triggers) }}</span>
        </div>
        <div class="form-group col-sm-6" *ngIf="showParametersFor('redispatch_triggers', 'assetAvailableFlexChange')">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.RT_ASSET_AVAILABLE_FLEX_CHANGE_THRESHOLD_KW'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.rt_asset_available_flex_change_threshold_kw"
                type="number"
                class="form-control"
                id="rt_asset_available_flex_change_threshold_kw">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.rt_asset_available_flex_change_threshold_kw }}</span>
        </div>
    </div>
    <div class="row">
        <div class="form-group col-sm-6" *ngIf="showParametersFor('redispatch_triggers', 'assetAvailableFlexChangeSinceActivation')">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.RT_ASSET_AVAILABLE_FLEX_CHANGE_SINCE_ACTIVATION_FACTOR'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.rt_asset_available_flex_change_since_activation_factor"
                type="number"
                class="form-control"
                id="rt_asset_available_flex_change_since_activation_factor">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.rt_asset_available_flex_change_since_activation_factor }}</span>
        </div>
    </div>
    <div class="row" *ngIf="showParametersFor('redispatch_triggers', 'dgTotalDeviation')">
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.RT_DG_TOTAL_DEVIATION_BUFFER_KW'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.rt_dg_total_deviation_buffer_kw"
                type="number"
                class="form-control"
                id="rt_dg_total_deviation_buffer_kw">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.rt_dg_total_deviation_buffer_kw }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.RT_DG_TOTAL_DEVIATION_SUPPRESS_REDISPATCH_SECONDS'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.rt_dg_total_deviation_suppress_redispatch_seconds"
                type="number"
                class="form-control"
                id="rt_dg_total_deviation_suppress_redispatch_seconds">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.rt_dg_total_deviation_suppress_redispatch_seconds }}</span>
        </div>
    </div>
</section>

<section>
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION_REACTION_TIME_SECONDS'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.setpoint_validation_reaction_time_seconds"
                type="number"
                class="form-control"
                id="setpoint_validation_reaction_time_seconds">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.setpoint_validation_reaction_time_seconds }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION_CAP_BY_NOMINATION'|translate}}</label>
            <div class="form-check">
                <label
                    class="eon-checkbox-label bg-eon-red"
                    (click)="toggleCheckboxAttribute('setpoint_validation_cap_by_nomination')"
                    [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.setpoint_validation_cap_by_nomination}">
                </label>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION_MINIMUM_DURATION_OF_DISPATCH_SECONDS'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.setpoint_validation_minimum_duration_of_dispatch_seconds"
                type="number"
                class="form-control"
                id="setpoint_validation_minimum_duration_of_dispatch_seconds">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.setpoint_validation_minimum_duration_of_dispatch_seconds }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION_NOMINATION_INTERVAL_VALIDATION'|translate}}</label>
            <ng-multiselect-dropdown
                *ngIf="isEditing"
                [placeholder]="('COMPONENTS.SELECT'|translate)"
                [data]="setpointValidationNominationIntervalValidationOptions"
                [(ngModel)]="dispatchGroup.setpoint_validation_nomination_interval_validation"
                [settings]="dropdownSettings(true, false)">
            </ng-multiselect-dropdown>
            <span *ngIf="!isEditing" class="value">{{ optionNames(dispatchGroup.setpoint_validation_nomination_interval_validation) }}</span>
        </div>
    </div>
    <div class="row">
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION_QUANTIZATION_FILTER_RESOLUTION_KW'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.setpoint_validation_quantization_filter_resolution_kw"
                type="number"
                class="form-control"
                id="setpoint_validation_quantization_filter_resolution_kw">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.setpoint_validation_quantization_filter_resolution_kw }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION_QUANTIZATION_FILTER_QUANTIZATION_DURATION_SECONDS'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.setpoint_validation_quantization_filter_quantization_duration_seconds"
                type="number"
                class="form-control"
                id="setpoint_validation_quantization_filter_quantization_duration_seconds">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.setpoint_validation_quantization_filter_quantization_duration_seconds }}</span>
        </div>
    </div>
</section>

<section>
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SIGNALS'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SIGNALS_OUTPUT_SIGNALS'|translate}}</label>
            <ng-multiselect-dropdown
                *ngIf="isEditing"
                [placeholder]="('COMPONENTS.SELECT'|translate)"
                [data]="signalsOutputSignalsOptions"
                [(ngModel)]="dispatchGroup.signals_output_signals"
                [settings]="dropdownSettings(false, true)">
            </ng-multiselect-dropdown>
            <span *ngIf="!isEditing" class="value">{{ optionNames(dispatchGroup.signals_output_signals) }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SIGNALS_DSO_FOR_SIGNALS'|translate}}</label>
            <ng-multiselect-dropdown
                *ngIf="isEditing"
                [placeholder]="('COMPONENTS.SELECT'|translate)"
                [data]="signalsDsoForSignalsOptions"
                [(ngModel)]="dispatchGroup.signals_dso_for_signals"
                [settings]="dropdownSettings(false, true)">
            </ng-multiselect-dropdown>
            <span *ngIf="!isEditing" class="value">{{ optionNames(dispatchGroup.signals_dso_for_signals) }}</span>
        </div>
    </div>
    <div class="row">
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SIGNALS_SUB_POOLS_VALUES_ON'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.signals_sub_pools_values_on"
                type="number"
                class="form-control"
                id="signals_sub_pools_values_on">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.signals_sub_pools_values_on }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SIGNALS_SUB_POOLS_VALUES_OFF'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.signals_sub_pools_values_off"
                type="number"
                class="form-control"
                id="signals_sub_pools_values_off">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.signals_sub_pools_values_off }}</span>
        </div>
    </div>
    <div class="row">
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SIGNALS_HEART_BEAT_MIRROR'|translate}}</label>
            <div class="form-check">
                <label
                    class="eon-checkbox-label bg-eon-red"
                    (click)="toggleCheckboxAttribute('signals_heart_beat_mirror')"
                    [ngClass]="{'non-clickable': !isEditing, 'checked': dispatchGroup.signals_heart_beat_mirror}">
                </label>
            </div>
        </div>
    </div>
</section>

<section>
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.BATTERY_DISPATCH_CONFIG'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.BATTERY_DISPATCH_CONFIG_MIN_POWER_FOR_DISPATCH_DISCHARGE'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.dpb_min_power_for_dispatch_discharge"
                type="number"
                class="form-control"
                id="dpb_min_power_for_dispatch_discharge">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.dpb_min_power_for_dispatch_discharge }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.BATTERY_DISPATCH_CONFIG_MIN_POWER_FOR_DISPATCH_CHARGE'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.dpb_min_power_for_dispatch_charge"
                type="number"
                class="form-control"
                id="dpb_min_power_for_dispatch_charge">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.dpb_min_power_for_dispatch_charge }}</span>
        </div>
    </div>
</section>
