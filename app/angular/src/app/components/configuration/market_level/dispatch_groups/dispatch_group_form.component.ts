import {Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild, ViewEncapsulation} from "@angular/core";
import {TranslateService} from "@ngx-translate/core";
import {NotificationService} from "../../../../services/notification.service";
import {ConfirmService} from "../../../../services/confirm.service";
import {angularData} from "../../../../../global.export";
import {DispatchGroupProvider} from "../../../../providers/dispatch_group.provider";
import {TsoProvider} from "../../../../providers/tso.provider";
import {MarketProvider} from "../../../../providers/market.provider";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import * as moment from "moment-timezone";

@Component({
    selector: 'vpp-dispatch-group-form',
    templateUrl: './dispatch_group_form.component.html',
    styleUrls: ['./dispatch_group_form.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class DispatchGroupFormComponent implements OnInit {
    @Output('done') done: any = new EventEmitter();
    @Output('canceled') canceled: any = new EventEmitter();
    @Input('dispatchGroup') dispatchGroup: any = {
        id: null,
        name: '',
        has_generic_config: false,
    };
    @Input() openInEditMode: boolean;
    @Input() embedded: boolean = false;

    errors: any = [];
    canSubmit: boolean = true;
    isEditing: boolean;
    confirmDeleteMessage = '';
    permissions: any;
    bsModalRef;
    editedBalancingGroup: any = {};
    @ViewChild('editBg', {static: true}) editModal;
    @ViewChild('errorsDiv', {read: ElementRef, static: false}) errorsDiv;

    tsos: any = [];
    selectedTsos: any = [];
    tsoDropdownSettings = {
        idField: 'id',
        textField: 'name',
        singleSelection: true,
        selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
        unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
        searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
        noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
        itemsShowLimit: 10,
        allowSearchFilter: true
    };
    tsosById: any = {};
    isSelectedTsoNationalGrid: boolean = false;

    capacityPriceGroupingSteps: any = [];
    capacityPriceGroupingStepsDropdownSettings = {
        singleSelection: false,
        selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
        unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
        searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
        noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
        itemsShowLimit: 10,
        allowSearchFilter: true
    };
    newCapacityPriceGroupingStep: number = null;

    markets: any = [];
    marketsById: any = {};
    selectedMarkets: any = [];
    isSelectedMarketAllowsNominationToolEnablement: boolean = false;
    isSelectedMarketFfrd: boolean = false;
    isSelectedMarketDlm: boolean = false;
    isSelectedMarketSpot: boolean = false;
    isSelectedMarketIntraday: boolean = false;
    isSelectedMarketDynamicServices: boolean = false;
    marketDropdownSettings = {
        idField: 'id',
        textField: 'name',
        singleSelection: true,
        selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
        unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
        searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
        noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
        itemsShowLimit: 10,
        allowSearchFilter: true
    };

    signals: any = [];
    signalsDropdownSettings = {
        selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
        unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
        searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
        noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
        itemsShowLimit: 10,
        allowSearchFilter: true
    };

    dsos: any = [];
    dsoDropdownSettings = {
        idField: 'id',
        textField: 'name',
        selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
        unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
        searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
        noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
        itemsShowLimit: 10,
        allowSearchFilter: true
    };

    eventTypes: any = [];
    eventTypesDropdownSettings = {
        singleSelection: false,
        selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
        unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
        searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
        noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
        itemsShowLimit: 10,
        allowSearchFilter: true
    };

    balancingGroups = [];
    availableBgNames = [];


    constructor(
        public translate: TranslateService,
        private _notificationService: NotificationService,
        private _dispatchGroupProvider: DispatchGroupProvider,
        private _tsoProvider: TsoProvider,
        private _marketProvider: MarketProvider,
        private _confirmService: ConfirmService,
        private _modalService: NgbModal
    ) {
        this.translate.get('CONFIRM.MESSAGE.DELETE_ENTRY').subscribe((x) => {
            this.confirmDeleteMessage = x;
        });
        this.permissions = angularData.permissions;
    }

    ngOnInit(): void {
        this.isEditing = this.openInEditMode;

        if (this.dispatchGroup.id > 0) {
            this.selectedTsos = [this.dispatchGroup.tso];
            this.selectedMarkets = [this.dispatchGroup.market];
        }

        this.getDispatchGroupDetails();
        this.loadFormData();
    }

    loadFormData() {
        this._dispatchGroupProvider.getFormData().subscribe(
            res => {
                this.signals = res.signals;
                this.dsos = res.dsos;
                this.eventTypes = res.event_types.map(x => x.id);
                this.markets = res.markets;
                this.tsos = res.tsos;
                this.availableBgNames = res.available_bg_names;

                this.tsosById = {};
                this.tsos.forEach(x => {
                    this.tsosById[x.id] = x;
                    if (x.id == this.dispatchGroup.tso_id) {
                        this.selectedTsos = [x];
                    }
                });
                this.onSelectedTsoChanged();

                this.marketsById = {};
                this.markets.forEach(x => {
                    this.marketsById[x.id] = x;
                    if (x.id == this.dispatchGroup.market_id) {
                        this.selectedMarkets = [x];
                    }
                });
                this.onSelectedMarketChanged();
            },
            err => {
                console.error('Failed to load dispatch group form data', err);
            }
        );
    }

    onSelectedTsoChanged() {
        this.isSelectedTsoNationalGrid = false;
        if (this.selectedTsos.length > 0) {
            let tso = this.tsosById[this.selectedTsos[0].id];
            this.isSelectedTsoNationalGrid = (tso.name == 'National Grid');
        }
    }

    onSelectedMarketChanged() {
        this.isSelectedMarketAllowsNominationToolEnablement = false;
        this.isSelectedMarketFfrd = false;
        this.isSelectedMarketDlm = false;
        this.isSelectedMarketSpot = false;
        this.isSelectedMarketIntraday = false;
        this.isSelectedMarketDynamicServices = false;
        if (this.selectedMarkets.length > 0) {
            let market = this.marketsById[this.selectedMarkets[0].id];
            this.isSelectedMarketAllowsNominationToolEnablement = !!(market.allows_nomination_tool_enablement);
            this.isSelectedMarketFfrd = (market.name == 'FFRDynamic');
            this.isSelectedMarketDlm = (market.name == 'DLM');
            this.isSelectedMarketSpot = (market.name == 'SPOT');
            this.isSelectedMarketIntraday = (market.name == 'Intraday');
            this.isSelectedMarketDynamicServices = (market.name == 'DynamicContainment') || (market.name == 'DynamicModeration') || (market.name == 'DynamicRegulation');
        }
    }

    getDispatchGroupDetails() {
        this._dispatchGroupProvider.getDispatchGroupDetails(this.dispatchGroup.id).subscribe(
            res => {
                this.dispatchGroup = res.dispatch_group;
                this.balancingGroups = res.bgs;
                this.capacityPriceGroupingSteps = [].concat(this.dispatchGroup.capacity_price_grouping_steps_list);
                console.log('Got DG', this.dispatchGroup);
            },
            err => {
                console.error('Failed to load dispatch group details', err);
            }
        );
    }

    addNewCapacityPriceGroupingStep(event) {
        if (this.newCapacityPriceGroupingStep) {
            if (!this.capacityPriceGroupingSteps.includes(this.newCapacityPriceGroupingStep) && !this.capacityPriceGroupingSteps.includes("" + this.newCapacityPriceGroupingStep)) {
                this.capacityPriceGroupingSteps = this.capacityPriceGroupingSteps.concat([this.newCapacityPriceGroupingStep])
            }
            if (!this.dispatchGroup.capacity_price_grouping_steps_list.includes(this.newCapacityPriceGroupingStep) && !this.dispatchGroup.capacity_price_grouping_steps_list.includes("" + this.newCapacityPriceGroupingStep)) {
                this.dispatchGroup.capacity_price_grouping_steps_list = this.dispatchGroup.capacity_price_grouping_steps_list.concat([this.newCapacityPriceGroupingStep])
            }
            this.newCapacityPriceGroupingStep = null;
        }
    }

    showParametersFor(attributeName, value) {
        if (this.dispatchGroup[attributeName]) {
          if (Array.isArray(this.dispatchGroup[attributeName])) {
            return this.dispatchGroup[attributeName].some(o => o.id === value);
          } else {
            return this.dispatchGroup[attributeName].id == value;
          }
        } else {
          return false;
        }
      }

    close(event) {
        this.done.emit();
    }

    cancel(event) {
        this.canceled.emit();
    }

    enterEditMode() {
        this.isEditing = true;
    }

    cancelEditMode() {
        this.isEditing = false;
        this.getDispatchGroupDetails();
        this.loadFormData();
    }

    prepareSubmitParams() {
        if (this.selectedMarkets.length > 0) {
            this.dispatchGroup.market_id = this.selectedMarkets[0].id;
        } else {
            this._notificationService.error({text: 'Please select market'});
            return null;
        }

        if (this.selectedTsos.length > 0) {
            this.dispatchGroup.tso_id = this.selectedTsos[0].id;
        } else {
            this._notificationService.error({text: 'Please select TSO'});
            return null;
        }

        if (!this.dispatchGroup.has_generic_config) {
          if (this.dispatchGroup.dsos)
            this.dispatchGroup.dso_ids = this.dispatchGroup.dsos.map(x => x.id);
          if (this.dispatchGroup.signals_list)
            this.dispatchGroup.signals = this.dispatchGroup.signals_list.join(' ');
        }

        let params = {
            dispatch_group: JSON.parse(JSON.stringify(this.dispatchGroup))
        };
        delete params.dispatch_group['allows_nomination_tool_enablement?'];
        delete params.dispatch_group['dsos'];
        delete params.dispatch_group['market'];
        delete params.dispatch_group['tso'];

        console.log('Submit DG', params);

        return params;
    }

    submit() {
        this.errors = [];

        let isNew = !this.dispatchGroup.id;

        let params = this.prepareSubmitParams();
        if (!params) {
            return false;
        }

        this.canSubmit = false;
        let fn = isNew ? this._dispatchGroupProvider.createDispatchGroup : this._dispatchGroupProvider.updateDispatchGroup;
        fn.call(this._dispatchGroupProvider, params).subscribe(
            (res) => {
                this.canSubmit = true;
                if (res.success == true) {
                    this._notificationService.success({text: 'SUCCESS'});
                    this.done.emit();
                } else {
                    this.errors = res.messages;
                    //this._notificationService.errors(this.errors);
                    this.scrollToErrors();
                }
            },
            (err) => {
                console.log('submit failed', err);
                this.errors = [JSON.stringify(err)];
                //this._notificationService.errors(this.errors);
                this.canSubmit = true;
                this.scrollToErrors();
            }
        );
    }

    scrollToErrors() {
        if (this.errors && this.errors.length > 0) {
            setTimeout(() => {
                if (this.errorsDiv) {
                    this.errorsDiv.nativeElement.scrollIntoView();
                } else {
                    console.log('cannot find errors div');
                }
            } );
        }
    }

    delete() {
        this._confirmService.confirm({
            message: this.confirmDeleteMessage,
        }).then(() => {
            this._deleteConfirmed();
        }, () => {
        });
    }

    _deleteConfirmed() {
        this._dispatchGroupProvider
            .deleteDispatchGroup(this.dispatchGroup.id)
            .subscribe(
                (res) => {
                    if (res.success == true) {
                        this._notificationService.success({text: 'SUCCESS'});
                        this.done.emit();
                    } else {
                        this._notificationService.error({text: res.error});
                    }
                },
                (err) => {
                    console.log('Failed to delete', err);
                    this._notificationService.error({text: JSON.stringify(err)});
                });
    }

    toggleGenericConfigEnabled() {
        if (this.isEditing) {
            this.dispatchGroup.has_generic_config = !this.dispatchGroup.has_generic_config;
        }
    }


    toggleEchoSignalActivePower() {
        if (this.isEditing) {
            this.dispatchGroup.echo_signal_active_power = !this.dispatchGroup.echo_signal_active_power;
        }
    }

    toggleEchoSignalHeartbeat() {
        if (this.isEditing) {
            this.dispatchGroup.echo_signal_heartbeat = !this.dispatchGroup.echo_signal_heartbeat;
        }
    }

    toggleAlarmAssetsWithNotRecoverableFaultsEnabled() {
        if (this.isEditing) {
            this.dispatchGroup.alarm_assets_with_not_recoverable_faults_enabled = !this.dispatchGroup.alarm_assets_with_not_recoverable_faults_enabled;
        }
    }

    toggleAlarmAssetsWithRecoverableFaultsEnabled() {
        if (this.isEditing) {
            this.dispatchGroup.alarm_assets_with_recoverable_faults_enabled = !this.dispatchGroup.alarm_assets_with_recoverable_faults_enabled;
        }
    }

    toggleAlarmSetpointReachableEnabled() {
        if (this.isEditing) {
            this.dispatchGroup.alarm_setpoint_reachable_enabled = !this.dispatchGroup.alarm_setpoint_reachable_enabled;
        }
    }

    toggleAlarmAvailableFlexTooLowEnabled() {
        if (this.isEditing) {
            this.dispatchGroup.alarm_available_flex_too_low_enabled = !this.dispatchGroup.alarm_available_flex_too_low_enabled;
        }
    }

    toggleIndividualAlarmsEnabled() {
        if (this.isEditing) {
            this.dispatchGroup.individual_alarms_enabled = !this.dispatchGroup.individual_alarms_enabled;
        }
    }

    toggleIndividualAlarmMerlinEnabled() {
        if (this.isEditing) {
            this.dispatchGroup.individual_alarm_merlin_enabled = !this.dispatchGroup.individual_alarm_merlin_enabled;
        }
    }

    toggleIndividualAlarmEchoSignalActivePowerEnabled() {
        if (this.isEditing) {
            this.dispatchGroup.individual_alarm_echo_signal_active_power_enabled = !this.dispatchGroup.individual_alarm_echo_signal_active_power_enabled;
        }
    }

    toggleIndividualAlarmOverdeliveryPosEnabled() {
        if (this.isEditing) {
            this.dispatchGroup.individual_alarm_overdelivery_pos_enabled = !this.dispatchGroup.individual_alarm_overdelivery_pos_enabled;
        }
    }

    toggleIndividualAlarmOverdeliveryNegEnabled() {
        if (this.isEditing) {
            this.dispatchGroup.individual_alarm_overdelivery_neg_enabled = !this.dispatchGroup.individual_alarm_overdelivery_neg_enabled;
        }
    }

    toggleSallySetpointReadEnabled() {
        if (this.isEditing) {
            this.dispatchGroup.sally_setpoint_read_enabled = !this.dispatchGroup.sally_setpoint_read_enabled;
        }
    }

    toggleMerlinDispatchEnabled() {
        if (this.isEditing) {
            this.dispatchGroup.merlin_dispatch_enabled = !this.dispatchGroup.merlin_dispatch_enabled;
        }
    }

    toggleNominationToolEnabled() {
        if (this.isEditing) {
            this.dispatchGroup.nomination_tool_enabled = !this.dispatchGroup.nomination_tool_enabled;
        }
    }

    toggleRollupsEnabled() {
        if (this.isEditing) {
            this.dispatchGroup.rollups_enabled = !this.dispatchGroup.rollups_enabled;
        }
    }

    toggleAutomaticAllocationEnabled() {
        if (this.isEditing) {
            this.dispatchGroup.automatic_allocation_enabled = !this.dispatchGroup.automatic_allocation_enabled;
        }
    }

    toggleExtBackupActive() {
        if (this.isEditing) {
            this.dispatchGroup.ext_backup_active = !this.dispatchGroup.ext_backup_active;
        }
    }


    editBalancingGroup(x) {
        this.editedBalancingGroup = {...x};
        this._openModal();
    }

    addBalancingGroup() {
        let emptyBg = {
            id: null,
            start_date: moment(),
            end_date: moment(),
            name: ''
        };
        this.editedBalancingGroup = {...emptyBg};
        this._openModal();
    }

    deleteBalancingGroup(x) {
        this._confirmService.confirm({
            message: this.confirmDeleteMessage,
        }).then(() => {
            this._deleteConfirmedBalancingGroup(x);
        }, () => {
        });
    }

    _deleteConfirmedBalancingGroup(x) {
        let params = {
            dispatch_group_id: this.dispatchGroup.id,
            balancing_group_id: x.id
        };
        this._dispatchGroupProvider.deleteBalancingGroups(params).subscribe(
            (res) => {
                if (res.success == true) {
                    this._notificationService.success({text: 'SUCCESS'});
                } else {
                    this._notificationService.error({text: JSON.stringify(res.error)});
                }
                this.getDispatchGroupDetails();
            },
            (err) => {
                console.log('Failed to delete', err);
                this._notificationService.error({text: JSON.stringify(err)});
            });
    }

    _openModal() {
        this.bsModalRef = this._modalService.open(
            this.editModal,
            //{ size: 'lg' }
        );
    }

    closeDispatchGroupModal(reload: boolean = true) {
        this.bsModalRef.close();
        if (reload) {
            this.getDispatchGroupDetails();
        }
    }

}