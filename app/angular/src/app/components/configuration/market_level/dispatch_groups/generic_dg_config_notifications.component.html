<section>
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NOTIFICATIONS'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NOTIFICATIONS'|translate}}</label>
            <ng-multiselect-dropdown
                *ngIf="isEditing"
                [placeholder]="('COMPONENTS.SELECT'|translate)"
                [data]="notificationsOptions"
                [(ngModel)]="dispatchGroup.notifications"
                [settings]="dropdownSettings(false, true)">
            </ng-multiselect-dropdown>
            <span *ngIf="!isEditing" class="value">{{ optionNames(dispatchGroup.notifications) }}</span>
        </div>
    </div>
</section>

<section *ngIf="showParametersFor('notifications', 'flexTooLow')">
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_FLEX_TOO_LOW'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_FLEX_TOO_LOW_THRESHOLD_FACTOR'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ntf_flex_too_low_threshold_factor"
                type="number"
                class="form-control"
                id="ntf_flex_too_low_threshold_factor">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ntf_flex_too_low_threshold_factor}}</span>
        </div>
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_FLEX_TOO_LOW_THRESHOLD_BUFFER_SECONDS'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ntf_flex_too_low_threshold_buffer_seconds"
                type="number"
                class="form-control"
                id="ntf_flex_too_low_threshold_buffer_seconds">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ntf_flex_too_low_threshold_buffer_seconds }}</span>
        </div>
    </div>
</section>

<section *ngIf="showParametersFor('notifications', 'overDeliveryNegative')">
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_OVER_DELIVERY_NEGATIVE'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_OVER_DELIVERY_NEGATIVE_THRESHOLD_KW'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ntf_over_delivery_negative_threshold_kw"
                type="number"
                class="form-control"
                id="ntf_over_delivery_negative_threshold_kw">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ntf_over_delivery_negative_threshold_kw }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_OVER_DELIVERY_NEGATIVE_DELAY_SECONDS'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ntf_over_delivery_negative_delay_seconds"
                type="number"
                class="form-control"
                id="ntf_over_delivery_negative_delay_seconds">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ntf_over_delivery_negative_delay_seconds }}</span>
        </div>
    </div>
</section>

<section *ngIf="showParametersFor('notifications', 'overDeliveryPositive')">
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_OVER_DELIVERY_POSITIVE'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_OVER_DELIVERY_POSITIVE_THRESHOLD_KW'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ntf_over_delivery_positive_threshold_kw"
                type="number"
                class="form-control"
                id="ntf_over_delivery_positive_threshold_kw">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ntf_over_delivery_positive_threshold_kw }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_OVER_DELIVERY_POSITIVE_DELAY_SECONDS'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ntf_over_delivery_positive_delay_seconds"
                type="number"
                class="form-control"
                id="ntf_over_delivery_positive_delay_seconds">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ntf_over_delivery_positive_delay_seconds }}</span>
        </div>
    </div>
</section>

<section *ngIf="showParametersFor('notifications', 'setpointNotReached')">
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED_REACH_SETPOINT_IN_SECONDS'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ntf_setpoint_not_reached_reach_setpoint_in_seconds"
                type="number"
                class="form-control"
                id="ntf_setpoint_not_reached_reach_setpoint_in_seconds">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ntf_setpoint_not_reached_reach_setpoint_in_seconds }}</span>
        </div>
    </div>
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED_IS_AT_SETPOINT_DETECTION'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED_IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_FACTOR'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ntf_setpoint_not_reached_is_at_setpoint_detection_upper_tolerance_factor"
                type="number"
                class="form-control"
                id="ntf_setpoint_not_reached_is_at_setpoint_detection_upper_tolerance_factor">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ntf_setpoint_not_reached_is_at_setpoint_detection_upper_tolerance_factor }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED_IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_MINIMUM_KW'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ntf_setpoint_not_reached_is_at_setpoint_detection_upper_tolerance_minimum_kw"
                type="number"
                class="form-control"
                id="ntf_setpoint_not_reached_is_at_setpoint_detection_upper_tolerance_minimum_kw">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ntf_setpoint_not_reached_is_at_setpoint_detection_upper_tolerance_minimum_kw }}</span>
        </div>
    </div>
    <div class="row">
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED_IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_FACTOR'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_factor"
                type="number"
                class="form-control"
                id="ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_factor">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_factor }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED_IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_MINIMUM_KW'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_minimum_kw"
                type="number"
                class="form-control"
                id="ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_minimum_kw">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_minimum_kw }}</span>
        </div>
    </div>
</section>

<section *ngIf="showParametersFor('notifications', 'setpointReachable')">
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_REACHABLE'|translate}}</h6>
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_REACHABLE_IS_AT_SETPOINT_DETECTION'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_REACHABLE_IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_FACTOR'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ntf_setpoint_reachable_is_at_setpoint_detection_upper_tolerance_factor"
                type="number"
                class="form-control"
                id="ntf_setpoint_reachable_is_at_setpoint_detection_upper_tolerance_factor">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ntf_setpoint_reachable_is_at_setpoint_detection_upper_tolerance_factor }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_REACHABLE_IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_MINIMUM_KW'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ntf_setpoint_reachable_is_at_setpoint_detection_upper_tolerance_minimum_kw"
                type="number"
                class="form-control"
                id="ntf_setpoint_reachable_is_at_setpoint_detection_upper_tolerance_minimum_kw">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ntf_setpoint_reachable_is_at_setpoint_detection_upper_tolerance_minimum_kw }}</span>
        </div>
    </div>
    <div class="row">
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_REACHABLE_IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_FACTOR'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ntf_setpoint_reachable_is_at_setpoint_detection_lower_tolerance_factor"
                type="number"
                class="form-control"
                id="ntf_setpoint_reachable_is_at_setpoint_detection_lower_tolerance_factor">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ntf_setpoint_reachable_is_at_setpoint_detection_lower_tolerance_factor }}</span>
        </div>
        <div class="form-group col-sm-6">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_REACHABLE_IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_MINIMUM_KW'|translate}}</label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.ntf_setpoint_reachable_is_at_setpoint_detection_lower_tolerance_minimum_kw"
                type="number"
                class="form-control"
                id="ntf_setpoint_reachable_is_at_setpoint_detection_lower_tolerance_minimum_kw">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.ntf_setpoint_reachable_is_at_setpoint_detection_lower_tolerance_minimum_kw }}</span>
        </div>
    </div>
</section>