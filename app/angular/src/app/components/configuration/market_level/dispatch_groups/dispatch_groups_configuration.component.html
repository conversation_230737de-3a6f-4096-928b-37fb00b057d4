<div class="container-fluid">

    <div class="row">
        <div class="col-md-8">
            <div class="page-actions">
                <ul class="list-unstyled d-flex">
                    <li *ngIf="permissions.can_create_dispatch_groups">
                        <a href="javascript:void(0)"
                           (click)="toggleSection($event, 'enter-dispatch-group')"
                           class="eon-button bg-eon-turquoise">
                            {{ 'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.ENTER_DISPATCH_GROUP' | translate }}
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <div class="overlay-on-top-of-list"
         [@slideRightContentAnimation]="selectedSelection == 'enter-dispatch-group' ? 'in' : 'out'">
        <vpp-dispatch-group-form
                class="embedded"
                *ngIf="selectedSelection == 'enter-dispatch-group'"
                [openInEditMode]="true"
                (done)="reloadList()"
                (canceled)="reloadList()">
        </vpp-dispatch-group-form>
    </div>

    <section>
        <div class="row">
            <div class="col-md-12">
                <vpp-dispatch-groups-list
                        *ngIf="loadList"
                        [selectedDgId]="selectedDgId"
                ></vpp-dispatch-groups-list>
            </div>
        </div>
    </section>

</div>