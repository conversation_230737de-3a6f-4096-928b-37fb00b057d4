import {Component, EventEmitter, Input, OnInit, Output, ViewEncapsulation} from "@angular/core";
import {DispatchGroupProvider} from "../../../../providers/dispatch_group.provider";
import {TranslateService} from "@ngx-translate/core";
import {NotificationService} from "../../../../services/notification.service";
import {GlobalService} from "../../../../services/global.service";
import * as moment from "moment-timezone";

@Component({
    selector: 'vpp-dg-bg-form',
    templateUrl: './dg_bg_form.component.html',
    styleUrls: ['./dg_bg_form.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class DgBgFormComponent implements OnInit {
    @Output('done') done: any = new EventEmitter();
    @Output('canceled') canceled: any = new EventEmitter();
    @Input() embedded: boolean = false;
    @Input('balancingGroup') balancingGroup: any = {
        id: null,
        start_date: moment(),
        end_date: moment(),
        name: ''
    };
    @Input() availableBgNames: string[] = [];
    @Input() dispatchGroupId: any = '';

    errors: any = [];
    canSubmit: boolean = true;


    constructor(
        public translate: TranslateService,
        public globalService: GlobalService,
        private _notificationService: NotificationService,
        private _dispatchGroupProvider: DispatchGroupProvider
    ) {

    }

    ngOnInit() {
    }

    close(event) {
        this.done.emit();
    }

    cancel(event) {
        this.canceled.emit();
    }

    startDateSelectedCallback(date) {
    }

    endDateSelectedCallback(date) {
    }

    submit() {
        console.log('updating balancing group', this.balancingGroup);

        this.errors = [];

        let isNew = !this.balancingGroup.id;

        let params = {
            dispatch_group_id: this.dispatchGroupId,
            balancing_group: {
                id: this.balancingGroup.id,
                name: this.balancingGroup.name,
                start_date: moment(this.balancingGroup.start_date).format('YYYY-MM-DD'),
                end_date: moment(this.balancingGroup.end_date).format('YYYY-MM-DD')
            }
        };

        this.canSubmit = false;
        let fn = isNew ? this._dispatchGroupProvider.createBalancingGroup : this._dispatchGroupProvider.updateBalancingGroup;
        fn.call(this._dispatchGroupProvider, params).subscribe(
            (res) => {
                this.canSubmit = true;
                if (res.success == true) {
                    this._notificationService.success({text: 'SUCCESS'});
                    this.done.emit();
                } else {
                    this.errors = res.messages;
                    this._notificationService.errors(this.errors);
                }
            },
            (err) => {
                console.log('submit failed', err);
                this.errors = [JSON.stringify(err)];
                this.canSubmit = true;
            }
        );
    }

}