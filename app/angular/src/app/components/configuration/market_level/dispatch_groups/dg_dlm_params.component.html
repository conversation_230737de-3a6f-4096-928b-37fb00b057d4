<section>
    <h6>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_PARAMS'|translate}}</h6>
    <div class="row">
        <div class="form-group col-sm-12">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE'|translate}}</label>
            <div class="row" style="padding-left:20px;" *ngIf="isEditing">
                <div class="col-sm-3">
                    <div class="form-check">
                        <label (click)="changeGroupingRule($event, 'site_based')" class="form-check-label" for="dlmGroupingSiteBased">
                            <input [(ngModel)]="dispatchGroup.grouping_rule" value="site_based" class="form-check-input" id="dlmGroupingSiteBased" name="languageRadios" type="radio">
                            {{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_SITE'|translate}}
                            <i class="fa fa-question-circle"
                               [ngbTooltip]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_SITE_INFO'|translate"
                               containerClass="wider-tooltip"></i>
                        </label>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-check">
                        <label (click)="changeGroupingRule($event, 'session_based')" class="form-check-label" for="dlmGroupingSessionBased">
                            <input [(ngModel)]="dispatchGroup.grouping_rule" value="session_based" class="form-check-input" id="dlmGroupingSessionBased" name="languageRadios" type="radio">
                            {{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_SESSION'|translate}}
                            <i class="fa fa-question-circle"
                               [ngbTooltip]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_SESSION_INFO'|translate"
                               containerClass="wider-tooltip"></i>
                        </label>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-check">
                        <label (click)="changeGroupingRule($event, 'Customer_based')" class="form-check-label" for="dlmGroupingCustomerBased">
                            <input [(ngModel)]="dispatchGroup.grouping_rule" value="customer_based" class="form-check-input" id="dlmGroupingCustomerBased" name="languageRadios" type="radio">
                            {{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_CUSTOMER'|translate}}
                            <i class="fa fa-question-circle"
                               [ngbTooltip]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_CUSTOMER_INFO'|translate"
                               containerClass="wider-tooltip"></i>
                        </label>
                    </div>
                </div>
            </div>
            <span *ngIf="!isEditing && dispatchGroup.grouping_rule == 'site_based'"
              class="value">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_SITE'|translate}}</span>
            <span *ngIf="!isEditing && dispatchGroup.grouping_rule == 'session_based'"
              class="value">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_SESSION'|translate}}</span>
            <span *ngIf="!isEditing && dispatchGroup.grouping_rule == 'customer_based'"
              class="value">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_CUSTOMER'|translate}}</span>
        </div>
    </div>

    <div class="row">
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_IMPORT_RESTRICTIONS'|translate}}</label>
        </div>
        <div class="form-group col-sm-6">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUP_ID'|translate}}</label>
        </div>
    </div>
    <div class="row">
        <div class="form-group col-sm-6">
            <div class="row">
                <div class="form-group col-sm-6">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_NOMINAL_SITE_CURRENT'|translate}}
                        <i class="fa fa-question-circle"
                           [ngbTooltip]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_NOMINAL_SITE_CURRENT_INFO'|translate"
                           containerClass="wider-tooltip"></i>
                    </label>
                    <input
                        *ngIf="isEditing"
                        [(ngModel)]="dispatchGroup.nominal_site_current"
                        type="number"
                        class="form-control"
                        id="nominal_site_current">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.nominal_site_current }}</span>
                </div>
                <div class="form-group col-sm-6">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_REDUCTION_FACTOR'|translate}}
                        <i class="fa fa-question-circle"
                           [ngbTooltip]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_REDUCTION_FACTOR_INFO'|translate"
                           containerClass="wider-tooltip"></i>
                    </label>
                    <input
                        *ngIf="isEditing"
                        [(ngModel)]="dispatchGroup.reduction_factor"
                        type="number"
                        class="form-control"
                        id="reduction_factor">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.reduction_factor }}</span>
                </div>
            </div>
        </div>
        <div class="form-group col-sm-6">
            <div class="row">
                <div class="form-group col-sm-6">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUP_REDUCED_ID'|translate}}
                        <i class="fa fa-question-circle"
                           [ngbTooltip]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUP_REDUCED_ID_INFO'|translate"
                           containerClass="wider-tooltip"></i>
                    </label>
                    <input
                        *ngIf="isEditing"
                        [(ngModel)]="dispatchGroup.dlm_group_reduced_id"
                        type="number"
                        class="form-control"
                        id="dlm_group_reduced_id">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.dlm_group_reduced_id }}</span>
                </div>
                <div class="form-group col-sm-6">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUP_BLOCKED_ID'|translate}}
                        <i class="fa fa-question-circle"
                           [ngbTooltip]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUP_BLOCKED_ID_INFO'|translate"
                           containerClass="wider-tooltip"></i>
                    </label>
                    <input
                        *ngIf="isEditing"
                        [(ngModel)]="dispatchGroup.dlm_group_blocked_id"
                        type="number"
                        class="form-control"
                        id="dlm_group_blocked_id">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.dlm_group_blocked_id }}</span>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="form-group col-sm-12">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_CONTROL_WINDOWS'|translate}}</label>

            <div class="row" *ngIf="dispatchGroup.control_windows.length == 0">
                <div class="form-group col-sm-3"></div>
                <div class="form-group col-sm-3"></div>
                <div class="form-group col-sm-3">
                    <span (click)="addControlWindow(0)"><i class="fa fa-plus fa-2x"></i></span>
                </div>
            </div>
            <div class="row" *ngFor="let cw of dispatchGroup.control_windows; let i = index">
                <div class="form-group col-sm-3">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_CONTROL_WINDOW_BEGIN'|translate}}
                        <i class="fa fa-question-circle"
                           [ngbTooltip]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_CONTROL_WINDOW_BEGIN_INFO'|translate"
                           containerClass="wider-tooltip"></i>
                    </label>
                    <input
                        *ngIf="isEditing"
                        [(ngModel)]="dispatchGroup.control_windows[i].start_time"
                        type="text"
                        class="form-control"
                        id="control_windows[{{i}}][start_time]">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.control_windows[i].start_time }}</span>
                </div>
                <div class="form-group col-sm-3">
                    <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_CONTROL_WINDOW_END'|translate}}
                        <i class="fa fa-question-circle"
                           [ngbTooltip]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_CONTROL_WINDOW_END_INFO'|translate"
                           containerClass="wider-tooltip"></i>
                    </label>
                    <input
                        *ngIf="isEditing"
                        [(ngModel)]="dispatchGroup.control_windows[i].end_time"
                        type="text"
                        class="form-control"
                        id="control_windows[{{i}}][end_time]">
                    <span *ngIf="!isEditing" class="value">{{ dispatchGroup.control_windows[i].end_time }}</span>
                </div>
                <div *ngIf="isEditing" class="form-group col-sm-3 align-self-end">
                    <span (click)="removeControlWindow(i)"><i class="fa fa-trash fa-2x"></i></span>
                    <span>&nbsp;</span>
                    <span (click)="addControlWindow(i + 1)"><i class="fa fa-plus fa-2x"></i></span>
                </div>
            </div>
        </div>
    </div>
    <div class="row" *ngIf="!groupingRuleIs('site_based')">
        <div class="form-group col-sm-12">
            <label>{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_LOAD_MGMT_PARAMS'|translate}}</label>
        </div>
    </div>
    <div class="row" *ngIf="groupingRuleIs('session_based')">
        <div class="form-group col-sm-5">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_SESSION_DURATION_THRESHOLD_MINUTES'|translate}}
                <i class="fa fa-question-circle"
                   [ngbTooltip]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_SESSION_DURATION_THRESHOLD_MINUTES_INFO'|translate"
                   containerClass="wider-tooltip"></i>
            </label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.session_duration_threshold_minutes"
                type="number"
                class="form-control"
                id="session_duration_threshold_minutes">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.session_duration_threshold_minutes }}</span>
        </div>
        <div class="form-group col-sm-2">
            <label [ngClass]="isEditing ? 'required' :''">
                <i class="fa fa-question-circle"
                   [ngbTooltip]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_BOTH_THRESHOLDS_INFO'|translate"
                   containerClass="wider-tooltip"></i>
            </label>
            <select *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.grouping_rule_both_thresholds"
                id="grouping_rule_both_thresholds"
                class="form-control">
                <option></option>
                <option value="and">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_BOTH_THRESHOLDS_AND'|translate}}</option>
                <option value="or">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_BOTH_THRESHOLDS_OR'|translate}}</option>
            </select>
            <span *ngIf="!isEditing && dispatchGroup.grouping_rule_both_thresholds == 'and'">
                {{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_BOTH_THRESHOLDS_AND'|translate}}
            </span>
            <span *ngIf="!isEditing && dispatchGroup.grouping_rule_both_thresholds == 'or'">
                {{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_BOTH_THRESHOLDS_OR'|translate}}
            </span>
        </div>
        <div class="form-group col-sm-5">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_SESSION_ENERGY_THRESHOLD_WATT_HOUR'|translate}}
                <i class="fa fa-question-circle"
                   [ngbTooltip]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_SESSION_ENERGY_THRESHOLD_WATT_HOUR_INFO'|translate"
                   containerClass="wider-tooltip"></i>
            </label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.session_energy_threshold_watt_hour"
                type="number"
                class="form-control"
                id="session_energy_threshold_watt_hour">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.session_energy_threshold_watt_hour }}</span>
        </div>
    </div>
    <div class="row" *ngIf="groupingRuleIs('customer_based')">
        <div class="form-group col-sm-5">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_MIN_AVERAGE_CHARGING_DURATION_FACTOR'|translate}}
                <i class="fa fa-question-circle"
                   [ngbTooltip]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_MIN_AVERAGE_CHARGING_DURATION_FACTOR_INFO'|translate"
                   containerClass="wider-tooltip"></i>
            </label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.min_average_charging_duration_factor"
                type="number"
                class="form-control"
                id="min_average_charging_duration_factor">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.min_average_charging_duration_factor }}</span>
        </div>
        <div class="form-group col-sm-2">
            <br/>
            <label [ngClass]="isEditing ? 'required' :''">
                <i class="fa fa-question-circle"
                   [ngbTooltip]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_BOTH_THRESHOLDS_INFO'|translate"
                   containerClass="wider-tooltip"></i>
            </label>
            <select *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.grouping_rule_both_thresholds"
                id="grouping_rule_both_thresholds"
                class="form-control">
                <option></option>
                <option value="and">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_BOTH_THRESHOLDS_AND'|translate}}</option>
                <option value="or">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_BOTH_THRESHOLDS_OR'|translate}}</option>
            </select>
            <span *ngIf="!isEditing && dispatchGroup.grouping_rule_both_thresholds == 'and'">
                {{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_BOTH_THRESHOLDS_AND'|translate}}
            </span>
            <span *ngIf="!isEditing && dispatchGroup.grouping_rule_both_thresholds == 'or'">
                {{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_BOTH_THRESHOLDS_OR'|translate}}
            </span>
        </div>
        <div class="form-group col-sm-5">
            <label [ngClass]="isEditing ? 'required' :''">{{'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_MIN_AVERAGE_CHARGED_ENERGY_FACTOR'|translate}}
                <i class="fa fa-question-circle"
                   [ngbTooltip]="'CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_MIN_AVERAGE_CHARGED_ENERGY_FACTOR_INFO'|translate"
                   containerClass="wider-tooltip"></i>
            </label>
            <input
                *ngIf="isEditing"
                [(ngModel)]="dispatchGroup.min_average_charged_energy_factor"
                type="number"
                class="form-control"
                id="min_average_charged_energy_factor">
            <span *ngIf="!isEditing" class="value">{{ dispatchGroup.min_average_charged_energy_factor }}</span>
        </div>
    </div>
</section>