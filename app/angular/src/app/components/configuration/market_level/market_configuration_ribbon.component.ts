import {
    Component,
    OnInit,
    ViewEncapsulation
} from "@angular/core";
import { GlobalService } from "../../../services/global.service";
import { SlideRightContentAnimation } from '../../../animations/slide-right-content';
import { TranslateService } from '@ngx-translate/core';
import {ActivatedRoute, Params} from "@angular/router";
import {environment} from "../../../../environments/environment";
import {Location} from "@angular/common";
import {angularData} from "../../../../global.export";

@Component({
    selector: "vpp-management-market-configuration-ribbon",
    templateUrl: "./market_configuration_ribbon.component.html",
    styleUrls: ["./market_configuration_ribbon.component.scss"],
    encapsulation: ViewEncapsulation.None,
    animations: [ SlideRightContentAnimation ],
})
export class MarketConfigurationRibbonComponent implements OnInit {
    selectedTab: string;
    permittedTabs: { subpools: false, balancingGroups: false, dispatchGroups: false, auctions: false };
    selectedDgId: any;

    constructor(
        public translate: TranslateService,
        private _globalService: GlobalService,
        private _route: ActivatedRoute,
        private _location: Location
    ) {
        this._globalService.changeTitle('Configuration - Market Level');
        //this.translate.get('PAGE.CAPTIONS.CONFIGURATION.ASSET_LEVEL').subscribe((res: string) => {
        //    this._globalService.changeTitle(res);
        //});
        let permissions = angularData.permissions;
        this.permittedTabs = {
            subpools: permissions.can_see_subpools || permissions.can_create_subpools,
            balancingGroups: permissions.can_see_balancing_groups || permissions.can_create_balancing_groups,
            dispatchGroups: permissions.can_see_dispatch_groups || permissions.can_create_dispatch_groups || permissions.can_update_dispatch_groups,
            auctions: permissions.can_see_bids || permissions.can_create_bids
        };
        if (this.permittedTabs.subpools) {
            this.selectedTab = 'subpools';
        } else if (this.permittedTabs.balancingGroups) {
            this.selectedTab = 'balancing-groups';
        } else if (this.permittedTabs.dispatchGroups) {
            this.selectedTab = 'dispatch-groups';
        } else if (this.permittedTabs.auctions) {
            this.selectedTab = 'auctions';
        }
    }

    ngOnInit() {
    }

    ngAfterViewInit() {
        this._route.params.forEach((params: Params) => {
            //console.log('checking route params', JSON.stringify(params));

            this.selectedDgId = null;

            let tab = params['tab'];
            let id = params['id'];

            this.selectTab(null, tab, id);
        });
    }

    selectTab(event, tab, id = null) {
        //console.log('selecting tab', tab, 'id', id);

        this.selectedTab = tab;
        if (id) {
            this.selectedDgId = id;
            this._location.replaceState(`${environment.routingPath}/market_configuration_ribbon/${tab}/${id}`);
        } else {
            this.selectedDgId = null;
            this._location.replaceState(`${environment.routingPath}/market_configuration_ribbon/${tab}`);
        }
    }

}
