<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="page-actions">
                <ul class="list-unstyled d-flex">
                    <li *ngIf="permissions.can_create_bids">
                        <a href="javascript:void(0)"
                           (click)="toggleSection($event, 'add-auction-configuration')"
                           class="eon-button bg-eon-turquoise">
                            {{ 'CONFIGURATION.MARKET_LEVEL.AUCTION_CONFIGURATION.ADD' | translate }}
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <div class="overlay-on-top-of-list"
         [@slideRightContentAnimation]="selectedSelection == 'add-auction-configuration' ? 'in' : 'out'">
        <vpp-auction-configuration-form
                class="embedded"
                *ngIf="selectedSelection == 'add-auction-configuration'"
                [openInEditMode]="true"
                (done)="reloadList()"
                (canceled)="reloadList()">
        </vpp-auction-configuration-form>
    </div>

    <section>
        <div class="row">
            <div class="col-md-12">
                <vpp-auction-configuration-list *ngIf="loadList"></vpp-auction-configuration-list>
            </div>
        </div>
    </section>
</div>
