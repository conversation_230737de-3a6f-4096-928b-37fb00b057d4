import {Component, OnInit, ViewChild, ViewEncapsulation} from "@angular/core";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {NotificationService} from "../../../../services/notification.service";
import {TranslateService} from "@ngx-translate/core";
import {GlobalService} from "../../../../services/global.service";
import {ConfirmService} from "../../../../services/confirm.service";
import {angularData} from "../../../../../global.export";
import {AuctionsProvider} from "../../../../providers/auctions.provider";

@Component({
    selector: 'vpp-auction-configuration-list',
    templateUrl: './auction_list.component.html',
    styleUrls: ['./auction_list.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class AuctionListComponent implements OnInit {
    showLoader = true;
    auctions = [];
    params = {
        page: 1,
        per_page: 25,
    };
    page = 1;
    pageSize = 25;
    collectionSize = 0;
    editedAuctionConfig: any;
    openInEditMode = true;
    limitedEditMode = false;
    confirmDeleteMessage = '';
    bsModalRef;
    permissions: any;

    @ViewChild('edit', {static: true}) editModal;

    deliveryIntervalOptions = [
        {id: "Blocks", name: "CONFIGURATION.AUCTION.deliveryIntervalOptions.Blocks"},
        {id: "Days", name: "CONFIGURATION.AUCTION.deliveryIntervalOptions.Days"},
        {id: "Weeks", name: "CONFIGURATION.AUCTION.deliveryIntervalOptions.Weeks"},
    ];

    offersTimeBlockOptions = [
        {id: 'QuarterOfHour', name: 'CONFIGURATION.AUCTION.offersTimeBlockOptions.QuarterOfHour'},
        {id: 'HalfHour', name: 'CONFIGURATION.AUCTION.offersTimeBlockOptions.HalfHour'},
        {id: 'OneHour', name: 'CONFIGURATION.AUCTION.offersTimeBlockOptions.OneHour'},
        {id: 'FourHours', name: 'CONFIGURATION.AUCTION.offersTimeBlockOptions.FourHours'},
    ];

    priceTypeOptions = [
        {id: "EnergyOnly", name: "CONFIGURATION.AUCTION.priceTypeOptions.EnergyOnly"},
        {id: "CapacityOnly", name: "CONFIGURATION.AUCTION.priceTypeOptions.CapacityOnly"},
        {id: "EnergyAndCapacity", name: "CONFIGURATION.AUCTION.priceTypeOptions.EnergyAndCapacity"},
    ];

    auctionSystemUploadBehaviourOptions = [
        {id: 'RegelleistungMRLEnergy', name: 'Regelleistung MRL Energy'},
        {id: 'RegelleistungMRLCapacity', name: 'Regelleistung MRL Capacity'},
        {id: 'RegelleistungSRLEnergy', name: 'Regelleistung SRL Energy'},
        {id: 'RegelleistungSRLCapacity', name: 'Regelleistung SRL Capacity'},
        {id: 'UKTradingDayAhead', name: 'UK Trading Day Ahead'},
        {id: 'nlAfrr', name: 'NL-aFRR'},
        {id: 'FOBSnapshot', name: 'FOB Snapshot'},
        {id: 'BOD_enVoy', name: 'BOD enVoy'},
    ];

    auctionSystemDownloadBehaviourOptions = [
        {id: 'RegelleistungMRLEnergy', name: 'Regelleistung MRL Energy'},
        {id: 'RegelleistungMRLCapacity', name: 'Regelleistung MRL Capacity'},
        {id: 'RegelleistungSRLEnergy', name: 'Regelleistung SRL Energy'},
        {id: 'RegelleistungSRLCapacity', name: 'Regelleistung SRL Capacity'},
        {id: 'UKTradingDayAhead', name: 'UK Trading Day Ahead'},
        {id: 'FOB', name: 'FOB'},
        {id: 'AllAccepted', name: 'All Accepted'},
    ];

    constructor(
        public translate: TranslateService,
        public globalService: GlobalService,
        private _modalService: NgbModal,
        private _notificationService: NotificationService,
        private _confirmService: ConfirmService,
        private _auctionsProvider: AuctionsProvider
    ) {
        this.translate.get('CONFIRM.MESSAGE.DELETE_ENTRY').subscribe((x) => {
            this.confirmDeleteMessage = x;
        });
        this.permissions = angularData.permissions;
    }

    ngOnInit(): void {
        this.getAuctions();
    }

    getAuctions() {
        this.showLoader = true;
        this.auctions = [];
        this._auctionsProvider.findAllWithPagination(this.params).subscribe(
            res => {
                this.auctions = res.auctions;
                console.log("LOADED AUCTIONS", this.auctions)
                this.collectionSize = res.total_entries;
                this.showLoader = false;
            }, err => {
                this.showLoader = false;
                console.log('Failed to load auctions', err);
            }
        );
    }

    pageChangedCallback(page) {
        this.params.page = page;
        this.getAuctions();
    }

    pageSizeChangedCallback(pageSize) {
        this.params.per_page = pageSize;
        this.params.page = 1;
        this.getAuctions();
    }

    showAuction(box) {
        this.editedAuctionConfig = {...box};
        this.openInEditMode = false;
        this.limitedEditMode = false;
        this.bsModalRef = this._modalService.open(this.editModal, {size: 'lg'});
    }

    editAuction(box) {
        this.editedAuctionConfig = {...box};
        this.openInEditMode = true;
        this.limitedEditMode = true;
        this.bsModalRef = this._modalService.open(this.editModal, {size: 'lg'});
    }

    cloneAuction(box) {
        let boxCopy = {...box};
        delete boxCopy.id;
        boxCopy.name = null;
        this.editedAuctionConfig = {...boxCopy};
        this.openInEditMode = true;
        this.limitedEditMode = false;
        this.bsModalRef = this._modalService.open(this.editModal, {size: 'lg'});
    }

    closeAuctionModal(reload: boolean = true) {
        this.bsModalRef.close();
        if (reload) {
            this.getAuctions();
        }
    }

    deleteAuction(auction) {
        this._confirmService.confirm({
            message: this.confirmDeleteMessage,
        }).then(() => {
            this._deleteConfirmedAuction(auction);
        }, () => {
        });
    }

    _deleteConfirmedAuction(auction) {
        this._auctionsProvider.delete(auction.id)
            .subscribe(
                (res) => {
                    if (res.success == true) {
                        this._notificationService.success({text: 'SUCCESS'});
                    } else {
                        this._notificationService.error({text: JSON.stringify(res.error)});
                    }
                    this.getAuctions();
                },
                (err) => {
                    console.log('Failed to delete', err);
                    this._notificationService.error({text: JSON.stringify(err)});
                });
    }

}
