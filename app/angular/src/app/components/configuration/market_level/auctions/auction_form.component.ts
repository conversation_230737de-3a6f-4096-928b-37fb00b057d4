import {
    Component,
    EventEmitter,
    Input,
    OnInit,
    SimpleChanges,
    Output,
    ViewChild,
    ViewEncapsulation
} from "@angular/core";
import {TranslateService} from "@ngx-translate/core";
import {NotificationService} from "../../../../services/notification.service";
import {ConfirmService} from "../../../../services/confirm.service";
import {GlobalService} from "../../../../services/global.service";
import {angularData} from "../../../../../global.export";
import {AssetProvider} from "../../../../providers/asset.provider";
import {AuctionsProvider} from "../../../../providers/auctions.provider";
import {DispatchGroupProvider} from "../../../../providers/dispatch_group.provider";
import {MarketProvider} from "../../../../providers/market.provider";
import * as moment from "moment-timezone";
import {Moment} from "moment-timezone/moment-timezone";

@Component({
    selector: 'vpp-auction-configuration-form',
    templateUrl: './auction_form.component.html',
    styleUrls: ['./auction_form.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class AuctionFormComponent implements OnInit {
    @Output('done') done: any = new EventEmitter();
    @Output('canceled') canceled: any = new EventEmitter();

    @Input() openInEditMode: boolean;
    @Input() limitedEditMode: boolean;
    @Input() embedded: boolean = false;

    canSubmit: boolean = true;
    errors: any = [];
    isEditing: boolean;
    confirmDeleteMessage = '';
    permissions: any;

    @Input('auctionConfig') auctionConfig: any = {
        id: null,
        version: null,
        name: null,
        auction_definition_interval: [],
        time_zone: [],
        dispatch_group_ids: [],
        bidding_method: [],
        bidding_method_market: [],
        assets: [],
        optimization_horizon_change: [],
        optimization_horizon_change_days_before_delivery: null,
        optimization_horizon_change_time_hours: null,
        optimization_horizon_change_time_minutes: null,
        swing_limit: false,
        swing_limit_value: null,
        delivery_interval: [],
        offers_time_block: [],
        bidding_direction: [],
        rounding_digits: null,
        minimum_mw_volume_for1_bid: null,
        minimum_mw_volume: null,
        price_type: [],
        energy_price_rounding_digits: null,
        capacity_price_rounding_digits: null,
        market_auction_start: [],
        market_auction_start_days_before_delivery: null,
        market_auction_start_time_hours: null,
        market_auction_start_time_minutes: null,
        market_auction_start_minutes: null,
        market_auction_end: [],
        market_auction_end_days_before_delivery: null,
        market_auction_end_time_hours: null,
        market_auction_end_time_minutes: null,
        market_auction_end_minutes: null,
        bid_inputs: [],
        internal_bidding_input_format: [],
        internal_auction_start: [],
        internal_auction_start_days_before_delivery: null,
        internal_auction_start_time_hours: null,
        internal_auction_start_time_minutes: null,
        internal_auction_start_minutes: null,
        internal_auction_end: [],
        internal_auction_end_days_before_delivery: null,
        internal_auction_end_time_hours: null,
        internal_auction_end_time_minutes: null,
        internal_auction_end_minutes: null,
        customer_bidding_input_format: [],
        customer_auction_start: [],
        customer_auction_start_days_before_delivery: null,
        customer_auction_start_local_time_hours: null,
        customer_auction_start_local_time_minutes: null,
        customer_auction_start_minutes: null,
        customer_auction_minimum_mw_volume_for1_bid: null,
        customer_auction_end: [],
        customer_auction_end_days_before_delivery: null,
        customer_auction_end_local_time_hours: null,
        customer_auction_end_local_time_minutes: null,
        customer_auction_end_minutes: null,
        optimizer_bod_frequency_minutes: null,
        optimizer_bod_computed_sp: null,
        optimizer_bod_delta_for_submission: null,
        optimizer_bod_delta_for_undo: null,
        api_auction_bidding: [],
        auction_system: [],
        auction_system_upload_behaviour: [],
        auction_system_upload_market_time: null,
        auction_system_upload_number_of_retries: null,
        auction_system_upload_seconds_between_retries: null,
        auction_system_download_behaviour: [],
        auction_system_download_market_time: null,
        auction_system_download_number_of_retries: null,
        auction_system_download_seconds_between_retries: null,
        auction_system_ui_trader_look_ahead_hours: null,
        auction_system_ui_price_grouping_step: null,
        auction_system_ui_bid_expiry_warning_minutes: null,
        auction_system_ui_currency: null,
        auction_system_ui_site_id: null,
        auction_system_ui_for_battery_uk_intraday_asset_id: [],
        auction_system_ui_for_battery_uk_intraday_portfolio_id: null,
        auction_system_ui_for_battery_uk_intraday_export_entrader: [],
        auction_system_ui_for_battery_uk_intraday_upload_folder: null,
        create_nominations: [],
        create_allocations: [],
        allocation_start_extension_seconds: null,
        allocation_end_extension_seconds: null,
        allocation_end_extension_from_ramp_rate: [],
    }

    assets: any = [];
    assetsUk: any = [];
    assetsDe: any = [];
    dispatchGroups: any[] = [];
    biddingMethodMarketOptions: any[] = [];

    auctionConfigCopy = null;

    yesNoOptions = [
        {id: true, name: 'Yes'},
        {id: false, name: 'No'}
    ];

    biddingMethodOptions = [
        {id: 'SRL/MRL', name: 'MRL/SRL'},
        {id: 'SpotOptimization', name: 'Spot Optimization'},
        {id: 'BatteryUk', name: 'Battery UK Optimization'},
        {id: 'BatteryUkBm', name: 'Battery UK BM'},
        {id: 'BatteryUkWithinDayOptimization', name: 'Battery UK Within Day Optimization'},
        {id: 'BatteryDe', name: 'Battery Germany Optimization'},
        {id: 'BatteryDeWithinDayOptimization', name: 'Battery Germany Within Day Optimization'},
        {id: 'nlAfrr', name: 'NL-aFRR'},
    ];

    deliveryIntervalOptions = [
        {id: "Blocks", name: "Blocks"},
        {id: "Days", name: "Days"},
        {id: "Weeks", name: "Weeks"},
        {id: "Various", name: "Other"},
    ];

    offersTimeBlockOptions = [
        {id: 'QuarterOfHour', name: 'Quarter Of Hour'},
        {id: 'HalfHour', name: 'Half Hour'},
        {id: 'OneHour', name: 'One Hour'},
        {id: 'FourHours', name: 'Four Hours'},
    ];

    biddingDirectionOptions = [
        {id: 'PositiveAndNegative', name: 'Positive And Negative'},
        {id: 'Positive', name: 'Positive'},
        {id: 'Negative', name: 'Negative'},
        {id: 'Symmetric', name: 'Symmetric'},
    ];

    priceTypeOptions = [
        {id: "EnergyOnly", name: "Energy Only"},
        {id: "CapacityOnly", name: "Capacity Only"},
        {id: "EnergyAndCapacity", name: "Energy And Capacity"},
    ];

    internalBiddingInputFormatOptions = [
        {id: 'RegelleistungMRL', name: 'Regelleistung MRL'},
        {id: 'RegelleistungSRL', name: 'Regelleistung SRL'},
        {id: 'RegelleistungPRL', name: 'Regelleistung PRL'},
        {id: 'GenericCSV', name: 'GenericCSV'},
        {id: 'N2EX1h', name: 'N2EX1h'},
        {id: 'EPEX30min', name: 'EPEX30min'},
        {id: 'DC', name: 'Dynamic Containment'},
        {id: 'DS', name: 'Dynamic Services'},
        {id: 'nlAfrr', name: 'NL-aFRR'},
    ];

    marketAuctionStartOptions = [
        {id: 'AuctionTimeAnyTime', name: 'Anytime before auction ends'},
        {id: 'AuctionTimeAbsolute', name: 'Absolute: Time before delivery interval starts'},
        {id: 'AuctionTimeRelative', name: 'Relative: Minutes before offer block starts'},
    ];

    marketAuctionEndOptions = [
        {id: 'AuctionTimeAbsolute', name: 'Absolute: Time before delivery interval starts'},
        {id: 'AuctionTimeRelative', name: 'Relative: Minutes before offer block starts'},
        {id: 'AuctionTimeRelativeToBid', name: 'Relative: Based on bid'},
    ];
    marketAuctionEndForDeliveryBlockOptions = [
        {id: 'AuctionTimeRelative', name: 'Relative: Minutes before offer block starts'},
    ];

    bidInputsOptions = [
        {id: 'InternalBiddingKind', name: 'Internal'},
        {id: 'CustomerBiddingKind', name: 'Customer'},
        {id: 'V2gBiddingKind', name: 'V2G API'},
        {id: 'SpotOptimisationBiddingKind', name: 'Spot Optimization Bidding API'},
        {id: 'OptimizerBodKind', name: 'Optimizer BOD'},
    ];

    internalAuctionStartOptions = [
        {id: 'AuctionTimeAnyTime', name: 'Anytime before auction ends'},
        {id: 'AuctionTimeAbsolute', name: 'Absolute: Time before delivery interval starts'},
        {id: 'AuctionTimeRelative', name: 'Relative: Minutes before offer block starts'},
    ];

    internalAuctionEndOptions = [
        {id: 'AuctionTimeAbsolute', name: 'Absolute: Time before delivery interval starts'},
        {id: 'AuctionTimeRelative', name: 'Relative: Minutes before offer block starts'},
    ]
    internalAuctionEndForDeliveryBlockOptions = [
        {id: 'AuctionTimeRelative', name: 'Relative: Minutes before offer block starts'},
    ];

    customerBiddingInputFormatOptions = [
        {id: 'RegelleistungMRL', name: 'Regelleistung MRL'},
        {id: 'RegelleistungSRL', name: 'Regelleistung SRL'},
    ];

    customerAuctionStartOptions = [
        {id: 'AuctionTimeAnyTime', name: 'Anytime before auction ends'},
        {id: 'AuctionTimeAbsolute', name: 'Absolute: Time before delivery interval starts'},
        {id: 'AuctionTimeRelative', name: 'Relative: Minutes before offer block starts'},
    ];

    customerAuctionEndOptions = [
        {id: 'AuctionTimeAbsolute', name: 'Absolute: Time before delivery interval starts'},
        {id: 'AuctionTimeRelative', name: 'Relative: Minutes before offer block starts'},
    ];
    customerAuctionEndForDeliveryBlockOptions = [
        {id: 'AuctionTimeRelative', name: 'Relative: Minutes before offer block starts'},
    ];

    apiAuctionBiddingOptions = [
        {id: "V2gBiddingKind", name: "V2G Bidding API"},
        {id: "SpotOptimisationBiddingKind", name: "Spot Optimization Bidding API"}
    ];

    auctionSystemOptions = [
        {id: "upload", name: "Upload"},
        {id: "download", name: "Download"},
        {id: "ui", name: "UI"},
        {id: "ui_for_battery_uk_intraday", name: "UI for UK Batteries"},
        {id: "uk_vatp", name: "UK VATP"},
        {id: "de_vatp", name: "Germany VATP"},
    ];

    auctionSystemUploadBehaviourOptions = [
        {id: 'RegelleistungMRLEnergy', name: 'Regelleistung MRL Energy'},
        {id: 'RegelleistungMRLCapacity', name: 'Regelleistung MRL Capacity'},
        {id: 'RegelleistungSRLEnergy', name: 'Regelleistung SRL Energy'},
        {id: 'RegelleistungSRLCapacity', name: 'Regelleistung SRL Capacity'},
        {id: 'UKTradingDayAhead', name: 'UK Trading Day Ahead'},
        {id: 'DETradingDayAhead', name: 'Germany Trading Day Ahead'},
        {id: 'nlAfrr', name: 'NL-aFRR'},
        {id: 'FOBSnapshot', name: 'FOB Snapshot'},
        {id: 'BOD_enVoy', name: 'BOD enVoy'},
    ];

    auctionSystemDownloadBehaviourOptions = [
        {id: 'RegelleistungMRLEnergy', name: 'Regelleistung MRL Energy'},
        {id: 'RegelleistungMRLCapacity', name: 'Regelleistung MRL Capacity'},
        {id: 'RegelleistungSRLEnergy', name: 'Regelleistung SRL Energy'},
        {id: 'RegelleistungSRLCapacity', name: 'Regelleistung SRL Capacity'},
        {id: 'UKTradingDayAhead', name: 'UK Trading Day Ahead'},
        {id: 'DETradingDayAhead', name: 'Germany Trading Day Ahead'},
        {id: 'FOB', name: 'FOB'},
        {id: 'AllAccepted', name: 'All Accepted'},
    ];

    optimizationHorizonChangeOptions = [
        {id: 'OptimizationHorizonChangeAbsolute', name: 'Absolute: Time before delivery interval starts'},
    ]

    time_zones = moment.tz.names();
    assetsDropdownSettings: any;
    multipleAssetsDropdownSettings: any;

    optionsWithEmpty(options) {
        return [{id: null, name: "Select"}].concat(options);
    }

    optionNames(optionsArray) {
        // console.log("###OPTIONNAMES", optionsArray)
        //TODO[mihai-x]: use pipe
        if (optionsArray) {
            return optionsArray.map((x) => x.name).join(", ");
        } else {
            return '-';
        }
    }

    optionDisplayNames(optionsArray) {
        // console.log("###OPTION_DISPLAY_NAMES", optionsArray)
        //TODO[mihai-x]: use pipe
        if (optionsArray) {
            return optionsArray.map((x) => x.display_name).join(", ");
        } else {
            return '-';
        }
    }

    dropdownSettings(singleSelection, allowSearchFilter, fieldName) {
      return {
        idField: 'id',
        textField: fieldName,
        singleSelection: singleSelection,
        selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
        unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
        searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
        noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
        itemsShowLimit: 10,
        allowSearchFilter: allowSearchFilter
      }
    };

    onSelectedAuctionValidityIntervalChanged(date) {
        if (this.auctionConfig.time_zone.length > 0 && date.value[0]) {
            date.value[0].set({'second': 0, 'millisecond': 0});
            if (date.value[1]) {
                date.value[1].set({'second': 0, 'millisecond': 0});
                this.auctionConfig.auction_definition_interval = [
                    date.value[0].tz(this.auctionConfig.time_zone[0].toString(), true),
                    date.value[1].tz(this.auctionConfig.time_zone[0].toString(), true)
                ]
            }
            else {
                this.auctionConfig.auction_definition_interval = [
                  date.value[0].tz(this.auctionConfig.time_zone[0].toString(), true)
                ]
            }
        }
    }

    selectedTimezoneChanged(values) {
        if (this.auctionConfig.time_zone.length > 0 && this.auctionConfig.auction_definition_interval) {
            this.auctionConfig.auction_definition_interval = this.auctionConfig.auction_definition_interval.map(
              x => x.tz(this.auctionConfig.time_zone.toString(), true)
            )
        }
    }

    onSelectedDeliveryIntervalChanged() {
        console.log("### DELIVERY INTERVAL CHANGED", this.auctionConfig.delivery_interval)
        if (Array.isArray(this.auctionConfig.delivery_interval) &&
            this.auctionConfig.delivery_interval.some(o => o.id === "Blocks")) {

            if (!this.auctionConfig.market_auction_end.some(o => o.id === "AuctionTimeRelative")) {
                this.auctionConfig.market_auction_end = [];
            }
            if (!this.auctionConfig.internal_auction_end.some(o => o.id === "AuctionTimeRelative")) {
                this.auctionConfig.internal_auction_end = [];
            }
            if (!this.auctionConfig.customer_auction_end.some(o => o.id === "AuctionTimeRelative")) {
                this.auctionConfig.customer_auction_end = [];
            }
        }
    }

    auctionConfigBiddingMethodChanged(values) {
        console.log("auctionConfigBiddingMethodChanged", this.auctionConfig.bidding_method)
        this.auctionConfig.assets = []
        this.loadBiddingMethodAssets();
        this.loadBiddingMethodMarkets();
    }

    loadBiddingMethodAssets() {
        if (this.auctionConfig.bidding_method.length > 0) {
            if (this.auctionConfig.bidding_method[0].id == 'BatteryDeWithinDayOptimization' ||
                this.auctionConfig.bidding_method[0].id == 'BatteryDe') {
                this.getAssets('VPP_DE')
            }
            if (this.auctionConfig.bidding_method[0].id == 'BatteryUkWithinDayOptimization' ||
                this.auctionConfig.bidding_method[0].id == 'BatteryUk') {
                this.getAssets('VPP_UK')
            }
        }
    }

    loadBiddingMethodMarkets() {
        if (this.auctionConfig.bidding_method.length > 0) {
            if (this.auctionConfig.bidding_method[0].id == 'BatteryDe' ||
                this.auctionConfig.bidding_method[0].id == 'BatteryDeWithinDayOptimization') {
                this.getBiddingMethodMarkets('VPP_DE')
            }
            if (this.auctionConfig.bidding_method[0].id == 'BatteryUk' ||
                this.auctionConfig.bidding_method[0].id == 'BatteryUkWithinDayOptimization') {
                this.getBiddingMethodMarkets('VPP_UK')
            }
        }
    }

    constructor(
        public translate: TranslateService,
        public _global: GlobalService,
        private _notificationService: NotificationService,
        private _Asset: AssetProvider,
        private _auctionsProvider: AuctionsProvider,
        private _dispatchGroupProvider: DispatchGroupProvider,
        private _marketProvider: MarketProvider,
        private _confirmService: ConfirmService
    ) {
        this.translate.get('CONFIRM.MESSAGE.DELETE_ENTRY').subscribe((x) => {
            this.confirmDeleteMessage = x;
        });
        this.permissions = angularData.permissions;
        this.assetsDropdownSettings = this.dropdownSettings(true, true, 'display_name');
        this.multipleAssetsDropdownSettings = this.dropdownSettings(false, true, 'display_name');

        this._translateOptions(this.yesNoOptions, 'CONFIGURATION.AUCTION.yesNoOptions.');
        this._translateOptions(this.deliveryIntervalOptions, 'CONFIGURATION.AUCTION.deliveryIntervalOptions.');
        this._translateOptions(this.offersTimeBlockOptions, 'CONFIGURATION.AUCTION.offersTimeBlockOptions.');
        this._translateOptions(this.biddingDirectionOptions, 'CONFIGURATION.AUCTION.biddingDirectionOptions.');
        this._translateOptions(this.priceTypeOptions, 'CONFIGURATION.AUCTION.priceTypeOptions.');
        this._translateOptions(this.marketAuctionStartOptions, 'CONFIGURATION.AUCTION.marketAuctionStartOptions.');
        this._translateOptions(this.marketAuctionEndOptions, 'CONFIGURATION.AUCTION.marketAuctionEndOptions.');
        this._translateOptions(this.bidInputsOptions, 'CONFIGURATION.AUCTION.bidInputsOptions.');
    }

    _translateOptions(options, prefix) {
        if (options) {
            let repl = options.map((x) => {
                return {
                    id: x.id,
                    name: this.translate.instant(prefix + x.id)
                };
            });
            options.splice(0);
            options.push(...repl);
        }
    }

    ngOnInit(): void {
        this.isEditing = this.openInEditMode;
        this.getDispatchGroups();
        this.getAssets('VPP_UK');
        this.getAssets('VPP_DE');
        console.log("### ON INIT")
    }

    ngOnChanges(changes: SimpleChanges) {
        if (changes['auctionConfig']) {
            console.log("### Auction Config CHANGES", this.auctionConfig)
            //set array objects for ng-multiselect
            this.setArrayOptionsWithIdAndName('bidding_method', this.biddingMethodOptions);
            this.loadBiddingMethodAssets();
            this.loadBiddingMethodMarkets();
            this.setArrayOptionsWithIdAndName('delivery_interval', this.deliveryIntervalOptions);
            this.setArrayOptionsWithIdAndName('offers_time_block', this.offersTimeBlockOptions);
            this.setArrayOptionsWithIdAndName('bidding_direction', this.biddingDirectionOptions);
            this.setArrayOptionsWithIdAndName('price_type', this.priceTypeOptions);
            this.setArrayOptionsWithIdAndName('market_auction_start', this.marketAuctionStartOptions);
            this.setArrayOptionsWithIdAndName('market_auction_end', this.marketAuctionEndOptions);
            this.setArrayOptionsWithIdAndName('bid_inputs', this.bidInputsOptions);
            this.setArrayOptionsWithIdAndName('internal_bidding_input_format', this.internalBiddingInputFormatOptions);
            this.setArrayOptionsWithIdAndName('internal_auction_start', this.internalAuctionStartOptions);
            this.setArrayOptionsWithIdAndName('internal_auction_end', this.internalAuctionEndOptions);
            this.setArrayOptionsWithIdAndName('customer_bidding_input_format', this.customerBiddingInputFormatOptions);
            this.setArrayOptionsWithIdAndName('customer_auction_start', this.customerAuctionStartOptions);
            this.setArrayOptionsWithIdAndName('customer_auction_end', this.customerAuctionEndOptions);
            this.setArrayOptionsWithIdAndName('api_auction_bidding', this.apiAuctionBiddingOptions);
            this.setArrayOptionsWithIdAndName('auction_system', this.auctionSystemOptions);
            this.setArrayOptionsWithIdAndName('auction_system_upload_behaviour', this.auctionSystemUploadBehaviourOptions);
            this.setArrayOptionsWithIdAndName('auction_system_download_behaviour', this.auctionSystemDownloadBehaviourOptions);
            this.setArrayOptionsWithIdAndName('create_nominations', this.yesNoOptions);
            this.setArrayOptionsWithIdAndName('create_allocations', this.yesNoOptions);
            this.setArrayOptionsWithIdAndName('auction_system_ui_for_battery_uk_intraday_export_entrader', this.yesNoOptions);
            this.setArrayOptionsWithIdAndName('optimization_horizon_change', this.optimizationHorizonChangeOptions);
            this.setArrayOptionsWithIdAndName('allocation_end_extension_from_ramp_rate', this.yesNoOptions);
            if (this.auctionConfig.time_zone) {
                this.auctionConfig.auction_definition_interval = this.auctionConfig.auction_definition_interval.map(t => moment(t).tz(this.auctionConfig.time_zone.toString()));
            }
            this.auctionConfigCopy = {...this.auctionConfig}
            console.log("#### COPY:", this.auctionConfigCopy)
        }
    }

    setArrayOptionsWithIdAndName(fieldName, options) {
        console.log("### SET", fieldName, options, "=", this.auctionConfig[fieldName])
        if (this.auctionConfig[fieldName] && !this.isArrayWithIds(this.auctionConfig[fieldName]) && this.auctionConfig[fieldName].length > 0) {
            this.auctionConfig[fieldName] = this.auctionConfig[fieldName].map((x) =>
                this.findOption(x, options));
        }
        console.log("### SET to", this.auctionConfig[fieldName]);
    }

    findOption(id, options) {
        return options.find(e => e.id == id);
    }

    isArrayWithIds(value) {
        let isArray = value && Array.isArray(value);
        if (isArray) {
            if (value.length == 0) {
                return true;
            } else {
                return value[0].hasOwnProperty("id");
            }
        } else {
            return false;
        }
    }


    close(event) {
        this.done.emit();
    }

    cancel(event) {
        this.canceled.emit();
    }

    getAssets(customerType) {
      const noData = angularData.railsExports.locale == 'en-GB' ? 'No Asset available' : 'Keine Anlage verfügbar';
      this.assetsDropdownSettings.noDataAvailablePlaceholderText = noData;
      this.multipleAssetsDropdownSettings.noDataAvailablePlaceholderText = noData;
      this._Asset.findAllForAssetOptimization({customer_type: customerType}).subscribe(
      result => {
        if (customerType == 'VPP_UK') {
            this.assetsUk = result.assets;
            this.assetsUk = this.mapAssetsAddingDisplayName(this.assetsUk);
            this.setArrayOptionsWithIdAndName('auction_system_ui_for_battery_uk_intraday_asset_id', this.assetsUk);
            this.assets = this.assetsUk;
        }
        if (customerType == 'VPP_DE') {
            this.assetsDe = result.assets;
            this.assetsDe = this.mapAssetsAddingDisplayName(this.assetsDe);
            this.assets = this.assetsDe;
        }
        this.setArrayOptionsWithIdAndName('assets', this.assets);
        this.auctionConfigCopy = {...this.auctionConfig}
      }, err => {
          console.error('Failed to load asset list', err);
      });
    }

    getAssetDisplayName(a) {
      return `#${a.id} ${a.name} (${a.customer_name})`;
    }

    mapAssetsAddingDisplayName(assets) {
      return assets.map((x) => {
        x['display_name'] = this.getAssetDisplayName(x);
        return x;
      });
    }

    getDispatchGroups() {
        this._dispatchGroupProvider.findAllWithPagination().subscribe(result => {
            this.dispatchGroups = result.dispatch_groups;
            this.setArrayOptionsWithIdAndName('dispatch_group_ids', this.dispatchGroups);
            this.auctionConfigCopy = {...this.auctionConfig}
        });
    }

    getBiddingMethodMarkets(customerType) {
        this._marketProvider.findAllWithPagination().subscribe(result => {
            var allMarkets = result.markets;
            const marketOptionsByCustomerType: { [key: string]: string[] } = {
                VPP_UK: [
                    "DynamicContainment", "N2EX1H", "EPEX30MIN",
                    "DynamicModeration", "DynamicRegulation",
                    "Imbalance", "Intraday"
                ],
                VPP_DE: [
                    // Remove EPEX15MIN once it's been renamed to EPEXIDA1 in PROD.
                    "FCR", "AFRR", "EPEXIDA1", "EPEX15MIN",
                    "Imbalance", "Intraday"
                ]
            };
            const validMarkets = marketOptionsByCustomerType[customerType] || [];
            this.biddingMethodMarketOptions = allMarkets
                .filter(m => validMarkets.includes(m.name))
                .map(x => ({ id: x.name, name: x.name }));
            this.setArrayOptionsWithIdAndName('bidding_method_market', this.biddingMethodMarketOptions);
            this.auctionConfigCopy = {...this.auctionConfig}
        });
    }

    enterEditMode() {
        this.isEditing = true;
        this.limitedEditMode = true;
    }

    cancelEditMode() {
        this.auctionConfig = {...this.auctionConfigCopy}
        console.log("#### CANCEL:", this.auctionConfig)
        this.isEditing = false;
    }

    biddingMethodSpotOptimisation() {
        return this.auctionConfig.bidding_method.filter(m => m.id == "SpotOptimization").length > 0
    }

    auctionSystemUiForBatteryUkIntraday() {
        return this.auctionConfig.auction_system.filter(m => m.id == "ui_for_battery_uk_intraday").length > 0
    }

    requireDispatchGroup() {
        return this.auctionConfig.create_allocations.filter(m => m.id == true).length > 0 ||
          this.auctionConfig.create_nominations.filter(m => m.id == true).length > 0
    }

    toggleSwingLimit() {
        if (this.isEditing) {
            this.auctionConfig.swing_limit = !this.auctionConfig.swing_limit;
        }
    }

    createOrUpdateAuctionConfig() {
        console.log('### createOrUpdateAuctionConfig', this.auctionConfig);

        this.errors = [];

        let isNew = !this.auctionConfig.id;

        let params = {
            auction_config: {...this.auctionConfig}
        };
        params.auction_config.dispatch_group_ids = params.auction_config.dispatch_group_ids.map((x) => x.id);
        if (params.auction_config.assets != null) {
            params.auction_config.assets = params.auction_config.assets.map((x) => x.id);
        }
        else {
            params.auction_config.assets = []
        }

        this.canSubmit = false;
        let fn = isNew ? this._auctionsProvider.create : this._auctionsProvider.update;
        fn.call(this._auctionsProvider, params).subscribe(
            (res) => {
                this.canSubmit = true;
                if (res.success == true) {
                    this._notificationService.success({text: 'SUCCESS'});
                    this.done.emit();
                } else {
                    this.errors = res.messages;
                }
            },
            (err) => {
                console.log('submit failed', err);
                this.errors = [JSON.stringify(err)];
                this.canSubmit = true;
            }
        );
    }

    delete() {
        this._confirmService.confirm({
            message: this.confirmDeleteMessage,
        }).then(() => {
            this._deleteConfirmed();
        }, () => {
        });
    }

    _deleteConfirmed() {
        this._auctionsProvider.delete(this.auctionConfig.id).subscribe(
            (res) => {
                if (res.success == true) {
                    this._notificationService.success({text: 'SUCCESS'});
                    this.done.emit();
                } else {
                    this._notificationService.error({text: JSON.stringify(res.error)});
                }
            },
            (err) => {
                console.log('Failed to delete', err);
                this._notificationService.error({text: JSON.stringify(err)});
            });
    }

}
