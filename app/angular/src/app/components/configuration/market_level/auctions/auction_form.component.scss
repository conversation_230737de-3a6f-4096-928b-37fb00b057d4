@import "./../../../../styles/colors";

vpp-auction-configuration-form{
  display: block;

  &.embedded {
    #add-auction-config-tab.tab-content.light-blue {
      &:before {
        border-bottom: 15px solid #c7e8eb;
      }
    }
  }

  .form-control[readonly] {
    background: white;
  }

  #add-auction-config-tab.tab-content.light-blue {

    position: relative;
    background: $eon-turquoise-25;
    padding: 30px;
    border-top: none;

    section {
      h6 {
        color: red;
      }
    }

    i.fa-question-circle {
      color: #5c7cfa;
    }

    h2 {
      color: $eon-turquoise-dark;
    }

    .form-control {
      border-color: $eon-turquoise;
    }

    .form-group.col-md-6 {
      max-width: 100%;
      flex: 0 0 100%;
    }

    &:before {
      content: "";
      position: absolute;
      width: 0;
      height: 0;
      border-left: 15px solid transparent;
      border-right: 15px solid transparent;
      //border-bottom: 15px solid #c7e8eb;
      top: -15px;
      left: 90px;
    }

    //TODO
    .multiselect-dropdown {
      .dropdown-btn {
        border: 2px solid $eon-turquoise !important;
        background: #fff !important;
        border-radius: 3px !important;
        min-height: 52px !important;
      }
    }

  }

  .alerts-wrapper-static {
    margin-bottom: 15px;
    margin-top: 15px;
  }

  ul.form-actions {
    li {
      margin-right: 1em;
    }
  }
}

ngb-modal-window {
  vpp-auction-config-form{
    #full-width-in-modal {
      flex: 0 0 100%;
      max-width: 100%;
    }

    .tab-content.light-blue:before {
      border: 0;
    }
  }
}
