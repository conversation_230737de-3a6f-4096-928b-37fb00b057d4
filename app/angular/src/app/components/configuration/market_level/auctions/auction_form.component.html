<div class="row">
    <div class="col-md-12" id="full-width-in-modal">
        <div class="tab-content light-blue" style="border: none" id="add-auction-config-tab">
            <span *ngIf="!embedded" class="pull-right" (click)="cancel($event)">
                <i class="fa fa-times"></i>
            </span>

            <h2>{{ 'CONFIGURATION.MARKET_LEVEL.AUCTION_CONFIGURATION.ADD' | translate }}</h2>

            <div class="alerts-wrapper-static" *ngIf="errors.length">
                <ng-container *ngFor="let errmsg of errors; let ix = index;">
                    <ngb-alert
                            [dismissible]="false"
                            [type]="'error'"> {{ errmsg }}
                    </ngb-alert>
                </ng-container>
            </div>

            <section>
                <h6>{{'CONFIGURATION.AUCTION.LABEL.GENERIC'|translate}}</h6>

                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.NAME' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.name"
                            type="text"
                            autocomplete="false"
                            class="form-control"
                            id="name"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.name }}</span>
                    </div>
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_VALIDITY_INTERVAL' | translate }}</label>
                        <div *ngIf="isEditing">
                            <input
                                type="text"
                                class="form-control"
                                [owlDateTime]="auctionValidityIntervalId"
                                [selectMode]="'range'"
                                [owlDateTimeTrigger]="auctionValidityIntervalId"
                                [(ngModel)]="auctionConfig.auction_definition_interval"
                                [owlDateTimeFilter]="maxRangeFilter"
                                (dateTimeChange)="onSelectedAuctionValidityIntervalChanged($event)"
                                readonly="true"
                                placeholder="">
                            <owl-date-time
                                #auctionValidityIntervalId
                                (afterPickerOpen)="_global.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
                                [pickerMode]="'popup'">
                            </owl-date-time>
                        </div>
                        <span *ngIf="!isEditing" class="value">
                            {{ auctionConfig.auction_definition_interval[0] | tzDate: auctionConfig.time_zone:'ddd, DD MMM YYYY HH:mm' }}
                            -
                            <span *ngIf="auctionConfig.auction_definition_interval.length > 1">
                                {{ auctionConfig.auction_definition_interval[1] | tzDate: auctionConfig.time_zone:'ddd, DD MMM YYYY HH:mm' }}
                            </span>
                        </span>
                    </div>
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.TIME_ZONE' | translate }}</label>
                        <ng-multiselect-dropdown *ngIf="isEditing && !limitedEditMode"
                            [placeholder]="('COMPONENTS.SELECT'|translate)"
                            [data]="time_zones"
                            [(ngModel)]="auctionConfig.time_zone"
                            [settings]="dropdownSettings(true, true, 'name')"
                            (ngModelChange)="selectedTimezoneChanged($event)"
                        >
                        </ng-multiselect-dropdown>
                        <span *ngIf="!isEditing || limitedEditMode" class="value">{{ auctionConfig.time_zone[0] }}</span>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-12">
                        <label [ngClass]="(isEditing && !biddingMethodSpotOptimisation() && !auctionSystemUiForBatteryUkIntraday() && requireDispatchGroup()) ? 'required' :''">{{ 'CONFIGURATION.AUCTION.DISPATCH_GROUPS' | translate }}</label>
                        <ng-multiselect-dropdown *ngIf="isEditing"
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="dispatchGroups"
                            [(ngModel)]="auctionConfig.dispatch_group_ids"
                            [settings]="dropdownSettings(false, true, 'name')">
                        </ng-multiselect-dropdown>
                        <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.dispatch_group_ids) }}</span>
                    </div>
                </div>
            </section>
            <section>
                <h6>{{'CONFIGURATION.AUCTION.LABEL.BIDDING_LOGIC'|translate}}</h6>

                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label>{{ 'CONFIGURATION.AUCTION.BIDDING_METHOD' | translate }}</label>
                        <ng-multiselect-dropdown *ngIf="isEditing"
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="biddingMethodOptions"
                            [(ngModel)]="auctionConfig.bidding_method"
                            (ngModelChange)="auctionConfigBiddingMethodChanged($event)"
                            [settings]="dropdownSettings(true, false, 'name')">
                        </ng-multiselect-dropdown>
                        <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.bidding_method) }}</span>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.bidding_method | showParametersFor: 'BatteryUk') || (auctionConfig.bidding_method | showParametersFor: 'BatteryDe') || (auctionConfig.bidding_method | showParametersFor: 'BatteryUkWithinDayOptimization') || (auctionConfig.bidding_method | showParametersFor: 'BatteryDeWithinDayOptimization')">
                        <label>{{ 'CONFIGURATION.AUCTION.BIDDING_METHOD_MARKET' | translate }}</label>
                        <ng-multiselect-dropdown *ngIf="isEditing"
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="biddingMethodMarketOptions"
                            [(ngModel)]="auctionConfig.bidding_method_market"
                            [settings]="dropdownSettings(false, false, 'name')">
                        </ng-multiselect-dropdown>
                        <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.bidding_method_market) }}</span>
                    </div>
                </div>
                <!-- Swing Limit -->
                <div class="form-row" *ngIf="(auctionConfig.bidding_method | showParametersFor: 'BatteryUk') || (auctionConfig.bidding_method | showParametersFor: 'BatteryDe')">
                    <div class="form-group col-md-4">
                        <label>{{ 'CONFIGURATION.AUCTION.SWING_LIMIT' | translate }}</label>
                        <div class="form-check">
                            <label
                                class="eon-checkbox-label bg-eon-red"
                                (click)="toggleSwingLimit()"
                                [ngClass]="{'non-clickable': !isEditing, 'checked': auctionConfig.swing_limit}">
                            </label>
                        </div>
                    </div>
                    <div class="form-group col-md-4" *ngIf="auctionConfig.swing_limit">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.SWING_LIMIT_VALUE' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.swing_limit_value"
                            type="number"
                            step="1"
                            autocomplete="false"
                            class="form-control"
                            id="swing_limit_value"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.swing_limit_value }}</span>
                    </div>
                </div>
                <div class="form-row" *ngIf="(auctionConfig.bidding_method | showParametersFor: 'BatteryUkWithinDayOptimization') || (auctionConfig.bidding_method | showParametersFor: 'BatteryDeWithinDayOptimization')">
                    <div class="form-group col-md-12">
                        <label>{{ 'CONFIGURATION.AUCTION.ASSETS' | translate }}</label>
                        <ng-multiselect-dropdown *ngIf="isEditing"
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="assets"
                            [(ngModel)]="auctionConfig.assets"
                            [settings]="multipleAssetsDropdownSettings">
                        </ng-multiselect-dropdown>
                        <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.assets) }}</span>
                    </div>
                </div>
                <div class="form-row" *ngIf="(auctionConfig.bidding_method | showParametersFor: 'BatteryUkWithinDayOptimization') || (auctionConfig.bidding_method | showParametersFor: 'BatteryDeWithinDayOptimization')">
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.FREQUENCY_TIME_MINUTES' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.frequency_time_minutes"
                            type="number"
                            step="1"
                            autocomplete="false"
                            class="form-control"
                            id="frequency_time_minutes"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.frequency_time_minutes }}</span>
                    </div>
                </div>
                <!--AuctionTimeSettings-->
                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.DELIVERY_INTERVAL' | translate }}</label>
                        <ng-multiselect-dropdown *ngIf="isEditing && !limitedEditMode"
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="deliveryIntervalOptions"
                            [(ngModel)]="auctionConfig.delivery_interval"
                            (ngModelChange)="onSelectedDeliveryIntervalChanged()"
                            [settings]="dropdownSettings(true, false, 'name')">
                        </ng-multiselect-dropdown>
                        <span *ngIf="!isEditing || limitedEditMode" class="value">{{ optionNames(auctionConfig.delivery_interval) }}</span>
                    </div>
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.OFFERS_TIME_BLOCK' | translate }}</label>
                        <ng-multiselect-dropdown *ngIf="isEditing && !limitedEditMode"
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="offersTimeBlockOptions"
                            [(ngModel)]="auctionConfig.offers_time_block"
                            [settings]="dropdownSettings(true, false, 'name')">
                        </ng-multiselect-dropdown>
                        <span *ngIf="!isEditing || limitedEditMode" class="value">{{ optionNames(auctionConfig.offers_time_block) }}</span>
                    </div>
                    <!--BidDirectionAndVolumeSettings-->
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.BIDDING_DIRECTION' | translate }}</label>
                        <ng-multiselect-dropdown *ngIf="isEditing"
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="biddingDirectionOptions"
                            [(ngModel)]="auctionConfig.bidding_direction"
                            [settings]="dropdownSettings(true, false, 'name')">
                        </ng-multiselect-dropdown>
                        <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.bidding_direction) }}</span>
                    </div>
                </div>
                <div class="form-row" *ngIf="(auctionConfig.delivery_interval | showParametersFor: 'Days') || (auctionConfig.delivery_interval | showParametersFor: 'Blocks')">
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.DELIVERY_INTERVAL_DAYS_SHIFTED_HOURS' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.delivery_interval_days_shifted_hours"
                            type="number"
                            step="1"
                            autocomplete="false"
                            class="form-control"
                            id="delivery_interval_days_shifted_hours"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.delivery_interval_days_shifted_hours }}</span>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.ROUNDING_DIGITS' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.rounding_digits"
                            type="number"
                            step="1"
                            autocomplete="false"
                            class="form-control"
                            id="rounding_digits"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.rounding_digits }}</span>
                    </div>
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? '' :''">{{ 'CONFIGURATION.AUCTION.MINIMUM_MW_VOLUME_FOR_1BID' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.minimum_mw_volume_for1_bid"
                            type="number"
                            step="0.001"
                            autocomplete="false"
                            class="form-control"
                            id="minimum_mw_volume_for1_bid"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.minimum_mw_volume_for1_bid }}</span>
                    </div>
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? '' :''">{{ 'CONFIGURATION.AUCTION.MINIMUM_MW_VOLUME' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.minimum_mw_volume"
                            type="number"
                            step="0.001"
                            autocomplete="false"
                            class="form-control"
                            id="minimum_mw_volume"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.minimum_mw_volume }}</span>
                    </div>
                </div>
                <!-- Price Type -->
                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label>{{ 'CONFIGURATION.AUCTION.PRICE_TYPE' | translate }}</label>
                        <ng-multiselect-dropdown *ngIf="isEditing"
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="priceTypeOptions"
                            [(ngModel)]="auctionConfig.price_type"
                            [settings]="dropdownSettings(true, false, 'name')">
                        </ng-multiselect-dropdown>
                        <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.price_type) }}</span>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.price_type | showParametersFor: 'EnergyOnly') || (auctionConfig.price_type | showParametersFor: 'CapacityOnly') || (auctionConfig.price_type | showParametersFor: 'EnergyAndCapacity')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.ENERGY_PRICE_ROUNDING_DIGITS' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.energy_price_rounding_digits"
                            type="number"
                            step="0.001"
                            autocomplete="false"
                            class="form-control"
                            id="energy_price_rounding_digits"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.energy_price_rounding_digits }}</span>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.price_type | showParametersFor: 'EnergyAndCapacity')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.CAPACITY_PRICE_ROUNDING_DIGITS' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.capacity_price_rounding_digits"
                            type="number"
                            step="0.001"
                            autocomplete="false"
                            class="form-control"
                            id="capacity_price_rounding_digits"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.capacity_price_rounding_digits }}</span>
                    </div>
                </div>
            </section>

            <section *ngIf="(auctionConfig.bidding_method | showParametersFor: 'BatteryUkWithinDayOptimization') || (auctionConfig.bidding_method | showParametersFor: 'BatteryDeWithinDayOptimization')">
                <h6>{{'CONFIGURATION.AUCTION.LABEL.OPTIMIZATION_HORIZON_CHANGE'|translate}}</h6>
                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.OPTIMIZATION_HORIZON_CHANGE' | translate }}</label>
                        <ng-multiselect-dropdown *ngIf="isEditing"
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="optimizationHorizonChangeOptions"
                            [(ngModel)]="auctionConfig.optimization_horizon_change"
                            [settings]="dropdownSettings(true, false, 'name')">
                        </ng-multiselect-dropdown>
                        <span *ngIf="!isEditing || limitedEditMode" class="value">{{ optionNames(auctionConfig.optimization_horizon_change) }}</span>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.optimization_horizon_change | showParametersFor: 'OptimizationHorizonChangeAbsolute')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.OPTIMIZATION_HORIZON_CHANGE_DAYS_BEFORE_DELIVERY' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.optimization_horizon_change_days_before_delivery"
                            type="number"
                            step="1"
                            autocomplete="false"
                            class="form-control"
                            id="optimization_horizon_change_days_before_delivery"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.optimization_horizon_change_days_before_delivery }}</span>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.optimization_horizon_change | showParametersFor: 'OptimizationHorizonChangeAbsolute')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.OPTIMIZATION_HORIZON_CHANGE_TIME' | translate }}</label>
                        <div class="form-row">
                            <div class="col-md-6">
                                <input *ngIf="isEditing"
                                    [(ngModel)]="auctionConfig.optimization_horizon_change_time_hours"
                                    type="number"
                                    step="1"
                                    autocomplete="false"
                                    class="form-control"
                                    id="optimization_horizon_change_time_hours"
                                    placeholder="hh"
                                >
                                <span *ngIf="!isEditing" class="value">{{ auctionConfig.optimization_horizon_change_time_hours }}</span>
                            </div>
                            <div class="col-md-6">
                                <input *ngIf="isEditing"
                                    [(ngModel)]="auctionConfig.optimization_horizon_change_time_minutes"
                                    type="number"
                                    step="1"
                                    autocomplete="false"
                                    class="form-control"
                                    id="optimization_horizon_change_time_minutes"
                                    placeholder="mm"
                                >
                                <span *ngIf="!isEditing" class="value">{{ auctionConfig.optimization_horizon_change_time_minutes }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <section>
                <h6>{{'CONFIGURATION.AUCTION.LABEL.MARKET_AUCTION'|translate}}</h6>

                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.MARKET_AUCTION_START' | translate }}</label>
                        <ng-multiselect-dropdown *ngIf="isEditing"
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="marketAuctionStartOptions"
                            [(ngModel)]="auctionConfig.market_auction_start"
                            [settings]="dropdownSettings(true, false, 'name')">
                        </ng-multiselect-dropdown>
                        <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.market_auction_start) }}</span>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.market_auction_start | showParametersFor: 'AuctionTimeAbsolute')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.MARKET_AUCTION_START_DAYS_BEFORE_DELIVERY' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.market_auction_start_days_before_delivery"
                            type="number"
                            step="1"
                            autocomplete="false"
                            class="form-control"
                            id="market_auction_start_days_before_delivery"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.market_auction_start_days_before_delivery }}</span>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.market_auction_start | showParametersFor: 'AuctionTimeAbsolute')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.MARKET_AUCTION_START_TIME' | translate }}</label>
                        <div class="form-row">
                            <div class="col-md-6">
                                <input *ngIf="isEditing"
                                    [(ngModel)]="auctionConfig.market_auction_start_time_hours"
                                    type="number"
                                    step="1"
                                    autocomplete="false"
                                    class="form-control"
                                    id="market_auction_start_time_hours"
                                    placeholder="hh"
                                >
                                <span *ngIf="!isEditing" class="value">{{ auctionConfig.market_auction_start_time_hours }}</span>
                            </div>
                            <div class="col-md-6">
                                <input *ngIf="isEditing"
                                    [(ngModel)]="auctionConfig.market_auction_start_time_minutes"
                                    type="number"
                                    step="1"
                                    autocomplete="false"
                                    class="form-control"
                                    id="market_auction_start_time_minutes"
                                    placeholder="mm"
                                >
                                <span *ngIf="!isEditing" class="value">{{ auctionConfig.market_auction_start_time_minutes }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.market_auction_start | showParametersFor: 'AuctionTimeRelative')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.MARKET_AUCTION_START_MINUTES' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.market_auction_start_minutes"
                            type="number"
                            step="1"
                            autocomplete="false"
                            class="form-control"
                            id="market_auction_start_minutes"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.market_auction_start_minutes }}</span>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.MARKET_AUCTION_END' | translate }}</label>
                        <span *ngIf="!(auctionConfig.delivery_interval | showParametersFor: 'Blocks') || (auctionConfig.bidding_method | showParametersFor: 'BatteryUk') || (auctionConfig.bidding_method | showParametersFor: 'BatteryDe')">
                            <ng-multiselect-dropdown *ngIf="isEditing"
                                [placeholder]="('COMPONENTS.SELECT' | translate )"
                                [data]="marketAuctionEndOptions"
                                [(ngModel)]="auctionConfig.market_auction_end"
                                [settings]="dropdownSettings(true, false, 'name')">
                            </ng-multiselect-dropdown>
                        </span>
                        <span *ngIf="(auctionConfig.delivery_interval | showParametersFor: 'Blocks') && (!(auctionConfig.bidding_method | showParametersFor: 'BatteryUk') && !(auctionConfig.bidding_method | showParametersFor: 'BatteryDe'))">
                            <ng-multiselect-dropdown *ngIf="isEditing"
                                [placeholder]="('COMPONENTS.SELECT' | translate )"
                                [data]="marketAuctionEndForDeliveryBlockOptions"
                                [(ngModel)]="auctionConfig.market_auction_end"
                                [settings]="dropdownSettings(true, false, 'name')">
                            </ng-multiselect-dropdown>
                        </span>
                        <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.market_auction_end) }}</span>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.market_auction_end | showParametersFor: 'AuctionTimeAbsolute')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.MARKET_AUCTION_END_DAYS_BEFORE_DELIVERY' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.market_auction_end_days_before_delivery"
                            type="number"
                            step="1"
                            autocomplete="false"
                            class="form-control"
                            id="market_auction_end_days_before_delivery"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.market_auction_end_days_before_delivery }}</span>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.market_auction_end | showParametersFor: 'AuctionTimeAbsolute')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.MARKET_AUCTION_END_TIME' | translate }}</label>
                        <div class="form-row">
                            <div class="col-md-6">
                                <input *ngIf="isEditing"
                                    [(ngModel)]="auctionConfig.market_auction_end_time_hours"
                                    type="number"
                                    step="1"
                                    autocomplete="false"
                                    class="form-control"
                                    id="market_auction_end_time_hours"
                                    placeholder="hh"
                                >
                                <span *ngIf="!isEditing" class="value">{{ auctionConfig.market_auction_end_time_hours }}</span>
                            </div>
                            <div class="col-md-6">
                                <input *ngIf="isEditing"
                                    [(ngModel)]="auctionConfig.market_auction_end_time_minutes"
                                    type="number"
                                    step="1"
                                    autocomplete="false"
                                    class="form-control"
                                    id="market_auction_end_time_minutes"
                                    placeholder="mm"
                                >
                                <span *ngIf="!isEditing" class="value">{{ auctionConfig.market_auction_end_time_minutes }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.market_auction_end | showParametersFor: 'AuctionTimeRelative')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.MARKET_AUCTION_END_MINUTES' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.market_auction_end_minutes"
                            type="number"
                            step="1"
                            autocomplete="false"
                            class="form-control"
                            id="market_auction_end_minutes"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.market_auction_end_minutes }}</span>
                    </div>
                </div>
            </section>

            <section>
                <h6>{{'CONFIGURATION.AUCTION.LABEL.BID_INPUTS'|translate}}</h6>

                <div class="form-row">
                    <div class="form-group col-md-4">
                        <ng-multiselect-dropdown *ngIf="isEditing"
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="bidInputsOptions"
                            [(ngModel)]="auctionConfig.bid_inputs"
                            [settings]="dropdownSettings(false, false, 'name')">
                        </ng-multiselect-dropdown>
                        <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.bid_inputs) }}</span>
                    </div>
                </div>
            </section>
            <section *ngIf="(auctionConfig.bid_inputs | showParametersFor: 'OptimizerBodKind')">
                <h6>{{'CONFIGURATION.AUCTION.LABEL.OPTIMIZER_BOD'|translate}}</h6>
                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.OPTIMIZER_BOD_FREQUENCY_MINUTES' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.optimizer_bod_frequency_minutes"
                            type="number"
                            step="1"
                            autocomplete="false"
                            class="form-control"
                            id="optimizer_bod_frequency_minutes"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.optimizer_bod_frequency_minutes }}</span>
                    </div>
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.OPTIMIZER_BOD_COMPUTED_SP' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.optimizer_bod_computed_sp"
                            type="number"
                            step="1"
                            autocomplete="false"
                            class="form-control"
                            id="optimizer_bod_computed_sp"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.optimizer_bod_computed_sp }}</span>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.OPTIMIZER_BOD_DELTA_FOR_SUBMISSION' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.optimizer_bod_delta_for_submission"
                            type="number"
                            step="0.001"
                            autocomplete="false"
                            class="form-control"
                            id="optimizer_bod_delta_for_submission"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.optimizer_bod_delta_for_submission }}</span>
                    </div>
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.OPTIMIZER_BOD_DELTA_FOR_UNDO' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.optimizer_bod_delta_for_undo"
                            type="number"
                            step="0.001"
                            autocomplete="false"
                            class="form-control"
                            id="optimizer_bod_delta_for_undo"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.optimizer_bod_delta_for_undo }}</span>
                    </div>
                </div>
            </section>

            <section *ngIf="(auctionConfig.bid_inputs | showParametersFor: 'InternalBiddingKind')">
                <h6>{{'CONFIGURATION.AUCTION.LABEL.INTERNAL_AUCTION'|translate}}</h6>

                <!-- Internal Bidding -->
                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.INTERNAL_BIDDING_FORMAT' | translate }}</label>
                        <ng-multiselect-dropdown *ngIf="isEditing"
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="internalBiddingInputFormatOptions"
                            [(ngModel)]="auctionConfig.internal_bidding_input_format"
                            [settings]="dropdownSettings(true, false, 'name')">
                        </ng-multiselect-dropdown>
                        <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.internal_bidding_input_format) }}</span>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.INTERNAL_AUCTION_START' | translate }}</label>
                        <ng-multiselect-dropdown *ngIf="isEditing"
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="internalAuctionStartOptions"
                            [(ngModel)]="auctionConfig.internal_auction_start"
                            [settings]="dropdownSettings(true, false, 'name')">
                        </ng-multiselect-dropdown>
                        <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.internal_auction_start) }}</span>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.internal_auction_start | showParametersFor: 'AuctionTimeAbsolute')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.INTERNAL_AUCTION_START_DAYS_BEFORE_DELIVERY' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.internal_auction_start_days_before_delivery"
                            type="number"
                            step="1"
                            autocomplete="false"
                            class="form-control"
                            id="internal_auction_start_days_before_delivery"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.internal_auction_start_days_before_delivery }}</span>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.internal_auction_start | showParametersFor: 'AuctionTimeAbsolute')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.INTERNAL_AUCTION_START_TIME' | translate }}</label>
                        <div class="form-row">
                            <div class="col-md-6">
                                <input *ngIf="isEditing"
                                    [(ngModel)]="auctionConfig.internal_auction_start_time_hours"
                                    type="number"
                                    step="1"
                                    autocomplete="false"
                                    class="form-control"
                                    id="internal_auction_start_time_hours"
                                    placeholder="hh"
                                >
                                <span *ngIf="!isEditing" class="value">{{ auctionConfig.internal_auction_start_time_hours }}</span>
                            </div>
                            <div class="col-md-6">
                                <input *ngIf="isEditing"
                                    [(ngModel)]="auctionConfig.internal_auction_start_time_minutes"
                                    type="number"
                                    step="1"
                                    autocomplete="false"
                                    class="form-control"
                                    id="internal_auction_start_time_minutes"
                                    placeholder="mm"
                                >
                                <span *ngIf="!isEditing" class="value">{{ auctionConfig.internal_auction_start_time_minutes }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.internal_auction_start | showParametersFor: 'AuctionTimeRelative')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.INTERNAL_AUCTION_START_MINUTES' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.internal_auction_start_minutes"
                            type="number"
                            step="1"
                            autocomplete="false"
                            class="form-control"
                            id="internal_auction_start_minutes"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.internal_auction_start_minutes }}</span>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.INTERNAL_AUCTION_END' | translate }}</label>
                        <span *ngIf="!(auctionConfig.delivery_interval | showParametersFor: 'Blocks')">
                            <ng-multiselect-dropdown *ngIf="isEditing"
                                [placeholder]="('COMPONENTS.SELECT' | translate )"
                                [data]="internalAuctionEndOptions"
                                [(ngModel)]="auctionConfig.internal_auction_end"
                                [settings]="dropdownSettings(true, false, 'name')">
                            </ng-multiselect-dropdown>
                        </span>
                        <span *ngIf="(auctionConfig.delivery_interval | showParametersFor: 'Blocks')">
                            <ng-multiselect-dropdown *ngIf="isEditing"
                                [placeholder]="('COMPONENTS.SELECT' | translate )"
                                [data]="internalAuctionEndForDeliveryBlockOptions"
                                [(ngModel)]="auctionConfig.internal_auction_end"
                                [settings]="dropdownSettings(true, false, 'name')">
                            </ng-multiselect-dropdown>
                        </span>
                        <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.internal_auction_end) }}</span>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.internal_auction_end | showParametersFor: 'AuctionTimeAbsolute')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.INTERNAL_AUCTION_END_DAYS_BEFORE_DELIVERY' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.internal_auction_end_days_before_delivery"
                            type="number"
                            step="1"
                            autocomplete="false"
                            class="form-control"
                            id="internal_auction_end_days_before_delivery"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.internal_auction_end_days_before_delivery }}</span>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.internal_auction_end | showParametersFor: 'AuctionTimeAbsolute')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.INTERNAL_AUCTION_END_TIME' | translate }}</label>
                        <div class="form-row">
                            <div class="col-md-6">
                                <input *ngIf="isEditing"
                                    [(ngModel)]="auctionConfig.internal_auction_end_time_hours"
                                    type="number"
                                    step="1"
                                    autocomplete="false"
                                    class="form-control"
                                    id="internal_auction_end_time_hours"
                                    placeholder="hh"
                                >
                                <span *ngIf="!isEditing" class="value">{{ auctionConfig.internal_auction_end_time_hours }}</span>
                            </div>
                            <div class="col-md-6">
                                <input *ngIf="isEditing"
                                    [(ngModel)]="auctionConfig.internal_auction_end_time_minutes"
                                    type="number"
                                    step="1"
                                    autocomplete="false"
                                    class="form-control"
                                    id="internal_auction_end_time_minutes"
                                    placeholder="mm"
                                >
                                <span *ngIf="!isEditing" class="value">{{ auctionConfig.internal_auction_end_time_minutes }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.internal_auction_end | showParametersFor: 'AuctionTimeRelative')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.INTERNAL_AUCTION_END_MINUTES' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.internal_auction_end_minutes"
                            type="number"
                            step="1"
                            autocomplete="false"
                            class="form-control"
                            id="internal_auction_end_minutes"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.internal_auction_end_minutes }}</span>
                    </div>
                </div>
            </section>

            <section *ngIf="(auctionConfig.bid_inputs | showParametersFor: 'CustomerBiddingKind')">
                <h6>{{'CONFIGURATION.AUCTION.LABEL.CUSTOMER_AUCTION'|translate}}</h6>

                <!-- Customer Bidding -->
                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.CUSTOMER_BIDDING_FORMAT' | translate }}</label>
                        <ng-multiselect-dropdown *ngIf="isEditing"
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="customerBiddingInputFormatOptions"
                            [(ngModel)]="auctionConfig.customer_bidding_input_format"
                            [settings]="dropdownSettings(true, false, 'name')">
                        </ng-multiselect-dropdown>
                        <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.customer_bidding_input_format) }}</span>
                    </div>
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.CUSTOMER_AUCTION_MINIMUM_MW_VOLUME_FOR_1BID' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.customer_auction_minimum_mw_volume_for1_bid"
                            type="number"
                            step="0.001"
                            autocomplete="false"
                            class="form-control"
                            id="customer_auction_minimum_mw_volume_for1_bid"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.customer_auction_minimum_mw_volume_for1_bid }}</span>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.CUSTOMER_AUCTION_START' | translate }}</label>
                        <ng-multiselect-dropdown *ngIf="isEditing"
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="customerAuctionStartOptions"
                            [(ngModel)]="auctionConfig.customer_auction_start"
                            [settings]="dropdownSettings(true, false, 'name')">
                        </ng-multiselect-dropdown>
                        <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.customer_auction_start) }}</span>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.customer_auction_start | showParametersFor: 'AuctionTimeAbsolute')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.CUSTOMER_AUCTION_START_DAYS_BEFORE_DELIVERY' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.customer_auction_start_days_before_delivery"
                            type="number"
                            step="1"
                            autocomplete="false"
                            class="form-control"
                            id="customer_auction_start_days_before_delivery"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.customer_auction_start_days_before_delivery }}</span>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.customer_auction_start | showParametersFor: 'AuctionTimeAbsolute')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.CUSTOMER_AUCTION_START_TIME' | translate }}</label>
                        <div class="form-row">
                            <div class="col-md-6">
                                <input *ngIf="isEditing"
                                    [(ngModel)]="auctionConfig.customer_auction_start_local_time_hours"
                                    type="number"
                                    step="1"
                                    autocomplete="false"
                                    class="form-control"
                                    id="customer_auction_start_local_time_hours"
                                    placeholder="hh"
                                >
                                <span *ngIf="!isEditing" class="value">{{ auctionConfig.customer_auction_start_local_time_hours }}</span>
                            </div>
                            <div class="col-md-6">
                                <input *ngIf="isEditing"
                                    [(ngModel)]="auctionConfig.customer_auction_start_local_time_minutes"
                                    type="number"
                                    step="1"
                                    autocomplete="false"
                                    class="form-control"
                                    id="customer_auction_start_local_time_minutes"
                                    placeholder="mm"
                                >
                                <span *ngIf="!isEditing" class="value">{{ auctionConfig.customer_auction_start_local_time_minutes }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.customer_auction_start | showParametersFor: 'AuctionTimeRelative')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.CUSTOMER_AUCTION_START_MINUTES' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.customer_auction_start_minutes"
                            type="number"
                            step="1"
                            autocomplete="false"
                            class="form-control"
                            id="customer_auction_start_minutes"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.customer_auction_start_minutes }}</span>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.CUSTOMER_AUCTION_END' | translate }}</label>
                        <span *ngIf="!(auctionConfig.delivery_interval | showParametersFor: 'Blocks')">
                            <ng-multiselect-dropdown *ngIf="isEditing"
                                [placeholder]="('COMPONENTS.SELECT' | translate )"
                                [data]="customerAuctionEndOptions"
                                [(ngModel)]="auctionConfig.customer_auction_end"
                                [settings]="dropdownSettings(true, false, 'name')">
                            </ng-multiselect-dropdown>
                        </span>
                        <span *ngIf="(auctionConfig.delivery_interval | showParametersFor: 'Blocks')">
                            <ng-multiselect-dropdown *ngIf="isEditing"
                                [placeholder]="('COMPONENTS.SELECT' | translate )"
                                [data]="customerAuctionEndForDeliveryBlockOptions"
                                [(ngModel)]="auctionConfig.customer_auction_end"
                                [settings]="dropdownSettings(true, false, 'name')">
                            </ng-multiselect-dropdown>
                        </span>
                        <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.customer_auction_end) }}</span>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.customer_auction_end | showParametersFor: 'AuctionTimeAbsolute')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.CUSTOMER_AUCTION_END_DAYS_BEFORE_DELIVERY' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.customer_auction_end_days_before_delivery"
                            type="number"
                            step="1"
                            autocomplete="false"
                            class="form-control"
                            id="customer_auction_end_days_before_delivery"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.customer_auction_end_days_before_delivery }}</span>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.customer_auction_end | showParametersFor: 'AuctionTimeAbsolute')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.CUSTOMER_AUCTION_END_TIME' | translate }}</label>
                        <div class="form-row">
                            <div class="col-md-6">
                                <input *ngIf="isEditing"
                                    [(ngModel)]="auctionConfig.customer_auction_end_local_time_hours"
                                    type="number"
                                    step="1"
                                    autocomplete="false"
                                    class="form-control"
                                    id="customer_auction_end_local_time_hours"
                                    placeholder="hh"
                                >
                                <span *ngIf="!isEditing" class="value">{{ auctionConfig.customer_auction_end_local_time_hours }}</span>
                            </div>
                            <div class="col-md-6">
                                <input *ngIf="isEditing"
                                    [(ngModel)]="auctionConfig.customer_auction_end_local_time_minutes"
                                    type="number"
                                    step="1"
                                    autocomplete="false"
                                    class="form-control"
                                    id="customer_auction_end_local_time_minutes"
                                    placeholder="mm"
                                >
                                <span *ngIf="!isEditing" class="value">{{ auctionConfig.customer_auction_end_local_time_minutes }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="form-group col-md-4" *ngIf="(auctionConfig.customer_auction_end | showParametersFor: 'AuctionTimeRelative')">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.CUSTOMER_AUCTION_END_MINUTES' | translate }}</label>
                        <input *ngIf="isEditing"
                            [(ngModel)]="auctionConfig.customer_auction_end_minutes"
                            type="number"
                            step="1"
                            autocomplete="false"
                            class="form-control"
                            id="customer_auction_end_minutes"
                        >
                        <span *ngIf="!isEditing" class="value">{{ auctionConfig.customer_auction_end_minutes }}</span>
                    </div>
                </div>
            </section>

            <section>
                <h6>{{'CONFIGURATION.AUCTION.LABEL.THIRD_PARTY_INTERFACE'|translate}}</h6>

                <!-- API Biding -->
                <div class="form-row" *ngIf="(auctionConfig.bid_inputs | showParametersFor: 'V2gBiddingKind')">
                    <div class="form-group col-md-4">
                        <label>{{ 'CONFIGURATION.AUCTION.API_AUCTION_BIDDING' | translate }}</label>
                        <ng-multiselect-dropdown *ngIf="isEditing"
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="apiAuctionBiddingOptions"
                            [(ngModel)]="auctionConfig.api_auction_bidding"
                            [settings]="dropdownSettings(true, false, 'name')">
                        </ng-multiselect-dropdown>
                        <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.api_auction_bidding) }}</span>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM' | translate }}</label>
                        <ng-multiselect-dropdown *ngIf="isEditing"
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="auctionSystemOptions"
                            [(ngModel)]="auctionConfig.auction_system"
                            [settings]="dropdownSettings(false, false, 'name')">
                        </ng-multiselect-dropdown>
                        <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.auction_system) }}</span>
                    </div>
                </div>

                <!-- Auction System upload -->
                <section *ngIf="(auctionConfig.auction_system | showParametersFor: 'upload')">
                    <h6>{{'CONFIGURATION.AUCTION.LABEL.AUCTION_SYSTEM_UPLOAD'|translate}}</h6>
                    <div class="form-row">
                        <div class="form-group col-md-4">
                            <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_UPLOAD_BEHAVIOUR' | translate }}</label>
                            <ng-multiselect-dropdown *ngIf="isEditing"
                                [placeholder]="('COMPONENTS.SELECT' | translate )"
                                [data]="auctionSystemUploadBehaviourOptions"
                                [(ngModel)]="auctionConfig.auction_system_upload_behaviour"
                                [settings]="dropdownSettings(true, false, 'name')">
                            </ng-multiselect-dropdown>
                            <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.auction_system_upload_behaviour) }}</span>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-4">
                            <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_UPLOAD_MARKET_TIME' | translate }}</label>
                            <input *ngIf="isEditing"
                                [(ngModel)]="auctionConfig.auction_system_upload_market_time"
                                type="number"
                                step="1"
                                autocomplete="false"
                                class="form-control"
                                id="auction_system_upload_market_time"
                            >
                            <span *ngIf="!isEditing" class="value">{{ auctionConfig.auction_system_upload_market_time }}</span>
                        </div>
                        <div class="form-group col-md-4">
                            <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_UPLOAD_NUMBER_OF_RETRIES' | translate }}</label>
                            <input *ngIf="isEditing"
                                [(ngModel)]="auctionConfig.auction_system_upload_number_of_retries"
                                type="number"
                                step="1"
                                autocomplete="false"
                                class="form-control"
                                id="auction_system_upload_number_of_retries"
                            >
                            <span *ngIf="!isEditing" class="value">{{ auctionConfig.auction_system_upload_number_of_retries }}</span>
                        </div>
                        <div class="form-group col-md-4">
                            <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_UPLOAD_SECONDS_BETWEEN_RETRIES' | translate }}</label>
                            <input *ngIf="isEditing"
                                [(ngModel)]="auctionConfig.auction_system_upload_seconds_between_retries"
                                type="number"
                                step="1"
                                autocomplete="false"
                                class="form-control"
                                id="auction_system_upload_seconds_between_retries"
                            >
                            <span *ngIf="!isEditing" class="value">{{ auctionConfig.auction_system_upload_seconds_between_retries }}</span>
                        </div>
                    </div>
                </section>

                <!-- Auction System download -->
                <section *ngIf="(auctionConfig.auction_system | showParametersFor: 'download')">
                    <h6>{{'CONFIGURATION.AUCTION.LABEL.AUCTION_SYSTEM_DOWNLOAD'|translate}}</h6>
                    <div class="form-row">
                        <div class="form-group col-md-4">
                            <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_DOWNLOAD_BEHAVIOUR' | translate }}</label>
                            <ng-multiselect-dropdown *ngIf="isEditing"
                                [placeholder]="('COMPONENTS.SELECT' | translate )"
                                [data]="auctionSystemDownloadBehaviourOptions"
                                [(ngModel)]="auctionConfig.auction_system_download_behaviour"
                                [settings]="dropdownSettings(true, false, 'name')">
                            </ng-multiselect-dropdown>
                            <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.auction_system_download_behaviour) }}</span>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-4">
                            <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_DOWNLOAD_MARKET_TIME' | translate }}</label>
                            <input *ngIf="isEditing"
                                [(ngModel)]="auctionConfig.auction_system_download_market_time"
                                type="number"
                                step="1"
                                autocomplete="false"
                                class="form-control"
                                id="auction_system_download_market_time"
                            >
                            <span *ngIf="!isEditing" class="value">{{ auctionConfig.auction_system_download_market_time }}</span>
                        </div>
                        <div class="form-group col-md-4">
                            <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_DOWNLOAD_NUMBER_OF_RETRIES' | translate }}</label>
                            <input *ngIf="isEditing"
                                [(ngModel)]="auctionConfig.auction_system_download_number_of_retries"
                                type="number"
                                step="1"
                                autocomplete="false"
                                class="form-control"
                                id="auction_system_download_number_of_retries"
                            >
                            <span *ngIf="!isEditing" class="value">{{ auctionConfig.auction_system_download_number_of_retries }}</span>
                        </div>
                        <div class="form-group col-md-4">
                            <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_DOWNLOAD_SECONDS_BETWEEN_RETRIES' | translate }}</label>
                            <input *ngIf="isEditing"
                                [(ngModel)]="auctionConfig.auction_system_download_seconds_between_retries"
                                type="number"
                                step="1"
                                autocomplete="false"
                                class="form-control"
                                id="auction_system_download_seconds_between_retries"
                            >
                            <span *ngIf="!isEditing" class="value">{{ auctionConfig.auction_system_download_seconds_between_retries }}</span>
                        </div>
                    </div>
                </section>

                <!-- Auction System download -->
                <section *ngIf="(auctionConfig.auction_system | showParametersFor: 'ui_for_battery_uk_intraday')">
                    <h6>{{'CONFIGURATION.AUCTION.LABEL.AUCTION_SYSTEM_UI_FOR_BATTERY_UK_INTRADAY'|translate}}</h6>
                    <div class="form-row">
                        <div class="form-group col-md-4">
                            <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_FOR_BATTERY_UK_INTRADAY_ASSET_ID' | translate }}</label>
                            <ng-multiselect-dropdown *ngIf="isEditing"
                                [placeholder]="('COMPONENTS.SELECT' | translate )"
                                [data]="assetsUk"
                                [(ngModel)]="auctionConfig.auction_system_ui_for_battery_uk_intraday_asset_id"
                                [settings]="assetsDropdownSettings">
                              </ng-multiselect-dropdown>
                            <span *ngIf="!isEditing" class="value">{{ optionDisplayNames(auctionConfig.auction_system_ui_for_battery_uk_intraday_asset_id) || '-' }}</span>
                        </div>
                        <div class="form-group col-md-4">
                            <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_FOR_BATTERY_UK_INTRADAY_PORTFOLIO_ID' | translate }}</label>
                            <input *ngIf="isEditing"
                                [(ngModel)]="auctionConfig.auction_system_ui_for_battery_uk_intraday_portfolio_id"
                                type="text"
                                autocomplete="false"
                                class="form-control"
                                id="auction_system_ui_for_battery_uk_intraday_portfolio_id"
                            >
                            <span *ngIf="!isEditing" class="value">{{ auctionConfig.auction_system_ui_for_battery_uk_intraday_portfolio_id || '-' }}</span>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-4">
                            <label>{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_FOR_BATTERY_UK_INTRADAY_EXPORT_ENTRADER' | translate }}</label>
                            <ng-multiselect-dropdown *ngIf="isEditing"
                                [placeholder]="('COMPONENTS.SELECT' | translate )"
                                [data]="yesNoOptions"
                                [(ngModel)]="auctionConfig.auction_system_ui_for_battery_uk_intraday_export_entrader"
                                [settings]="dropdownSettings(true, false, 'name')">
                            </ng-multiselect-dropdown>
                            <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.auction_system_ui_for_battery_uk_intraday_export_entrader) }}</span>
                        </div>
                        <div class="form-group col-md-4">
                            <label>{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_FOR_BATTERY_UK_INTRADAY_UPLOAD_FOLDER' | translate }}</label>
                            <input *ngIf="isEditing"
                                [(ngModel)]="auctionConfig.auction_system_ui_for_battery_uk_intraday_upload_folder"
                                type="text"
                                autocomplete="false"
                                class="form-control"
                                id="auction_system_ui_for_battery_uk_intraday_upload_folder"
                            >
                            <span *ngIf="!isEditing" class="value">{{ auctionConfig.auction_system_ui_for_battery_uk_intraday_upload_folder || '-' }}</span>
                        </div>
                    </div>
                </section>

                <section *ngIf="(auctionConfig.auction_system | showParametersFor: 'uk_vatp')">
                    <h6>{{'CONFIGURATION.AUCTION.LABEL.AUCTION_SYSTEM_UK_VATP'|translate}}</h6>
                    <div class="form-row">
                        <div class="form-group col-md-12">
                            <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_UK_VATP_SMART_VOLUME_STRATEGY_TEMPLATE_ID' | translate }}</label>
                            <input *ngIf="isEditing"
                                [(ngModel)]="auctionConfig.auction_system_uk_vatp_smart_volume_strategy_template_id"
                                type="text"
                                autocomplete="false"
                                class="form-control"
                                id="auction_system_uk_vatp_smart_volume_strategy_template_id"
                            >
                            <span *ngIf="!isEditing" class="value">{{ auctionConfig.auction_system_uk_vatp_smart_volume_strategy_template_id || '-' }}</span>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-12">
                            <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_UK_VATP_HIDDEN_ICEBERG_STRATEGY_TEMPLATE_ID' | translate }}</label>
                            <input *ngIf="isEditing"
                                [(ngModel)]="auctionConfig.auction_system_uk_vatp_hidden_iceberg_strategy_template_id"
                                type="text"
                                autocomplete="false"
                                class="form-control"
                                id="auction_system_uk_vatp_hidden_iceberg_strategy_template_id"
                            >
                            <span *ngIf="!isEditing" class="value">{{ auctionConfig.auction_system_uk_vatp_hidden_iceberg_strategy_template_id || '-' }}</span>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-12">
                            <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_UK_VATP_SMART_ICEBERG_STRATEGY_TEMPLATE_ID' | translate }}</label>
                            <input *ngIf="isEditing"
                                [(ngModel)]="auctionConfig.auction_system_uk_vatp_smart_iceberg_strategy_template_id"
                                type="text"
                                autocomplete="false"
                                class="form-control"
                                id="auction_system_uk_vatp_smart_iceberg_strategy_template_id"
                            >
                            <span *ngIf="!isEditing" class="value">{{ auctionConfig.auction_system_uk_vatp_smart_iceberg_strategy_template_id || '-' }}</span>
                        </div>
                    </div>
                </section>

                <section *ngIf="(auctionConfig.auction_system | showParametersFor: 'de_vatp')">
                    <h6>{{'CONFIGURATION.AUCTION.LABEL.AUCTION_SYSTEM_DE_VATP'|translate}}</h6>
                    <div class="form-row">
                        <div class="form-group col-md-12">
                            <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_DE_VATP_SMART_VOLUME_STRATEGY_TEMPLATE_ID' | translate }}</label>
                            <input *ngIf="isEditing"
                                [(ngModel)]="auctionConfig.auction_system_de_vatp_smart_volume_strategy_template_id"
                                type="text"
                                autocomplete="false"
                                class="form-control"
                                id="auction_system_de_vatp_smart_volume_strategy_template_id"
                            >
                            <span *ngIf="!isEditing" class="value">{{ auctionConfig.auction_system_de_vatp_smart_volume_strategy_template_id || '-' }}</span>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-12">
                            <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_DE_VATP_HIDDEN_ICEBERG_STRATEGY_TEMPLATE_ID' | translate }}</label>
                            <input *ngIf="isEditing"
                                [(ngModel)]="auctionConfig.auction_system_de_vatp_hidden_iceberg_strategy_template_id"
                                type="text"
                                autocomplete="false"
                                class="form-control"
                                id="auction_system_de_vatp_hidden_iceberg_strategy_template_id"
                            >
                            <span *ngIf="!isEditing" class="value">{{ auctionConfig.auction_system_de_vatp_hidden_iceberg_strategy_template_id || '-' }}</span>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group col-md-12">
                            <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_DE_VATP_SMART_ICEBERG_STRATEGY_TEMPLATE_ID' | translate }}</label>
                            <input *ngIf="isEditing"
                                [(ngModel)]="auctionConfig.auction_system_de_vatp_smart_iceberg_strategy_template_id"
                                type="text"
                                autocomplete="false"
                                class="form-control"
                                id="auction_system_de_vatp_smart_iceberg_strategy_template_id"
                            >
                            <span *ngIf="!isEditing" class="value">{{ auctionConfig.auction_system_de_vatp_smart_iceberg_strategy_template_id || '-' }}</span>
                        </div>
                    </div>
                </section>                

                <section *ngIf="(auctionConfig.auction_system | showParametersFor: 'ui')">
                    <h6>{{'CONFIGURATION.AUCTION.LABEL.AUCTION_SYSTEM_UI'|translate}}</h6>
                    <div class="form-row" *ngIf="(auctionConfig.auction_system | showParametersFor: 'ui')">
                        <div class="form-group col-md-4">
                            <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_SITE_ID' | translate }}</label>
                            <input *ngIf="isEditing"
                                [(ngModel)]="auctionConfig.auction_system_ui_site_id"
                                type="text"
                                autocomplete="false"
                                class="form-control"
                                id="auction_system_ui_site_id"
                            >
                            <span *ngIf="!isEditing" class="value">{{ auctionConfig.auction_system_ui_site_id || '-' }}</span>
                        </div>
                    </div>
                    <div class="form-row" *ngIf="(auctionConfig.auction_system | showParametersFor: 'ui')">
                        <div class="form-group col-md-4">
                            <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_TRADER_LOOK_AHEAD_HOURS' | translate }}</label>
                            <input *ngIf="isEditing"
                                [(ngModel)]="auctionConfig.auction_system_ui_trader_look_ahead_hours"
                                type="number"
                                step="1"
                                autocomplete="false"
                                class="form-control"
                                id="auction_system_ui_trader_look_ahead_hours"
                            >
                            <span *ngIf="!isEditing" class="value">{{ auctionConfig.auction_system_ui_trader_look_ahead_hours }}</span>
                        </div>
                        <div class="form-group col-md-4">
                            <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_PRICE_GROUPING_STEP' | translate }}</label>
                            <input *ngIf="isEditing"
                                [(ngModel)]="auctionConfig.auction_system_ui_price_grouping_step"
                                type="number"
                                step="0.001"
                                autocomplete="false"
                                class="form-control"
                                id="auction_system_ui_price_grouping_step"
                            >
                            <span *ngIf="!isEditing" class="value">{{ auctionConfig.auction_system_ui_price_grouping_step }}</span>
                        </div>
                        <div class="form-group col-md-4">
                            <label [ngClass]="isEditing ? 'required' :''">{{ 'CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_BID_EXPIRY_WARNING_MINUTES' | translate }}</label>
                            <input *ngIf="isEditing"
                                [(ngModel)]="auctionConfig.auction_system_ui_bid_expiry_warning_minutes"
                                type="number"
                                step="1"
                                autocomplete="false"
                                class="form-control"
                                id="auction_system_ui_bid_expiry_warning_minutes"
                            >
                            <span *ngIf="!isEditing" class="value">{{ auctionConfig.auction_system_ui_bid_expiry_warning_minutes }}</span>
                        </div>
                    </div>
                </section>

                <section>
                    <h6>{{'CONFIGURATION.AUCTION.LABEL.AUCTION_RESULTS_POST_PROCESSING'|translate}}</h6>
                    <div class="form-row">
                        <div class="form-group col-md-4">
                            <label>{{ 'CONFIGURATION.AUCTION.CREATE_NOMINATIONS' | translate }}</label>
                            <ng-multiselect-dropdown *ngIf="isEditing"
                                [placeholder]="('COMPONENTS.SELECT' | translate )"
                                [data]="yesNoOptions"
                                [(ngModel)]="auctionConfig.create_nominations"
                                [settings]="dropdownSettings(true, false, 'name')">
                            </ng-multiselect-dropdown>
                            <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.create_nominations) }}</span>
                        </div>
                        <div class="form-group col-md-4">
                            <label>{{ 'CONFIGURATION.AUCTION.CREATE_ALLOCATIONS' | translate }}</label>
                            <ng-multiselect-dropdown *ngIf="isEditing"
                                [placeholder]="('COMPONENTS.SELECT' | translate )"
                                [data]="yesNoOptions"
                                [(ngModel)]="auctionConfig.create_allocations"
                                [settings]="dropdownSettings(true, false, 'name')">
                            </ng-multiselect-dropdown>
                            <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.create_allocations) }}</span>
                        </div>
                    </div>
                    <div class="form-row" *ngIf="auctionConfig.create_allocations | showParametersFor: true">
                        <div class="form-group col-md-4">
                            <label>{{ 'CONFIGURATION.AUCTION.ALLOCATION_START_EXTENSION_SECONDS' | translate }}</label>
                            <input *ngIf="isEditing"
                                [(ngModel)]="auctionConfig.allocation_start_extension_seconds"
                                type="number"
                                step="1"
                                autocomplete="false"
                                class="form-control"
                                id="allocation_start_extension_seconds"
                            >
                            <span *ngIf="!isEditing" class="value">{{ auctionConfig.allocation_start_extension_seconds }}</span>
                        </div>
                        <div class="form-group col-md-4">
                            <label>{{ 'CONFIGURATION.AUCTION.ALLOCATION_END_EXTENSION_SECONDS' | translate }}</label>
                            <input *ngIf="isEditing"
                                [(ngModel)]="auctionConfig.allocation_end_extension_seconds"
                                type="number"
                                step="1"
                                autocomplete="false"
                                class="form-control"
                                id="allocation_end_extension_seconds"
                            >
                            <span *ngIf="!isEditing" class="value">{{ auctionConfig.allocation_end_extension_seconds }}</span>
                        </div>
                        <div class="form-group col-md-4">
                            <label>{{ 'CONFIGURATION.AUCTION.ALLOCATION_END_EXTENSION_FROM_RAMP_RATE' | translate }}</label>
                            <ng-multiselect-dropdown *ngIf="isEditing"
                                [placeholder]="('COMPONENTS.SELECT' | translate )"
                                [data]="yesNoOptions"
                                [(ngModel)]="auctionConfig.allocation_end_extension_from_ramp_rate"
                                [settings]="dropdownSettings(true, false, 'name')">
                            </ng-multiselect-dropdown>
                            <span *ngIf="!isEditing" class="value">{{ optionNames(auctionConfig.allocation_end_extension_from_ramp_rate) }}</span>
                        </div>
                    </div>
                </section>
            </section>

            <ng-template [ngIf]="isEditing && permissions.can_create_bids">
                <!-- Action button -->
                <ul class="list-inline list-unstyled d-flex form-actions">
                    <li>
                        <button
                                [disabled]="!canSubmit"
                                (click)="createOrUpdateAuctionConfig()"
                                class="eon-button bg-eon-red">
                            <span>{{ (auctionConfig.id ? 'CONFIGURATION.AUCTION_CONFIGURATION.SAVE' : 'CONFIGURATION.AUCTION_CONFIGURATION.CREATE')| translate }}</span>
                        </button>
                    </li>
                    <li *ngIf="!openInEditMode">
                        <button
                                (click)="cancelEditMode()"
                                class="eon-button bg-eon-red">
                            <span>{{'CONFIGURATION.AUCTION_CONFIGURATION.CANCEL'|translate}}</span>
                        </button>
                    </li>
                </ul>
            </ng-template>

            <ng-template [ngIf]="!isEditing && permissions.can_create_bids">
                <ul class="list-inline list-unstyled d-flex form-actions">
                <li>
                    <button
                            (click)="enterEditMode()"
                            class="eon-button bg-eon-red">
                        <span>{{'CONFIGURATION.AUCTION_CONFIGURATION.EDIT'|translate}}</span>
                    </button>
                </li>
                <li>
                    <button
                            (click)="delete()"
                            class="eon-button bg-eon-red">
                        <span>{{'CONFIGURATION.AUCTION_CONFIGURATION.DELETE'|translate}}</span>
                    </button>
                </li>
            </ul>
            </ng-template>

        </div>
    </div>
</div>
