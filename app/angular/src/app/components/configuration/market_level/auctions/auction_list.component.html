<div class="container-fluid">
    <div class="row">
        <div class="col">
            <h2>{{ 'CONFIGURATION.MARKET_LEVEL.AUCTION_CONFIGURATION.LIST' | translate }}</h2>

            <div class="table-filter d-flex"></div>

            <table class="table table-auction-configuration table-auction-results table-striped table-bg-turquoise">
                <tr>
                    <th class="name">{{ 'CONFIGURATION.AUCTION.NAME' | translate }}</th>
                    <th class="name">{{ 'CONFIGURATION.AUCTION.DISPATCH_GROUPS' | translate }}</th>
                    <th class="name">{{ 'CONFIGURATION.AUCTION.AUCTION_VALIDITY_INTERVAL_END' | translate }}</th>
                    <th class="name">{{ 'CONFIGURATION.AUCTION.DELIVERY_INTERVAL' | translate }}</th>
                    <th class="name">{{ 'CONFIGURATION.AUCTION.OFFERS_TIME_BLOCK' | translate }}</th>
                    <th class="name">{{ 'CONFIGURATION.AUCTION.PRICE_TYPE' | translate }}</th>
                    <th class="name">{{ 'CONFIGURATION.AUCTION.TH_AUCTION_SYSTEM_UPLOAD_BEHAVIOUR' | translate }}</th>
                    <th class="name">{{ 'CONFIGURATION.AUCTION.TH_AUCTION_SYSTEM_DOWNLOAD_BEHAVIOUR' | translate }}</th>
                    <th class="name">{{ 'CONFIGURATION.AUCTION.CREATE_NOMINATIONS' | translate }}</th>
                    <th class="text-center"></th>
                </tr>
                <tr *ngIf="showLoader">
                    <td colspan="9" class="text-center vertical-center">
                        <vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
                    <td>
                </tr>
                <ng-container *ngFor="let x of auctions">
                    <tr>
                        <input type="hidden" value="{{ x.id }}">
                        <td class="name">{{ x.name }}</td>
                        <td class="dispatch-groups">
                            <ng-container *ngFor="let dg of x.dispatch_group_ids_and_names; let idx = index;">
                                {{dg.name}}<span *ngIf="idx < x.dispatch_group_ids_and_names.length - 1">, </span>
                            </ng-container>
                        </td>
                        <td class="name">
                            <span *ngIf="x.auction_definition_interval.length > 1">
                                {{ x.auction_definition_interval[1] | tzDate: x.time_zone:'ddd, DD MMM YYYY HH:mm'}}
                            </span>
                        </td>
                        <td class="name">{{ deliveryIntervalOptions | findNameById: x.delivery_interval | translate }}</td>
                        <td class="name">{{ offersTimeBlockOptions | findNameById: x.offers_time_block | translate }}</td>
                        <td class="name">
                            <ng-container *ngFor="let p of x.price_type; let idx = index;">
                                {{priceTypeOptions | findNameById: p | translate}}<span *ngIf="idx < x.price_type.length - 1">, </span>
                            </ng-container>
                        </td>
                        <td class="name">{{ auctionSystemUploadBehaviourOptions  | findNameById: x.auction_system_upload_behaviour  }}</td>
                        <td class="name">{{ auctionSystemDownloadBehaviourOptions  | findNameById: x.auction_system_download_behaviour }}</td>
                        <td class="create-nominations">
                            <span class="eon-checkbox-label non-clickable bg-eon-red"
                                  *ngIf="x.create_nominations"
                                  [ngClass]="x.create_nominations ==  'true' ? 'checked' : ''"></span>
                        </td>
                        <td class="text-center actions-column actions">
                            <ul class="list-inline list-unstyled">
                                <li *ngIf="permissions.can_create_bids"><span (click)="showAuction(x)" style="display: inline-block"><i class="fa fa-eye"></i></span> </li>
                                <li *ngIf="permissions.can_create_bids"><span (click)="editAuction(x)" style="display: inline-block"><i class="fa fa-pencil"></i></span> </li>
                                <li *ngIf="permissions.can_create_bids"><span (click)="deleteAuction(x)" style="display: inline-block"><i class="fa fa-trash"></i></span></li>
                                <li *ngIf="permissions.can_create_bids"><span (click)="cloneAuction(x)" style="display: inline-block"><i class="fa fa-copy"></i></span></li>
                            </ul>
                        </td>
                    </tr>
                </ng-container>
            </table>
            <div class="d-flex justify-content-between">
                <ngb-pagination
                        [collectionSize]="collectionSize"
                        [(page)]="page"
                        [ellipses]="true"
                        [maxSize]="5"
                        (pageChange)="pageChangedCallback($event)"
                        [pageSize]="pageSize">
                </ngb-pagination>

                <select
                        class="custom-select"
                        style="width: auto"
                        [(ngModel)]="pageSize"
                        (ngModelChange)="pageSizeChangedCallback($event)">
                    <option [ngValue]="25">25 {{ 'PAGINATION.PER_PAGE' | translate }}</option>
                    <option [ngValue]="50">50 {{ 'PAGINATION.PER_PAGE' | translate }}</option>
                    <option [ngValue]="75">75 {{ 'PAGINATION.PER_PAGE' | translate }}</option>
                </select>
            </div>


        </div>
    </div>
</div>

<ng-template #edit>
    <vpp-auction-configuration-form
            [auctionConfig]="editedAuctionConfig"
            [openInEditMode]="openInEditMode"
            [limitedEditMode]="limitedEditMode"
            (done)="closeAuctionModal(true)"
            (canceled)="closeAuctionModal(false)">
    </vpp-auction-configuration-form>
</ng-template>
