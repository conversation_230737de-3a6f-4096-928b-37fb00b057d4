@import "../../../../styles/colors";

vpp-auctions-configuration{

  td.actions-column {
    color: $eon-red;

    ul {
      display: flex;

      li {
        margin-right: 16px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .page-actions {
    padding: 30px 0;

    ul {
      margin-bottom: 0;
    }

    ul > li {
      margin-right: 20px;
    }
  }

  .overlay-on-top-of-list {
    position: relative;
    z-index: 999;
  }

  .tab-content{
    margin-left: 0;
    padding: 30px;

    h2{
      color: $eon-turquoise;
    }
    .form-control{
      border-color: $eon-turquoise;
    }
    .input-group-text{
      border-color: $eon-turquoise;
    }
  }

  .form-check{
    padding-left: 0;
  }

  section{
    margin-top:15px;
  }

  //TODO
  .padding-top-10{
    padding-top: 10px;
  }

  //TODO
  .multiselect-dropdown {
    .dropdown-btn {
      border: 2px solid $eon-darkgrey-25 !important;
      background: #fff !important;
      border-radius: 3px !important;
      min-height: 52px !important;
      font-size: 18px;
      padding-top: 10px;
    }
  }

}
