import {Component, OnInit, ViewChild, ViewEncapsulation} from "@angular/core";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {NotificationService} from "../../../../services/notification.service";
import {TranslateService} from "@ngx-translate/core";
import {GlobalService} from "../../../../services/global.service";
import {ConfirmService} from "../../../../services/confirm.service";
import {angularData} from "../../../../../global.export";
import {SubpoolProvider} from "../../../../providers/subpool.provider";

@Component({
    selector: 'vpp-subpools-list',
    templateUrl: './subpools_list.component.html',
    styleUrls: ['./subpools_list.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class SubpoolsListComponent implements OnInit {
    showLoader = true;
    subpools = [];
    params = {
        page: 1,
        per_page: 25,
    };
    page = 1;
    pageSize = 25;
    collectionSize = 0;
    editedSubpool: any;
    openInEditMode = true;
    confirmDeleteMessage = '';
    bsModalRef;
    permissions: any;

    @ViewChild('edit', { static: true }) editModal;


    constructor(
        public translate: TranslateService,
        public globalService: GlobalService,
        private _modalService: NgbModal,
        private _notificationService: NotificationService,
        private _confirmService: ConfirmService,
        private _subpoolProvider: SubpoolProvider
    ) {
        this.translate.get('CONFIRM.MESSAGE.DELETE_ENTRY').subscribe((x) => {
            this.confirmDeleteMessage = x;
        });
        this.permissions = angularData.permissions;
    }

    ngOnInit(): void {
        this.getSubpools();
    }

    getSubpools() {
        this.showLoader = true;
        this.subpools = [];
        this._subpoolProvider.getSubpools(this.params).subscribe(
            res => {
                this.subpools = res.subpools;
                this.collectionSize = res.total_entries;
                this.showLoader = false;
            }, err => {
                this.showLoader = false;
                console.log('Failed to load subpools', err);
            }
        );
    }

    pageChangedCallback(page) {
        this.params.page = page;
        this.getSubpools();
    }

    pageSizeChangedCallback(pageSize) {
        this.params.per_page = pageSize;
        this.params.page = 1;
        this.getSubpools();
    }

    showSubpool(box) {
        this.editedSubpool = { ...box};
        this.openInEditMode = false;
        this.bsModalRef = this._modalService.open(this.editModal, { size: 'lg' });
    }

    editSubpool(box) {
        this.editedSubpool = { ...box};
        this.openInEditMode = true;
        this.bsModalRef = this._modalService.open(this.editModal, { size: 'lg' });
    }

    closeSubpoolModal(reload: boolean = true) {
        this.bsModalRef.close();
        if (reload) {
            this.getSubpools();
        }
    }

    deleteSubpool(subpool) {
        this._confirmService.confirm({
            message: this.confirmDeleteMessage,
        }).then(() => {
            this._deleteConfirmedSubpool(subpool);
        }, () => {
        });
    }

    _deleteConfirmedSubpool(subpool) {
        this._subpoolProvider
            .deleteSubpool(subpool.id)
            .subscribe(
                (res) => {
                    if (res.success == true) {
                        this._notificationService.success({ text: 'SUCCESS'});
                    } else {
                        this._notificationService.error({ text: JSON.stringify(res.error)});
                    }
                    this.getSubpools();
                },
                (err) => {
                    console.log('Failed to delete', err);
                    this._notificationService.error({ text: JSON.stringify(err)});
                });
    }

}