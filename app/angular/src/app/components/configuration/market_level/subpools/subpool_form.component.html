<div class="row">
    <div class="col-md-8" id="full-width-in-modal">
        <div class="tab-content light-blue" style="border: none" id="enter-subpool-tab">
            <span *ngIf="!embedded" class="pull-right" (click)="cancel($event)">
                <i class="fa fa-times"></i>
            </span>

            <h2>{{ 'CONFIGURATION.MARKET_LEVEL.SUBPOOLS.ENTER_SUBPOOL' | translate }}</h2>

            <div class="alerts-wrapper-static" *ngIf="errors.length">
                <ng-container *ngFor="let errmsg of errors; let ix = index;">
                    <ngb-alert
                            [dismissible]="false"
                            [type]="'error'"> {{ errmsg }}
                    </ngb-alert>
                </ng-container>
            </div>

            <div class="row">
                <div class="form-group col-sm-6">
                    <label>{{ 'CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.NAME' | translate }}</label>
                    <input
                            [(ngModel)]="subpool.name"
                            type="text"
                            class="form-control"
                            id="inputName">
                </div>

                <div class="form-group col-sm-6">
                    <label>{{ 'CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.IMPLICIT' | translate }}</label>
                    <!--
                    <input
                        [(ngModel)]="subpool.implicit"
                        type="checkbox"
                        class="form-control"
                        id="inputImplicit"
                    >
                    -->
                    <div class="form-check">
                        <label
                                class="eon-checkbox-label bg-eon-red"
                                (click)="toggleImplicit()"
                                [ngClass]="subpool.implicit ? 'checked': ''">
                        </label>
                    </div>
                    <span>
                        {{ 'CONFIGURATION.MARKET_LEVEL.SUBPOOLS.IMPLICIT.NOTICE' | translate }}
                    </span>
                </div>
            </div><!-- row -->

            <div class="row">
                <div class="form-group col-sm-6">
                    <label>{{ 'CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.SIGNALS' | translate }}</label>
                    <ng-multiselect-dropdown
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="availableSignals"
                            [(ngModel)]="subpool.signals_list"
                            [settings]="signalsDropdownSettings"
                    >
                    </ng-multiselect-dropdown>
                </div>

                <div class="form-group col-sm-6">
                    <!-- empty -->
                </div>
            </div>

            <div class="row">
                <div class="form-group col-sm-6">
                    <label>{{ 'CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.DISPATCH_GROUPS' | translate }}</label>
                    <ng-multiselect-dropdown
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="dispatchGroups"
                            [(ngModel)]="subpool.dispatch_groups"
                            [settings]="dgDropdownSettings"
                    >
                    </ng-multiselect-dropdown>
                </div>


                <div class="form-group col-sm-6" *ngIf="!subpool.implicit">
                    <label>{{ 'CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.ASSETS' | translate }}</label>
                    <ng-multiselect-dropdown
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="assets"
                            [(ngModel)]="subpool.assets"
                            [settings]="assetDropdownSettings"
                    >
                    </ng-multiselect-dropdown>
                </div>
            </div>

            <!-- Action button -->
            <ul class="list-inline list-unstyled d-flex form-actions">
                <li>
                    <button
                            (click)="submit()"
                            class="eon-button bg-eon-red">
                        <span>{{ 'CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.SAVE' | translate }}</span>
                    </button>
                </li>
            </ul>

        </div>
    </div>
</div>