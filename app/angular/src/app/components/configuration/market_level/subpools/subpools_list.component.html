<div class="container-fluid">
    <div class="row">
        <div class="col">
            <h2>{{ 'CONFIGURATION.MARKET_LEVEL.SUBPOOLS.LIST.TITLE' | translate }}</h2>

            <div class="table-filter d-flex"></div>

            <table class="table table-subpools table-auction-results table-striped table-bg-turquoise">
                <tr>
                    <th class="name">{{ 'CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.NAME' | translate }}</th>
                    <th class="dispatch_groups">{{ 'CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.DISPATCH_GROUPS' | translate }}</th>
                    <th class="signals">{{ 'CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.SIGNALS' | translate }}</th>
                    <th class="implicit">{{ 'CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.IMPLICIT' | translate }}</th>
                    <th class="assets">{{ 'CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.ASSETS' | translate }}</th>
                    <th class="text-center">{{ 'CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.ACTIONS' | translate }}</th>
                </tr>
                <tr *ngIf="showLoader">
                    <td colspan="6" class="text-center vertical-center">
                        <vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
                    <td>
                </tr>
                <ng-container *ngFor="let x of subpools">
                    <tr>
                        <input type="hidden" value="{{ x.id }}">
                        <td class="name">{{ x.name }}</td>
                        <td class="dispatch_groups">
                            <ng-template ngFor let-dg [ngForOf]="x.dispatch_groups" let-isLast="last">
                                <span>{{ dg.label }}</span>
                                <br *ngIf="!isLast">
                            </ng-template>
                        </td>
                        <td class="signals">
                            <ng-template ngFor let-signal [ngForOf]="x.signals_list">
                                <span>{{ signal }}</span>
                                <br>
                            </ng-template>
                        </td>
                        <td class="implicit">
                            <span
                                    class="eon-checkbox-label non-clickable bg-eon-red"
                                    [ngClass]="x.implicit ? 'checked' : ''"></span>
                        </td>
                        <td class="assets">
                            {{ x.implicit ? 'n/a' : x.asset_count }}
                        </td>

                        <td class="text-center actions-column">
                            <ul class="list-inline list-unstyled">
                                <li *ngIf="permissions.can_create_subpools"><span (click)="editSubpool(x)" style="display: inline-block"><i class="fa fa-pencil"></i></span> </li>
                                <li *ngIf="permissions.can_create_subpools"><span (click)="deleteSubpool(x)" style="display: inline-block"><i class="fa fa-trash"></i></span></li>
                            </ul>
                        </td>
                    </tr>
                </ng-container>
            </table>
            <div class="d-flex justify-content-between">
                <ngb-pagination
                        [collectionSize]="collectionSize"
                        [(page)]="page"
                        [ellipses]="true"
                        [maxSize]="5"
                        (pageChange)="pageChangedCallback($event)"
                        [pageSize]="pageSize">
                </ngb-pagination>

                <select
                        class="custom-select"
                        style="width: auto"
                        [(ngModel)]="pageSize"
                        (ngModelChange)="pageSizeChangedCallback($event)">
                    <option [ngValue]="25">25 {{ 'PAGINATION.PER_PAGE' | translate }}</option>
                    <option [ngValue]="50">50 {{ 'PAGINATION.PER_PAGE' | translate }}</option>
                    <option [ngValue]="75">75 {{ 'PAGINATION.PER_PAGE' | translate }}</option>
                </select>
            </div>


        </div>
    </div>
</div>

<ng-template #edit>
    <vpp-subpool-form
            [subpool]="editedSubpool"
            [openInEditMode]="openInEditMode"
            (done)="closeSubpoolModal(true)"
            (canceled)="closeSubpoolModal(false)">
    </vpp-subpool-form>
</ng-template>