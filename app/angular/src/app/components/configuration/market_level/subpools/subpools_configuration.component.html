<div class="container-fluid">
    <div class="row">
        <div class="col-md-8">
            <div class="page-actions">
                <ul class="list-unstyled d-flex">
                    <li *ngIf="permissions.can_create_subpools">
                        <a href="javascript:void(0)"
                           (click)="toggleSection($event, 'enter-subpool')"
                           class="eon-button bg-eon-turquoise">
                            {{ 'CONFIGURATION.MARKET_LEVEL.SUBPOOLS.ENTER_SUBPOOL' | translate }}
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <div class="overlay-on-top-of-list"
         [@slideRightContentAnimation]="selectedSelection == 'enter-subpool' ? 'in' : 'out'">
        <vpp-subpool-form
                class="embedded"
                *ngIf="selectedSelection == 'enter-subpool'"
                [openInEditMode]="true"
                (done)="reloadList()"
                (canceled)="reloadList()">
        </vpp-subpool-form>
    </div>

    <section>
        <div class="row">
            <div class="col-md-12">
                <vpp-subpools-list *ngIf="loadList"></vpp-subpools-list>
            </div>
        </div>
    </section>
</div>