import {Component, EventEmitter, Input, OnInit, Output, ViewChild, ViewEncapsulation} from "@angular/core";
import {TranslateService} from "@ngx-translate/core";
import {NotificationService} from "../../../../services/notification.service";
import {ConfirmService} from "../../../../services/confirm.service";
import {angularData} from "../../../../../global.export";
import {SubpoolProvider} from "../../../../providers/subpool.provider";
import {DispatchGroupProvider} from "../../../../providers/dispatch_group.provider";
import {AssetProvider} from "../../../../providers/asset.provider";

@Component({
    selector: 'vpp-subpool-form',
    templateUrl: './subpool_form.component.html',
    styleUrls: ['./subpool_form.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class SubpoolFormComponent implements OnInit {
    @Output('done') done: any = new EventEmitter();
    @Output('canceled') canceled: any = new EventEmitter();
    @Input('subpool') subpool: any = {
        id: null,
        name: '',
        implicit: false,
        signals_list: [],
        dispatch_groups: [],
        assets: []
    };
    @Input() openInEditMode: boolean;
    @Input() embedded: boolean = false;

    availableSignals: any = [];
    selectedSignal: any = null;
    dispatchGroups: any = [];
    assets: any = [];

    errors: any = [];
    canSubmit: boolean = true;
    isEditing: boolean;
    confirmDeleteMessage = '';
    permissions: any;

    signalsDropdownSettings = {
        singleSelection: false,
        selectAllText: angularData.railsExports.locale  == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
        unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
        searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
        noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
        itemsShowLimit: 10,
        allowSearchFilter: true
    };

    dgDropdownSettings = {
        singleSelection: false,
        idField: 'id',
        textField: 'ps_name',
        selectAllText: angularData.railsExports.locale  == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
        unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
        searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
        noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
        itemsShowLimit: 10,
        allowSearchFilter: true
    };

    assetDropdownSettings = {
        singleSelection: false,
        idField: 'id',
        textField: 'ps_name',
        selectAllText: angularData.railsExports.locale  == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
        unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
        searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
        noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
        itemsShowLimit: 10,
        allowSearchFilter: true
    };

    constructor(
        public translate: TranslateService,
        private _notificationService: NotificationService,
        private _subpoolProvider: SubpoolProvider,
        private _dispatchGroupProvider: DispatchGroupProvider,
        private _assetProvider: AssetProvider,
        private _confirmService: ConfirmService
    ) {
        this.translate.get('CONFIRM.MESSAGE.DELETE_ENTRY').subscribe((x) => {
            this.confirmDeleteMessage = x;
        });
        this.permissions = angularData.permissions;
    }

    ngOnInit(): void {
        this.isEditing = this.openInEditMode;

        this.getSignals();
        this.getDispatchGroups();
        this.getAssets();
    }

    getDispatchGroups() {
        this._dispatchGroupProvider.findAllWithPagination().subscribe(result => {
            this.dispatchGroups = result.dispatch_groups;
            this.dispatchGroups = this.dispatchGroups.map((d) => {
                d['ps_name'] = `#${d.id} ${d.name} (${d.tso.name})`;
                return d;
            })
        });
    }

    getAssets() {
        this._assetProvider.findAllWithPagination().subscribe(result => {
            this.assets = result.assets;
            this.assets = this.assets.map((d) => {
                d['ps_name'] = `#${d.id} ${d.name} (${d.customer_name})`;
                return d;
            })
        });
    }

    close(event) {
        this.done.emit();
    }

    cancel(event) {
        this.canceled.emit();
    }

    enterEditMode() {
        this.isEditing = true;
    }

    cancelEditMode() {
        this.isEditing = false;
    }

    toggleImplicit() {
        this.subpool.implicit = !this.subpool.implicit;
    }

    getSignals() {
        this._subpoolProvider.getSubpoolSignals({}).subscribe(
            (res) => {
                this.availableSignals = res.subpool_signals;
                console.log('Got subpool signals', this.availableSignals);
            },
            (err) => {
                console.error('Failed to get subpool signals', err);
            });
    }

    submit() {
        console.log('Submit', this.subpool);

        this.errors = [];

        let isNew = !this.subpool.id;

        let params = {
            subpool: {
                id: this.subpool.id,
                name: this.subpool.name,
                signals_list: this.subpool.signals_list,
                implicit: this.subpool.implicit,
                dispatch_group_ids: this.subpool.dispatch_groups.map(x => x.id),
                asset_ids: this.subpool.assets.map(x => x.id)
            }
        };

        this.canSubmit = false;
        let fn = isNew ? this._subpoolProvider.createSubpool : this._subpoolProvider.updateSubpool;
        fn.call(this._subpoolProvider, params).subscribe(
            (res) => {
                this.canSubmit = true;
                if (res.success == true) {
                    this._notificationService.success({ text: 'SUCCESS'});
                    this.done.emit();
                } else {
                    this.errors = res.messages;
                }
            },
            (err) => {
                console.log('submit failed', err);
                this.errors = [JSON.stringify(err)];
                this.canSubmit = true;
            }
        );
    }

    delete() {
        this._confirmService.confirm({
            message: this.confirmDeleteMessage,
        }).then(() => {
            this._deleteConfirmed();
        }, () => {
        });
    }

    _deleteConfirmed() {
        this._subpoolProvider
            .deleteSubpool(this.subpool.id)
            .subscribe(
                (res) => {
                    if (res.success == true) {
                        this._notificationService.success({ text: 'SUCCESS'});
                        this.done.emit();
                    } else {
                        this._notificationService.error({ text: JSON.stringify(res.error)});
                    }
                },
                (err) => {
                    console.log('Failed to delete', err);
                    this._notificationService.error({ text: JSON.stringify(err)});
                });
    }

}