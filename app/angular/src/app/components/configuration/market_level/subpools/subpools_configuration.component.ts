import {
    Component,
    OnInit,
    ViewEncapsulation,
    ViewChild,
    ElementRef
} from "@angular/core";
import {
    Router,
    ActivatedRoute,
    Params,
    PRIMARY_OUTLET
} from "@angular/router";
import { Location } from "@angular/common";
import { GlobalService} from "../../../../services/global.service";
import { SlideRightContentAnimation} from "../../../../animations/slide-right-content";
import { environment} from "../../../../../environments/environment";
import { angularData} from "../../../../../global.export";
import { TranslateService } from '@ngx-translate/core';

@Component({
    selector: "vpp-subpools-configuration",
    templateUrl: "./subpools_configuration.component.html",
    styleUrls: ["./subpools_configuration.component.scss"],
    encapsulation: ViewEncapsulation.None,
    animations: [ SlideRightContentAnimation ],
})
export class SubpoolsConfigurationComponent implements OnInit {
    selectedSelection = "";
    loadList: boolean = true;
    permissions: any;

    constructor(
        public translate: TranslateService,
        private _global: GlobalService,
        private _route: ActivatedRoute,
        private _location: Location
    ) {
        this.permissions = angularData.permissions;
        this.translate.get('PAGE.CAPTIONS.CONFIGURATION.MARKET_LEVEL.SUBPOOLS').subscribe((res: string) => {
            this._global.changeTitle(res);
        });
    }

    ngOnInit() {}

    ngAfterViewInit() {
    }

    toggleSection(event, section) {
        if (event) {
            //event.preventDefault();
            //event.stopPropagation();
        }

        if (this.selectedSelection == section) {
            this.selectedSelection = '';
        } else {
            this.selectedSelection = section;
        }
    }

    reloadList() {
        this.toggleSection(null, '');
        // reload the list in the next refresh cycle
        this.loadList = false;
        setTimeout(() => {
            this.loadList = true;
        });
    }

}
