<section> <!-- Asset Level Configuration -->
    <div class="container-fluid">
        <div id="ngb-tabset" class="ngb-tabset">
            <ul role="tablist" class="nav nav-tabs justify-content-start">

                <!-- Subpools -->
                <li class="nav-item"
                    *ngIf="permittedTabs.subpools">
                    <a (click)="selectTab($event, 'subpools')"
                       [ngClass]="selectedTab == 'subpools' ? 'active' : ''"
                       class="nav-link"
                       href="javascript:void(0)"
                       role="tab"
                       id="subpools" >{{'CONFIGURATION.MARKET_LEVEL.TAB.SUBPOOLS' | translate}}</a>
                </li>

                <!-- Balancing Groups -->
                <li class="nav-item"
                    *ngIf="permittedTabs.balancingGroups">
                    <a (click)="selectTab($event, 'balancing-groups')"
                       [ngClass]="selectedTab == 'balancing-groups' ? 'active' : ''"
                       class="nav-link"
                       href="javascript:void(0)"
                       role="tab"
                       id="balancing-groups" >{{'CONFIGURATION.MARKET_LEVEL.TAB.BALANCING_GROUPS' | translate}}</a>
                </li>

                <!-- Dispatch Groups -->
                <li class="nav-item"
                    *ngIf="permittedTabs.dispatchGroups">
                    <a (click)="selectTab($event, 'dispatch-groups')"
                       [ngClass]="selectedTab == 'dispatch-groups' ? 'active' : ''"
                       class="nav-link"
                       href="javascript:void(0)"
                       role="tab"
                       id="dispatch-groups" >{{'CONFIGURATION.MARKET_LEVEL.TAB.DISPATCH_GROUPS' | translate}}</a>
                </li>

                <!-- Auctions -->
                <li class="nav-item"
                    *ngIf="permittedTabs.auctions">
                    <a (click)="selectTab($event, 'auctions')"
                       [ngClass]="selectedTab == 'auctions' ? 'active' : ''"
                       class="nav-link"
                       href="javascript:void(0)"
                       role="tab"
                       id="auctions" >{{'CONFIGURATION.MARKET_LEVEL.TAB.AUCTIONS' | translate}}</a>
                </li>
            </ul>

            <div class="tab-content">
                <div [@slideRightContentAnimation]="selectedTab == 'subpools' ? 'in' : 'out'"
                     *ngIf="permittedTabs.balancingGroups">
                    <vpp-subpools-configuration *ngIf="selectedTab == 'subpools'"></vpp-subpools-configuration>
                </div>

                <div [@slideRightContentAnimation]="selectedTab == 'balancing-groups' ? 'in' : 'out'"
                     *ngIf="permittedTabs.balancingGroups">
                    <vpp-balancing-groups-configuration *ngIf="selectedTab == 'balancing-groups'"></vpp-balancing-groups-configuration>
                </div>

                <div [@slideRightContentAnimation]="selectedTab == 'dispatch-groups' ? 'in' : 'out'"
                     *ngIf="permittedTabs.dispatchGroups">
                    <vpp-dispatch-groups-configuration
                            *ngIf="selectedTab == 'dispatch-groups'"
                            [selectedDgId]="selectedDgId"
                        ></vpp-dispatch-groups-configuration>
                </div>
                <div [@slideRightContentAnimation]="selectedTab == 'auctions' ? 'in' : 'out'"
                     *ngIf="permittedTabs.auctions"
                >
                    <vpp-auctions-configuration *ngIf="selectedTab == 'auctions'"></vpp-auctions-configuration>
                </div>
            </div>
        </div>
    </div>
</section>