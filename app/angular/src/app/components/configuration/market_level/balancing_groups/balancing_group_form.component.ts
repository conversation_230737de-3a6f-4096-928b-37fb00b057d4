import {Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild, ViewEncapsulation} from "@angular/core";
import {BalancingGroupProvider} from "../../../../providers/balancing_group.provider";
import {NotificationService} from "../../../../services/notification.service";
import {ConfirmService} from "../../../../services/confirm.service";
import {GlobalService} from "../../../../services/global.service";
import * as Moment from "moment";
import * as mTZ from "moment-timezone";


@Component({
    selector: 'vpp-balancing-group-form',
    templateUrl: './balancing_group_form.component.html',
    styleUrls: ['./balancing_group_form.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class BalancingGroupFormComponent implements OnInit {
    @Output('done') done: any = new EventEmitter();
    @Output('canceled') canceled: any = new EventEmitter();

    @ViewChild('lastBgName', {read: ElementRef, static: false}) lastBgNameInputElement: ElementRef;

    errors: any = [];
    permissions: any;

    newEntryTemplate = {
        id: null,
        name: ''
    };
    newEntry = {...this.newEntryTemplate};

    canSubmit: boolean = true;

    //@Input() bgs = [];
    //@Input() title = '';

    @Input() data: any = {
        type: '',
        hasDateRange: false,
        bgs: []
    };


    constructor(
        private _notificationService: NotificationService,
        private _confirmService: ConfirmService,
        private _balancingGroupProvider: BalancingGroupProvider,
        public globalService: GlobalService,
    ) {
    }

    ngOnInit(): void {
        // setup intervals for datepicker
        if (this.data.hasDateRange) {
            this.data.bgs.forEach((x) => {
                x.dateRange = [
                    Moment(x.start_date, 'YYYYMMDD'),
                    Moment(x.end_date, 'YYYYMMDD'),
                ];
            });
        }
    }

    close(event) {
        this.done.emit();
    }

    cancel(event) {
        this.canceled.emit();
    }

    onNewEntry(event) {
        this.data.bgs.push(this.newEntry);
        this.newEntry = {...this.newEntryTemplate};

        setTimeout(() => {
            let el = this.lastBgNameInputElement.nativeElement;
            el.focus();
        });
    }

    onDeleteEntry(x) {
        this.data.bgs = this.data.bgs.filter(i => i != x);
    }

    submit() {
        this.errors = [];

        let balancingGroups = this.data.bgs.filter(x => x.name && x.name.length > 0);

        if (this.data.hasDateRange) {
            balancingGroups.forEach((x) => {
                x.start_date = x.dateRange[0].format('YYYYMMDD');
                x.end_date = x.dateRange[1].format('YYYYMMDD');
            });
        }

        let params = {
          type: this.data.type,
          balancing_groups: balancingGroups
        };

        this.canSubmit = false;

        this._balancingGroupProvider.updateBalancingGroupNames(params).subscribe(
            (res) => {
                this.canSubmit = true;
                if (res.success == true) {
                    this._notificationService.success({text: 'SUCCESS'});
                    this.done.emit();
                } else {
                    this.errors = res.messages;
                    this._notificationService.errors(this.errors);
                }
            },
            (err) => {
                console.error('submit failed', err);
                this.errors = [JSON.stringify(err)];
                this._notificationService.errors(this.errors);
                this.canSubmit = true;
            }
        );
    }

    dateRangeSelectedCallback(bg, date) {

    }

}