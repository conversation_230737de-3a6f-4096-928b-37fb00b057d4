<div class="container-fluid">
    <div class="row">
        <div class="col">
            <h2>{{'CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.LIST.TITLE'|translate}}</h2>

            <div class="table-filter d-flex"></div>

            <table class="table table-balancing-groups table-auction-results table-striped table-bg-turquoise">
                <tr>
                    <th class="type">{{'CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TH.TYPE'|translate}}</th>
                    <th class="name">{{'CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TH.NAME'|translate}}</th>
                    <th class="scheduling">{{'CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TH.SCHEDULING'|translate}}</th>
                    <th class="actions">{{'CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TH.ACTIONS'|translate}}</th>
                </tr>
                <tr *ngIf="showLoader">
                    <td colspan="4" class="text-center vertical-center">
                        <vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
                    <td>
                </tr>
                <tbody *ngIf="!showLoader">
                    <tr>
                        <td class="type">{{'CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TYPE.SUPPLIER_BGS'|translate}}</td>
                        <td class="name">
                            <ng-template ngFor let-bg [ngForOf]="bgs.supplier_bgs">
                                <span>{{ bg.name }}</span>
                                <br>
                            </ng-template>
                        </td>
                        <td class="scheduling">
                            <div class="form-check">
                                <label class="eon-checkbox-label non-clickable bg-eon-red"></label>
                            </div>
                        </td>
                        <td class="actions text-center">
                            <ul class="list-inline list-unstyled">
                                <li *ngIf="permissions.can_create_balancing_groups">
                                    <span (click)="editBalancingGroup(bgs.supplier_bgs, 'supplier_bgs')" style="display: inline-block"><i class="fa fa-pencil"></i></span>
                                </li>
                            </ul>
                        </td>
                    </tr>

                    <tr>
                        <td class="type">{{'CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TYPE.COLLECTING_BGS'|translate}}</td>
                        <td class="name">
                            <ng-template ngFor let-bg [ngForOf]="bgs.collecting_bgs">
                                <span>{{ bg.name }}</span>
                                <br>
                            </ng-template>
                        </td>
                        <td class="scheduling">
                            <div class="form-check">
                                <label class="eon-checkbox-label non-clickable bg-eon-red"></label>
                            </div>
                        </td>
                        <td class="actions text-center">
                            <ul class="list-inline list-unstyled">
                                <li *ngIf="permissions.can_create_balancing_groups">
                                    <span (click)="editBalancingGroup(bgs.collecting_bgs, 'collecting_bgs', true)" style="display: inline-block"><i class="fa fa-pencil"></i></span>
                                </li>
                            </ul>
                        </td>
                    </tr>

                    <tr>
                        <td class="type">{{'CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TYPE.INTERNAL_BGS'|translate}}</td>
                        <td class="name">
                            <ng-template ngFor let-bg [ngForOf]="bgs.internal_bgs">
                                <span>{{ bg.name }}</span>
                                <br>
                            </ng-template>
                        </td>
                        <td class="scheduling">
                            <div class="form-check">
                                <label class="eon-checkbox-label non-clickable bg-eon-red checked"></label>
                            </div>
                        </td>
                        <td class="actions text-center">
                            <ul class="list-inline list-unstyled">
                                <li *ngIf="permissions.can_create_balancing_groups">
                                    <span (click)="editBalancingGroup(bgs.internal_bgs, 'internal_bgs')" style="display: inline-block"><i class="fa fa-pencil"></i></span>
                                </li>
                            </ul>
                        </td>
                    </tr>

                    <tr>
                        <td class="type">{{'CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TYPE.THIRD_PARTY_BGS'|translate}}</td>
                        <td class="name">
                            <ng-template ngFor let-bg [ngForOf]="bgs.third_party_bgs">
                                <span>{{ bg.name }}</span>
                                <br>
                            </ng-template>
                        </td>
                        <td class="scheduling">
                            <div class="form-check">
                                <label class="eon-checkbox-label non-clickable bg-eon-red checked"></label>
                            </div>
                        </td>
                        <td class="actions text-center">
                            <ul class="list-inline list-unstyled">
                                <li *ngIf="permissions.can_create_balancing_groups">
                                    <span (click)="editBalancingGroup(bgs.third_party_bgs, 'third_party_bgs')" style="display: inline-block"><i class="fa fa-pencil"></i></span>
                                </li>
                            </ul>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<ng-template #edit>
    <vpp-balancing-group-form
        [data]="editedBgData"
        (done)="closeBalancingGroupModal(true)"
        (canceled)="closeBalancingGroupModal(false)">
    </vpp-balancing-group-form>
</ng-template>