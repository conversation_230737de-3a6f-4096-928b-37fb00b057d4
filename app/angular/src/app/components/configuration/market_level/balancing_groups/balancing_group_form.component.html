<div class="row">
    <div class="col-md-8" id="full-width-in-modal">
        <div class="tab-content light-blue" style="border: none" id="enter-balancing-group-tab">

        <span class="pull-right" (click)="cancel($event)">
                <i class="fa fa-times"></i>
            </span>


            <h2>{{'CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.ENTER_BALANCING_GROUP'|translate}}</h2>

            <h3>{{ 'CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TYPE.'+data.type|uppercase|translate}}</h3>

            <div class="alerts-wrapper-static" *ngIf="errors.length">
                <ng-container *ngFor="let errmsg of errors; let ix = index;">
                    <ngb-alert
                            [dismissible]="false"
                            [type]="'error'"> {{ errmsg }}
                    </ngb-alert>
                </ng-container>
            </div>

            <div class="row" *ngFor="let bg of data.bgs; let last = last">
                <div class="form-group col-md-7">
                    <input
                            *ngIf="!last"
                            class="form-control"
                            type="text"
                            [(ngModel)]="bg.name"
                    >
                    <input
                            *ngIf="last"
                            #lastBgName
                            class="form-control"
                            [(ngModel)]="bg.name"
                    >
                </div>
                <div class="form-group col-md-4" *ngIf="data.hasDateRange">
                    <input
                            type="text"
                            class="form-control"
                            [owlDateTime]="dt1"
                            [selectMode]="'range'"
                            [(ngModel)]="bg.dateRange"
                            [owlDateTimeTrigger]="dt1"
                            (dateTimeChange)="dateRangeSelectedCallback(bg, $event)"
                            placeholder=""
                        >
                    <owl-date-time
                        #dt1
                        (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
                        [pickerType]="'calendar'"
                        [pickerMode]="'popup'">
                    </owl-date-time>
                </div>
                <div class="form-group">
                    <button (click)="onDeleteEntry(bg)"
                            class="btn btn-link form-control"
                            style="border: none; padding: 0">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
            </div>

            <div class="row">
                <div class="col-md-3 form-group">
                    <button (click)="onNewEntry($event)"
                            class="btn btn-link form-control"
                            style="border: none; padding: 0">
                        <span>
                            <i class="fa fa-plus"></i>
                            Add
                        </span>
                    </button>
                </div>
            </div>


            <!-- Action button -->
            <ul class="list-inline list-unstyled d-flex form-actions">
                <li>
                    <button
                            (click)="submit()"
                            class="eon-button bg-eon-red">
                        <span>{{'CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.SAVE'|translate}}</span>
                    </button>
                </li>
            </ul>

        </div>
    </div>
</div>