import {Component, OnInit, ViewChild, ViewEncapsulation} from "@angular/core";
import {NgbModal, NgbModalOptions} from "@ng-bootstrap/ng-bootstrap";
import {NotificationService} from "../../../../services/notification.service";
import {TranslateService} from "@ngx-translate/core";
import {GlobalService} from "../../../../services/global.service";
import {ConfirmService} from "../../../../services/confirm.service";
import {angularData} from "../../../../../global.export";
import {BalancingGroupProvider} from "../../../../providers/balancing_group.provider";

@Component({
    selector: 'vpp-balancing-groups-list',
    templateUrl: './balancing_groups_list.component.html',
    styleUrls: ['./balancing_groups_list.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class BalancingGroupsListComponent implements OnInit {
    showLoader = true;
    bgs: any = {
        supplier_bgs: [],
        collecting_bgs: [],
        internal_bgs: [],
        third_party_bgs: []
    };

    //editedBalancingGroup: any;
    //editedBalancingGroupTitle: any;
    editedBgData: any = {};

    openInEditMode = true;
    bsModalRef;
    permissions: any;

    @ViewChild('edit', { static: true }) editModal;


    constructor(
        public translate: TranslateService,
        public globalService: GlobalService,
        private _modalService: NgbModal,
        private _notificationService: NotificationService,
        private _balancingGroupProvider: BalancingGroupProvider
    ) {
        this.permissions = angularData.permissions;
    }

    ngOnInit(): void {
        this.getBgs();
    }

    getBgs() {
        this.showLoader = true;
        this._balancingGroupProvider.getBalancingGroupNames().subscribe(
            res => {
                this.bgs = res.bgs;
                this.showLoader = false;
            }, err => {
                this.showLoader = false;
                console.log('Failed to load subpools', err);
            }
        );
    }

    editBalancingGroup(x, type, hasDateRange = false) {
        this.editedBgData = {
            type: type,
            bgs: x,
            hasDateRange: hasDateRange
        };

        let options: NgbModalOptions = hasDateRange ? {size: 'lg'} : {
            //size: 'sm'
        };
        this.openInEditMode = true;
        this.bsModalRef = this._modalService.open(this.editModal, options);
    }

    closeBalancingGroupModal(reload: boolean = true) {
        this.bsModalRef.close();
        if (reload) {
            this.getBgs();
        }
    }



}