import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import {NgbDate, NgbCalendar} from '@ng-bootstrap/ng-bootstrap';
import * as moment from "moment-timezone";
import { Observable } from "rxjs";
import { debounceTime, distinctUntilChanged, map } from "rxjs/operators";
import { angularData } from "./../../../global.export";
import { GlobalService } from "./../../services/global.service";
import { SlideRightContentAnimation } from './../../animations/slide-right-content';
import { Location } from "@angular/common";
import { environment } from "../../../environments/environment";
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: "vpp-management-automatic-report-emailings",
  templateUrl: "./automatic_report_emailings.component.html",
  styleUrls: ["./automatic_report_emailings.component.scss"],
  encapsulation: ViewEncapsulation.None,
  animations: [ SlideRightContentAnimation ],
})

export class AutomaticReportEmailingsComponent implements OnInit {
  selectedTab: string = 'scheduling-report';

  constructor(
    private _Global: GlobalService,
    private route: ActivatedRoute,
    private _Location: Location,
    public translate: TranslateService
  ) {
    this.translate.get('PAGE.CAPTIONS.REPORTING_AND_NOTIFICATIONS.EMAILS').subscribe((res: string) => {
      this._Global.changeTitle(res);
    });
  }

  ngOnInit() {
  }

  ngAfterViewInit() {
    this.route.params.forEach((params: Params) => {
      if (params["tab"]) {
        this.selectTab(null, params["tab"]);
      } else {
        this.selectTab(null, this.selectedTab);
      }
    });
  }

  selectTab(event, tab) {
    if (event) {
      event.preventDefault();
      //event.stopPropagation();
    }
    this.selectedTab = tab;
    this._Location.replaceState(`${environment.routingPath}/automatic-report-emailings/${tab}`);
  }

}
