import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import {NgbDate, NgbCalendar} from '@ng-bootstrap/ng-bootstrap';
import * as moment from "moment-timezone";
import { Observable } from "rxjs";
import { debounceTime, distinctUntilChanged, map } from "rxjs/operators";
import { angularData } from "./../../../../global.export";
import { GlobalService } from "./../../../services/global.service";
import { EmailConfigurationProvider } from './../../../providers/email_configuration.provider';
import { NotificationService } from "./../../../services/notification.service";
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: "vpp-management-automatic-report-emailings-rollup",
  templateUrl: "./rollup.component.html",
  styleUrls: ["./rollup.component.scss"],
  encapsulation: ViewEncapsulation.None
})

export class AutomaticReportEmailingsRollupComponent implements OnInit {
  rollup: any = {
    id: null,
    rollup_notifications_email_recipients: null,
  };
  permissions: any;

  constructor(
    private _Global: GlobalService,
    private _EmailConfiguration: EmailConfigurationProvider,
    private _NotificationService: NotificationService,
    public translate: TranslateService
  ) {
    this.translate.get('PAGE.CAPTIONS.REPORTING_AND_NOTIFICATIONS.EMAILS.ROLLUPS').subscribe((res: string) => {
      this._Global.changeTitle(res);
    });
    this.permissions = angularData.permissions;
  }

  ngOnInit() {
    this._EmailConfiguration.rolllup().subscribe((result) => {
      this.rollup = result.rollup;
      this.rollup.rollup_notifications_email_recipients = this.rollup.rollup_notifications_email_recipients.join('\n');
    })
  }

  save() {
    let params = {
      rollup_email_sendings_configuration: {
        rollup_notifications_active: this.rollup.rollup_notifications_active,
        rollup_notifications_email_recipients: this.rollup.rollup_notifications_email_recipients
      }
    };

    this._EmailConfiguration.saveRolllup(params).subscribe(
      (result) => {
        if (result.success == true) {
          this.rollup = result.rollup;
          this.rollup.rollup_notifications_email_recipients = this.rollup.rollup_notifications_email_recipients.join('\n');
          this._NotificationService.success({ text: 'SUCCESS'});
        } else {
          this._NotificationService.error({ text: JSON.stringify(result.error)});
        }
      },
      (err) => {
        this._NotificationService.error({ text: JSON.stringify(err)});
      }
    )
  }
}
