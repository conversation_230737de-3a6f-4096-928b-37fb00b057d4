<div class="container-fluid">
  <div class="row">
     <div class="col">
        <form>
           <div class="form-group">
              <label>{{ 'AUTOMATIC_REPORT_EMAILINGS.ROLLUPS.RECIPIENTS' | translate }}</label>
              <textarea name="name" class="form-control" rows="4" cols="80"
                  [readonly]="!permissions.can_create_reporting_and_nofications"
                  [(ngModel)]="rollup.rollup_notifications_email_recipients"></textarea>
              <small class="form-text text-muted">{{ 'AUTOMATIC_REPORT_EMAILINGS.ROLLUPS.INSTRUCTIONS' | translate }}</small>

           </div>
           <div class="form-group">
              <div class="form-check">
                 <label
                 class="eon-checkbox-label bg-eon-red"
                 [ngStyle]="!permissions.can_create_reporting_and_nofications ? {'pointer-events': 'none'}:{}"
                 (click)="rollup.rollup_notifications_active = !rollup.rollup_notifications_active"
                 [ngClass]="rollup.rollup_notifications_active ? 'checked': ''">{{ 'AUTOMATIC_REPORT_EMAILINGS.ROLLUPS.ROLLUP_NOTIFICATIONS_ACTIVE' | translate }}</label>
              </div>
           </div>
           <button (click)="save()" class="eon-button bg-eon-red" *ngIf="permissions.can_create_reporting_and_nofications">
           <span>{{ 'AUTOMATIC_REPORT_EMAILINGS.ROLLUPS.UPDATE' | translate }}</span>
           </button>
        </form>
     </div>

  </div>
</div>