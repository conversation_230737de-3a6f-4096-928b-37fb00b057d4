import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import {NgbDate, NgbCalendar} from '@ng-bootstrap/ng-bootstrap';
import * as moment from "moment-timezone";
import { Observable } from "rxjs";
import { debounceTime, distinctUntilChanged, map } from "rxjs/operators";
import { angularData } from "./../../../../global.export";
import { GlobalService } from "./../../../services/global.service";
import { EmailConfigurationProvider } from './../../../providers/email_configuration.provider';
import { NotificationService } from "./../../../services/notification.service";
import { TranslateService } from '@ngx-translate/core';
import {ConfirmService} from "../../../services/confirm.service";

@Component({
  selector: "vpp-management-automatic-report-emailings-scheduling-report",
  templateUrl: "./scheduling-report.component.html",
  styleUrls: ["./scheduling-report.component.scss"],
  encapsulation: ViewEncapsulation.None
})

export class AutomaticReportEmailingsSchedulingReportComponent implements OnInit {
  schedulingReportsSettings: any = {
    id: null,
    scheduling_balancing_groups_email_recipients: null,
    scheduling_balancing_groups_active: false,
    scheduling_day_after_email_recipients: null,
    scheduling_day_after_active: false,
    tso_schedule_alarm_email_recipients: null,
    tso_schedule_alarm_active: false,
    scheduling_balancing_groups_third_party_email_recipients: null,
    scheduling_balancing_groups_third_party_active: false,
    main_address: null
  };
  permissions: any;

  constructor(
    private _Global: GlobalService,
    private _EmailConfiguration: EmailConfigurationProvider,
    private _NotificationService: NotificationService,
    private _ConfirmService: ConfirmService,
    public translate: TranslateService
  ) {
    this.translate.get('PAGE.CAPTIONS.REPORTING_AND_NOTIFICATIONS.EMAILS.SCHEDULING_REPORT').subscribe((res: string) => {
      this._Global.changeTitle(res);
    });
    this.permissions = angularData.permissions;
  }

  ngOnInit() {
    this._EmailConfiguration.schedulingReport().subscribe((result) => {
      this._onReceivedSchedulingReportSettings(result.scheduling_report_settings);
    })
  }

  private _onReceivedSchedulingReportSettings(settings) {
    this.schedulingReportsSettings = settings;
    this.schedulingReportsSettings.schedulingBalancingGroupsEmailRecipientsInput = this.schedulingReportsSettings.schedulingBalancingGroupsEmailRecipients.join(';');
    this.schedulingReportsSettings.schedulingDayAfterEmailRecipientsInput = this.schedulingReportsSettings.schedulingDayAfterEmailRecipients.join(';');
    this.schedulingReportsSettings.tsoScheduleAlarmEmailRecipientsInput = this.schedulingReportsSettings.tsoScheduleAlarmEmailRecipients.join(';');
    this.schedulingReportsSettings.schedulingBalancingGroupsThirdPartyEmailRecipientsInput = this.schedulingReportsSettings.schedulingBalancingGroupsThirdPartyEmailRecipients.map((x) => {
      return {
        group: x[0],
        emails: x[1].join(';'),
        active: x[2]
      };
    });
  }

  save() {
    let params = {
      report_email_sendings_configuration: {
        scheduling_balancing_groups_email_recipients: this.schedulingReportsSettings.schedulingBalancingGroupsEmailRecipientsInput,
        scheduling_balancing_groups_active: this.schedulingReportsSettings.schedulingBalancingGroupsActive,
        scheduling_day_after_email_recipients: this.schedulingReportsSettings.schedulingDayAfterEmailRecipientsInput,
        scheduling_day_after_active: this.schedulingReportsSettings.schedulingDayAfterActive,
        tso_schedule_alarm_email_recipients: this.schedulingReportsSettings.tsoScheduleAlarmEmailRecipientsInput,
        tso_schedule_alarm_active: this.schedulingReportsSettings.tsoScheduleAlarmActive,
        scheduling_balancing_groups_third_party_email_recipients: this.schedulingReportsSettings.schedulingBalancingGroupsThirdPartyEmailRecipientsInput.map((x) => {
          return [
            x.group,
            x.emails,
            x.active
          ];
        }),
        scheduling_balancing_groups_third_party_active: this.schedulingReportsSettings.schedulingBalancingGroupsThirdPartyActive,
        main_address: this.schedulingReportsSettings.mainAddress
      }
    };


    this._EmailConfiguration.saveSchedulingReport(params).subscribe(
      (result) => {
        if (result.success == true) {
          this._onReceivedSchedulingReportSettings(result.scheduling_report_settings);
          //this._NotificationService.success({ text: 'SUCCESS'});
          this._ConfirmService.alert({
            message: this.translate.instant('AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.UPDATED')});
        } else {
          this._NotificationService.error({ text: JSON.stringify(result.error)});
        }
      },
      (err) => {
        console.log('Failed to save scheduling report', err);
        this._NotificationService.error({ text: JSON.stringify(err)});
      }
    );
  }
}
