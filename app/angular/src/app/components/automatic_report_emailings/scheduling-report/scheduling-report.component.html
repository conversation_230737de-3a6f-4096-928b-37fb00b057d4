<div class="container-fluid">
  <br>
  <small class="form-text text-muted">{{ 'AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.INSTRUCTIONS' | translate }}</small>
   <div class="form-row">
      <div class="col-md-8">
         <label>{{ 'AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.EDG_REPORTS_RECIPIENTS' | translate }}</label>
         <textarea name="name" class="form-control" rows="3" cols="80"
                   id="schedulingBalancingGroupsEmailRecipients"
                   [readonly]="!permissions.can_create_reporting_and_nofications"
                   [(ngModel)]="schedulingReportsSettings.schedulingBalancingGroupsEmailRecipientsInput"></textarea>

      </div>
      <div class="col align-self-center">
         <div class="form-check">
            <label
            class="eon-checkbox-label bg-eon-red"
            [ngStyle]="!permissions.can_create_reporting_and_nofications ? {'pointer-events': 'none'}:{}"
            (click)="schedulingReportsSettings.schedulingBalancingGroupsActive = !schedulingReportsSettings.schedulingBalancingGroupsActive"
            [ngClass]="schedulingReportsSettings.schedulingBalancingGroupsActive ? 'checked': ''">{{ 'AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.EDG_REPORTS_ACTIVE' | translate }}</label>
         </div>
      </div>
   </div>

   <div class="form-row">
      <div class="col-md-8">
         <label>{{ 'AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.UGC_REPORTS_RECIPIENTS' | translate }}</label>
         <textarea name="name" class="form-control" rows="3" cols="80"
                   id="schedulingDayAfterEmailRecipients"
                   [readonly]="!permissions.can_create_reporting_and_nofications"
                   [(ngModel)]="schedulingReportsSettings.schedulingDayAfterEmailRecipientsInput"></textarea>
      </div>
      <div class="col align-self-center">
         <div class="form-check">
            <label
            class="eon-checkbox-label bg-eon-red"
            [ngStyle]="!permissions.can_create_reporting_and_nofications ? {'pointer-events': 'none'}:{}"
            (click)="schedulingReportsSettings.schedulingDayAfterActive = !schedulingReportsSettings.schedulingDayAfterActive"
            [ngClass]="schedulingReportsSettings.schedulingDayAfterActive ? 'checked': ''">{{ 'AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.UGC_REPORTS_ACTIVE' | translate }}</label>
         </div>
      </div>
   </div>

   <div class="form-row">
      <div class="col-md-8">
        <div class="form-group">
         <label>{{ 'AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.TSO_SCHEDULING_UPLOAD_ALARM_RECIPIENTS' | translate }}</label>
         <textarea name="name" class="form-control" rows="3" cols="80"
                   id=""tsoScheduleAlarmEmailRecipients
                   [readonly]="!permissions.can_create_reporting_and_nofications"
                   [(ngModel)]="schedulingReportsSettings.tsoScheduleAlarmEmailRecipientsInput"></textarea>

         </div>
      </div>
      <div class="col align-self-center">
        <div class="form-group">
         <div class="form-check">
            <label
            class="eon-checkbox-label bg-eon-red"
            [ngStyle]="!permissions.can_create_reporting_and_nofications ? {'pointer-events': 'none'}:{}"
            (click)="schedulingReportsSettings.tsoScheduleAlarmActive = !schedulingReportsSettings.tsoScheduleAlarmActive"
            [ngClass]="schedulingReportsSettings.tsoScheduleAlarmActive ? 'checked': ''">{{ 'AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.TSO_SCHEDULING_UPLOAD_ALARM_ACTIVE' | translate }}</label>
         </div>
         </div>
      </div>
   </div>


   <h2>{{ 'AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.THIRD_PARTY_REPORTS_RECIPIENTS' | translate }}</h2>
   <div class="form-group">
      <div class="form-check">
         <label
         class="eon-checkbox-label bg-eon-red"
         [ngStyle]="!permissions.can_create_reporting_and_nofications ? {'pointer-events': 'none'}:{}"
         (click)="schedulingReportsSettings.schedulingBalancingGroupsThirdPartyActive = !schedulingReportsSettings.schedulingBalancingGroupsThirdPartyActive"
         [ngClass]="schedulingReportsSettings.schedulingBalancingGroupsThirdPartyActive ? 'checked': ''">{{ 'AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.THIRD_PARTY_REPORTS_RECIPIENTS_ACTIVE' | translate }}</label>
      </div>
   </div>

    <div class="form-row" *ngFor="let tp of schedulingReportsSettings.schedulingBalancingGroupsThirdPartyEmailRecipientsInput">
        <div class="col-md-8">
            <div class="input-group mb-3">
                <div class="input-group-prepend">
                    <span class="input-group-text" id="basic-addon3"> {{ tp.group }}</span>
                </div>
                <input type="text" class="form-control" id="basic-url" aria-describedby="basic-addon3"
                       [readonly]="!permissions.can_create_reporting_and_nofications"
                       [(ngModel)]="tp.emails">
            </div>
        </div>
        <div class="col">
            <div class="form-check">
                <label
                        class="eon-checkbox-label bg-eon-red"
                        [ngStyle]="!permissions.can_create_reporting_and_nofications ? {'pointer-events': 'none'}:{}"
                        (click)="tp.active = !tp.active"
                        [ngClass]="tp.active ? 'checked': ''">{{ 'AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.ONLY_IN_CASE_OF_ACTIVATION' | translate }}</label>
            </div>
        </div>
    </div>

   <div class="form-row">
     <div class="col-md-8">
       <div class="form-group">
         <label>{{ 'AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.SEND_ALL_EMAILS_TO' | translate }}</label>
         <input class="form-control" type="text"
                [readonly]="!permissions.can_create_reporting_and_nofications"
                [(ngModel)]="schedulingReportsSettings.mainAddress">
       </div>
     </div>
   </div>
   <div class="form-group" *ngIf="permissions.can_create_reporting_and_nofications">
     <button (click)="save()" class="eon-button bg-eon-red">
       <span>{{ 'AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.UPDATE' | translate }}</span>
     </button>
   </div>
   <br>
</div>