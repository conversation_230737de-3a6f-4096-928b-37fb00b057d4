import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import {NgbDate, NgbCalendar} from '@ng-bootstrap/ng-bootstrap';
import * as moment from "moment-timezone";
import { Observable } from "rxjs";
import { debounceTime, distinctUntilChanged, map } from "rxjs/operators";
import { angularData } from "./../../../../global.export";
import { EmailConfigurationProvider } from './../../../providers/email_configuration.provider';
import { NotificationService } from "./../../../services/notification.service";
import { GlobalService } from "./../../../services/global.service";
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: "vpp-management-automatic-report-emailings-allocation",
  templateUrl: "./allocation.component.html",
  styleUrls: ["./allocation.component.scss"],
  encapsulation: ViewEncapsulation.None
})

export class AutomaticReportEmailingsAllocationComponent implements OnInit {
  allocation: any = {
    automatic_allocation_notifications_active: null,
    automatic_allocation_notifications_email_recipients: null
  };
  permissions: any;

  constructor(
    private _Global: GlobalService,
    private _EmailConfiguration: EmailConfigurationProvider,
    private _NotificationService: NotificationService,
    public translate: TranslateService
    ) {
    this.translate.get('PAGE.CAPTIONS.REPORTING_AND_NOTIFICATIONS.EMAILS.ALLOCATION').subscribe((res: string) => {
      this._Global.changeTitle(res);
    });
    this.permissions = angularData.permissions;
  }

  ngOnInit() {
    this._EmailConfiguration.allocation().subscribe((result) => {
      this.allocation = result.allocation;
      this.allocation.automatic_allocation_notifications_email_recipients = this.allocation.automatic_allocation_notifications_email_recipients.join('\n');
    })
  }

  save() {
    let params = {
      automatic_allocation_email_sendings_configuration: {
        automatic_allocation_notifications_active: this.allocation.automatic_allocation_notifications_active,
        automatic_allocation_notifications_email_recipients: this.allocation.automatic_allocation_notifications_email_recipients
      }
    };

    this._EmailConfiguration.saveAllocation(params).subscribe(
      (result) => {
        if (result.success == true) {
          this.allocation = result.allocation;
          this.allocation.automatic_allocation_notifications_email_recipients = this.allocation.automatic_allocation_notifications_email_recipients.join('\n');
          this._NotificationService.success({ text: 'SUCCESS'});
        } else {
          this._NotificationService.error({ text: JSON.stringify(result.error)});
        }
      },
      (err) => {
        this._NotificationService.error({ text: JSON.stringify(err)});
      }
    )
  }

}
