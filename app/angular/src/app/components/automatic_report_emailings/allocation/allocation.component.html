<div class="container-fluid">
  <div class="row">
    <div class="col">
       <form>
         <div class="form-group">
            <div class="form-check">
               <label
               class="eon-checkbox-label bg-eon-red"
               [ngStyle]="!permissions.can_create_reporting_and_nofications ? {'pointer-events': 'none'}:{}"
               (click)="allocation.automatic_allocation_notifications_active = !allocation.automatic_allocation_notifications_active"
               [ngClass]="allocation.automatic_allocation_notifications_active ? 'checked': ''">{{ 'AUTOMATIC_REPORT_EMAILINGS.ALLOCATIONS.AUTOMATIC_ALLOCATIONS_NOTIFICATIONS_ACTIVE' | translate }}</label>
            </div>
         </div>
          <div class="form-group">
             <label>{{ 'AUTOMATIC_REPORT_EMAILINGS.ALLOCATIONS.AUTOMATIC_ALLOCATIONS_NOTIFICATIONS_RECIPIENTS' | translate }}</label>
             <textarea name="name" class="form-control" rows="4" cols="80"
                [readonly]="!permissions.can_create_reporting_and_nofications"
                [(ngModel)]="allocation.automatic_allocation_notifications_email_recipients"></textarea>
             <small class="form-text text-muted">{{ 'AUTOMATIC_REPORT_EMAILINGS.ALLOCATIONS.INSTRUCTIONS' | translate }}</small>

          </div>

          <button (click)="save()" class="eon-button bg-eon-red" *ngIf="permissions.can_create_reporting_and_nofications">
          <span>{{ 'AUTOMATIC_REPORT_EMAILINGS.ALLOCATIONS.UPDATE' | translate }}</span>
          </button>
       </form>
    </div>
  </div>
</div>