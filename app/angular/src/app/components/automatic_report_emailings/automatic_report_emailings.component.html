<section>
   <div class="container-fluid">
      <div id="ngb-tabset">
        <ul role="tablist" class="nav nav-tabs justify-content-start">
          <li class="nav-item">
            <a href="javascript:void(0)" (click)="selectTab($event, 'scheduling-report')"
               class="nav-link" [ngClass]="selectedTab == 'scheduling-report' ? 'active' : ''"
               role="tab" id="scheduling-report" >{{ 'AUTOMATIC_REPORT_EMAILINGS.TABS.SCHEDULING_REPORT' | translate }}</a>
          </li>
          <li class="nav-item">
            <a href="javascript:void(0)" (click)="selectTab($event, 'allocation')"
               class="nav-link" [ngClass]="selectedTab == 'allocation' ? 'active' : ''"
               role="tab" id="allocation">{{ 'AUTOMATIC_REPORT_EMAILINGS.TABS.ALLOCATION' | translate }}</a>
          </li>
          <li class="nav-item">
            <a href="javascript:void(0)" (click)="selectTab($event, 'rollup')"
               class="nav-link" [ngClass]="selectedTab == 'rollup' ? 'active' : ''"
               role="tab" id="rollup">{{ 'AUTOMATIC_REPORT_EMAILINGS.TABS.ROLLUPS' | translate }}</a>
          </li>
        </ul>
        <div class="tab-content">
          <div [@slideRightContentAnimation]="selectedTab == 'scheduling-report' ? 'in' : 'out'">
            <vpp-management-automatic-report-emailings-scheduling-report *ngIf="selectedTab == 'scheduling-report'"></vpp-management-automatic-report-emailings-scheduling-report>
          </div>
          <div [@slideRightContentAnimation]="selectedTab == 'allocation' ? 'in' : 'out'">
            <vpp-management-automatic-report-emailings-allocation *ngIf="selectedTab == 'allocation'"></vpp-management-automatic-report-emailings-allocation>
          </div>
          <div [@slideRightContentAnimation]="selectedTab == 'rollup' ? 'in' : 'out'">
            <vpp-management-automatic-report-emailings-rollup *ngIf="selectedTab == 'rollup'"></vpp-management-automatic-report-emailings-rollup>
          </div>
        </div>
      </div>
   </div>
</section>
