import { Component, Input, OnInit, Output, EventEmitter, ViewEncapsulation } from '@angular/core';

@Component({
  selector: 'vpp-management-loader',
  styleUrls: ['./loader.component.scss'],
  template: `
    <div *ngIf="showLoader" class="cd-loader">
      <div>
        <ul class="circles">
          <li>&nbsp;</li>
          <li>&nbsp;</li>
          <li>&nbsp;</li>
        </ul>
        <div *ngIf="loaderText" class="text">
          {{ loaderText }}
        </div>
      </div>
    </div>
  `,
  encapsulation: ViewEncapsulation.None
})

export class LoaderComponent {
  @Input('showLoader') showLoader: boolean;
  @Input('loaderText') loaderText;

  constructor() {}
}
