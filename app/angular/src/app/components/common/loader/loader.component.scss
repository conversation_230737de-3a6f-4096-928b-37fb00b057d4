vpp-management-loader {
  @keyframes blink {
    50% {
      opacity: 0.1;
    }
  }

  .cd-loader {
    .circles {
      padding: 3px;
      border-radius: 3px;
      margin: 0 6px;

      li {
        display: inline-block;
        border-radius: 50%;
        width: 10px;
        height: 10px;
        background-color: gray;
        margin: 2px;
        border-top: 3px solid rgba(#000,0.2);
        animation: blink 0.9s linear infinite;

        &:nth-child(1) {
          animation-delay: 0s;
        }
        &:nth-child(2) {
          animation-delay: 0.3s;
        }
        &:nth-child(3) {
          animation-delay: 0.6s;
        }
      }
    }
  }
}