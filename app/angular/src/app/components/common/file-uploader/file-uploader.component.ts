import {
  Component,
  OnInit,
  ViewChild,
  ViewEncapsulation,
  Output,
  Input,
  EventEmitter
} from "@angular/core";
import { FileUploader } from "ng2-file-upload";

@Component({
  selector: "vpp-management-file-uploader",
  templateUrl: "./file-uploader.component.html",
  styleUrls: ["./file-uploader.component.scss"],
  encapsulation: ViewEncapsulation.None
})

export class FileUploaderComponent implements OnInit {
  public uploaderObject;
  public fileName: String = "...";
  @Input() gnericUpload: boolean;
  @Input() options = {
    url: '',
    allowedMimeType: [''],
  };
  @Output() propagateFile: any = new EventEmitter();
  @Output() uploader: any = new EventEmitter();
  @ViewChild("uploadInput", { static: true }) uploadInput: any;
  @ViewChild("fakeUploadButton", { static: false }) fakeUploadButton: any;

  constructor() {}

  ngOnInit() {
    this.uploaderObject = new FileUploader(this.options);

    this.uploader.emit(this.uploaderObject);
  }

  clearFileUploaderQueue() {
    //this.uploaderObject.clearQueue();
    this.uploaderObject.queue = [];
    this.uploader.emit(this.uploaderObject);
    this.fileName = '...';
    this.uploadInput.nativeElement.value = "";
  }

  fileSelectedCallback(event) {
    let queue = this.uploaderObject.queue;
    let file = queue[queue.length - 1];
    this.fileName = file && file.file ? file.file.name : "...";
    this.propagateFile.emit(file);
  }

  clearFile() {
    this.uploadInput.nativeElement.value = "";
  }
}
