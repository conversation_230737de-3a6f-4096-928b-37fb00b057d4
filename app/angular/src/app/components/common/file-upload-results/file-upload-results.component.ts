import {Component, Input, <PERSON><PERSON>hanges, OnInit, SimpleChanges, ViewEncapsulation} from "@angular/core";

@Component({
  selector: "vpp-file-upload-results",
  templateUrl: "./file-upload-results.component.html",
  styleUrls: ["./file-upload-results.component.scss"],
  encapsulation: ViewEncapsulation.None
})

export class FileUploadResultsComponent implements OnInit, OnChanges {
  notifications: any = [];

  @Input() response;

  constructor() {
  }

  ngOnInit(): void {
    this.update();
  }

  ngOnChanges(changes: SimpleChanges): void {
    this.update();
  }

  update() {
    this.notifications = [];
    if (this.response) {
      if (this.response.success) {
        this.showValidationResults(this.response.result, this.response.messages);
      } else {
        if (this.response.error) {
          this.notifications.push({text: this.response.error, type: 'error'});
        }
        for (let i in this.response.messages) {
          let e = this.response.messages[i];
          this.notifications.push({text: e, type: 'error'});
        }
      }
      if (this.response.warning_msgs && this.response.warning_msgs.length > 0) {
        this.notifications.push({text: this.response.warning_msgs, type: 'warning'})
      }
    }
  }

  showValidationResults(source, messages) {
    let validationResults = source && source.validation_result ? (typeof (source.validation_result) == 'string' ? JSON.parse(source.validation_result) : source.validation_result) : null;

    if (validationResults) {
      if (validationResults.validationSuccess == true) {
        if (messages && messages.length > 0) {
          for (let i in messages) {
            this.notifications.push({text: messages[i], type: 'success'})
          }
        } else {
          this.notifications.push({text: 'SUCCESS', type: 'success'})
        }
      } else {
        //this.notifications.push({ text: 'CREATED_WITH_ERRORS', type: 'warning'});
        for (let i in validationResults.errors) {
          let e = validationResults.errors[i];
          let n = {
            text: e.name || e,
            type: 'error',
            params: null
          };

          if (e.details) {
            if (typeof (e.details) == 'string') {
              try {
                n['params'] = JSON.parse(e.details);
              } catch (e) {
                console.log('caught', e);
                n['params'] = e.details;
              }
            }
          }

          if (!this.notifications.find((x) => {
            return x.text == n.text && JSON.stringify(x.params) == JSON.stringify(n.params);
          })) {
            this.notifications.push(n);
          }
        }
      }
    }
  }

}