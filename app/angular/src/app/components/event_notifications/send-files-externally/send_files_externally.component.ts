import {Component, OnInit, ViewChild, ViewEncapsulation} from "@angular/core";
import {Moment} from "moment-timezone/moment-timezone";
import * as moment from "moment-timezone";
import {GlobalService} from "../../../services/global.service";
import {SendFilesExternallyProvider} from "../../../providers/send_files_externally.provider";
import {NotificationService} from "../../../services/notification.service";

@Component({
    selector: 'vpp-send-files-externally',
    templateUrl: './send_files_externally.component.html',
    styleUrls: ['./send_files_externally.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class SendFilesExternallyComponent implements OnInit {
    sending: boolean = false;
    dateFormat = 'DD/MM/YYYY';

    allocationsSendEnabled: boolean = false;
    allocationsDate: Moment = moment().add(-1, 'day');

    measurementsSendEnabled: boolean = false;
    measurementsDate: Moment = moment().add(-1, 'day');

    aFrrMeasurementsSendEnabled: boolean = false;
    aFrrMeasurementsDateTime: Moment = moment().add(-1, 'day').set({minute: 0, second: 0, millisecond: 0})

    aFrrActivatedEnergyDocumentsSendEnabled: boolean = false;
    aFrrActivatedEnergyDocumentsDate: Moment = moment().add(-1, 'day');
    aFrrActivatedEnergyDocumentsGenerateNewRandomMrId: boolean = true;
    aFrrActivatedEnergyDocumentsMrId: string = "";

    constructor(
        public globalService: GlobalService,
        private _sendFilesExternallyProvider: SendFilesExternallyProvider,
        private _notificationService: NotificationService
    ) {
        this.globalService.changeTitle('Send Files Externally');
    }

    ngOnInit() {
        this.updateSendAllocations();
        this.updateSendMeasurements();
        this.updateSendAfrrMeasurements(this.aFrrMeasurementsDateTime);
        this.updateSendAfrrActivatedEnergyDocuments();
    }

    updateSendAllocations() {
        this.allocationsSendEnabled = this.allocationsDate != null;
    }

    updateSendMeasurements() {
        this.measurementsSendEnabled = this.measurementsDate != null;
    }

    updateSendAfrrMeasurements(selectedDateTime) {
        if (this.aFrrMeasurementsDateTime != null) {
            this.aFrrMeasurementsDateTime.set({minute: 0, second: 0, millisecond: 0})
            this.aFrrMeasurementsSendEnabled = true
        }
        else {
            this.aFrrMeasurementsSendEnabled = false
        }
    }

    updateSendAfrrActivatedEnergyDocuments() {
        this.aFrrActivatedEnergyDocumentsSendEnabled = this.aFrrActivatedEnergyDocumentsDate != null
    }

    sendAllocations() {
        if (this.sending) {
            return;
        }
        let params = {
            date: this.allocationsDate.format(this.dateFormat)
        };
        this.sending = true;
        this._sendFilesExternallyProvider.sendAllocations(params).subscribe(
            res => {
                this.sending = false;
                if (res.success === true) {
                    this._notificationService.info({text: res.notice});
                } else {
                    this._notificationService.error({text: res.error});
                }
            },
            err => {
                this.sending = false;
                this._notificationService.error({text: 'Failed to resend allocations files to TenneT'});
            }
        );
    }

    sendMeasurements() {
        if (this.sending) {
            return;
        }
        let params = {
            date: this.measurementsDate.format(this.dateFormat)
        };
        this.sending = true;
        this._sendFilesExternallyProvider.sendMeasurements(params).subscribe(
            res => {
                this.sending = false;
                if (res.success === true) {
                    this._notificationService.info({text: res.notice});
                } else {
                    this._notificationService.error({text: res.error});
                }
            },
            err => {
                this.sending = false;
                this._notificationService.error({text: 'Failed to resend measurement files to TenneT'});
            }
        );
    }

    sendAfrrMeasurements() {
        if (this.sending) {
            return;
        }
        let params = {
            date_time: this.aFrrMeasurementsDateTime.toISOString()
        };
        this.sending = true;
        this._sendFilesExternallyProvider.sendAfrrMeasurements(params).subscribe(
            res => {
                this.sending = false;
                if (res.success === true) {
                    this._notificationService.info({text: res.notice});
                } else {
                    this._notificationService.error({text: res.error});
                }
            },
            err => {
                this.sending = false;
                this._notificationService.error({text: 'Failed to resend AFRR measurement files to TenneT'});
            }
        );
    }

    sendAfrrActivatedEnergyDocuments() {
        if (this.sending) {
            return;
        }
        let params = {
            date: this.aFrrActivatedEnergyDocumentsDate.format(this.dateFormat),
            generate_new_random_mr_id: this.aFrrActivatedEnergyDocumentsGenerateNewRandomMrId,
            mr_id: this.aFrrActivatedEnergyDocumentsMrId
        };
        this.sending = true;
        this._sendFilesExternallyProvider.sendAfrrActivatedEnergyDocuments(params).subscribe(
            res => {
                this.sending = false;
                if (res.success === true) {
                    this._notificationService.info({text: res.notice});
                } else {
                    this._notificationService.error({text: res.error});
                }
            },
            err => {
                this.sending = false;
                this._notificationService.error({text: 'Failed to resend AFRR measurement files to TenneT'});
            }
        );
    }

    toggleActivatedEnergyDocumentsGenerateNewRandomMrIdCheckbox() {
        this.aFrrActivatedEnergyDocumentsGenerateNewRandomMrId = !this.aFrrActivatedEnergyDocumentsGenerateNewRandomMrId;
        if (this.aFrrActivatedEnergyDocumentsGenerateNewRandomMrId) {
            this.aFrrActivatedEnergyDocumentsMrId = ""
        }
    }

    onlyHoursFilter(dateTime) {
        if (dateTime == null || (dateTime != null && moment(dateTime).minutes() != 0)) {
            return false
        }
        else {
            return true
        }
  }
}