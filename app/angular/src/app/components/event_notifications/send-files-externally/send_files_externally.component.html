<div class="container-fluid">
    <section>
        <h2>{{ "SEND_FILES_EXTERNALLY.SEND_ALLOCATION_FILES" | translate }}</h2>

        <div class="form-row">
            <div class="form-group">
                <label class="required">{{ "SEND_FILES_EXTERNALLY.DATE" | translate }}</label>
                <input
                        type="text"
                        class="form-control"
                        [owlDateTime]="dtAllocations"
                        [selectMode]="'single'"
                        [owlDateTimeTrigger]="dtAllocations"
                        [(ngModel)]="allocationsDate"
                        (dateTimeChange)="updateSendAllocations()"
                        readonly="true"
                        placeholder=""
                >
                <owl-date-time
                    #dtAllocations
                    (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
                    [pickerType]="'calendar'"
                    [pickerMode]="'popup'"
                ></owl-date-time>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <button class="eon-button bg-eon-red"
                        [disabled]="sending || !allocationsSendEnabled"
                        (click)="sendAllocations()">
                    <span>{{ "SEND_FILES_EXTERNALLY.SEND" | translate }}</span>
                </button>
            </div>
        </div>
    </section>

    <section>
        <h2>{{ "SEND_FILES_EXTERNALLY.SEND_MEASUREMENT_FILES" | translate }}</h2>

        <div class="form-row">
            <div class="form-group">
                <label class="required">{{ "SEND_FILES_EXTERNALLY.DATE" | translate }}</label>
                <input
                        type="text"
                        class="form-control"
                        [owlDateTime]="dtMeasurements"
                        [selectMode]="'single'"
                        [owlDateTimeTrigger]="dtMeasurements"
                        [(ngModel)]="measurementsDate"
                        (dateTimeChange)="updateSendMeasurements()"
                        readonly="true"
                        placeholder=""
                >
                <owl-date-time
                    #dtMeasurements
                    (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
                    [pickerType]="'calendar'"
                    [pickerMode]="'popup'"
                ></owl-date-time>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <button class="eon-button bg-eon-red"
                        [disabled]="sending || !measurementsSendEnabled"
                        (click)="sendMeasurements()">
                    <span>{{ "SEND_FILES_EXTERNALLY.SEND" | translate }}</span>
                </button>
            </div>
        </div>
    </section>

    <section>
        <h2>{{ "SEND_FILES_EXTERNALLY.SEND_AFRR_MEASUREMENT_FILES" | translate }}</h2>

        <div class="form-row">
            <div class="form-group">
                <label class="required">{{ "SEND_FILES_EXTERNALLY.DATE_TIME" | translate }}</label>
                <input
                        type="text"
                        class="form-control"
                        [owlDateTime]="dtAfrrMeasurements"
                        [selectMode]="'single'"
                        [owlDateTimeTrigger]="dtAfrrMeasurements"
                        [(ngModel)]="aFrrMeasurementsDateTime"
                        (dateTimeChange)="updateSendAfrrMeasurements($event)"
                        [owlDateTimeFilter]="onlyHoursFilter"
                        readonly="true"
                        placeholder=""
                >
                <owl-date-time
                    #dtAfrrMeasurements
                    (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
                    [pickerMode]="'popup'"
                    [stepMinute]="60"
                ></owl-date-time>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <button class="eon-button bg-eon-red"
                        [disabled]="sending || !aFrrMeasurementsSendEnabled"
                        (click)="sendAfrrMeasurements()">
                    <span>{{ "SEND_FILES_EXTERNALLY.SEND" | translate }}</span>
                </button>
            </div>
        </div>
    </section>

    <section>
        <h2>{{ "SEND_FILES_EXTERNALLY.SEND_AFRR_ACTIVATED_ENERGY_DOCUMENTS" | translate }}</h2>

        <div class="form-row">
            <div class="form-group col-md-3 pr-5" >
                <label class="required">{{ "SEND_FILES_EXTERNALLY.DATE" | translate }}</label>
                <input
                        type="text"
                        class="form-control"
                        [owlDateTime]="dtAfrrActivatedEnergyDocuments"
                        [selectMode]="'single'"
                        [owlDateTimeTrigger]="dtAfrrActivatedEnergyDocuments"
                        [(ngModel)]="aFrrActivatedEnergyDocumentsDate"
                        (dateTimeChange)="updateSendAfrrActivatedEnergyDocuments()"
                        readonly="true"
                        placeholder=""
                >
                <owl-date-time
                    #dtAfrrActivatedEnergyDocuments
                    (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
                    [pickerType]="'calendar'"
                    [pickerMode]="'popup'"
                ></owl-date-time>
            </div>
            <div class="form-check col-md-3 pr-5">
                <label>{{'SEND_FILES_EXTERNALLY.SEND_AFRR_ACTIVATED_ENERGY_DOCUMENTS.GENERATE_NEW_RANDOM_MR_ID'|translate}}</label>
                <label
                    class="eon-checkbox-label bg-eon-red"
                    [ngClass]="aFrrActivatedEnergyDocumentsGenerateNewRandomMrId ? 'checked': ''"
                    (click)="toggleActivatedEnergyDocumentsGenerateNewRandomMrIdCheckbox()"></label>
            </div>
            <div class="form-group  col-md-3" *ngIf="aFrrActivatedEnergyDocumentsGenerateNewRandomMrId == false">
                <label>{{'SEND_FILES_EXTERNALLY.SEND_AFRR_ACTIVATED_ENERGY_DOCUMENTS.MR_ID'|translate}}</label>
                <input
                    [(ngModel)]="aFrrActivatedEnergyDocumentsMrId"
                    type="text"
                    class="form-control"
                    id="aFrrActivatedEnergyDocumentsMrId">
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <button class="eon-button bg-eon-red"
                        [disabled]="sending || !aFrrActivatedEnergyDocumentsSendEnabled"
                        (click)="sendAfrrActivatedEnergyDocuments()">
                    <span>{{ "SEND_FILES_EXTERNALLY.SEND" | translate }}</span>
                </button>
            </div>
        </div>
    </section>

</div>