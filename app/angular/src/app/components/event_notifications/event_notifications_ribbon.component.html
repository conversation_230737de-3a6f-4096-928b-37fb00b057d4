<section>
    <div class="container-fluid">
        <div id="ngb-tabset">
            <ul role="tablist" class="nav nav-tabs justify-content-start">
                <li class="nav-item">
                    <a href="javascript:void(0)" (click)="selectTab($event, 'event_notifications_recipients')"
                       class="nav-link" [ngClass]="selectedTab == 'event_notifications_recipients' ? 'active' : ''"
                       role="tab" id="event_notifications_recipients" >{{ "EVENT_NOTIFICATIONS.TITLE" | translate }}</a>
                </li>
                <li class="nav-item">
                    <a href="javascript:void(0)" (click)="selectTab($event, 'event_notifications_dispatch_group_recipients')"
                       class="nav-link" [ngClass]="selectedTab == 'event_notifications_dispatch_group_recipients' ? 'active' : ''"
                       role="tab" id="event_notifications_dispatch_group_recipients" >{{ "EVENT_NOTIFICATIONS_DISPATCH_GROUP.TITLE" | translate }}</a>
                </li>
                <li class="nav-item">
                    <a href="javascript:void(0)" (click)="selectTab($event, 'event_notifications_auction_config_recipients')"
                       class="nav-link" [ngClass]="selectedTab == 'event_notifications_auction_config_recipients' ? 'active' : ''"
                       role="tab" id="event_notifications_auction_config_recipients" >{{ "EVENT_NOTIFICATIONS_AUCTION_CONFIG.TITLE" | translate }}</a>
                </li>
                <li class="nav-item">
                    <a href="javascript:void(0)" (click)="selectTab($event, 'send_files_externally')"
                       class="nav-link" [ngClass]="selectedTab == 'send_files_externally' ? 'active' : ''"
                       role="tab" id="send_files_externally" >{{ "SEND_FILE_EXTERNALLY.TITLE" | translate }}</a>
                </li>
            </ul>

            <div class="tab-content">
                <div [@slideRightContentAnimation]="selectedTab == 'event_notifications_recipients' ? 'in' : 'out'" *ngIf="permissions.can_see_reporting_and_nofications || permissions.can_see_nofications">
                    <vpp-management-event-notification-recipients *ngIf="selectedTab == 'event_notifications_recipients'">

                    </vpp-management-event-notification-recipients>
                </div>
                <div [@slideRightContentAnimation]="selectedTab == 'event_notifications_dispatch_group_recipients' ? 'in' : 'out'" *ngIf="permissions.can_see_reporting_and_nofications || permissions.can_see_nofications">
                    <vpp-management-event-notification-dispatch-group-recipients *ngIf="selectedTab == 'event_notifications_dispatch_group_recipients'">

                    </vpp-management-event-notification-dispatch-group-recipients>
                </div>
                <div [@slideRightContentAnimation]="selectedTab == 'event_notifications_auction_config_recipients' ? 'in' : 'out'" *ngIf="permissions.can_see_reporting_and_nofications || permissions.can_see_nofications">
                    <vpp-management-event-notification-auction-config-recipients *ngIf="selectedTab == 'event_notifications_auction_config_recipients'">

                    </vpp-management-event-notification-auction-config-recipients>
                </div>
                <div [@slideRightContentAnimation]="selectedTab == 'send_files_externally' ? 'in' : 'out'" *ngIf="permissions.can_see_reporting_and_nofications || permissions.can_see_nofications">
                    <vpp-send-files-externally *ngIf="selectedTab == 'send_files_externally'">
                    </vpp-send-files-externally>
                </div>
            </div>
        </div>
    </div>
</section>
