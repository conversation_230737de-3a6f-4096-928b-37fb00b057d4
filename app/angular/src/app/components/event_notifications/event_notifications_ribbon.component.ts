import {
    Component,
    OnInit,
    ViewEncapsulation
} from "@angular/core";
import { GlobalService } from "./../../services/global.service";
import { SlideRightContentAnimation } from './../../animations/slide-right-content';
import { TranslateService } from '@ngx-translate/core';
import {angularData} from "../../../global.export";
import {ActivatedRoute, Params} from "@angular/router";
import {environment} from "../../../environments/environment";
import {Location} from "@angular/common";

@Component({
    selector: "vpp-management-event-notifications-ribbon",
    templateUrl: "./event_notifications_ribbon.component.html",
    styleUrls: ["./event_notifications_ribbon.component.scss"],
    encapsulation: ViewEncapsulation.None,
    animations: [ SlideRightContentAnimation ],
})
export class EventNotificationsRibbonComponent implements OnInit {

    selectedTab: string = 'event_notifications_recipients';
    permissions: any;

    constructor(
        private _Global: GlobalService,
        private route: ActivatedRoute,
        private _Location: Location,
        public translate: TranslateService
    ) {
        this.translate.get('EVENT_NOTIFICATIONS.TITLE').subscribe((res: string) => {
            this._Global.changeTitle(res);
        });
        this.permissions = angularData.permissions;
    }

    ngOnInit() {
    }

    ngAfterViewInit() {
        this.route.params.forEach((params: Params) => {
            if (params['tab']) {
                this.selectTab(null, params['tab']);
            } else {
                this.selectTab(null, this.selectedTab);
            }
        });
    }

    selectTab(event, tab) {
        if (event) {
            event.preventDefault();
            //event.stopPropagation();
        }
        this.selectedTab = tab;
        this._Location.replaceState(`${environment.routingPath}/event_notifications_ribbon/${tab}`);
    }

}
