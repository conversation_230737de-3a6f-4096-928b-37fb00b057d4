@import "./../../../styles/colors";

.tooltip-inner {
  //white-space: nowrap;
  max-width: 600px;
}

vpp-management-event-notification-auction-config-recipients {
  display: block;
  background-color: $eon-turquoise-25;

  .event-type {
    color: $eon-turquoise;

    i {
      color: #5c7cfa;
      padding-right: 6px;
    }
  }

  .form-control {
    border-color: $eon-turquoise;
  }

  .input-group-text {
    border-color: $eon-turquoise;
  }

  button.eon-button {
    margin-top: 39px;
    margin-bottom: 30px;
    margin-left: 40px;
  }

  .multiselect-dropdown {
    .dropdown-btn {
      border: 2px solid $eon-darkgrey-25 !important;
      background: #fff !important;
      border-radius: 3px !important;
      padding: 6px;
    }

    .dropdown-list {
      position: relative !important;
      clear: both;
      margin-top: 0;
      transform: none !important;
    }
  }

  .accordion {
    .card {
      //border: 1px solid #0c5460;
      background-color: $eon-turquoise-25;
      margin-bottom: 0;
    }
  }

}