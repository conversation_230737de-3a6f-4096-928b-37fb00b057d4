import {
    Component,
    OnInit,
    ViewEncapsulation
} from "@angular/core";
import { GlobalService} from "../../../services/global.service";
import { TranslateService} from "@ngx-translate/core";
import {EventNotificationProvider} from "../../../providers/event_notification.provider";
import {angularData} from "../../../../global.export";

@Component({
    selector: 'vpp-management-event-notification-auction-config-recipients',
    templateUrl: './event_notification_auction_config_recipients.component.html',
    styleUrls: ['./event_notification_auction_config_recipients.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class EventNotificationAuctionConfigRecipientsComponent implements OnInit {
    eventTypes: any;
    users: any;
    auctionConfigs: any;
    selectViewModel: any;
    channels = ['email', 'phonecall', 'sms'];
    showLoader = true;

    dropdownSettings = {
        singleSelection: false,
        idField: 'id',
        textField: 'name',
        selectAllText: angularData.railsExports.locale  == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
        unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
        searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
        noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
        itemsShowLimit: 10,
        allowSearchFilter: true
    };
    permissions: any;

    constructor(
        private _eventNotificationProvider: EventNotificationProvider
    ) {
        this.permissions = angularData.permissions;
    }


    ngOnInit(): void {
        this.showLoader = true;
        this._eventNotificationProvider.eventNotificationAuctionConfigRecipients().subscribe(
            (res) => {
                this.showLoader = false;
                this.parseEventNotificationReceivers(res);
            },
            (err) => {
                this.showLoader = false;
                console.log('Failed to load the event notification receivers', err);
            });
    }

    parseEventNotificationReceivers(res): void {
        this.users = res.users;
        this.eventTypes = res.eventTypes;
        this.auctionConfigs = res.auctionConfigs;

        let uidMap = {};
        for (let iu in this.users) {
            const u = this.users[iu];
            uidMap[u.id] = u;
        }

        // create empty select view model structure
        let r = {};
        this.eventTypes.forEach(t => {
            r[t.toString()] = {};
            this.auctionConfigs.forEach(aCfg => {
              r[t.toString()][aCfg.id] = {}
              this.channels.forEach(c => {
                  r[t.toString()][aCfg.id][c.toString()] = [];
              });
            });
        });

        // populate selections
        res.selection.forEach(s => {
            let u = uidMap[s.uid];
            if (u) {
                if (r[s.eventType] && r[s.eventType][s.owner_id] ) {
                    r[s.eventType][s.owner_id][s.channel].push(u);
                } else {
                    console.log('unnotifiable event type', s.eventType);
                }
            } else {
                console.log('unexpected user id', s.uid);
            }
        });

        this.selectViewModel = r;

        console.log('this.recipients', r);
    }

    update() {
        // create params
        let params = {
            event_notification_auction_config_recipients: this.selectViewModel
        };

        this.showLoader = true;
        this._eventNotificationProvider.updateNotificationAuctionConfigRecipients(params).subscribe(
            (res) => {
                this.showLoader = false;
                this.parseEventNotificationReceivers(res);
            },
            (err) => {
                this.showLoader = false;
                console.log('Failed to update notification recipients', err);
            });
    }

}
