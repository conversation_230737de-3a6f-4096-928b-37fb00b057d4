<div class="form-row">
    <vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
</div>

<!--<ngb-accordion #acc="ngbAccordion" [activeIds]="eventTypes">-->
<ngb-accordion #acc="ngbAccordion">
    <ngb-panel *ngFor="let eventType of eventTypes" [id]="eventType">
        <ng-template ngbPanelHeader let-opened="opened">
            <button ngbPanelToggle class="btn btn-link">
                <h5 class="event-type">
                    <i class="fas" [ngClass]="opened ? 'fas fa-caret-down' : 'fas fa-caret-right'"></i>
                    {{ "EVENT_NOTIFICATIONS.EVENT_TYPE." + eventType | translate }}
                    <i class="fa fa-question-circle"
                       [ngbTooltip]='"EVENT_NOTIFICATIONS.TOOLTIP." + eventType | translate'
                       placement="right" container="body" containerClass="wider-tooltip"></i>
                </h5>
            </button>
        </ng-template>
        <ng-template ngbPanelContent>
            <div class="container-fluid">
                <div class="form-row">
                    <ng-container *ngFor="let channel of ['email', 'phonecall', 'sms']">
                        <div class="form-group col-md-4">
                            <label>{{ "EVENT_NOTIFICATIONS.CHANNEL." + channel | uppercase | translate }}</label>
                            <ng-multiselect-dropdown
                                    [placeholder]="('COMPONENTS.SELECT' | translate )"
                                    [data]="users"
                                    [(ngModel)]="selectViewModel[eventType][channel]"
                                    [settings]="dropdownSettings"
                                    [disabled]="!permissions.can_create_reporting_and_nofications && !permissions.can_create_nofications"
                            >
                            </ng-multiselect-dropdown>
                        </div>
                    </ng-container>
                </div>
            </div>
        </ng-template>
    </ngb-panel>
</ngb-accordion>


<div class="form-row">
    <button
            [disabled]="showLoader"
            *ngIf="permissions.can_create_reporting_and_nofications || permissions.can_create_nofications"
            (click)="update()"
            class="eon-button bg-eon-red">
        <span>{{ "EVENT_NOTIFICATIONS.SUBMIT" | translate }}</span>
    </button>
</div>
