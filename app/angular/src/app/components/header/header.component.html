<header id="app-header">
  <div class="container-fluid" #header>
    <div class="row">
      <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
          <div class="app-header--nav">
            <button
              (click)="toggleSideMenu($event)"
              class="hamburger hamburger--spring"
              id="hamburgerMenu"
              [ngClass]="sideMenuActive ? 'is-active' : ''"
              type="button">
              <span class="hamburger-box">
                <span class="hamburger-inner"></span>
              </span>
            </button>
            <nav id="side-menu" class="ang" [ngClass]="sideMenuActive ? 'is-active' : ''">
              <ul class="list-unstyled"></ul>
              <li>
                <a [routerLink]="[environment.routingPath + '/dashboard']" (click)="sideMenuActive = false">
                  <i class="fas fa-home"></i>
                  <span>{{ 'HEADER.HOME' | translate }}</span>
                </a>
              </li>

              <li *ngIf="permissions.can_see_bids || permissions.can_create_bids">
                <a [routerLink]="[environment.routingPath + '/bidding']" (click)="sideMenuActive = false">
                  <i class="fas fa-euro-sign"></i>
                  <span>{{ 'HEADER.BIDDING' | translate }}</span>
                </a>
              </li>

              <li *ngIf="permissions.can_see_nominations || permissions.can_create_nominations || permissions.can_update_nominations || permissions.can_delete_nominations">
                <a [routerLink]="[environment.routingPath + '/nominations']" (click)="sideMenuActive = false">
                  <i class="fas fa-upload"></i>
                  <span>{{ 'HEADER.NOMINATIONS' | translate }}</span>
                </a>
              </li>

              <li *ngIf="permissions.can_see_tso_reports || permissions.can_create_tso_reports">
                <a [routerLink]="[environment.routingPath + '/reporting/tso']" (click)="sideMenuActive = false">
                  <i class="fa fa-book"></i>
                  <span>{{ 'HEADER.TSO_REPORTS' | translate }}</span>
                </a>
              </li>

              <li *ngIf="permissions.can_see_allocations || permissions.can_create_allocations">
                <a [routerLink]="[environment.routingPath + '/allocations']" (click)="sideMenuActive = false">
                  <i class="fas fa-bullseye"></i>
                  <span>{{ 'HEADER.ALLOCATIONS' | translate }}</span>
                </a>
              </li>

              <li *ngIf="permissions.can_see_reporting_and_nofications">
                <a class="has-subnav" (click)="toggleReportingSubNavActive($event)">
                  <i class="fas fa-phone-volume"></i>
                  <span>{{ 'HEADER.REPORTING_AND_NOTIFICATIONS' | translate }}</span>
                  <i [ngClass]="reportingSubNavActive ? 'fas fa-caret-down' : 'fas fa-caret-right'"></i>
                </a>
                <ul class="list-unstyled submenu" [ngClass]="reportingSubNavActive ? 'is-active' : ''">
                  <!--
                  <li>
                    <a href="{{ environment.routingPath + '/scheduling_reports' }}">
                      {{ 'HEADER.SCHEDULING_REPORTS' | translate }} OLD
                    </a>
                  </li>
                  -->
                  <li>
                    <a href="{{ environment.routingPath + '/scheduling_reports_ribbon' }}">
                      {{ 'HEADER.SCHEDULING_REPORTS' | translate }}
                    </a>
                  </li>
                  <li>
                    <a [routerLink]="[environment.routingPath + '/automatic-report-emailings']" (click)="sideMenuActive = false">{{ 'HEADER.EMAILS' | translate }}</a>
                  </li>
                  <!--
                  <li>
                    <a href="{{ environment.routingPath + '/event_notifications' }}">
                      {{ 'HEADER.NOTIFICATIONS' | translate }} (Old)
                    </a>
                  </li>
                  -->
                  <li>
                    <a href="{{ environment.routingPath + '/event_notifications_ribbon' }}">
                      {{ 'HEADER.NOTIFICATIONS' | translate }}
                    </a>
                  </li>
                </ul>
              </li>

              <li *ngIf="permissions.can_see_nofications">
                <a [routerLink]="[environment.routingPath + '/event_notifications_ribbon']" (click)="sideMenuActive = false">
                  <i class="fas fa-phone-volume"></i>
                  <span>{{ 'HEADER.NOTIFICATIONS' | translate }}</span>
                </a>
              </li>

              <li *ngIf="permissions.can_see_nominations || permissions.can_see_allocations || permissions.can_see_rollups || permissions.can_see_bids || permissions.can_see_dlm_charging_sessions">
                <a class="has-subnav" (click)="toggleHistorySubNavActive($event)">
                  <i class="fas fa-history"></i>
                  <span>{{ 'HEADER.HISTORY' | translate }}</span>
                  <i [ngClass]="historySubNavActive ? 'fas fa-caret-down' : 'fas fa-caret-right'"></i>
                </a>
                <ul class="list-unstyled submenu" [ngClass]="historySubNavActive ? 'is-active' : ''">
                  <li *ngIf="permissions.can_see_nominations || permissions.can_see_allocations || permissions.can_see_bids || permissions.can_see_dlm_charging_sessions">
                    <a (click)="toggleSideMenu($event)" [routerLink]="[environment.routingPath + '/history']">{{ 'HEADER.USAGE' | translate }}</a>
                  </li>
                  <li *ngIf="permissions.can_see_rollups">
                    <a (click)="toggleSideMenu($event)" [routerLink]="[environment.routingPath + '/rollups']">{{ 'HEADER.ROLLUPS' | translate }}</a>
                  </li>
                </ul>
              </li>

              <li *ngIf="permissions.can_see_asset_configurations || permissions.can_see_rollups || permissions.can_see_generic_steering_config || permissions.can_see_dispatch_groups || permissions.can_see_bids">
                <a class="has-subnav" (click)="toggleConfigurationSubNavActive($event)">
                  <i class="fas fa-tools"></i>
                  <span>{{ 'HEADER.CONFIGURATION' | translate }}</span>
                  <i [ngClass]="configurationSubNavActive ? 'fas fa-caret-down' : 'fas fa-caret-right'"></i>
                </a>
                <ul class="list-unstyled submenu" [ngClass]="configurationSubNavActive ? 'is-active' : ''">
                  <li *ngIf="permissions.can_see_asset_configurations || permissions.can_see_generic_steering_config">
                    <a
                            (click)="toggleSideMenu($event)"
                            [routerLink]="[environment.routingPath + '/asset_configuration_ribbon']"
                    >{{ 'HEADER.ASSET_LEVEL' | translate }}</a>
                  </li>
                  <li *ngIf="permissions.can_see_subpools || permissions.can_see_dispatch_groups || permissions.can_see_bids">
                    <a
                            (click)="toggleSideMenu($event)"
                            [routerLink]="[environment.routingPath + '/market_configuration_ribbon']"
                    >{{ 'HEADER.MARKET_LEVEL' | translate }}</a>
                  </li>
                  <li *ngIf="permissions.can_see_rollups">
                    <a
                            (click)="toggleSideMenu($event)"
                            [routerLink]="[environment.routingPath + '/rollup_configuration_ribbon']"
                    >{{ 'HEADER.ROLLUPS' | translate }}</a>
                  </li>
                </ul>
              </li>

              <!--
              <li *ngIf="permissions.can_see_configurations">
                <a href="{{ environment.routingPath + '/configurations' }}">
                  <i class="fas fa-tools"></i>
                  <span>{{ 'HEADER.CONFIGURATION' | translate }} (OLD)</span>
                </a>
              </li>
              -->
            </nav>
            <div class="app-header-title">
              <p class="app-name">
                Portfolio Management
              </p>
              <h1 class="page-title" id="pageTitle">{{ pageTitle.innerText }}</h1>
            </div>
          </div>
          <div class="app-header--menu">
            <div id="app-nav">
              <ul class="list-inline list-unstyled list-nav">
                <li #userTrigger>
                  <a class="fa-user-button" (click)="toggleUserSelector($event)">
                    <i class="fas fa-user"></i>
                  </a>
                  <div #userMenu class="submenu-block" [@toggleHeaderSelectorAnimation]="userMenuActive ? 'in' : 'out'">
                    <div class="container">
                      <div class="row">
                        <div class="col-md-9"></div>
                        <div class="col-md-3">
                          <ul class="d-flex flex-column justify-content-end">
                            <li>
                              <select *ngIf="allowTimezoneChange"
                                      class="dropdown-item"
                                      [(ngModel)]="strTimeZone"
                                      (ngModelChange)="changeTimezone(this)">
                                <ng-container *ngFor="let tz of highligtedTimeZones">
                                  <option [value]="tz" [selected]="tz == strTimeZone">{{ tz }}</option>
                                </ng-container>
                              </select>
                            </li>

                            <li><a href="{{environment.routingPath + '/vpp_authentication/new_password'}}">{{ 'USER.CHANGE_PASSWORD' | translate }}</a></li>
                            <li>
                              <form #logout method="post" action="{{environment.routingPath + '/vpp_authentication/logout?_method=DELETE'}}">
                                <input type="hidden" name="authenticity_token" value="{{ csr_token }}"/>
                                <input type="hidden" name="_method" value="DELETE"/>
                                <input type="submit" (click)="logout.submit()" value="{{ 'USER.LOGOUT' | translate }}" class="link-button" name="logout"/>
                              </form>
                            </li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
                <li #languageTrigger>
                  <a class="globe-button" (click)="toggleLanguageSelector($event)" href="javascript:void(0)">
                    <i class="fas fa-globe"></i>
                  </a>
                  <div #languageMenu class="submenu-block" id="languageMenu" [@toggleHeaderSelectorAnimation]="languageMenuActive ? 'in' : 'out'">
                    <div class="container">
                      <div class="row">
                        <div class="col-md-6 d-flex">
                          <h3 class="eon-title">
                            {{ 'USER.LANGUAGE' | translate }}
                          </h3>
                        </div>
                        <div class="col-md-6 d-flex">
                          <form>
                            <div class="form-group">
                              <div class="form-check">
                                <label (click)="changeLanguage($event, 'en-GB')" class="form-check-label" for="languageRadios1">
                                  <input [(ngModel)]="language" value="en-GB" class="form-check-input" id="languageRadios1" name="languageRadios" type="radio"> English
                                </label>
                              </div>
                              <div class="form-check">
                                <label (click)="changeLanguage($event, 'de')" class="form-check-label" for="languageRadios1">
                                  <input [(ngModel)]="language" value="de" class="form-check-input" id="languageRadios1" name="languageRadios" type="radio"> Deutsch
                                </label>
                              </div>
                            </div>
                          </form>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
                <div class="app-logo">
                  <img alt="E.ON Logo" src="{{ environment.routingPath + '/assets/images/eon-logo.svg'}}">
                </div>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</header>
