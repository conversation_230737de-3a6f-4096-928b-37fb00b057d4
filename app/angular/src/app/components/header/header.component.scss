header {
  position: relative;
  z-index: 1234;
  .header-links {
    color: #f21c0a !important;
  }

  .link-button {
    color: #D51607;
    padding: 0 15px;
    vertical-align: middle;
    font-size: 18px;
    font-weight: 600;
    cursor: pointer;
    border: 0;
    background: none;

    &:hover {
      text-decoration: underline;
    };
  }

  .form-group {
    .form-check {
      float: left;
      clear: both;
    }
  }
}

#side-menu {
  a {
    span {
      margin-left: 12px;
    }

    &:hover {
      border-left: white 4px solid;
    }

    i {
      width: 1em;
      text-align: center;
    }

    transition-property: border-left-width;
    transition-duration: 0.1s;
    transition-timing-function: ease-in;
  }

  .has-subnav {
    i:last-of-type {
      float: right;
    }
  }
}