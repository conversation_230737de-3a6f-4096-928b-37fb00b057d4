import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { GlobalService } from "./../../services/global.service";
import { environment } from "../../../environments/environment";
import { ToggleHeaderSelectorAnimation } from './../../animations/toggle-header-selector';
import { HttpClientService } from "./../../services/httpclient.service";
import { TranslateService } from '@ngx-translate/core';
import { angularData } from "./../../../global.export";
import * as moment from 'moment'
import 'moment-timezone'

@Component({
  selector: "vpp-management-header",
  templateUrl: "./header.component.html",
  styleUrls: ["./header.component.scss"],
  encapsulation: ViewEncapsulation.None,
  animations:[ ToggleHeaderSelectorAnimation ],
  host: {
    '(document:click)': 'globalClick($event)',
  }
})
export class HeaderComponent implements OnInit {
  _element;
  environment = environment;
  pageTitle: any = {
    innerText: 'Dashboard'
  };
  sideMenuActive: boolean = false;
  languageMenuActive: boolean = false;
  userMenuActive: boolean = false;
  reportingSubNavActive: boolean = false;
  historySubNavActive: boolean = false;
  configurationSubNavActive: boolean = false;
  language: string = angularData.railsExports.locale;
  csr_token: string = '';
  permissions: any;
  highligtedTimeZones = ['Europe/Amsterdam', 'Europe/Berlin', 'Europe/Bucharest', 'Europe/London', 'Europe/Paris', 'Europe/Stockholm'];
  strTimeZone = angularData.railsExports.timeZone
  allowTimezoneChange = angularData.railsExports.allowTimezoneChange

  @ViewChild('header', { static: true }) header;
  @ViewChild('languageTrigger', { static: true }) languageTrigger;
  @ViewChild('languageMenu', { static: true }) languageMenu;
  @ViewChild('userTrigger', { static: true }) userTrigger;
  @ViewChild('userMenu', { static: true }) userMenu;

  constructor(
    private _Global: GlobalService,
    private _HttpClientService: HttpClientService,
    public translate: TranslateService,
    _element: ElementRef) {

    this._Global.title$.subscribe(
      title => {
        this.pageTitle.innerText = title;
        document.title = title;
      }
    );

    this.permissions = angularData.permissions;

    console.log('got permissions', this.permissions);
  }

  ngOnInit() {
    this._Global.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault = this.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault.bind(this);
    this.csr_token = this._HttpClientService.getAuthToken();
  }

  hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault(event) {
    if (this.sideMenuActive) {
      this.sideMenuActive = false;
    }

    if (this.languageMenuActive) {
      this.languageMenuActive = false;
    }

    if (this.userMenuActive) {
      this.userMenuActive = false;
    }
  }

  globalClick(event) {
    if (this.sideMenuActive && !this.header.nativeElement.contains(event.target)) {
      this.sideMenuActive = false;
    }

    if (this.languageMenuActive
        && !this.languageMenu.nativeElement.contains(event.target)
        && !this.languageTrigger.nativeElement.contains(event.target)) {
      this.languageMenuActive = false;
    }

    if (this.userMenuActive
        && !this.userMenu.nativeElement.contains(event.target)
        && !this.userTrigger.nativeElement.contains(event.target)) {
      this.userMenuActive = false;
    }
  }

  toggleSideMenu(event) {
    this.sideMenuActive = !this.sideMenuActive;
    this.languageMenuActive = false;
    this.userMenuActive = false;
  }

  toggleLanguageSelector(event) {
    this.languageMenuActive = !this.languageMenuActive;
    this.userMenuActive = false;
    this.sideMenuActive = false;
  }

  toggleUserSelector(event) {
    this.userMenuActive = !this.userMenuActive;
    this.languageMenuActive = false;
    this.sideMenuActive = false;
  }

  toggleReportingSubNavActive(event) {
    event.preventDefault();
    event.stopPropagation();
    this.reportingSubNavActive = !this.reportingSubNavActive;
    this.historySubNavActive = false;
    this.configurationSubNavActive = false;
  }

  toggleHistorySubNavActive(event) {
    event.preventDefault();
    event.stopPropagation();
    this.reportingSubNavActive = false;
    this.historySubNavActive = !this.historySubNavActive;
    this.configurationSubNavActive = false;
  }

  toggleConfigurationSubNavActive(event) {
    event.preventDefault();
    event.stopPropagation();
    this.reportingSubNavActive = false;
    this.historySubNavActive = false;
    this.configurationSubNavActive = !this.configurationSubNavActive;
  }

  changeTimezone(value) {
    window.location.href = "/timezone?tz=" + value.strTimeZone
  }

  closeSubNavs(event) {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    this.reportingSubNavActive = false;
    this.historySubNavActive = false;
    this.configurationSubNavActive = false;
  }

  changeLanguage(event, data) {
    if (event) {
      event.preventDefault();
      event.stopPropagation();
    }
    this.language = data;
    console.log('ChangeLanguage', data);
    window.location.href = `/locale/${data}`;
  }
}
