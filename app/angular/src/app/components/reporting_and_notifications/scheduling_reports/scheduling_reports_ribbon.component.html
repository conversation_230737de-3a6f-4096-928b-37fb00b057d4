<section>
    <div class="container-fluid">
        <div id="ngb-tabset">
            <ul role="tablist" class="nav nav-tabs justify-content-start">
                <li class="nav-item">
                    <a href="javascript:void(0)" (click)="selectTab($event, 'download_scheduling')"
                       class="nav-link" [ngClass]="selectedTab == 'download_scheduling' ? 'active' : ''"
                       role="tab" id="download_scheduling" >{{ "CONFIGURATION.SCHEDULING_REPORTS.DOWNLOAD_SCHEDULING" | translate }}</a>
                </li>
                <li class="nav-item">
                    <a href="javascript:void(0)" (click)="selectTab($event, 'send_scheduling')"
                       class="nav-link" [ngClass]="selectedTab == 'send_scheduling' ? 'active' : ''"
                       role="tab" id="send_scheduling" >{{ "CONFIGURATION.SCHEDULING_REPORTS.SEND_SCHEDULING" | translate }}</a>
                </li>
            </ul>

            <div class="tab-content">
                <div [@slideRightContentAnimation]="selectedTab == 'download_scheduling' ? 'in' : 'out'">
                    <vpp-download-scheduling *ngIf="selectedTab == 'download_scheduling'"></vpp-download-scheduling>
                </div>
                <div [@slideRightContentAnimation]="selectedTab == 'send_scheduling' ? 'in' : 'out'">
                    <vpp-send-scheduling *ngIf="selectedTab == 'send_scheduling'"></vpp-send-scheduling>
                </div>
            </div>
        </div>
    </div>
</section>