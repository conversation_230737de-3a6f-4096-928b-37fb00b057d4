<div class="container-fluid">

    <section>
        <h2>{{ "CONFIGURATION.SCHEDULING_REPORTS.DOWNLOAD_EDG" | translate }}</h2>
        <div class="form-row">
            <div class="form-group">
                <label class="required" for="inputEdgTso">{{ "CONFIGURATION.SCHEDULING_REPORTS.TSO" | translate }}</label>
                <select class="form-control" [(ngModel)]="edgTsoId" (ngModelChange)="updateEdg()" id="inputEdgTso">
                    <option value="" selected="" disabled *ngIf="edgTsoId==null"></option>
                    <option *ngFor="let x of tsos" [ngValue]="x.id">{{ x.name }}</option>
                </select>
            </div>

            <div class="form-group">
                <label class="required">{{ "CONFIGURATION.SCHEDULING_REPORTS.DATE" | translate }}</label>
                <input
                        type="text"
                        class="form-control"
                        [owlDateTime]="dtEdg"
                        [selectMode]="'single'"
                        [owlDateTimeTrigger]="dtEdg"
                        [(ngModel)]="edgDate"
                        (dateTimeChange)="onSelectedEdgDateChanged($event)"
                        placeholder=""
                >
                <owl-date-time
                        #dtEdg
                        (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
                        [pickerType]="'calendar'"
                        [pickerMode]="'popup'"
                ></owl-date-time>
            </div>

            <div class="form-group">
                <label class="required" for="inputEdgMarket">{{ "CONFIGURATION.SCHEDULING_REPORTS.MARKET" | translate }}</label>
                <select class="form-control" [(ngModel)]="edgMarketId" (ngModelChange)="updateEdg()" id="inputEdgMarket">
                    <option value="" selected="" disabled *ngIf="edgMarketId==null"></option>
                    <option *ngFor="let x of availableMarkets" [ngValue]="x.id">{{ x.name }}</option>
                </select>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <button
                        class="eon-button bg-eon-red"
                        [disabled]="sending || !edgDownloadEnabled"
                        (click)="downloadEdg()">
                    <span>{{ "CONFIGURATION.SCHEDULING_REPORTS.DOWNLOAD" | translate }}</span>
                </button>
            </div>
        </div>

    </section>

    <section>
        <h2>{{ "CONFIGURATION.SCHEDULING_REPORTS.DOWNLOAD_UGC" | translate }}</h2>
        <div class="form-row">
            <div class="form-group">
                <label class="required" for="inputUgcTso">{{ "CONFIGURATION.SCHEDULING_REPORTS.TSO" | translate }}</label>
                <select class="form-control" [(ngModel)]="ugcTsoId" (ngModelChange)="updateUgc()" id="inputUgcTso">
                    <option value="" selected="" disabled *ngIf="ugcTsoId==null"></option>
                    <option *ngFor="let x of tsos" [ngValue]="x.id">{{ x.name }}</option>
                </select>
            </div>

            <div class="form-group">
                <label class="required">{{ "CONFIGURATION.SCHEDULING_REPORTS.DATE" | translate }}</label>
                <input
                        type="text"
                        class="form-control"
                        [owlDateTime]="dtUgc"
                        [selectMode]="'single'"
                        [owlDateTimeTrigger]="dtUgc"
                        [(ngModel)]="ugcDate"
                        (dateTimeChange)="onSelectedUgcDateChanged($event)"
                        placeholder=""
                >
                <owl-date-time
                    #dtUgc
                    (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
                    [pickerType]="'calendar'"
                    [pickerMode]="'popup'"
                ></owl-date-time>
            </div>

            <div class="form-group">
                <label class="required" for="inputUgcMarket">{{ "CONFIGURATION.SCHEDULING_REPORTS.MARKET" | translate }}</label>
                <select class="form-control" [(ngModel)]="ugcMarketId" (ngModelChange)="updateUgc()" id="inputUgcMarket">
                    <option value="" selected="" disabled *ngIf="ugcMarketId==null"></option>
                    <option *ngFor="let x of availableMarkets" [ngValue]="x.id">{{ x.name }}</option>
                </select>
            </div>

            <div class="form-group">
                <label class="required" for="inputUgcEnergyDirection">{{ "CONFIGURATION.SCHEDULING_REPORTS.DIRECTION" | translate }}</label>
                <select class="form-control" [(ngModel)]="ugcEnergyDirection" (ngModelChange)="updateUgc()" id="inputUgcEnergyDirection">
                    <option value="" selected="" disabled *ngIf="ugcEnergyDirection==null"></option>
                    <option *ngFor="let d of energyDirections" [ngValue]="d">{{ d }}</option>
                </select>
            </div>
        </div>

        <div class="form-row">
            <div class="form-group">
                <button class="eon-button bg-eon-red"
                        [disabled]="sending || !ugcDownloadEnabled"
                        (click)="downloadUgc()">
                    <span>{{ "CONFIGURATION.SCHEDULING_REPORTS.DOWNLOAD" | translate }}</span>
                </button>
            </div>
        </div>
    </section>

    <section>
        <h2>{{ "CONFIGURATION.SCHEDULING_REPORTS.DOWNLOAD_TP" | translate }}</h2>
        <div class="form-row">
            <div class="form-group">
                <label class="required" for="inputTpTso">{{ "CONFIGURATION.SCHEDULING_REPORTS.TSO" | translate }}</label>
                <select class="form-control" [(ngModel)]="tpTsoId" (ngModelChange)="updateTp()" id="inputTpTso">
                    <option value="" selected="" disabled *ngIf="tpTsoId==null"></option>
                    <option *ngFor="let x of tsos" [ngValue]="x.id">{{ x.name }}</option>
                </select>
            </div>

            <div class="form-group">
                <label class="required">{{ "CONFIGURATION.SCHEDULING_REPORTS.DATE" | translate }}</label>
                <input
                        type="text"
                        class="form-control"
                        [owlDateTime]="dtTp"
                        [selectMode]="'single'"
                        [owlDateTimeTrigger]="dtTp"
                        [(ngModel)]="tpDate"
                        (dateTimeChange)="updateTp()"
                        placeholder=""
                >
                <owl-date-time
                    #dtTp
                    (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
                    [pickerType]="'calendar'"
                    [pickerMode]="'popup'"
                ></owl-date-time>
            </div>

            <div class="form-group wide">
                <label class="required">{{ "CONFIGURATION.SCHEDULING_REPORTS.BALANCING_GROUP" | translate }}</label>
                <input
                    type="text"
                    class="form-control"
                    [(ngModel)]="tpBg"
                    (ngModelChange)="updateTp()"
                    placeholder="eg 11XWEMAG-------Q">
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <button class="eon-button bg-eon-red"
                        [disabled]="sending || !tpDownloadEnabled"
                        (click)="downloadTp()">
                    <span>{{ "CONFIGURATION.SCHEDULING_REPORTS.DOWNLOAD" | translate }}</span>
                </button>
            </div>
        </div>
    </section>

</div>