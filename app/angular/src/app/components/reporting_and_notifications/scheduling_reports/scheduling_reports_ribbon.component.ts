import {Component, OnInit, ViewEncapsulation} from "@angular/core";
import {SlideRightContentAnimation} from "../../../animations/slide-right-content";
import {GlobalService} from "../../../services/global.service";
import {ActivatedRoute, Params} from "@angular/router";
import {Location} from "@angular/common";
import {TranslateService} from "@ngx-translate/core";
import {environment} from "../../../../environments/environment";


@Component({
    selector: 'vpp-management-scheduling-reports-ribbon',
    templateUrl: './scheduling_reports_ribbon.component.html',
    styleUrls: ['./scheduling_reports_ribbon.component.scss'],
    encapsulation: ViewEncapsulation.None,
    animations: [ SlideRightContentAnimation ]
})
export class SchedulingReportsRibbonComponent implements OnInit {
    selectedTab: string = 'download_scheduling';

    constructor(
        private _Global: GlobalService,
        private route: ActivatedRoute,
        private _Location: Location,
        public translate: TranslateService
    ) {
        // this.translate.get('PAGE.CAPTIONS.REPORTING_AND_NOTIFICATIONS.EMAILS').subscribe((res: string) => {
        //     this._Global.changeTitle(res);
        // });
        this._Global.changeTitle('Scheduling Reports Ribbon');
    }

    ngOnInit() {
    }

    ngAfterViewInit() {
        this.route.params.forEach((params: Params) => {
            if (params['tab']) {
                this.selectTab(null, params['tab']);
            } else {
                this.selectTab(null, this.selectedTab);
            }
        });
    }

    selectTab(event, tab) {
        if (event) {
            event.preventDefault();
            //event.stopPropagation();
        }
        this.selectedTab = tab;
        this._Location.replaceState(`${environment.routingPath}/scheduling_reports_ribbon/${tab}`);
    }
}