import {Component, OnInit, ViewEncapsulation} from "@angular/core";
import {Moment} from "moment-timezone/moment-timezone";
import * as moment from "moment-timezone";
import {GlobalService} from "../../../services/global.service";
import {SchedulingReportProvider} from "../../../providers/scheduling_report.provider";
import {NotificationService} from "../../../services/notification.service";

@Component({
    selector: 'vpp-send-scheduling',
    templateUrl: './send_scheduling.component.html',
    styleUrls: ['./send_scheduling.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class SendSchedulingComponent implements OnInit {
    sending: boolean = false;
    dateFormat = 'DD/MM/YYYY';

    tpSendEnabled: boolean = false;
    tpDate: Moment = moment().add(-1, 'day');

    bkSendEnabled: boolean = false;
    bkDate: Moment = moment().add(-1, 'day');

    constructor(
        public globalService: GlobalService,
        private _schedulingReportProvider: SchedulingReportProvider,
        private _notificationService: NotificationService
    ) {
        this.globalService.changeTitle('Download Scheduling Report');
    }

    ngOnInit() {
        this.updateTp();
        this.updateBk();
    }

    updateTp() {
        this.tpSendEnabled = this.tpDate != null;
    }

    updateBk() {
        this.bkSendEnabled = this.bkDate != null;
    }

    regenerateTp() {
        if (this.sending) {
            return;
        }
        let params = {
            date: this.tpDate.format(this.dateFormat)
        };
        this.sending = true;
        this._schedulingReportProvider.regenerateSchedulingBalancingGroupsThirdPartyReports(params).subscribe(
            res => {
                this.sending = false;
                if (res.success === true) {
                    this._notificationService.info({text: res.notice});
                } else {
                    this._notificationService.error({text: res.error});
                }
            },
            err => {
                this.sending = false;
                this._notificationService.error({text: 'Failed to regenerate the report'});
            }
        );
    }

    regenerateBk() {
        if (this.sending) {
            return;
        }
        let params = {
            date: this.tpDate.format(this.dateFormat)
        };
        this.sending = true;
        this._schedulingReportProvider.regenerateBgAssetActivationsReports(params).subscribe(
            res => {
                this.sending = false;
                if (res.success === true) {
                    this._notificationService.info({text: res.notice});
                } else {
                    this._notificationService.error({text: res.error});
                }
            },
            err => {
                this.sending = false;
                this._notificationService.error({text: 'Failed to regenerate the report'});
            }
        );
    }
}