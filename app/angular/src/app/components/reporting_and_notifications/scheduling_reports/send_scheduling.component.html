<div class="container-fluid">
    <section>
        <h2>{{ "CONFIGURATION.SCHEDULING_REPORTS.SEND_TP" | translate }}</h2>

        <div class="form-row">
            <div class="form-group">
                <label class="required">{{ "CONFIGURATION.SCHEDULING_REPORTS.DATE" | translate }}</label>
                <input
                        type="text"
                        class="form-control"
                        [owlDateTime]="dtTp"
                        [selectMode]="'single'"
                        [owlDateTimeTrigger]="dtTp"
                        [(ngModel)]="tpDate"
                        (dateTimeChange)="updateTp()"
                        placeholder=""
                >
                <owl-date-time
                    #dtTp
                    (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
                    [pickerType]="'calendar'"
                    [pickerMode]="'popup'"
                ></owl-date-time>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <button class="eon-button bg-eon-red"
                        [disabled]="sending || !tpSendEnabled"
                        (click)="regenerateTp()">
                    <span>{{ "CONFIGURATION.SCHEDULING_REPORTS.SEND" | translate }}</span>
                </button>
            </div>
        </div>
    </section>

    <section>
        <h2>{{ "CONFIGURATION.SCHEDULING_REPORTS.SEND_BK" | translate }}</h2>

        <div class="form-row">
            <div class="form-group">
                <label class="required">Date</label>
                <input
                        type="text"
                        class="form-control"
                        [owlDateTime]="dtBk"
                        [selectMode]="'single'"
                        [owlDateTimeTrigger]="dtBk"
                        [(ngModel)]="bkDate"
                        (dateTimeChange)="updateBk()"
                        placeholder=""
                >
                <owl-date-time
                    #dtBk
                    (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
                    [pickerType]="'calendar'"
                    [pickerMode]="'popup'"
                ></owl-date-time>
            </div>
        </div>
        <div class="form-row">
            <div class="form-group">
                <button class="eon-button bg-eon-red"
                        [disabled]="sending || !bkSendEnabled"
                        (click)="regenerateBk()">
                    <span>{{ "CONFIGURATION.SCHEDULING_REPORTS.SEND" | translate }}</span>
                </button>
            </div>
        </div>
    </section>
</div>