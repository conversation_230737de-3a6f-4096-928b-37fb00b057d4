import {Component, OnInit, ViewChild, ViewEncapsulation} from "@angular/core";
import {SchedulingReportProvider} from "../../../providers/scheduling_report.provider";
import {GlobalService} from "../../../services/global.service";
import * as moment from "moment-timezone";
import {Moment} from "moment-timezone/moment-timezone";
import {HttpClientService} from "../../../services/httpclient.service";
import {NotificationService} from "../../../services/notification.service";

@Component({
    selector: 'vpp-download-scheduling',
    templateUrl: './download_scheduling.component.html',
    styleUrls: ['./download_scheduling.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class DownloadSchedulingComponent implements OnInit {
    tsos: Array<any> = [];
    availableMarkets: Array<any> = [];
    energyDirections = ['positive', 'negative'];
    sending = false;
    dateFormat = 'DD/MM/YYYY';

    edgDownloadEnabled: boolean = false;
    edgTsoId: any = null;
    edgDate: Moment = moment().add(-1, 'day');
    edgMarketId: any = null;

    ugcDownloadEnabled: boolean = false;
    ugcTsoId: any = null;
    ugcDate: Moment = moment().add(-1, 'day');
    ugcMarketId: any = null;
    ugcEnergyDirection: any = this.energyDirections[0];

    tpDownloadEnabled: boolean = false;
    tpTsoId: any = null;
    tpDate: Moment = moment().add(-1, 'day');
    tpBg: any = null;


    constructor(
        public globalService: GlobalService,
        private _schedulingReportProvider: SchedulingReportProvider,
        private _httpClientService: HttpClientService,
        private _notificationService: NotificationService
    ) {
        this.globalService.changeTitle('Download Scheduling Report');
    }

    ngOnInit() {
        this.downloadFormData();
    }

    downloadFormData() {
        this._schedulingReportProvider.getDownloadFormData().subscribe(
            res => {
                this.tsos = res.tsos;
                this.availableMarkets = res.available_markets;
            }, err => {
                console.error('Failed to download form data', err);
            }
        );
    }


    onSelectedEdgDateChanged(event) {
        console.log('selected edg date changed', this.edgDate, event);
        this.updateEdg();
    }

    updateEdg() {
        this.edgDownloadEnabled = this.edgTsoId != null && this.edgTsoId != '' &&
                                    this.edgDate != null &&
                                    this.edgMarketId != null && this.edgMarketId != '';
    }

    downloadTp() {
        if (this.sending) {
            return;
        }

        let params = {
            report_type: 'tp',
            tso_id: this.tpTsoId,
            date: this.tpDate.format(this.dateFormat),
            third_party_balancing_group: this.tpBg
        };
        this.sending = true;
        this._schedulingReportProvider.getTpReport(params).subscribe(
            res => {
                this.sending = false;
                this.processReportResponse(res);
            },
            err => {
                this.sending = false;
                this.processReportError(err);
            }
        );
    }


    downloadUgc() {
        if (this.sending) {
            return;
        }

        let params = {
            report_type: 'ugc',
            tso_id: this.ugcTsoId,
            date: this.ugcDate.format(this.dateFormat),
            market_id: this.ugcMarketId,
            energy_direction: this.ugcEnergyDirection
        };
        this.sending = true;
        this._schedulingReportProvider.getUgcReport(params).subscribe(
            res => {
                this.sending = false;

                this.processReportResponse(res);
            },
            err => {
                this.sending = false;
                this.processReportError(err);
            }
        );
    }

    downloadEdg() {
        if (this.sending) {
            return;
        }

        let params = {
            report_type: 'edg',
            tso_id: this.edgTsoId,
            date: this.edgDate.format(this.dateFormat),
            market_id: this.edgMarketId
        };
        let mockParams = { // Tennet TSO, 14/9/2020, MRL
            tso_id: '2',
            date: '14/9/2020',
            market_id: '1'
        };
        this.sending = true;
        this._schedulingReportProvider.getEdgReport(params).subscribe(
            res => {
                this.sending = false;

                this.processReportResponse(res);
            },
            err => {
                this.sending = false;
                this.processReportError(err);
            }
        );
    }

    processReportResponse(res) {
        console.log('got report response', res);

        if (res.success === true) {
            console.log('opening file');

            //let blob = new Blob([atob(res.content)], {type: res.type});
            //let url  = window.URL.createObjectURL(blob);

            let url = 'data:' + res.type + ';base64,' + res.content;

            //window.location.assign(url);

            const anchor = document.createElement('a');
            anchor.download = res.filename;
            anchor.href = url;
            anchor.click();
        } else {
            console.log('got error response when downloading report');
            if (res.error) {
                this._notificationService.error({text: res.error});
            }
            if (res.notice) {
                this._notificationService.info({text: res.notice});
            }
        }
    }

    processReportError(err) {
        console.log('failed scheduling report', err);
        this._notificationService.error({text: 'Failed to generate the report'});
    }

    onSelectedUgcDateChanged(event) {
        console.log('selected ugc date changed', this.ugcDate, event);
        this.updateUgc();
    }


    updateUgc() {
        this.ugcDownloadEnabled = this.ugcTsoId != null &&
            this.ugcDate != null &&
            this.ugcMarketId != null &&
            this.ugcEnergyDirection != null;
    }

    updateTp() {
        this.tpDownloadEnabled = this.tpTsoId != null &&
            this.tpDate != null &&
            this.tpBg != null && this.tpBg !== '';
    }
}