import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { GlobalService } from "./../../services/global.service";
import { environment } from "../../../environments/environment";
import { TranslateService } from '@ngx-translate/core';
import { angularData } from "./../../../global.export";

@Component({
  selector: "vpp-management-dashboard",
  templateUrl: "./dashboard.component.html",
  styleUrls: ["./dashboard.component.scss"],
  encapsulation: ViewEncapsulation.None,
  animations: []
})
export class DashboardComponent implements OnInit {
  environment = environment;
  permissions: any;

  constructor(
    public _Global: GlobalService,
    public translate: TranslateService) {
    this.permissions = angularData.permissions;
    this._Global.changeClass("dashboard-page");
    this.translate.get('PAGE.CAPTIONS.DASHBOARD').subscribe((res: string) => {
      this._Global.changeTitle(res);
    });
  }

  ngOnInit() {}
}
