@import "./../../styles/colors";

vpp-management-dashboard {
  float: left;
  width: 100%;
  display: block;

  .modules-actions {
    background: $eon-lightgrey;
    padding: 30px;

    .module-panel {
      border-radius: 8px;
      background: #fff;
      padding: 30px 40px;
      //width: 24%;
      flex-grow: 1;
      flex-basis: 0;
      margin-right: 10px;
      box-shadow: 0px 3px 6px 0 rgba(0,0,0,0.1);
      border-left: 8px solid;

      &:first-child {
        border-color: $eon-limeyellow-50;
      }

      &:nth-child(2n) {
        border-color: $eon-bordeaux-75;
      }

      &:nth-child(3n) {
        border-color: $eon-red-75;
      }

      &:nth-child(4n) {
        border-color: $eon-turquoise-50;
      }

      .module-description{
        margin-top: 20px;
        color: $eon-middlegrey;
        margin-bottom: 0;
      }

      .eon-button{
        text-align: center;
        width: 84%;
      }
    }

    .center-aligned {
      justify-content: center !important;

      .module-panel {
        margin: 0 10px !important
      }
    }
  }

  a.eon-button:hover {
    color: #FFF;
    text-decoration: none;
  }
}
.ngb-tp {
  .btn.btn-link{
    display: none;
  }
}

@media only screen and (max-width: 1450px) {
  vpp-management-dashboard .modules-actions .module-panel {
    margin-bottom: 2%;
  }
}
