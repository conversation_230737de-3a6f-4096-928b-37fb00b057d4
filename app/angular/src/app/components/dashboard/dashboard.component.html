<div class="modules-actions">
    <div class="container-fluid">
        <div class="d-flex justify-content-between flex-wrap" [ngClass]="{ 'center-aligned': (!permissions.can_see_bids || !permissions.can_see_auction_results || !permissions.can_see_allocations || !permissions.can_see_tso_reports) }">
            <div class="module-panel" *ngIf="permissions.can_see_bids">
              <a [routerLink]="[environment.routingPath + '/bidding']" class="eon-button bg-eon-red">
                <span class="ellip">{{ 'DASHBOARD.BUTTONS.BIDDING' | translate }}</span>
              </a>
              <p class="module-description">{{ 'DASHBOARD.BIDDING' | translate }}</p>
            </div>
            <div class="module-panel" *ngIf="permissions.can_create_auction_results">
              <a [routerLink]="[environment.routingPath + '/nominations/upload-market-result']" class="eon-button bg-eon-bordeaux">
                <span class="ellip">{{ 'DASHBOARD.BUTTONS.UPLOAD_MARKET_RESULTS' | translate }}</span>
              </a>
              <p class="module-description">{{ 'DASHBOARD.AUCTION_RESULTS' | translate }}</p>
            </div>
            <div class="module-panel" *ngIf="permissions.can_see_tso_reports">
              <a [routerLink]="[environment.routingPath + '/reporting/tso']" class="eon-button bg-eon-turquoise">
                  <span class="ellip">{{ 'DASHBOARD.BUTTONS.TSO_REPORT' | translate }}</span>
                </a>
                <p class="module-description">{{ 'DASHBOARD.TSO_REPORT' | translate }}</p>
            </div>
            <div class="module-panel" *ngIf="permissions.can_create_allocations">
              <a [routerLink]="[environment.routingPath + '/allocations/upload-allocation-file']" class="eon-button bg-eon-limeyellow">
                <span class="ellip">{{ 'DASHBOARD.BUTTONS.UPLOAD_ALLOCATION_FILE' | translate }}</span>
              </a>
              <p class="module-description">{{ 'DASHBOARD.ALLOCATIONS' | translate }}</p>
            </div>
        </div>
    </div>
</div>
<section class="pd-30" *ngIf="permissions.can_see_auction_results">
  <vpp-management-auction-results></vpp-management-auction-results>
</section>
