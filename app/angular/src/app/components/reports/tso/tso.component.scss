@import "./../../../styles/colors";
vpp-management-reports-tso {
  .alerts-wrapper-static {
    margin-bottom: 15px;
  }

  .input-group-append {
    color: #343a40;
    border: 2px solid #bfbfbf;
    border-left: 0;
    border-radius: 0 4px 4px 0;
  }

  table {
    max-width: 1280px;

    &.table-bg-turquoise {
      th, td {
        vertical-align: middle;
      }
    }

    td {
      &.lh40 {
        line-height: 40px;
      }
    }
  }
  .bg-light-blue{
    background-color: $eon-turquoise-25;
    h2{
      color: $eon-darkgrey;
      font-size: 26px;
      font-weight: bold;
      margin-bottom: 30px;
    }
  }
  .tso-form{
    border-top: 8px solid $eon-red;
    padding: 40px;
    margin: 30px 0;
    .form-inline label{
      justify-content: flex-start;
    }
    select.form-control,vpp-management-file-uploader {
      border-color: $eon-turquoise-dark;
    }
  }
}
