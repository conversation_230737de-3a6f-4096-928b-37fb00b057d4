import {
    Component,
    OnInit,
    ViewEncapsulation,
    ViewChild,
    ElementRef
} from "@angular/core";
import {
    Router,
    ActivatedRoute,
    Params,
    PRIMARY_OUTLET
} from "@angular/router";
import {NgbDate, NgbCalendar} from "@ng-bootstrap/ng-bootstrap";
import * as moment from "moment-timezone";
import {Observable} from "rxjs";
import {debounceTime, distinctUntilChanged, map} from "rxjs/operators";
import {angularData} from "./../../../../global.export";
import {GlobalService} from "./../../../services/global.service";
import {ReportProvider} from "./../../../providers/report.provider";
import {TsoProvider} from "./../../../providers/tso.provider";
import {MarketProvider} from "./../../../providers/market.provider";
import {UrlProvider} from "./../../../providers/url.provider";
import {HttpClientService} from "./../../../services/httpclient.service";
import {NotificationService} from "./../../../services/notification.service";
import {environment} from "../../../../environments/environment";
import {TranslateService} from '@ngx-translate/core';

@Component({
    selector: "vpp-management-reports-tso",
    templateUrl: "./tso.component.html",
    styleUrls: ["./tso.component.scss"],
    encapsulation: ViewEncapsulation.None
})
export class TsoReportsComponent implements OnInit {
    environment = environment;
    params: any = {
        date: ""
    };
    startDate = {
        year: moment()
            .tz(angularData.railsExports.timeZone)
            .year(),
        month: moment().month() + 1,
        day: moment().date()
    };
    selectedDate: any;
    tsos: any[] = [];
    markets: any[];
    reportsList: any = [];
    page: any = 1;
    pageSize = 10;
    fileToUpload;
    fileUploaderReference;
    uploaderOptions = {
        url: ''
    };
    csr_token: string = "";
    disableButton: boolean = true;
    stringDate = "";
    notifications = [];
    permissions = angularData.permissions;
    sending: boolean = false;

    @ViewChild("fileUploader", {static: false}) fileUploaderElement: any;

    constructor(
        public _Global: GlobalService,
        private _Report: ReportProvider,
        private _Tso: TsoProvider,
        private _UrlProvider: UrlProvider,
        private _Market: MarketProvider,
        private _NotificationService: NotificationService,
        private _HttpClientService: HttpClientService,
        public translate: TranslateService
    ) {
        this.translate.get('PAGE.CAPTIONS.REPORTING.TSO').subscribe((res: string) => {
            this._Global.changeTitle(res);
        });
        this.uploaderOptions.url = this._UrlProvider.getUploadImportedReportUrl();
        this.csr_token = this._HttpClientService.getAuthToken();
        this.permissions = angularData.permissions;
    }

    ngOnInit() {
        console.log(this.permissions.can_create_tso_reports);
        this.getTsos();
        this.getMarkets();
        this.getImportedReports();
    }

    getImportedReports() {
        this._Report.findBy({date: this.params.date}).subscribe(results => {
            this.reportsList = results.data.imported_reports;
        });
    }

    getTsos() {
        this._Tso.findAllWithPagination({}).subscribe(results => {
            this.tsos = results.tsos;
            if (this.tsos && this.tsos.length > 0) {
                this.params.tsoId = this.tsos[0].id;
            }
        });
    }

    getMarkets() {
        this._Market.findAllWithPagination({}).subscribe(results => {
            this.markets = results.markets.filter((m) => m.name == 'SRL');
            this.params['marketId'] = this.markets[0].id;
        });
    }

    startDateSelectedCallback(date) {
        if (date.value) {
            this.stringDate = this.params.date = moment(date.value.toISOString())
                .format("DD/MM/YYYY");

            this.params.date = moment(date.value.toISOString())
                .format("YYYY-MM-DD");
        } else {
            this.params.date = "";
        }
        this.getImportedReports();
    }

    clearDateValue() {
        this.selectedDate = null;
        this.params.date = "";
        this.getImportedReports();
    }

    onPageChanged(event) {
        console.log('page changed', event);
    }

    onPageSizeChanged(event) {
        console.log('page size changed', event, 'page size is now', this.pageSize);
    }

    fileChangedCallback(file) {
        console.log('File changed', file);
        this.fileToUpload = file;
        this.disableButton = false;
    }

    uploaderChangedCallback(uploader) {
        this.fileUploaderReference = uploader;
    }

    clearUploadQueue() {
        this.fileUploaderElement.clearFileUploaderQueue();
        this.fileToUpload = null;
        this.disableButton = true;
    }

    uploadFile(event) {
        this.notifications = [];
        event.preventDefault();
        event.stopPropagation();
        this.fileToUpload.url = this._UrlProvider.getUploadImportedReportUrl();

        this.fileUploaderReference.onBuildItemForm = (
            fileItem: any,
            form: any
        ) => {
            form.append("authenticity_token", this.csr_token);
            form.append("market_id", this.params.marketId);
            form.append("tso_id", this.params.tsoId);
        };
        this.fileToUpload.upload();

        this.sending = true;
        this.disableButton = true;
        this.fileUploaderReference.response.subscribe(
            (data) => {
                this.sending = false;
                data = JSON.parse(data.toString());
                if (data.success) {
                    this.notifications.push({text: 'SUCCESS', type: 'success'});
                    this.getImportedReports();
                    this.clearUploadQueue();
                } else {
                    if (data.error) {
                        for (let i in data.error) {
                            let key = `REPORTS.TSO_REPORT.ERRORS.${i.toUpperCase().replace(/ /g, '_')}`;
                            let e = data.error[i];
                            for (let j in e) {
                                let err = e[j];
                                this.notifications.push({
                                    text: `${this.translate.instant(key)}: ${err}`,
                                    type: 'error'
                                });
                            }
                        }
                    }
                    if (data.messages) {
                        for (let m of data.messages) {
                            this.notifications.push({
                                text: m,
                                type: 'error'
                            });
                        }
                    }
                }
            }, (err) => {
                this.sending = false;
                console.log('Failed to upload file', err);
            });
    }
}
