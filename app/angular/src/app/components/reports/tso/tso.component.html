<div class="container-fluid">
  <section class="bg-light-blue tso-form" *ngIf="permissions.can_create_tso_reports">
  <h2>{{ 'REPORTS.TSO_REPORT.TITLE' | translate }}</h2>
  <div class="alerts-wrapper-static" *ngIf="notifications.length">
    <ng-container *ngFor="let message of notifications; let ix = index;">
      <ngb-alert
        [dismissible]="false"
        [type]="message.type"> {{ message.text | translate }}
      </ngb-alert>
    </ng-container>
  </div>
  <form>
  <div class="form-row">
    <div class="col col-md-3">
      <label class="required">{{ 'REPORTS.TSO_REPORT.TSO' | translate }}</label>
      <select
        name="tso"
        [(ngModel)]="params.tsoId"
        class="form-control">
         <option *ngFor="let t of tsos" [ngValue]="t.id"> {{ t.name | humanize: {titleize: true} }} </option>
      </select>
    </div>

    <div class="col col-md-3">
      <label class="required">{{ 'REPORTS.TSO_REPORT.MARKET' | translate }}</label>
      <select
        name="market"
        [(ngModel)]="params.marketId"
         class="form-control">
         <option *ngFor="let m of markets" [ngValue]="m.id"> {{ m.name | humanize: {titleize: true} }} </option>
      </select>
    </div>

    <div class="col col-md-3">
      <label class="required">{{ 'REPORTS.TSO_REPORT.FILE' | translate }}</label>
      <vpp-management-file-uploader
        [options]="uploaderOptions"
        (uploader)="uploaderChangedCallback($event)"
        (propagateFile)="fileChangedCallback($event)"
        #fileUploader>>
      </vpp-management-file-uploader>
    </div>

    <div class="col">
      <label>&nbsp;</label>
      <button
        [disabled]="!fileToUpload || !permissions.can_create_tso_reports || sending"
        (click)="uploadFile($event)"
        class="eon-button bg-eon-bordeaux">
        <span>{{ 'REPORTS.TSO_REPORT.UPLOAD_TSO_REPORT' | translate }}</span>
      </button>
      <vpp-management-loader [showLoader]="sending"></vpp-management-loader>
    </div>

    </div>
  </form>
  </section>


  <div class="container-fluid">
    <div class="row">
        <h2>{{ 'REPORTS.TSO_REPORT.TABLE.TITLE' | translate }}</h2>
    </div>
  </div>
  <div class="table-filter d-flex">
    <div class="form-group">
        <label>{{ 'REPORTS.TSO_REPORT.TABLE.FILTER.DATE' | translate }}</label>
        <div class="input-group">
          <input
            type="text"
            class="form-control"
            [owlDateTime]="dt1"
            [selectMode]="'single'"
            [(ngModel)]="selectedDate"
            [owlDateTimeTrigger]="dt1"
            (dateTimeChange)="startDateSelectedCallback($event)"
            placeholder="">
          <div class="input-group-append">
            <button class="btn btn-default" (click)="clearDateValue()">
              <i class="fa fa-times"></i>
            </button>
          </div>
          <owl-date-time
            #dt1
            (afterPickerOpen)="_Global.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
            [pickerType]="'calendar'"
            [pickerMode]="'popup'"></owl-date-time>
        </div>
    </div>
  </div>

  <table class="table table-auction-results table-striped table-bg-turquoise">
  <tr>
    <th>{{ 'REPORTS.TSO_REPORT.TABLE.REPORT_DATE' | translate }}</th>
    <th>{{ 'REPORTS.TSO_REPORT.TABLE.TSO' | translate }}</th>
    <th>{{ 'REPORTS.TSO_REPORT.TABLE.MARKET' | translate }}</th>
    <th>{{ 'REPORTS.TSO_REPORT.TABLE.CREATED' | translate }}</th>
    <th>&nbsp;</th>
    <th>&nbsp;</th>
    </tr>
    <tbody>
      <tr *ngFor="let row of reportsList | slice: (page-1) * pageSize : page * pageSize ">
        <td>{{ row.report_date | date:'dd/MM/yyyy' }}</td>
        <td>{{ row.report_tso }}</td>
        <td>{{ row.report_market }}</td>
        <td class="lh40">
          <div *ngFor="let rep of row.reports">
           {{ rep.created | localDate:'DD MMM YYYY HH:mm' }} {{ rep.userAccount ? '/ ' + rep.userAccount.name : ''}}
          </div>
        </td>
        <td class="lh40">
          <ng-container *ngIf="row.report_date">
            <div *ngFor="let rep of row.reports">
              <a href="{{environment.apiPath}}/api/v1/reports/tso/download/{{rep.id}}" target="_blank" class="eon-button small bg-eon-red"><i class="fas fa-file-download"></i></a>
            </div>
          </ng-container>
        </td>
        <td class="text-right">
          <ng-container *ngIf="row.report_date">
            <a target="_blank" href="{{environment.apiPath}}/api/v1/reports/tso/control/download/{{row.report_date}}/{{row.report_tso_id}}/{{row.report_market_id}}" class="eon-button small bg-eon-red">{{ 'REPORTS.TSO_REPORT.TABLE.CONTROL_REPORT' | translate }}</a>
          </ng-container>
          &nbsp;
        </td>
      </tr>
    </tbody>
</table>

  <div class="d-flex justify-content-between">
    <ngb-pagination
            [collectionSize]="reportsList.length"
            [(page)]="page"
            [pageSize]="pageSize"
            (pageChange)="onPageChanged($event)"
    >
    </ngb-pagination>

    <select class="custom-select" style="width: auto" [(ngModel)]="pageSize" (ngModelChange)="onPageSizeChanged($event)">
      <option [ngValue]="10">10 {{ 'AUCTIONS.RESULTS.TABLE.PER_PAGE' | translate }}</option>
      <option [ngValue]="20">20 {{ 'AUCTIONS.RESULTS.TABLE.PER_PAGE' | translate }}</option>
      <option [ngValue]="50">50 {{ 'AUCTIONS.RESULTS.TABLE.PER_PAGE' | translate }}</option>
    </select>
  </div>

</div>
