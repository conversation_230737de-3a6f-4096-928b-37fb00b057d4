import {Component, OnInit, ViewEncapsulation} from "@angular/core";
import {GlobalService} from "../../../../services/global.service";
import {HistoryProvider} from "../../../../providers/history.provider";
import {TranslateService} from "@ngx-translate/core";
import { environment } from "../../../../../environments/environment";

@Component({
    selector: "vpp-management-bids-history",
    templateUrl: "./bids_history.component.html",
    styleUrls: ["./bids_history.component.scss"],
    encapsulation: ViewEncapsulation.None
})
export class BidsHistoryComponent implements OnInit {
    environment = environment;
    page = 1;
    pageSize = 25;
    collectionSize = 0;
    bidSources = [];
    params = {
        page: this.page,
        per_page: this.pageSize
    };
    showLoader: boolean = false;

    constructor(
        private _Global: GlobalService,
        private _History: HistoryProvider,
        public translate: TranslateService
    ) {
        this.translate.get('PAGE.CAPTIONS.HISTORY.BIDS').subscribe((res: string) => {
            this._Global.changeTitle(res);
        });
    }

    ngOnInit(): void {
        this.getHistoryData();
    }

    getHistoryData() {
        this.showLoader = true;
        this.bidSources = [];
        this._History.getBids(this.params).subscribe(
            results => {
                this.showLoader = false;
                this.bidSources = results.data.bid_sources;
                this.collectionSize = results.total_entries;
            },
            error => {
                this.showLoader = false;
            }
        );
    }

    pageChangedCallback(page) {
        this.params.page = page;
        this.getHistoryData();
    }

    pageSizeChangedCallback(pageSize) {
        this.params.per_page = pageSize;
        this.params.page = 1;
        this.getHistoryData();
    }
}