import {environment} from "environments/environment";
import {Component, Input, ViewEncapsulation} from "@angular/core";
import {GlobalService} from "../../../../services/global.service";
import {NominationProvider} from "../../../../providers/nomination.provider";
import {ActivatedRoute, Params} from "@angular/router";
import {TranslateService} from "@ngx-translate/core";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {angularData} from "../../../../../global.export";
import {HistoryProvider} from "../../../../providers/history.provider";

@Component({
    selector: 'vpp-management-bid-detail',
    styleUrls: ['./bid_detail.component.scss'],
    templateUrl: './bid_detail.component.html',
    encapsulation: ViewEncapsulation.None,
    animations: []
})
export class BidDetailComponent {
    environment = environment;
    bidSource: any = {
        validation_result: {
            errors: [],
            warnings: [],
            validationSuccess: null
        },
        content: {
            fileName: null
        },
        content_type: null,
        user: {
            name: null,
            email: null
        },
        nomination_bids: []
    };
    permissions: any;

    @Input('id') bidSourceId;

    constructor(
        private _Global: GlobalService,
        private _Nomination: NominationProvider,
        private _Route: ActivatedRoute,
        public translate: TranslateService,
        private _ModalService: NgbModal,
        private _History: HistoryProvider,
    ) {
        this.permissions = angularData.permissions;
        this.translate.get('PAGE.CAPTIONS.HISTORY.BID_DETAIL').subscribe((res: string) => {
            this._Global.changeTitle(res);
        });
    }

    ngOnInit() {
        if (this.bidSourceId) {
            this.getBidSource();
        }
    }

    ngAfterViewInit() {
        this._Route.params.forEach((params: Params) => {
            if (params["id"]) {
                this.bidSourceId = params["id"];
                this.getBidSource();
            }
        });
    }

    getBidSource() {
        this._History.getBidSource(this.bidSourceId).subscribe(
            (data) => {
                if (data && data.bid_source) {
                    this.bidSource = data.bid_source;
                } else {
                    console.error('Missing bid source from response data', data);
                }
            },
            (err) => {
                console.error('Failed to get bid source', this.bidSourceId, err);
            }
        );
    }
}