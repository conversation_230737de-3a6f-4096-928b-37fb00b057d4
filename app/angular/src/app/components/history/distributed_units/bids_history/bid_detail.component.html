<div class="container-fluid">
    <section>
        <div class="row">
            <div class="col-md-2">
                <a class="back-button" [routerLink]="[environment.routingPath + '/history/bids']">
                    <i class="fa fa-chevron-left"></i> {{ 'BACK' | translate }}</a>
                <br>
            </div>
        </div>

        <div class="row">
            <div class="alerts-wrapper-static col-md-8 col-lg-6" *ngIf="bidSource.id">
                <ngb-alert
                        *ngIf="bidSource.validation_result.validationSuccess"
                        [dismissible]="true"
                        [type]="'success'"> {{ 'SUCCESS' | translate }}
                </ngb-alert>
                <ngb-alert
                        *ngIf="!bidSource.validation_result.validationSuccess"
                        [dismissible]="false"
                        [type]="'error'"> {{ 'FAILURE' | translate }}
                </ngb-alert>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8 col-lg-6">
                <div class="card card-allocated">
                    <div class="card-body">
                        <div class="stats-group">
                            <label>{{ 'HISTORY.BIDS.DETAIL.VALIDATION' | translate }}</label>
                            <span class="value">
                                {{ 'HISTORY.BIDS.DETAIL.ERRORS' | translate }}
                                : {{ bidSource.validation_result && bidSource.validation_result.errors ? bidSource.validation_result.errors.length : 0}}
                                <br>
                                {{ 'HISTORY.BIDS.DETAIL.WARNINGS' | translate }}
                                : {{ bidSource.validation_result && bidSource.validation_result.warnings ? bidSource.validation_result.warnings.length : 0}}
                            </span>
                        </div>
                        <div class="stats-group">
                            <label>{{ 'HISTORY.BIDS.DETAIL.TYPE' | translate }}</label>
                            <span class="value">
                                {{ ('HISTORY.BIDS.DETAIL.CONTENT_TYPE.' + (bidSource.content_type | bidSourceType)) | translate }}
                            </span>
                        </div>
                        <div class="stats-group">
                            <label>{{ 'HISTORY.BIDS.DETAIL.PERFORMED_ON' | translate }}</label>
                            <span class="value">{{ bidSource.created | localDate:'ddd, DD MMM YYYY HH:mm' }}</span>
                        </div>
                        <div class="stats-group">
                            <label>{{ 'HISTORY.BIDS.DETAIL.USER' | translate }}</label>
                            <span class="value">{{ bidSource.user.name }} {{ '<' + bidSource.user.email + '>' }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <table class="table table-bg-turquoise table-striped"
           *ngIf="bidSource && bidSource.validation_result && bidSource.validation_result.errors && bidSource.validation_result.errors.length">
        <thead>
            <tr>
                <th colspan="5">{{ 'HISTORY.BIDS.DETAIL.TABLE.ERROR_DESCRIPTION' | translate }}
                    ({{ bidSource.validation_result.errors.length }})
                </th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let w of bidSource.validation_result.errors">
                <td title="error name">{{ w.name }}</td>
                <td>
                    <table style="width: 100%">
                        <tr *ngFor="let e of w.entries">
                            <ng-template [ngIf]="e[0]">
                                <td title="date">{{ e[0].date }}</td>
                                <td title="marketName">{{ e[0].marketName }}</td>
                                <td title="productName">{{ e[0].productName }}</td>
                                <td title="tsoIdentifier">{{ e[0].tsoIdentifier }}</td>
                                <td title="capacityPrice">{{ e[0].capacityPrice }}</td>
                                <td title="energyPrice">{{ e[0].energyPrice }}</td>
                                <td title="paymentDirection">{{ e[0].paymentDirection }}</td>
                                <td title="flexVolumeMegawatt">{{ e[0].flexVolumeMegawatt }}</td>
                                <td title="assetId">{{ e[0].assetId }}</td>
                                <td></td>
                            </ng-template>
                        </tr>
                    </table>
                    {{ 'HISTORY.BIDS.DETAIL.TABLE.DETAILS' | translate }}: {{ w.details }}
                </td>
            </tr>
        </tbody>
    </table>

    <table class="table table-bg-turquoise table-striped"
           *ngIf="bidSource && bidSource.validation_result && bidSource.validation_result.warnings && bidSource.validation_result.warnings.length">
        <thead>
            <tr>
                <th colspan="5">{{ 'HISTORY.BIDS.DETAIL.TABLE.WARNING_DESCRIPTION' | translate }}
                    ({{ bidSource.validation_result.warnings.length }})
                </th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let w of bidSource.validation_result.warnings">
                <td title="error name">{{ w.name }}</td>
                <td>
                    <table style="width: 100%">
                        <tr *ngFor="let e of w.entries">
                            <ng-template [ngIf]="e[0]">
                                <td title="date">{{ e[0].date }}</td>
                                <td title="marketName">{{ e[0].marketName }}</td>
                                <td title="productName">{{ e[0].productName }}</td>
                                <td title="tsoIdentifier">{{ e[0].tsoIdentifier }}</td>
                                <td title="capacityPrice">{{ e[0].capacityPrice }}</td>
                                <td title="energyPrice">{{ e[0].energyPrice }}</td>
                                <td title="paymentDirection">{{ e[0].paymentDirection }}</td>
                                <td title="flexVolumeMegawatt">{{ e[0].flexVolumeMegawatt }}</td>
                                <td title="assetId">{{ e[0].assetId }}</td>
                                <td></td>
                            </ng-template>
                        </tr>
                    </table>
                    {{ 'HISTORY.BIDS.DETAIL.TABLE.DETAILS' | translate }}: {{ w.details }}
                </td>
            </tr>
        </tbody>
    </table>

    <table class="table table-bg-turquoise table-striped"
           *ngIf="bidSource.nomination_bids && bidSource.nomination_bids.length">
        <thead>
            <tr>
                <th colspan="5">Bids ({{ bidSource.nomination_bids.length }})</th>
            </tr>
            <tr>
                <th class="text-center">{{ 'BIDDING.LIST.TABLE.MARKET' | translate }}</th>
                <th class="text-center">{{ 'BIDDING.LIST.TABLE.PRODUCT' | translate }}</th>
                <th class="text-center">{{ 'BIDDING.LIST.TABLE.TSO' | translate }}</th>
                <th class="text-center">{{ 'BIDDING.LIST.TABLE.CAPACITY_PRICE' | translate }}</th>
                <th class="text-center">{{ 'BIDDING.LIST.TABLE.ENERGY_PRICE' | translate }}</th>
                <th class="text-center">{{ 'BIDDING.LIST.TABLE.CAPACITY' | translate }}</th>
                <th class="text-center">{{ 'BIDDING.LIST.TABLE.ASSET_ID' | translate }}</th>
                <th class="text-center">{{ 'BIDDING.LIST.TABLE.ASSET_NAME' | translate }}</th>
                <th class="text-center">{{ 'BIDDING.LIST.TABLE.ASSET_EXTERNAL_ID' | translate }}</th>
                <th></th>
            </tr>
        </thead>
        <tbody>
            <tr *ngFor="let b of bidSource.nomination_bids">
                <td class="text-center">{{ b.market_name }}</td>
                <td class="text-center">{{ b.product_with_minutes }}</td>
                <td class="text-center">{{ b.tso_name }}</td>
                <td class="text-center">{{ b.capacity_price }}</td>
                <td class="text-center">{{ b.energy_price }}</td>
                <td class="text-center">{{ b.flex_volume | megawatt }}</td>
                <td class="text-center">{{ b.asset_id }}</td>
                <td class="text-center">{{ b.asset_name }}</td>
                <td class="text-center">{{ b.asset_external_id }}</td>
                <td class="text-center actions-column">
                    <span *ngIf="b.deleted">{{ 'NOMINATIONS.LIST.TABLE.DELETED' | translate }}</span>
                </td>
            </tr>
        </tbody>
    </table>

</div>