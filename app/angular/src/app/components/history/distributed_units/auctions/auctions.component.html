<table class="table table-auction-results table-striped table-bg-turquoise">
    <tr>
        <th class="text-center vertical-center">{{ 'HISTORY.AUCTIONS.TABLE.PERFORMED_ON' | translate }}</th>
        <th class="text-center vertical-center">{{ 'HISTORY.AUCTIONS.TABLE.USER' | translate }}</th>
        <th class="text-center vertical-center">{{ 'HISTORY.AUCTIONS.TABLE.DELIVERY_DATE' | translate }}</th>
        <th class="text-center vertical-center"></th>
    </tr>
    <tr *ngIf="showLoader">
      <td colspan="4" class="text-center vertical-center"><vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
      <td>
    </tr>
    <ng-container *ngFor="let a of auctionSources">
      <tr>
          <td class="text-center vertical-center">{{ a.created | localDate:'DD MMM, YYYY HH:mm:ss' }}</td>
          <td class="text-center vertical-center">
            <ng-container *ngIf="a.user_account">
              {{ a.user_account.name }} <br> {{ a.user_account.email }}
            </ng-container>
          </td>
          <td class="text-center vertical-center">{{ a.delivery_date | localDate:'DD MMM' }}</td>
          <td class="vertical-center">
            <a class="pdf-link"
              target="_blank"
              *ngIf="a.meta_data_link"
              href="{{environment.routingPath}}/api/v1/auction/{{ a.id }}/meta_data">
              <i class="fa fa-file-excel"></i>{{ 'HISTORY.AUCTIONS.TABLE.LINK.META_DATA' | translate }}
            </a>
            <br>
            <a class="pdf-link"
              target="_blank"
              *ngIf="a.source_link"
              href="{{environment.routingPath}}/api/v1/auction/{{ a.id }}/download">
                <i class="fa fa-file-excel"></i>{{ 'HISTORY.AUCTIONS.TABLE.LINK.AUCTION' | translate }}
            </a>
          </td>
      </tr>
    </ng-container>
</table>
<div class="d-flex justify-content-between">
  <ngb-pagination
    [collectionSize]="collectionSize"
    [(page)]="page"
    [ellipses]="true"
    [maxSize]="5"
    (pageChange)="pageChangedCallback($event)"
    [pageSize]="pageSize">
  </ngb-pagination>

  <select class="custom-select" style="width: auto"
    [(ngModel)]="pageSize"
    (ngModelChange)="pageSizeChangedCallbac($event)">
    <option [ngValue]="25">25 {{ 'HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.PER_PAGE' | translate }}</option>
    <option [ngValue]="50">50 {{ 'HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.PER_PAGE' | translate }}</option>
    <option [ngValue]="75">75 {{ 'HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.PER_PAGE' | translate }}</option>
  </select>
</div>
