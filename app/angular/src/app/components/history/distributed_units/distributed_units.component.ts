import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { Location } from "@angular/common";
import { GlobalService } from "./../../../services/global.service";
import { SlideRightContentAnimation } from './../../../animations/slide-right-content';
import { environment } from "../../../../environments/environment";
import { angularData } from "./../../../../global.export";
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: "vpp-management-distributed-units",
  templateUrl: "./distributed_units.component.html",
  styleUrls: ["./distributed_units.component.scss"],
  encapsulation: ViewEncapsulation.None,
  animations: [ SlideRightContentAnimation ],
})

export class HistoryDistributedUnitComponent implements OnInit {
  @ViewChild("tabs", { static: false }) tabs;
  selectedTab: string = 'nominations';
  permissions: any;

  constructor(
    private _Global: GlobalService,
    private route: ActivatedRoute,
    private _Location: Location,
    public translate: TranslateService
  ) {
    this.translate.get('HISTORY.TITLE').subscribe((res: string) => {
        this._Global.changeTitle(res);
    });
    this.permissions = angularData.permissions;
    if (this.permissions.can_see_nominations) {
        this.selectedTab = 'nominations';
    } else if (this.permissions.can_see_allocations) {
        this.selectedTab = 'manual-allocations';
    } else if (this.permissions.can_see_bids) {
        this.selectedTab = 'auctions';
    } else if (this.permissions.can_see_reporting_and_nofications) {
        this.selectedTab = 'reporting-emails';
    } else if (this.permissions.can_see_dlm_charging_sessions) {
        this.selectedTab = 'dlm-charging-sessions';
    }
  }

  ngOnInit() {}

  ngAfterViewInit() {
    this.route.params.forEach((params: Params) => {
      if (params["tab"]) {
        this.selectTab(null, params["tab"]);
      } else {
        this.selectTab(null, this.selectedTab);
      }
    });
  }

  selectTab(event, tab) {
    if (event) {
      event.preventDefault();
      //event.stopPropagation();
    }
    this.selectedTab = tab;
    this._Location.replaceState(`${environment.routingPath}/history/${tab}`);
  }
}
