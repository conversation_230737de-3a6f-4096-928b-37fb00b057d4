<div class="table-filter d-flex">
  <div class="form-group col-md-3">
      <label>{{ 'HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.START' | translate }}</label>
      <div class="input-group">
          <input
              type="text"
              class="form-control"
              [owlDateTime]="dt1"
              [selectMode]="'range'"
              [(ngModel)]="selectedDateModel"
              [owlDateTimeTrigger]="dt1"
              (dateTimeChange)="startDateSelectedCallback($event)"
              placeholder="">
          <div class="input-group-append">
              <button class="btn btn-default" (click)="clearDateValue()">
                  <i class="fa fa-times"></i>
              </button>
          </div>
          <owl-date-time
              #dt1
              (afterPickerOpen)="_Global.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
              [pickerMode]="'popup'">
          </owl-date-time>
      </div>
  </div>
  <div class="form-group">
      <label>{{ 'HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.TYPE' | translate }}</label>
      <select class="form-control"
        [(ngModel)]="search.type"
        (ngModelChange)="typeChangedCallback($event)">
          <option [ngValue]="t" *ngFor="let t of types">
              {{ "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.TYPE." + t | uppercase | translate }}
          </option>
      </select>
  </div>
  <div class="form-group">
      <label>{{ 'HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.STATUS' | translate }}</label>
      <select class="form-control"
        [(ngModel)]="search.status"
        (ngModelChange)="statusChangedCallback($event)">
          <option [ngValue]="s" *ngFor="let s of statuses">
              {{ "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.STATUS." + s | uppercase | translate }}
          </option>
      </select>
  </div>
  <div class="form-group col-md-2">
      <label>{{ 'HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.DISPATCH_GROUP' | translate }}</label>
      <input
        id="typeahead-basic"
        type="text"
        class="form-control"
        [(ngModel)]="search.dispatchGroup"
        #dgSearch="ngbTypeahead"
        [ngbTypeahead]="dispatchGroupSearch"
        (focus)="focusD$.next($event.target.value)"
        (click)="clickD$.next($event.target.value)"
        (blur)="dispatchGroupBlurCallback($event.target.value)"
        [resultTemplate]="rtd"
        [inputFormatter]="dispatchGroupformatter"/>
      <ng-template #rtd let-r="result" let-t="term">
        <ngb-highlight [result]="'#' + r.id + ' ' + r.name" [term]="t"></ngb-highlight>
      </ng-template>
  </div>
</div>
<table class="table table-auction-results table-striped table-bg-turquoise">
    <tr>
        <th class="text-center">{{ 'HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.START' | translate }}</th>
        <th class="text-center">{{ 'HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.END' | translate }}</th>
        <th class="text-center">{{ 'HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.FALLBACK_START' | translate }}</th>
        <th class="text-center">{{ 'HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.STATUS' | translate }}</th>
        <th class="text-center">{{ 'HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.DISPATCH_GROUP' | translate }}</th>
        <th class="text-center">{{ 'HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.TSO' | translate }}</th>
        <th class="text-center">{{ 'HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.IMPORT_RESULT' | translate }}</th>
        <th class="text-center"></th>
    </tr>
    <tr *ngIf="showLoader">
      <td colspan="7" class="text-center vertical-center"><vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
      <td>
    </tr>
    <ng-container *ngFor="let a of automaticAllocations">
      <tr >
          <input type="hidden" value="{{ a._id.$oid }}">
          <td class="text-center vertical-center">{{ a.startedAt | localDate:'DD MMM, YYYY HH:mm:ss' }}</td>
          <td class="text-center vertical-center">{{ a.endTime | localDate:'HH:mm:ss' }}</td>
          <td class="text-center vertical-center">
            <span *ngIf="a.fallbackStart">{{ a.fallbackStart | localDate:'HH:mm:ss' }}</span>
            <span *ngIf="!a.fallbackStart">-</span>
          </td>
          <td class="text-center vertical-center">
<!--              {{ a.status.type | humanize: {capitalize: true} }}-->
              {{ "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.STATUS." + a.status.type | uppercase | translate }}
          </td>
          <td class="text-center vertical-center">
            <ng-container *ngIf="dispatchGroupsByIds">
              <ng-container *ngFor="let dId of a.dgIds">
                <span *ngIf="dispatchGroupsByIds[dId]">
                  {{ dispatchGroupsByIds[dId].name}}<br>
                </span>
              </ng-container>
            </ng-container>
          </td>
          <td class="text-center vertical-center">
              <input type="hidden" value="{{ a.tsoId?.value }}">
              <span>{{ tsosByIds[a.tsoId?.value]?.name }}</span>
          </td>
          <td class="text-center vertical-center">
              <ng-container *ngIf="a.source">
                  <span>{{ 'HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.IMPORT_RESULT_SUCCESS' | translate }}&nbsp;
                      <label
                              class="eon-checkbox-label non-clickable bg-eon-red"
                              [ngClass]="a.source.validation_result?.validationSuccess ? 'checked' : ''"
                      ></label></span>
                  <br>
                  <span
                          *ngIf="a.source.validation_result?.errors?.length > 0">
                      {{ 'HISTORY.BIDS.TABLE.ERRORS' | translate }}: {{ a.source.validation_result?.errors?.length }}
                  </span>
                  <br>
                  <span
                          *ngIf="a.source.validation_result?.warnings?.length > 0">
                      {{ 'HISTORY.BIDS.TABLE.WARNINGS' | translate }}: {{ a.source.validation_result?.warnings?.length }}
                  </span>
              </ng-container>
          </td>
          <td class="text-center vertical-center">
            <a [routerLink]="[environment.routingPath + '/allocations/automatic-allocation/' + a._id.$oid ]">
              <i class="fa fa-eye"></i></a></td>
      </tr>
    </ng-container>
</table>
<div class="d-flex justify-content-between">
  <ngb-pagination
    [collectionSize]="collectionSize"
    [(page)]="page"
    [ellipses]="true"
    [maxSize]="5"
    (pageChange)="pageChangedCallback($event)"
    [pageSize]="pageSize">
  </ngb-pagination>

  <select class="custom-select" style="width: auto"
    [(ngModel)]="pageSize"
    (ngModelChange)="pageSizeChangedCallback($event)">
    <option [ngValue]="25">25 {{ 'HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.PER_PAGE' | translate }}</option>
    <option [ngValue]="50">50 {{ 'HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.PER_PAGE' | translate }}</option>
    <option [ngValue]="75">75 {{ 'HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.PER_PAGE' | translate }}</option>
  </select>
</div>
