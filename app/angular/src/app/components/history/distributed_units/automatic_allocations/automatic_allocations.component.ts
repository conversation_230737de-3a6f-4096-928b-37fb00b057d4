import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { Observable, Subject, merge } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map } from 'rxjs/operators';
import { HistoryProvider } from "./../../../../providers/history.provider";
import * as moment from "moment-timezone";
import { angularData } from "./../../../../../global.export";
import { TranslateService } from '@ngx-translate/core';
import { environment } from "../../../../../environments/environment";
import { NgbTypeahead } from '@ng-bootstrap/ng-bootstrap';
import { GlobalService } from "./../../../../services/global.service";

@Component({
  selector: "vpp-management-automatic-allocations-history",
  templateUrl: "./automatic_allocations.component.html",
  styleUrls: ["./automatic_allocations.component.scss"],
  encapsulation: ViewEncapsulation.None
})

export class AutomaticAllocationsHistoryComponent implements OnInit {
  environment = environment;
  page = 1;
  pageSize = 25;
  collectionSize = 0;
  params = {
    page: this.page,
    start_date: "",
    end_date: "",
    type: "",
    status: "",
    per_page: this.pageSize,
    dispatch_group_id: ''
  };
  search = {
    startDate: "",
    endDate: "",
    type: "",
    status: "",
    dispatchGroup: null
  };
  history = [];
  types = ["Choose", "Fallback", "Standard"];

  statuses = [
    "Choose",
    "Started",
    "Success",
    "Fallback",
    "DataPreparationFailure",
    "Failure",
    "AllocationNotAccepted"
  ];

  automaticAllocations: any[] = [];
  allocationSources: any[] = [];
  dispatchGroups = [];
  dispatchGroupsByIds = {};
  tsos = [];
  tsosByIds = {};
  showLoader: boolean = false;
  selectedDate = {
    startDateTime: null,
    endDateTime: null
  };
  selectedDateModel: any;

  focusD$ = new Subject<string>();
  clickD$ = new Subject<string>();

  @ViewChild('dgSearch', { static: false }) dgSearch: NgbTypeahead;

  dispatchGroupSearch = (text$: Observable<string>) => {
    const debouncedText$ = text$.pipe(debounceTime(200), distinctUntilChanged());
    const clicksWithClosedPopup$ = this.clickD$.pipe(filter(() => !this.dgSearch.isPopupOpen()));
    const inputFocus$ = this.focusD$;

    return merge(debouncedText$, inputFocus$, clicksWithClosedPopup$).pipe(
      map(term => (term === '' ? this.dispatchGroups
        : this.dispatchGroups.filter(v => (v.name.toLowerCase().indexOf(term.toLowerCase()) > -1 || (v.id + '').indexOf(term.toLowerCase()) > -1 ) )))
    );
  };

  dispatchGroupformatter = (result: any) => {
    this.dispatchGroupSelectedCallback(result);
    return `#${result.id} ${result.name}`;
  };

  constructor(
    private _History: HistoryProvider,
    public translate: TranslateService,
    public _Global: GlobalService
  ) {
    this.translate.get('PAGE.CAPTIONS.HISTORY.AUTOMATIC_ALLOCATIONS').subscribe((res: string) => {
      this._Global.changeTitle(res);
    });
  }

  ngOnInit() {
    this.getHistoryData();
  }

  dispatchGroupSelectedCallback(dg) {
    console.log("DG", dg);
    this.params.dispatch_group_id = dg.id;
    this.getHistoryData();
  }

  getHistoryData() {
    this.automaticAllocations = [];
    this.showLoader = true;
    this._History.getAssetAutomaticAllocations(this.params).subscribe(
      results => {
        this.showLoader = false;
        this.automaticAllocations = results.data.automatic_allocations;
        this.allocationSources = results.data.allocation_sources;
        this.dispatchGroups = results.data.dispatch_groups;
        this.tsos = results.data.tsos;
        this.collectionSize = results.total_entries;

        this.dispatchGroupsByIds = {};
        for (let i in this.dispatchGroups) {
          let d = this.dispatchGroups[i];
          this.dispatchGroupsByIds[d.id] = d;
        }

        this.tsosByIds = {};
        for (let i in this.tsos) {
          let x = this.tsos[i];
          this.tsosByIds[x.id] = x;
        }
        console.log('tsosByIds', this.tsosByIds);

        for (let a of this.automaticAllocations) {
          if (a.allocationSourceId && a.allocationSourceId.id) {
            let sourceId = a.allocationSourceId.id;
            let source = this.allocationSources[sourceId];
            if (source) {
              a.source = source;
            } else {
              //console.log('no source for', a);
            }
          }
        }
      },
      error => {
        this.showLoader = false;
      }
    );
  }

  paginatedNominations() {}

  startDateSelectedCallback(date) {
    if (date.value[0] && date.value[1]) {
      this.selectedDate = {
        startDateTime: date.value[0].startOf('minute').toISOString(),
        endDateTime: date.value[1].startOf('minute').toISOString()
      };
      this.params.start_date = this.selectedDate.startDateTime;
      this.params.end_date = this.selectedDate.endDateTime;
    } else {
      this.params.start_date = '';
      this.params.end_date = '';
    }
    this.getHistoryData();
  }

  clearDateValue() {
    this.params.start_date = '';
    this.params.end_date = '';
    this.selectedDateModel = null;
    this.getHistoryData();
  }

  statusChangedCallback(status) {
    this.params.status = status == 'Choose' ? '' : status;
    this.getHistoryData();
  }

  typeChangedCallback(type) {
    this.params.type = type == 'Choose' ? '' : type;
    this.getHistoryData();
  }

  pageChangedCallback(page) {
    this.params.page = page;
    this.getHistoryData();
  }

  pageSizeChangedCallback(pageSize) {
    this.params.per_page = pageSize;
    this.params.page = 1;
    this.getHistoryData();
  }

  dispatchGroupBlurCallback(value) {
    if (!value) {
       this.params.dispatch_group_id = '';
      this.getHistoryData();
    }
  }
}
