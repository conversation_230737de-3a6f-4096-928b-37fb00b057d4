
<div class="row">
    <div class="col-md-8">
        <button
                class="eon-button bg-eon-red"
                (click)="submitNewOptimization()"
        >
            <span>{{'HISTORY.LOGS.SUBMIT'|translate}}</span>
        </button>
    </div>
</div>

<div class="table-filter d-flex">
    <div class="form-group col-md-3">
        <div class="form-check form-check-inline">
            <label class="form-check-label">{{'HISTORY.LOGS.SELECT_LABEL'|translate}}</label>
        </div>
        <div class="form-check form-check-inline">
            <label class="form-check-label" (click)="changeJobType('trading')" >
                <input type="radio" [(ngModel)]="params.job_type" class="form-check-input" id="jobType" value="trading">
                {{'HISTORY.LOGS.SELECT_TRADING'|translate}}
            </label>
        </div>
        <div class="form-check form-check-inline">
            <label class="form-check-label" (click)="changeJobType('steering')">
                <input type="radio" [(ngModel)]="params.job_type" class="form-check-input" id="jobType" value="steering">
                {{'HISTORY.LOGS.SELECT_STEERING'|translate}}
            </label>
        </div>
    </div>
</div>

<table class="table table-auction-results table-striped table-bg-turquoise">
    <tr>
        <th class="text-center vertical-center">{{"HISTORY.LOGS.TH.ID"|translate}}</th>
        <th class="text-center vertical-center">{{"HISTORY.LOGS.TH.TYPE"|translate}}</th>
        <th class="text-center vertical-center">{{"HISTORY.LOGS.TH.CREATED"|translate}}</th>
        <th class="text-center vertical-center">{{"HISTORY.LOGS.TH.UPDATED"|translate}}</th>
        <th class="text-center vertical-center">{{"HISTORY.LOGS.TH.START"|translate}}</th>
        <th class="text-center vertical-center">{{"HISTORY.LOGS.TH.END"|translate}}</th>
        <th class="text-center vertical-center">{{"HISTORY.LOGS.TH.RETRIES"|translate}}</th>
        <th class="text-center vertical-center">{{"HISTORY.LOGS.TH.FAILED"|translate}}</th>
        <th class="text-center vertical-center">{{"HISTORY.LOGS.TH.SENT"|translate}}</th>
        <th class="text-center vertical-center"></th>
        <th class="text-center vertical-center">{{"HISTORY.LOGS.TH.RESPONDED"|translate}}</th>
    </tr>
    <tr *ngIf="showLoader">
        <td colspan="10" class="text-center vertical-center">
            <vpp-management-loader [showLoader]="showLoader">
            </vpp-management-loader>
        <td>
    </tr>
    <ng-container *ngFor="let x of entries">
        <tr>
            <td class="text-center vertical-center">{{x.id}}</td>
            <td class="text-center vertical-center">{{x.optimization_type}}</td>
            <td class="text-center vertical-center">{{x.created | localDate: 'DD MMM HH:mm'}}</td>
            <td class="text-center vertical-center">{{x.updated | localDate: 'DD MMM HH:mm'}}</td>
            <td class="text-center vertical-center">{{x.start_time | localDate: 'DD MMM HH:mm'}}</td>
            <td class="text-center vertical-center">{{x.end_time | localDate: 'DD MMM HH:mm'}}</td>
            <td class="text-center vertical-center">{{x.retries_count}}</td>
            <td class="text-center vertical-center"><div class="text-error">{{x.failed_at | localDate: 'DD MMM HH:mm'}}</div></td>
            <td class="text-center vertical-center"><div class="text-success">{{x.sent_at | localDate: 'DD MMM HH:mm'}}</div></td>
            <td class="text-center vertical-center">
                <form method="get" class="inline" #download action="{{environment.routingPath}}/v2g_optimization_jobs/{{x.id}}/download" target="_blank">
                    <input type="hidden" name="authenticity_token" value="{{csr_token}}"/>
                    <button
                            type="submit"
                            (click)="download.submit()"
                            class="btn btn-default x-eon-button x-bg-eon-red">
                        <span class="fa fa-file-alt"></span>
                    </button>
                </form>
            </td>
            <td class="text-center vertical-center">
                <div class="text-center vertical-center text-success">
                    <ng-container *ngFor="let o of optimizations[x.id]">

                        <form method="get" class="inline" #dd
                              action="{{environment.routingPath}}/{{x.optimization_type == 'steering' ? 'steering_optimizations' : 'bidding_optimizations'}}/{{o.id}}/download" target="_blank">
                            <input type="hidden" name="authenticity_token" value="{{csr_token}}"/>
                            <span>{{o.created | localDate: 'DD MMM HH:mm'}}</span>
                            <button
                                    type="submit"
                                    (click)="dd.submit()"
                                    class="btn btn-default text-success x-eon-button x-bg-eon-red">
                                <span class="fa fa-file-alt"></span>
                            </button>
                        </form>
                    </ng-container>
                </div>
            </td>

        </tr>
    </ng-container>
</table>

<div class="d-flex justify-content-between">
    <ngb-pagination
            [collectionSize]="collectionSize"
            [(page)]="page"
            [ellipses]="true"
            [maxSize]="5"
            (pageChange)="pageChangedCallback($event)"
            [pageSize]="pageSize">
    </ngb-pagination>

    <select class="custom-select" style="width: auto"
            [(ngModel)]="pageSize"
            (ngModelChange)="pageSizeChangedCallback($event)">
        <option [ngValue]="25">25 items per page</option>
        <option [ngValue]="50">50 items per page</option>
        <option [ngValue]="75">75 items per page</option>
    </select>
</div>