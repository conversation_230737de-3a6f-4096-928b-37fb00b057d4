import {Component, OnInit, ViewEncapsulation} from "@angular/core";
import {environment} from "../../../../../environments/environment";
import {GlobalService} from "../../../../services/global.service";
import {TranslateService} from "@ngx-translate/core";
import {HistoryProvider} from "../../../../providers/history.provider";
import {HttpClientService} from "../../../../services/httpclient.service";
import {ConfirmService} from "../../../../services/confirm.service";
import {NotificationService} from "../../../../services/notification.service";


@Component({
    selector: 'vpp-management-v2g-history',
    templateUrl: './v2g.component.html',
    styleUrls: ['./v2g.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class V2gHistoryComponent implements OnInit {
    environment = environment;
    page = 1;
    pageSize = 25;
    collectionSize = 0;
    entries = [];
    optimizations = {};
    params = {
        page: this.page,
        per_page: this.pageSize,
        job_type: 'trading'
    };
    showLoader: boolean = false;
    csr_token: string = '';


    constructor(
        private _Global: GlobalService,
        private _history: HistoryProvider,
        public translate: TranslateService,
        private _httpClientService: HttpClientService,
        private _confirmService: ConfirmService,
        private _notificationService: NotificationService
    ) {
        this.translate.get('PAGE.CAPTIONS.HISTORY.V2G').subscribe((res: string) => {
            this._Global.changeTitle(res);
        });
        this.csr_token = this._httpClientService.getAuthToken();
    }

    ngOnInit() {
        this.getHistoryData();
    }

    getHistoryData() {
        this.showLoader = true;
        this.entries = [];
        this.optimizations = {};
        this._history.getV2gOptimizationJobs(this.params).subscribe(
            res => {
                this.showLoader = false;
                this.entries = res.jobs;
                this.optimizations = res.optimizations;
                this.collectionSize = res.total_entries;
            }, err => {
                console.log('Failed to get V2G history', err);
                this.showLoader = false;
            }
        );
    }

    submitNewOptimization() {
        let confirmMessage = 'Are you sure? This will trigger a new optimization for the next optimization interval.';
        this._confirmService.confirm({message: confirmMessage}).then(
            () => {
                this._newOptimization();
            },
            () => {
            });
    }

    _newOptimization() {
        this._history.createV2gOptimizationJob({}).subscribe(
            res => {
                if (res.success) {
                    this._notificationService.success({text: 'SUCCESS'});
                } else {
                    this._notificationService.error({text: JSON.stringify(res.error)});
                }
            },
            err => {
                console.error('failed to create v2g optimization job');
                this._notificationService.error({text: 'ERROR'});
            }
        );
    }

    pageChangedCallback(page) {
        this.params.page = page;
        this.getHistoryData();
    }

    pageSizeChangedCallback(pageSize) {
        this.params.per_page = pageSize;
        this.params.page = 1;
        this.getHistoryData();
    }

    changeJobType(jt) {
        this.params.job_type = jt;
        this.getHistoryData();
    }
}