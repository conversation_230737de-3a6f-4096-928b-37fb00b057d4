<section>
    <div class="container-fluid">
      <div id="ngb-tabset">
        <ul role="tablist" class="nav nav-tabs justify-content-start">
          <li class="nav-item" *ngIf="permissions.can_see_nominations">
            <a (click)="selectTab($event, 'nominations')"
               class="nav-link" [ngClass]="selectedTab == 'nominations' ? 'active' : ''"
               href="javascript:void(0)" role="tab" id="nominations" >{{ 'HISTORY.TABS.NOMINATIONS' | translate }}</a>
          </li>
          <li class="nav-item" *ngIf="permissions.can_see_allocations">
            <a (click)="selectTab($event, 'manual-allocations')"
               class="nav-link" [ngClass]="selectedTab == 'manual-allocations' ? 'active' : ''"
               href="javascript:void(0)" role="tab" id="manual-allocations">{{ 'HISTORY.TABS.MANUAL_ALLOCATIONS' | translate }}</a>
          </li>
          <li class="nav-item" *ngIf="permissions.can_see_allocations">
            <a (click)="selectTab($event, 'automatic-allocations')"
               class="nav-link" [ngClass]="selectedTab == 'automatic-allocations' ? 'active' : ''"
               href="javascript:void(0)" role="tab" id="automatic-allocations">{{ 'HISTORY.TABS.AUTOMATIC_ALLOCATIONS' | translate }}</a>
          </li>
          <li class="nav-item" *ngIf="permissions.can_see_bids">
            <a (click)="selectTab($event, 'auctions')"
               class="nav-link" [ngClass]="selectedTab == 'auctions' ? 'active' : ''"
               href="" role="tab" id="auctions">{{ 'HISTORY.TABS.AUCTIONS' | translate }}</a>
          </li>
          <li class="nav-item" *ngIf="permissions.can_see_bids">
            <a (click)="selectTab($event, 'bids')"
               class="nav-link" [ngClass]="selectedTab == 'bids' ? 'active' : ''"
               href="javascript:void(0)" role="tab" id="bids" > Bids </a>
          </li>
          <li class="nav-item" *ngIf="permissions.can_see_reporting_and_nofications">
            <a (click)="selectTab($event, 'reporting-emails')" class="nav-link" [ngClass]="selectedTab == 'reporting-emails' ? 'active' : ''" href="" role="tab" id="reporting-emails">{{ 'HISTORY.TABS.REPORTING_EMAILS' | translate }}</a>
          </li>
          <li class="nav-item" *ngIf="permissions.can_see_dlm_charging_sessions">
            <a (click)="selectTab($event, 'dlm-charging-sessions')" class="nav-link" [ngClass]="selectedTab == 'dlm-charging-sessions' ? 'active' : ''" href="" role="tab" id="dlm-charging-sessions">{{ 'HISTORY.TABS.DLM_CHARGING_SESSIONS' | translate }}</a>
          </li>
          <li class="nav-item">
            <a (click)="selectTab($event, 'v2g')" class="nav-link" [ngClass]="selectedTab == 'v2g' ? 'active' : ''" href="" role="tab" id="v2g">V2G</a>
          </li>
        </ul>
        <div class="tab-content">
          <div [@slideRightContentAnimation]="selectedTab == 'nominations' ? 'in' : 'out'" *ngIf="permissions.can_see_nominations">
            <vpp-management-nominations-history *ngIf="selectedTab == 'nominations'"></vpp-management-nominations-history>
          </div>
          <div [@slideRightContentAnimation]="selectedTab == 'manual-allocations' ? 'in' : 'out'" *ngIf="permissions.can_see_allocations">
            <vpp-management-manual-allocations-history *ngIf="selectedTab == 'manual-allocations'"></vpp-management-manual-allocations-history>
          </div>
          <div [@slideRightContentAnimation]="selectedTab == 'automatic-allocations' ? 'in' : 'out'" *ngIf="permissions.can_see_allocations">
            <vpp-management-automatic-allocations-history *ngIf="selectedTab == 'automatic-allocations'"></vpp-management-automatic-allocations-history>
          </div>
          <div [@slideRightContentAnimation]="selectedTab == 'auctions' ? 'in' : 'out'" *ngIf="permissions.can_see_bids">
            <vpp-management-auctions-history *ngIf="selectedTab == 'auctions'"></vpp-management-auctions-history>
          </div>
          <div [@slideRightContentAnimation]="selectedTab == 'reporting-emails' ? 'in' : 'out'" *ngIf="permissions.can_see_reporting_and_nofications">
            <vpp-management-reporting-emails-history *ngIf="selectedTab == 'reporting-emails'"></vpp-management-reporting-emails-history>
          </div>
          <div [@slideRightContentAnimation]="selectedTab == 'dlm-charging-sessions' ? 'in' : 'out'" *ngIf="permissions.can_see_dlm_charging_sessions">
            <vpp-dlm-charging-sessions *ngIf="selectedTab == 'dlm-charging-sessions'"></vpp-dlm-charging-sessions>
          </div>
        </div>
        <div [@slideRightContentAnimation]="selectedTab == 'bids' ? 'in' : 'out'" *ngIf="permissions.can_see_bids">
          <vpp-management-bids-history *ngIf="selectedTab == 'bids'"></vpp-management-bids-history>
        </div>
        <div [@slideRightContentAnimation]="selectedTab == 'v2g' ? 'in' : 'out'" >
          <vpp-management-v2g-history *ngIf="selectedTab == 'v2g'"></vpp-management-v2g-history>
        </div>
      </div>
  </div>
</section>