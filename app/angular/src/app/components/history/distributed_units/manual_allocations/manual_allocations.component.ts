import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { HistoryProvider } from "./../../../../providers/history.provider";
import { environment } from "../../../../../environments/environment";
import { TranslateService } from '@ngx-translate/core';
import { GlobalService } from "./../../../../services/global.service";

@Component({
  selector: "vpp-management-manual-allocations-history",
  templateUrl: "./manual_allocations.component.html",
  styleUrls: ["./manual_allocations.component.scss"],
  encapsulation: ViewEncapsulation.None
})

export class ManualAllocationsHistoryComponent implements OnInit {
  environment = environment;
  page = 1;
  pageSize = 25;
  collectionSize = 0;
  params = {
    page: this.page,
    per_page: this.pageSize
  };
  history = [];
  showLoader: boolean = false;

  constructor(
    public _Global: GlobalService,
    private _History: HistoryProvider,
    public translate: TranslateService) {
    this.translate.get('PAGE.CAPTIONS.HISTORY.MANUAL_ALLOCATIONS').subscribe((res: string) => {
      this._Global.changeTitle(res);
    });
  }

  ngOnInit() {
    this.getHistoryData();
  }

  getHistoryData() {
    this.showLoader = true;
    this.history = [];
    this._History.getAssetManualAllocations(this.params).subscribe(
      results=> {
        this.showLoader = false;
        this.history = results.data;
        this.collectionSize = results.total_entries;
      },
      error => {
        this.showLoader = false;
      }
    )
  }

  pageChangedCallback(page) {
    this.params.page = page;
    this.getHistoryData();
  }

  pageSizeChangedCallback(pageSize) {
    this.params.per_page = pageSize;
    this.params.page = 1;
    this.getHistoryData();
  }
}
