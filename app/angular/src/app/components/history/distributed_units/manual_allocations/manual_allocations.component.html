<table class="table table-auction-results table-striped table-bg-turquoise">
    <tr>
        <th class="text-center">{{ 'HISTORY.MANUAL_ALLOCATIONS.TABLE.PERFORMED_ON' | translate }}</th>
        <th class="text-center">{{ 'HISTORY.MANUAL_ALLOCATIONS.TABLE.USER' | translate }}</th>
        <th class="text-center">{{ 'HISTORY.MANUAL_ALLOCATIONS.TABLE.CONTENT' | translate }}</th>
        <th class="text-center">{{ 'HISTORY.MANUAL_ALLOCATIONS.TABLE.SUCCESFULLY_IMPORTED' | translate }}</th>
        <th class="text-center">{{ 'HISTORY.MANUAL_ALLOCATIONS.TABLE.ERRORS' | translate }}</th>
        <th class="text-center">{{ 'HISTORY.MANUAL_ALLOCATIONS.TABLE.WARNINGS' | translate }}</th>
        <th class="text-center"></th>
    </tr>
    <tr *ngIf="showLoader">
      <td colspan="7" class="text-center vertical-center"><vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
      <td>
    </tr>
    <ng-container *ngFor="let h of history">
      <tr>
          <td class="text-center">{{ h.created | localDate:'DD MMM HH:mm' }}</td>
          <td class="text-center">
            {{ h.user_account.name }}<br>
            {{ h.user_account.email }}
          </td>
          <td class="text-center">
            <span>{{ ('HISTORY.MANUAL_ALLOCATIONS.CONTENT_TYPE.' + (h.content_type | bidSourceType)) | translate }}</span>
              <br *ngIf="h.content_type == 'file_import'">
              <a href="{{environment.routingPath}}/asset_dg_allocation_sources/{{ h.id }}/download" *ngIf="h.content_type == 'file_import'"> {{ h.content.fileName }}</a>
          </td>
          <td class="text-center">
            <label
              class="eon-checkbox-label non-clickable bg-eon-red"
              [ngClass]="h.validation_result?.validationSuccess ? 'checked' : ''"></label>
          </td>
          <td class="text-center">{{ h.validation_result?.errors?.length }}</td>
          <td class="text-center">{{ h.validation_result?.warnings?.length }}</td>
          <td class="text-center"><a [routerLink]="[environment.routingPath + '/allocations/manual-allocation/' + h.id ]"><i class="fa fa-eye"></i></a></td>
      </tr>
    </ng-container>
</table>
<div class="d-flex justify-content-between">
  <ngb-pagination
    [collectionSize]="collectionSize"
    [(page)]="page"
    [ellipses]="true"
    [maxSize]="5"
    (pageChange)="pageChangedCallback($event)"
    [pageSize]="pageSize">
  </ngb-pagination>

  <select class="custom-select" style="width: auto"
    [(ngModel)]="pageSize"
    (ngModelChange)="pageSizeChangedCallback($event)">
    <option [ngValue]="25">25 items per page</option>
    <option [ngValue]="50">50 items per page</option>
    <option [ngValue]="75">75 items per page</option>
  </select>
</div>
