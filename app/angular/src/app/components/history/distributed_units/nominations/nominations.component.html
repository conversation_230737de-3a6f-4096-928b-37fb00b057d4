<table class="table table-auction-results table-striped table-bg-turquoise">
    <tr>
        <th class="text-center vertical-center">{{ 'HISTORY.NOMINATIONS.TABLE.PERFORMED_ON' | translate }}</th>
        <th class="text-center vertical-center">{{ 'HISTORY.NOMINATIONS.TABLE.USER' | translate }}</th>
        <th class="text-center vertical-center">{{ 'HISTORY.NOMINATIONS.TABLE.CONTENT' | translate }}</th>
        <th class="text-center vertical-center">{{ 'HISTORY.NOMINATIONS.TABLE.SUCCESFULLY_IMPORTED' | translate }}</th>
        <th class="text-center vertical-center">{{ 'HISTORY.NOMINATIONS.TABLE.ERRORS' | translate }}</th>
        <th class="text-center vertical-center">{{ 'HISTORY.NOMINATIONS.TABLE.WARNINGS' | translate }}</th>
        <th class="text-center vertical-center"></th>
    </tr>
    <tr *ngIf="showLoader">
      <td colspan="7" class="text-center vertical-center"><vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
      <td>
    </tr>
    <ng-container *ngFor="let n of nominationSources">
      <tr>
          <td class="text-center vertical-center">{{ n.created | localDate:'DD MMM, YYYY HH:mm:ss' }}</td>
          <td class="text-center vertical-center">{{ n.user_account.name }} <br> {{ n.user_account.email }}</td>
          <td class="text-center vertical-center">
            <span>{{ ('HISTORY.NOMINATIONS.DETAIL.CONTENT_TYPE.' + (n.content_type | nominationSourceType)) | translate }}</span>
            <br *ngIf="n.content_type == 'file_import'">
            <a target="_blank" class="eon-link" href="{{environment.apiPath}}/api/v1/nomination/download/{{n.id}}" *ngIf="n.content_type == 'file_import'"> {{ n.content?.fileName }}</a>
          </td>
          <td class="text-center vertical-center">
            <label
              class="eon-checkbox-label non-clickable bg-eon-red"
              [ngClass]="n.validation_result?.validationSuccess ? 'checked' : ''"></label>
          </td>
          <td class="text-center vertical-center">{{ n.validation_result && n.validation_result.errors ? n.validation_result.errors.length : '' }}</td>
          <td class="text-center vertical-center">{{ n.validation_result && n.validation_result.warnings ? n.validation_result.warnings.length : '' }}</td>
          <td class="text-center vertical-center">
            <a *ngIf="n.content"
              class="eon-link"
              [routerLink]="[environment.routingPath + '/nominations/nomination_detail/' + n.id ]"><i class="fa fa-eye"></i></a>
          </td>
      </tr>
    </ng-container>
</table>
<div class="d-flex justify-content-between">
  <ngb-pagination
    [collectionSize]="collectionSize"
    [(page)]="page"
    [ellipses]="true"
    [maxSize]="5"
    (pageChange)="pageChangedCallback($event)"
    [pageSize]="pageSize">
  </ngb-pagination>

  <select class="custom-select" style="width: auto"
    [(ngModel)]="pageSize"
    (ngModelChange)="pageSizeChangedCallback($event)">
    <option [ngValue]="25">25 items per page</option>
    <option [ngValue]="50">50 items per page</option>
    <option [ngValue]="75">75 items per page</option>
  </select>
</div>
