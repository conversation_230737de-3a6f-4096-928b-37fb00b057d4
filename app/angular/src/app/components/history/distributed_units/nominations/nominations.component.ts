import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { HistoryProvider } from "./../../../../providers/history.provider";
import * as moment from "moment-timezone";
import { angularData } from "./../../../../../global.export";
import { TranslateService } from '@ngx-translate/core';
import { environment } from "../../../../../environments/environment";
import { GlobalService } from "./../../../../services/global.service";

@Component({
  selector: "vpp-management-nominations-history",
  templateUrl: "./nominations.component.html",
  styleUrls: ["./nominations.component.scss"],
  encapsulation: ViewEncapsulation.None
})

export class NominationsHistoryComponent implements OnInit {
  environment = environment;
  page = 1;
  pageSize = 25;
  collectionSize = 0;
  nominationSources = [];
  params = {
    page: this.page,
    per_page: this.pageSize
  };
  showLoader: boolean = false;

  constructor(
    private _Global: GlobalService,
    private _History: HistoryProvider,
    public translate: TranslateService
  ) {
    this.translate.get('PAGE.CAPTIONS.HISTORY.NOMINATIONS').subscribe((res: string) => {
      this._Global.changeTitle(res);
    });
  }

  ngOnInit() {
    this.getHistoryData();
  }

  getHistoryData() {
    this.showLoader = true;
    this.nominationSources = [];
    this._History.getNominations(this.params).subscribe(
      results => {
        this.showLoader = false;
        this.nominationSources = results.data.nomination_sources;
        this.collectionSize = results.total_entries;
      },
      error => {
        this.showLoader = false;
      }
    );
  }

  pageChangedCallback(page) {
    this.params.page = page;
    this.getHistoryData();
  }

  pageSizeChangedCallback(pageSize) {
    this.params.per_page = pageSize;
    this.params.page = 1;
    this.getHistoryData();
  }

}
