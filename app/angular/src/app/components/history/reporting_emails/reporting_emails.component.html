<div class="table-filter d-flex">
  <div class="form-group col-md-2">
    <label>{{ 'HISTORY.REPORTING_EMAILS.FILTERS.REPORT_TYPE' | translate }}</label>
    <select class="form-control"
      [(ngModel)]="search.reportType"
      (ngModelChange)="reportTypeChangedCallback($event)">
      <option [ngValue]="s.id" *ngFor="let s of reportTypes">{{ s.name }}</option>
    </select>
  </div>
  <div class="form-group col-md-3">
    <label>{{ 'HISTORY.REPORTING_EMAILS.FILTERS.TIME' | translate }}</label>
    <div class="input-group">
      <input
        type="text"
        class="form-control"
        [owlDateTime]="dt1"
        [selectMode]="'range'"
        [(ngModel)]="search.selectedMoments"
        [owlDateTimeTrigger]="dt1"
        (dateTimeChange)="startTimeSelectedCallback($event)"
        placeholder="">
      <div class="input-group-append">
        <button class="btn btn-default" (click)="clearSelectedTimeValue()">
          <i class="fa fa-times"></i>
        </button>
      </div>
      <owl-date-time
        #dt1
        (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
        [pickerMode]="'popup'">
      </owl-date-time>
    </div>
  </div>
  <div class="form-group">
    <label>{{ 'HISTORY.REPORTING_EMAILS.FILTERS.STATUS' | translate }}</label>
    <select class="form-control"
      [(ngModel)]="search.status"
      (ngModelChange)="statusChangedCallback($event)">
      <option [ngValue]="s" *ngFor="let s of statuses">{{ s }}</option>
    </select>
  </div>
  <div class="form-group">
    <label>{{ 'HISTORY.REPORTING_EMAILS.FILTERS.RECIPIENTS' | translate }}</label>
    <select class="form-control"
      [(ngModel)]="search.recipients"
      (ngModelChange)="recipientsChangedCallback($event)">
      <option [ngValue]="r" *ngFor="let r of recipients">{{ r }}</option>
    </select>
  </div>
  <div class="form-group col-md-2">
    <label>{{ 'HISTORY.REPORTING_EMAILS.FILTERS.ACTIVE' | translate }}</label>
    <select class="form-control"
      [(ngModel)]="search.active"
      (ngModelChange)="activeChangedCallback($event)">
      <option [ngValue]="o.id" *ngFor="let o of yesNoOptions">{{ o.name }}</option>
    </select>
  </div>
</div>
<table class="table table-auction-results table-striped table-bg-turquoise">
    <tr>
        <th class="text-left">#</th>
        <th class="text-center">{{ 'HISTORY.REPORTING_EMAILS.TABLE.TIME' | translate }}</th>
        <th class="text-center">{{ 'HISTORY.REPORTING_EMAILS.TABLE.STATUS' | translate }}</th>
        <th class="text-center">{{ 'HISTORY.REPORTING_EMAILS.TABLE.REPORT_TYPE' | translate }}</th>
        <th class="text-left">{{ 'HISTORY.REPORTING_EMAILS.TABLE.PARAMS' | translate }}</th>
        <th class="text-center">{{ 'HISTORY.REPORTING_EMAILS.TABLE.RECIPIENTS' | translate }}</th>
        <th class="text-center">{{ 'HISTORY.REPORTING_EMAILS.TABLE.ACTIVE' | translate }}</th>
        <th class="text-center"></th>
    </tr>
    <tr *ngIf="showLoader">
      <td colspan="7" class="text-center vertical-center"><vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
      <td>
    </tr>
    <ng-container *ngFor="let r of reportEmails">
        <tr>
          <td class="text-center vertical-center"> {{ r.id }} </td>
          <td class="text-center vertical-center">
            {{ r.report_date | localDate:'DD MMM, YYYY' }}
            <br/>
            <small>(generated at {{ r.created | localDate:'DD MMM, YYYY HH:mm:ss' }})</small>
          </td>
          <td class="text-center vertical-center">{{ r.status }}</td>
          <td class="text-center vertical-center">
            {{ reportTypeById(r.report_type).name }}
          </td>
          <td class="text-left vertical-center">
            <ng-container *ngFor="let p of r.localized_report_parameters; let last = last">
              <span>{{ p.name }}: {{ p.value }}</span><span *ngIf="!last"><br/></span>
            </ng-container>
          </td>
          <td class="text-center vertical-center">
            <ng-container *ngFor="let er of r.email_recipients">
              <span>{{ er }}<br></span>
            </ng-container>
          </td>
          <td class="text-center vertical-center">
            <label
              class="eon-checkbox-label non-clickable bg-eon-red"
              [ngClass]="r.report_delivery_skipped ? '' : 'checked'"></label>
          </td>
          <td class="text-center vertical-center">
            <a [routerLink]="[environment.routingPath + '/reporting-emails/reporting_email_detail/' + r.id ]">
              <i class="fa fa-eye"></i></a>
            &nbsp;
            <a class="pdf-link"
              target="_blank"
              *ngIf="r.has_attachment"
              href="{{environment.routingPath}}/api/v1/history/report_email/{{ r.id }}/download"
              title="{{ 'HISTORY.REPORTING_EMAILS.TABLE.DOWNLOAD' | translate }}">
              <i class="fa {{iconClass(r.report_file_extension)}}"></i>
            </a>
          </td>
      </tr>
    </ng-container>
</table>
<div class="d-flex justify-content-between">
  <ngb-pagination
    [collectionSize]="collectionSize"
    [(page)]="page"
    [ellipses]="true"
    [maxSize]="5"
    (pageChange)="pageChangedCallback($event)"
    [pageSize]="pageSize">
  </ngb-pagination>

  <select class="custom-select" style="width: auto"
    [(ngModel)]="pageSize"
    (ngModelChange)="pageSizeChangedCallback($event)">
    <option [ngValue]="25">25 {{ 'HISTORY.REPORTING_EMAILS.TABLE.PER_PAGE' | translate }}</option>
    <option [ngValue]="50">50 {{ 'HISTORY.REPORTING_EMAILS.TABLE.PER_PAGE' | translate }}</option>
    <option [ngValue]="75">75 {{ 'HISTORY.REPORTING_EMAILS.TABLE.PER_PAGE' | translate }}</option>
  </select>
</div>
