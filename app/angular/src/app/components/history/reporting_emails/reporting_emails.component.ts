import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { Observable, Subject, merge } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map } from 'rxjs/operators';
import { HistoryProvider } from "../../../providers/history.provider";
import * as moment from "moment-timezone";
import { angularData } from "../../../../global.export";
import { TranslateService } from '@ngx-translate/core';
import { environment } from "../../../../environments/environment";
import { NgbTypeahead } from '@ng-bootstrap/ng-bootstrap';
import { GlobalService } from "../../../services/global.service";

@Component({
  selector: "vpp-management-reporting-emails-history",
  templateUrl: "./reporting_emails.component.html",
  styleUrls: ["./reporting_emails.component.scss"],
  encapsulation: ViewEncapsulation.None
})

export class ReportingEmailsHistoryComponent implements OnInit {
  environment = environment;
  page = 1;
  pageSize = 25;
  collectionSize = 0;
  params = {
    page: this.page,
    report_type: "",
    start_date: "",
    end_date: "",
    status: "",
    recipients: "",
    active: "",
    per_page: this.pageSize,
  };
  search = {
    reportType: null,
    selectedMoments: null,
    startTime: "",
    endTime: "",
    status: "",
    recipients: null,
    active: ""
  };
  history = [];
  reportTypes = [
    {id: "Choose", name: "Choose"},
    {id: "scheduling_control", name: "Control"},
    {id: "bg_asset_activations", name: "BK6-17-046 Report"},
    {id: "scheduling_day_after", name: "UGC"},
    {id: "scheduling_balancing_groups_third_party", name: "ThirdParty"},
    {id: "rollups_not_generated", name: "Rollups not generated"},
    {id: "scheduling_balancing_groups", name: "EDG"},
  ];
  statuses = [
    "Choose",
    "Success",
    "Skipped",
    "Error",
  ];
  yesNoOptions = [
    {id: "Choose", name: "Choose"},
    {id: "true", name: "Yes"},
    {id: "false", name: "No"},
  ]
  recipients = ['Choose']

  showLoader: boolean = false;
  reportEmails = []

  ngOnInit() {
    this.initFromStore();
    this.getEmailRecipients();
    this.getHistoryData();
  }

  constructor(
    private _History: HistoryProvider,
    public translate: TranslateService,
    public globalService: GlobalService
  ) {
    this.translate.get('PAGE.CAPTIONS.HISTORY.REPORTING_EMAILS').subscribe((res: string) => {
      this.globalService.changeTitle(res);
    });
  }


  initFromStore() {
    let storedParams = JSON.parse(sessionStorage.getItem('reportingEmailsQueryPamars'));
    if (storedParams) {
      this.params = storedParams;
      this.page = this.params.page;
      this.pageSize = this.params.per_page;
    }
    let storedSearch = JSON.parse(sessionStorage.getItem('reportingEmailsQuerySearch'));
    if (storedSearch) {
      this.search = storedSearch;
    }
  }

  setToStore() {
    sessionStorage.setItem('reportingEmailsQueryPamars', JSON.stringify(this.params));
    sessionStorage.setItem('reportingEmailsQuerySearch', JSON.stringify(this.search));
  }

  reportTypeById(id) {
    return this.reportTypes.find((s) => s.id == id);
  }

  getEmailRecipients() {
    this._History.getReportEmailRecipients({}).subscribe(
      results => {
          this.recipients = ['Choose'].concat(results);
        },
        error => {
          console.log("error loading report email recipients")
        }
    );
  }

  getHistoryData() {
    this.setToStore();
    this.showLoader = true;
    this.reportEmails = [];
    this._History.getReportEmails(this.params).subscribe(
      results => {
        this.showLoader = false;
        this.reportEmails = results.data.report_emails;
        this.collectionSize = results.total_entries;
      },
      error => {
        this.showLoader = false;
      }
    );
  }

  startTimeSelectedCallback(date) {
    if (date.value[0] && date.value[1]) {
      this.params.start_date = date.value[0].startOf('minute').toISOString();
      this.params.end_date = date.value[1].startOf('minute').toISOString()
    } else {
      this.params.start_date = '';
      this.params.end_date = '';
    }
    this.getHistoryData();
  }

  clearSelectedTimeValue() {
    this.params.start_date = '';
    this.params.end_date = '';
    this.search.selectedMoments = null;
    this.getHistoryData();
  }

  reportTypeChangedCallback(reportType) {
    this.params.report_type = reportType == 'Choose' ? '' : reportType;
    this.getHistoryData();
  }
  
  statusChangedCallback(status) {
    this.params.status = status == 'Choose' ? '' : status;
    this.getHistoryData();
  }

  recipientsChangedCallback(recipients) {
    this.params.recipients = recipients == 'Choose' ? '' : recipients;
    this.getHistoryData();
  }

  activeChangedCallback(active) {
    this.params.active = active == 'Choose' ? '' : active;
    this.getHistoryData();
  }
  
  pageChangedCallback(page) {
    this.params.page = page;
    this.getHistoryData();
  }

  pageSizeChangedCallback(pageSize) {
    this.params.per_page = pageSize;
    this.params.page = 1;
    this.getHistoryData();
  }

  iconClass(extension) {
    if (extension == "xls" || extension == "xlsx" || extension == "csv")
      return "fa-file-excel"
    else if (extension == "zip")
      return "fa-file-archive-o"
    else
      return "fa-file-text-o"
  }
}
