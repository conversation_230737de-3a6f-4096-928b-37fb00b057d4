import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef,
  Input,
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { HistoryProvider } from "../../../providers/history.provider";
import { TranslateService } from '@ngx-translate/core';
import { environment } from "../../../../environments/environment";
import { GlobalService } from "./../../../services/global.service";

@Component({
  selector: 'vpp-management-reporting-email-detail',
  styleUrls: ['./reporting_email.component.scss'],
  templateUrl: "./reporting_email.component.html",
  encapsulation: ViewEncapsulation.None
})

export class ReportingEmailComponent {
  reportTypes = [
    {id: "Choose", name: "Choose"},
    {id: "scheduling_control", name: "Control"},
    {id: "bg_asset_activations", name: "BK6-17-046 Report"},
    {id: "scheduling_day_after", name: "UGC"},
    {id: "scheduling_balancing_groups_third_party", name: "ThirdParty"},
    {id: "rollups_not_generated", name: "Rollups not generated"},
    {id: "scheduling_balancing_groups", name: "EDG"},
  ];

  reportingEmail: any = {};
  environment: any = environment;

  @Input('id') reportingEmailId;

  constructor(
    private _Global: GlobalService,
    private _History: HistoryProvider,
    private _Route: ActivatedRoute,
    public translate: TranslateService
    ) {
    this.translate.get('HISTORY.REPORTING_EMAILS.DETAIL.TITLE').subscribe((res: string) => {
        this._Global.changeTitle(res);
    });
  }

  ngOnInit() {
    if (this.reportingEmailId) {
      this.getReportingEmail();
    }
  }

  ngAfterViewInit() {
    this._Route.params.forEach((params: Params) => {
      if (params["id"]) {
        this.reportingEmailId = params["id"];
        this.getReportingEmail();
      }
    });
  }

  getReportingEmail() {
    this._History.findReportEmailById(this.reportingEmailId).subscribe(
      (data) => {
        this.reportingEmail = data;
      }
    )
  }

  reportTypeById(id) {
    return this.reportTypes.find((s) => s.id == id);
  }

  iconClass(extension) {
    if (extension == "xls" || extension == "xlsx" || extension == "csv")
      return "fa-file-excel"
    else if (extension == "zip")
      return "fa-file-archive-o"
    else
      return "fa-file-text-o"
  }
}