<div class="container-fluid">
  <section>
    <div class="row">
      <div class="col-md-2">
        <a class="back-button" [routerLink]="[environment.routingPath + '/history/reporting-emails']"><i class="fa fa-chevron-left"></i> {{ 'BACK' | translate }}</a>
        <br>
      </div>
    </div>
    <div class="row">
      <div class="alerts-wrapper-static col-md-8 col-lg-6">
        <ngb-alert
          *ngIf="reportingEmail.status == 'Success'"
          [dismissible]="true"
          [type]="'success'"> {{ reportingEmail.status }}
        </ngb-alert>
        <ngb-alert
            *ngIf="reportingEmail.status == 'Skipped'"
          [dismissible]="false"
          [type]="'warning'"> {{ reportingEmail.status }}
        </ngb-alert>
        <ngb-alert
            *ngIf="reportingEmail.status == 'Error'"
          [dismissible]="false"
          [type]="'error'"> {{ reportingEmail.status }}
        </ngb-alert>
      </div>
    </div>

    <div class="row" *ngIf="reportingEmail.localized_report_generation_errors">
      <div class="alerts-wrapper-static col-md-10 col-lg-12">
        <ngb-alert [type]="'error'">
          <span *ngFor="let e of reportingEmail.localized_report_generation_errors"> {{ e }} <br/></span>
        </ngb-alert>
      </div>
    </div>

    <div class="row" *ngIf="reportingEmail.localized_report_feedback">
      <div class="alerts-wrapper-static col-md-10 col-lg-12">
        <ngb-alert [type]="'warning'">
          <span *ngFor="let e of reportingEmail.localized_report_feedback"> {{ e }} <br/></span>
        </ngb-alert>
      </div>
    </div>

    <div class="row">
      <div class="col-md-10 col-lg-12">
        <div class="card card-allocated">
          <div class="card-body">
            <div class="stats-group">
              <label>{{ 'HISTORY.REPORTING_EMAILS.TABLE.REPORT_TYPE' | translate }}</label>
              <span class="value">{{ reportTypeById(reportingEmail.report_type)?.name }}</span>
            </div>
            <div class="stats-group">
              <label>{{ 'HISTORY.REPORTING_EMAILS.TABLE.ACTIVE' | translate }}</label>
              <span class="value">
                <label
                  class="eon-checkbox-label non-clickable bg-eon-red"
                  [ngClass]="reportingEmail.report_delivery_skipped ? '' : 'checked'"></label>
              </span>
            </div>
            <div class="stats-group">
              <label>{{ 'HISTORY.REPORTING_EMAILS.TABLE.REPORT_DATE' | translate }}</label>
              <span class="value">{{ reportingEmail.report_date | localDate:'ddd, DD MMM YYYY'}}</span>
            </div>
            <div class="stats-group">
              <label>{{ 'HISTORY.REPORTING_EMAILS.TABLE.CREATED' | translate }}</label>
              <span class="value">{{ reportingEmail.created | localDate:'ddd, DD MMM YYYY'}}</span>
            </div>
            <div class="stats-group">
              <label>{{ 'HISTORY.REPORTING_EMAILS.TABLE.RECIPIENTS' | translate }}</label>
              <span class="value">
                <ng-container *ngFor="let er of reportingEmail.email_recipients">
                  <span>{{ er }}<br></span>
                </ng-container>
              </span>
            </div>
            <div class="stats-group">
              <label>{{ 'HISTORY.REPORTING_EMAILS.TABLE.PARAMS' | translate }}</label>
              <span class="value">
                <ng-container *ngFor="let p of reportingEmail.localized_report_parameters">
                  <span>{{ p.name }}: {{p.value}}<br></span>
                </ng-container>
              </span>
            </div>
            <div class="stats-group" *ngIf="reportingEmail.has_attachment">
              <label>{{ 'HISTORY.REPORTING_EMAILS.TABLE.FILE' | translate }}</label>
              <span class="value">
                <a class="pdf-link"
                  target="_blank"
                  href="{{environment.routingPath}}/api/v1/history/report_email/{{ reportingEmail.id }}/download"
                  title="{{ 'HISTORY.REPORTING_EMAILS.TABLE.DOWNLOAD' | translate }}">
                  <i class="fa {{iconClass(reportingEmail.report_file_extension)}}"></i>
                  {{reportingEmail.report_file_name}}.{{reportingEmail.report_file_extension}}
                </a>
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>

  </section>
</div>