import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { Location } from "@angular/common";
import { GlobalService } from "../../../services/global.service";
import { SlideRightContentAnimation } from '../../../animations/slide-right-content';
import { environment } from "../../../../environments/environment";
import { angularData } from "../../../../global.export";
import { TranslateService } from '@ngx-translate/core';
import { HistoryProvider } from "./../../../providers/history.provider";
import * as moment from "moment-timezone";

@Component({
  selector: "vpp-dlm-charging-sessions",
  templateUrl: "./dlm_charging_sessions.component.html",
  styleUrls: ["./dlm_charging_sessions.component.scss"],
  encapsulation: ViewEncapsulation.None,
  animations: [ SlideRightContentAnimation ],
})

export class DlmChargingSessionsComponent implements OnInit {
  selectedSelection = "";
  showLoader: boolean = false;
  permissions: any;

  params = {
    page: 1,
    per_page: 25
  };

  page = 1;
  pageSize = 25;
  collectionSize = 0;
  dlmChargingSessions: any = [];

  constructor(
    private _Global: GlobalService,
    private route: ActivatedRoute,
    private _Location: Location,
    private _History: HistoryProvider,
    public translate: TranslateService
  ) {
    this.translate.get('PAGE.CAPTIONS.HISTORY.DLM_CHARGING_SESSIONS').subscribe((res: string) => {
      this._Global.changeTitle(res);
    });
    this.permissions = angularData.permissions;
  }

  ngOnInit() {
    this.getDlmChargingSessionsData();
  }

  ngAfterViewInit() {
    this.route.params.forEach((params: Params) => {
      console.log(params);
      if (params["tab"]) {
        setTimeout(() => {
          this.toggleSection(null, params["tab"]);
        })
      }
    });
  }

  toggleSection(event, section) {
    if (event) {
      //event.preventDefault();
      //event.stopPropagation();
    }
    if (this.selectedSelection == section) {
      this.selectedSelection = '';
      this._Location.replaceState(`${environment.routingPath}/history/`);
    } else {
      this.selectedSelection = section;
      this._Location.replaceState(`${environment.routingPath}/history/${section}`);
    }
  }

  getDlmChargingSessionsData() {
    this.showLoader = true;
    this.dlmChargingSessions = [];
    this._History.getDlmChargingSessions(this.params).subscribe(
      results => {
        this.showLoader = false;
        this.dlmChargingSessions = results.data;
        this.collectionSize = results.total_entries;
      },
      error => {
        this.showLoader = false;
      }
    );
  }

  pageChangedCallback(page) {
    this.params.page = page;
    this.getDlmChargingSessionsData();
  }

  pageSizeChangedCallback(pageSize) {
    this.params.per_page = pageSize;
    this.params.page = 1;
    this.getDlmChargingSessionsData();
  }

  fileUploadedCallback($event) {
    console.log("EVENT", $event);
    this.getDlmChargingSessionsData();
  }

}
