<div class="row">
  <div class="col-md-8">
    <div class="tab-content light-yellow">
      <h2>{{ 'HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.TITLE' | translate }}</h2>
      <vpp-file-upload-results [response]="response"></vpp-file-upload-results>
      <a (click)="showFileFormat = !showFileFormat" class="view-more-link" *ngIf="showFileFormat">{{ 'HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.HIDE' | translate }}</a>
      <a (click)="showFileFormat = !showFileFormat" class="view-more-link" *ngIf="!showFileFormat">{{ 'HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.SHOW' | translate }}</a>
      <div class="file-format-box" *ngIf="showFileFormat">
        <p>{{ 'HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.CSV_FORMAT_DETAIL' | translate }}</p>
        <code [innerHtml]="'HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.CSV_FORMAT' | translate"></code>

        <p>{{ 'HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.CSV_LIST_TITLE' | translate }}</p>
        <ul>
          <li>{{ 'HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.CSV_1' | translate }}</li>
          <li>{{ 'HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.CSV_2' | translate }}</li>
          <li>{{ 'HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.CSV_3' | translate }}</li>
        </ul>
      </div>
      <div class="form-row">
        <div class="form-group col-md-6">
            <vpp-management-file-uploader
              [options]="uploaderOptions"
              (uploader)="uploaderChangedCallback($event)"
              (propagateFile)="fileChangedCallback($event)"
              #fileUploader>>
            </vpp-management-file-uploader>
        </div>
        <div class="form-group col-md-6"></div>
        <div class="form-group col-md-12">
          <button [disabled]="!fileToUpload" (click)="uploadFile($event)" class="eon-button bg-eon-red">
            <span>{{ 'HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.UPLOAD_FILE' | translate }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>