<div class="container-fluid">
  <div class="row">
    <div class="col-md-8">
      <div class="page-actions">
        <ul class="list-unstyled d-flex">
          <li *ngIf="permissions.can_create_dlm_charging_sessions">
            <a href="javascript:void(0)" (click)="toggleSection($event, 'upload-dlm-charging-session-file')" class="eon-button bg-eon-limeyellow">{{ 'HISTORY.DLM_CHARGING_SESSIONS.UPLOAD' | translate }}</a>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <div [@slideRightContentAnimation]="selectedSelection == 'upload-dlm-charging-session-file' ? 'in' : 'out'">
    <vpp-upload-dlm-charging-session-file *ngIf="selectedSelection == 'upload-dlm-charging-session-file'"
      (uploaded)="fileUploadedCallback($event)"></vpp-upload-dlm-charging-session-file>
  </div>
  <section>
    <div class="row">
      <div class="col-md-12">
        <div class="container-fluid" *ngIf="permissions.can_see_dlm_charging_sessions">
          <div class="row">
            <div class="col">
              <h2>{{ 'HISTORY.DLM_CHARGING_SESSIONS.TITLE' | translate }}</h2>
              <table class="table table-auction-results table-striped table-bg-turquoise">
                <tr>
                    <th class="text-center">{{ 'HISTORY.DLM_CHARGING_SESSIONS.TABLE.TH.CUSTOMER' | translate }}</th>
                    <th class="text-center">{{ 'HISTORY.DLM_CHARGING_SESSIONS.TABLE.TH.CREATED_AT' | translate }}</th>
                    <th class="text-center">{{ 'HISTORY.DLM_CHARGING_SESSIONS.TABLE.TH.AVG_CHARGED_ENERGY' | translate }}</th>
                    <th class="text-center">{{ 'HISTORY.DLM_CHARGING_SESSIONS.TABLE.TH.AVG_CHARGED_DURATION' | translate }}</th>
                </tr>
                <tr *ngIf="showLoader">
                  <td colspan="7" class="text-center vertical-center"><vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
                  <td>
                </tr>
                <ng-container *ngFor="let dlmCs of dlmChargingSessions">
                  <tr>
                      <input type="hidden" value="{{ dlmCs.id }}">
                      <td class="text-center">{{ dlmCs.customer_id }}</td>
                      <td class="text-center">{{ dlmCs.created | localDate:'DD MMM, YYYY HH:mm:ss' }}</td>
                      <td class="text-center">{{ dlmCs.average_charged_energy_watt_hour }}</td>
                      <td class="text-center">{{ dlmCs.average_charging_duration_minutes }}</td>
                  </tr>
                </ng-container>
              </table>
              <div class="d-flex justify-content-between">
                <ngb-pagination
                  [collectionSize]="collectionSize"
                  [(page)]="page"
                  [ellipses]="true"
                  [maxSize]="5"
                  (pageChange)="pageChangedCallback($event)"
                  [pageSize]="pageSize">
                </ngb-pagination>

                <select
                  class="custom-select"
                  style="width: auto"
                  [(ngModel)]="pageSize"
                  (ngModelChange)="pageSizeChangedCallback($event)">
                  <option [ngValue]="25">25 {{ 'HISTORY.DLM_CHARGING_SESSIONS.TABLE.PER_PAGE' | translate }}</option>
                  <option [ngValue]="50">50 {{ 'HISTORY.DLM_CHARGING_SESSIONS.TABLE.PER_PAGE' | translate }}</option>
                  <option [ngValue]="75">75 {{ 'HISTORY.DLM_CHARGING_SESSIONS.TABLE.PER_PAGE' | translate }}</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
