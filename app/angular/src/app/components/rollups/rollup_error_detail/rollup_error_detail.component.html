<div class="container-fluid">
    <section *ngIf="showLoader">
        <div class="row">
            <div class="col-md-12 text-centered">
                <vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
            </div>
        </div>
    </section>

    <section *ngIf="rollupError.id">
        <div class="row">
          <div class="col-md-2">
            <a class="back-button" [routerLink]="[environment.routingPath + '/rollups/rollup-errors']"><i class="fa fa-chevron-left"></i> {{ 'BACK' | translate }}</a>
            <br>
          </div>
        </div>
        <div class="row">
            <div class="col-md-12 alerts-wrapper-static">
                <ngb-alert
                    [type]="'success'"
                    [dismissible]=""true>{{ 'HISTORY.ROLLUPS.ROLLUP_ERROR_DETAIL.ON' | translate }} {{ rollupError.formatted_date }}</ngb-alert>
            </div>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="card card-allocated">
                    <div class="card-body pre">
                        {{ rollupError.description }}
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>