import {Component, Input, OnInit, ViewEncapsulation,} from "@angular/core";
import {RollupsProvider} from "../../../providers/rollups.provider";
import {ActivatedRoute, Params} from "@angular/router";
import * as moment from "moment-timezone";
import {angularData} from "../../../../global.export";
import {TranslateService} from "@ngx-translate/core";
import {GlobalService} from "../../../services/global.service";
import { environment } from "../../../../environments/environment";

@Component({
  selector: "vpp-management-rollup-error-details",
  templateUrl: "./rollup_error_detail.component.html",
  styleUrls: ["./rollup_error_detail.component.scss"],
  encapsulation: ViewEncapsulation.None,
  animations: []
})
export class RollupErrorDetailComponent implements OnInit {
  showLoader: boolean = false;
  environment: any = environment;
  @Input('id') rollupErrorId;
  rollupError: any = {};

  constructor(
    private _Global: GlobalService,
    private _Rollups: RollupsProvider,
    private _Route: ActivatedRoute,
    public translate: TranslateService
  ) {
    this.translate.get('PAGE.CAPTIONS.HISTORY.ROLLUPS.ROLLUP_ERRORS').subscribe((res: string) => {
      this._Global.changeTitle(res);
    });
  }

  ngOnInit() {
    if (this.rollupErrorId) {
      this.getRollupError();
    }
  }

  ngAfterViewInit() {
    this._Route.params.forEach((params: Params) => {
      if (params["id"]) {
        this.rollupErrorId = params["id"];
        this.getRollupError();
      }
    });
  }

  getRollupError() {
    this.showLoader = true;
    this._Rollups.getRollupError(this.rollupErrorId).subscribe(
      (data) => {
        this.showLoader = false;
        if (data && data.rollup_error) {
          this.rollupError = data.rollup_error;
          this.rollupError.formatted_date = moment(this.rollupError.created).utc().tz(angularData.railsExports.timeZone).format('YYYY-MM-DD HH:mm');
          console.log('Got rollup error detail', this.rollupError);
        } else {
          console.log('Got rollup error detail response', data);
        }
      }
    );
  }
}