import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef, Input
} from "@angular/core";
import { RollupsProvider } from "./../../../providers/rollups.provider";
import { UserProvider } from "../../../providers/user.provider";
import { environment } from "../../../../environments/environment";
import * as moment from "moment-timezone";
import { AssetProvider } from "../../../providers/asset.provider";
import { DispatchGroupProvider } from "../../../providers/dispatch_group.provider";
import { angularData } from "../../../../global.export";
import { TranslateService } from '@ngx-translate/core';
import {Router} from "@angular/router";
import {GlobalService} from "../../../services/global.service";

@Component({
  selector: "vpp-management-rollup-errors",
  templateUrl: "./rollup_errors.component.html",
  styleUrls: ["./rollup_errors.component.scss"],
  encapsulation: ViewEncapsulation.None
})
export class RollupErrorsComponent implements OnInit {
  environment = environment;
  showLoader: boolean = false;

  page = 1;
  pageSize = 25;
  collectionSize = 0;

  params = {
    page: this.page,
    per_page: this.pageSize,
    start_date: '',
    end_date: ''
  };
  selectedDate = {
    startDateTime: '',
    endDateTime: ''
  };
  intervalModel: any;

  rollupErrors: any[] = [];


  constructor(
    private _Rollups: RollupsProvider,
    public translate: TranslateService,
    private _Router: Router,
    private _Global: GlobalService
  ) {
    this.translate.get('PAGE.CAPTIONS.HISTORY.ROLLUPS.ROLLUP_ERRORS').subscribe((res: string) => {
      this._Global.changeTitle(res);
    });
  }

  ngOnInit() {
    this.getRollupErrors();
  }

  getRollupErrors() {
    this.showLoader = true;
    this.rollupErrors = [];
    this._Rollups.getRollupErrors(this.params).subscribe(
      result => {
        this.showLoader = false;
        this.rollupErrors = result.rollup_errors.map((e) => {
          e.formatted_date = moment(e.created).utc().tz(angularData.railsExports.timeZone).format('YYYY-MM-DD HH:mm');
          return e;
        });
        this.collectionSize = result.total_entries;
        this.page = this.params.page = result.page;
        console.log('Loaded rollup errors', result);
      },
      error => {
        this.showLoader = false;
        console.error('Failed to load rollup errors', error);
      }
    );
  }

  intervalChangedCallback(date) {
    console.log('Interval changed', date);
    if (date.value[0] && date.value[1]) {
      this.selectedDate = {
        startDateTime: date.value[0].startOf('minute').toISOString(),
        endDateTime: date.value[1].startOf('minute').toISOString()
      };
      this.params.start_date = this.selectedDate.startDateTime;
      this.params.end_date = this.selectedDate.endDateTime;
    } else {
      this.params.start_date = '';
      this.params.end_date = '';
    }
    this.getRollupErrors();
  }

  clearIntervalValue() {
    this.intervalModel = null;
    this.params.start_date = '';
    this.params.end_date = '';
    this.getRollupErrors();
  }

  pageChangedCallback(page) {
    console.log('Page changed');
    this.params.page = page;
    this.getRollupErrors();
  }

  pageSizeChangedCallback(pageSize) {
    console.log('Page size changed');
    this.params.per_page = pageSize;
    this.params.page = 1;
    this.getRollupErrors();
  }
}
