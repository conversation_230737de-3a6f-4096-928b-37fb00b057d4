<div class="table-filter">
    <div class="d-flex flex-row">
        <div class="form-group col-md-4">
            <label>{{ 'HISTORY.ROLLUPS.ERRORS.FILTER.TIME_DATE' | translate }}</label>
            <div class="input-group">
                <input
                        type="text"
                        class="form-control"
                        name="intervalFilter"
                        [owlDateTime]="dt1"
                        [selectMode]="'range'"
                        [(ngModel)]="intervalModel"
                        [owlDateTimeTrigger]="dt1"
                        (dateTimeChange)="intervalChangedCallback($event)"
                        placeholder="">
                <div class="input-group-append">
                    <button class="btn btn-default" (click)="clearIntervalValue()">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <owl-date-time
                  #dt1
                  [pickerMode]="'popup'">
                </owl-date-time>
            </div>
        </div>
    </div>
</div>

<table class="table table-auction-results table-striped table-bg-turquoise">
    <tr>
        <th class="text-center">{{ 'HISTORY.ROLLUPS.ERRORS.TABLE.TIME_DATE' | translate }}</th>
        <th class="text-center">{{ 'HISTORY.ROLLUPS.ERRORS.TABLE.DESCRIPTION' | translate }}</th>
        <th class="text-center"></th>
    </tr>
    <tr *ngIf="showLoader">
        <td colspan="2" class="text-center vertical-center">
            <vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
        <td>
    </tr>
    <ng-container *ngFor="let e of rollupErrors">
        <tr>
            <td class="text-center">{{ e.formatted_date }}</td>
            <td class="">{{ e.description }} </td>
            <td class="text-center"><a [routerLink]="[environment.routingPath + '/rollups/rollup-error-detail/' + e.id ]"><i class="fa fa-eye"></i></a></td>
        </tr>
    </ng-container>
</table>

<div class="d-flex justify-content-between">
    <ngb-pagination
            [collectionSize]="collectionSize"
            [(page)]="page"
            [ellipses]="true"
            [maxSize]="5"
            (pageChange)="pageChangedCallback($event)"
            [pageSize]="pageSize">
    </ngb-pagination>

    <select class="custom-select" style="width: auto"
            [(ngModel)]="pageSize"
            (ngModelChange)="pageSizeChangedCallback($event)">
        <option [ngValue]="25">25 {{ 'HISTORY.ROLLUPS.ERRORS.TABLE.PER_PAGE' | translate }}</option>
        <option [ngValue]="50">50 {{ 'HISTORY.ROLLUPS.ERRORS.TABLE.PER_PAGE' | translate }}</option>
        <option [ngValue]="75">75 {{ 'HISTORY.ROLLUPS.ERRORS.TABLE.PER_PAGE' | translate }}</option>
    </select>
</div>