import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { Location } from "@angular/common";
import { GlobalService } from "./../../services/global.service";
import { SlideRightContentAnimation } from './../../animations/slide-right-content';
import { environment } from "../../../environments/environment";
import { angularData } from "./../../../global.export";
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: "vpp-management-rollups",
  templateUrl: "./rollups.component.html",
  styleUrls: ["./rollups.component.scss"],
  encapsulation: ViewEncapsulation.None,
  animations: [ SlideRightContentAnimation ],
})

export class RollupsComponent implements OnInit {
  selectedTab: string = 'asset-rollups';
  permissions: any;

  constructor(
    private _Global: GlobalService,
    private route: ActivatedRoute,
    private _Location: Location,
    public translate: TranslateService
  ) {
    this._Global.changeTitle("Rollups");
    this.translate.get('PAGE.CAPTIONS.HISTORY.ROLLUPS').subscribe((res: string) => {
      this._Global.changeTitle(res);
    });
    this.permissions = angularData.permissions;
  }

  ngOnInit() {}

  ngAfterViewInit() {
    this.route.params.forEach((params: Params) => {
      if (params["tab"]) {
        this.selectTab(null, params["tab"]);
      } else {
        this.selectTab(null, this.selectedTab);
      }
    });
  }

  selectTab(event, tab) {
    this.selectedTab = tab;
    this._Location.replaceState(`${environment.routingPath}/rollups/${tab}`);
  }
}