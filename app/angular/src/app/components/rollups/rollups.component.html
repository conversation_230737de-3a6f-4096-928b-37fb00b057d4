<section>
    <div class="container-fluid">
        <div id="ngb-tabset">
            <ul role="tablist" class="nav nav-tabs justify-content-start">
                <li class="nav-item" *ngIf="permissions.can_see_rollups">
                    <a (click)="selectTab($event, 'asset-rollups')" class="nav-link"
                       [ngClass]="selectedTab == 'asset-rollups' ? 'active' : ''"
                       role="tab" id="asset-rollups">{{ 'HISTORY.ROLLUPS.TABS.ASSET_ROLLUPS' | translate }}</a>
                </li>
                <li class="nav-item" *ngIf="permissions.can_see_rollups">
                    <a (click)="selectTab($event, 'dispatch-group-rollups')" class="nav-link"
                       [ngClass]="selectedTab == 'dispatch-group-rollups' ? 'active' : ''"
                       role="tab" id="dispatch-group-rollups">{{ 'HISTORY.ROLLUPS.TABS.DISPATCH_GROUP_ROLLUPS' | translate }}</a>
                </li>
                <li class="nav-item" *ngIf="permissions.can_see_rollups">
                    <a (click)="selectTab($event, 'rollup-errors')" class="nav-link"
                       [ngClass]="selectedTab == 'rollup-errors' ? 'active' : ''"
                       role="tab" id="rollup-errors">{{ 'HISTORY.ROLLUPS.TABS.ROLLUP_ERRORS' | translate }}</a>
                </li>
            </ul>

            <div class="tab-content">
                <div [@slideRightContentAnimation]="selectedTab == 'asset-rollups' ? 'in' : 'out'" *ngIf="permissions.can_see_rollups">
                    <vpp-management-rollup-history
                            entityType="Asset"
                            *ngIf="selectedTab == 'asset-rollups'"></vpp-management-rollup-history>
                </div>
                <div [@slideRightContentAnimation]="selectedTab == 'dispatch-group-rollups' ? 'in' : 'out'" *ngIf="permissions.can_see_rollups">
                    <vpp-management-rollup-history
                            entityType="DispatchGroup"
                            *ngIf="selectedTab == 'dispatch-group-rollups'"></vpp-management-rollup-history>
                </div>
                <div [@slideRightContentAnimation]="selectedTab == 'rollup-errors' ? 'in' : 'out'" *ngIf="permissions.can_see_rollups">
                    <vpp-management-rollup-errors
                            *ngIf="selectedTab == 'rollup-errors'"></vpp-management-rollup-errors>
                </div>
            </div>
        </div>
    </div>
</section>