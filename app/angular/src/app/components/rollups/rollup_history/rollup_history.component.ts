import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef, Input
} from "@angular/core";
import {RollupsProvider} from "./../../../providers/rollups.provider";
import {UserProvider} from "../../../providers/user.provider";
import {environment} from "../../../../environments/environment";
import * as moment from "moment-timezone";
import {AssetProvider} from "../../../providers/asset.provider";
import {DispatchGroupProvider} from "../../../providers/dispatch_group.provider";
import {TranslateService} from '@ngx-translate/core';
import {GlobalService} from "./../../../services/global.service";
import { IDropdownSettings } from 'ng-multiselect-dropdown';
import { angularData } from "./../../../../global.export";

@Component({
  selector: "vpp-management-rollup-history",
  templateUrl: "./rollup_history.component.html",
  styleUrls: ["./rollup_history.component.scss"],
  encapsulation: ViewEncapsulation.None
})
export class RollupHistoryComponent implements OnInit {
  environment = environment;
  page = 1;
  pageSize = 25;
  collectionSize = 0;
  params = {
    entity_type: '',
    page: this.page,
    per_page: this.pageSize,
    start_date: '',
    end_date: '',
    status: '',
    rollup_metrics: null,
    triggered_by: null,
    entity_ids: '',
    rollup_type_ids: '',
    triggered_by_user_ids: ''
  };
  selectedDate = {
    startDateTime: '',
    endDateTime: ''
  };
  intervalModel: any;
  showLoader: boolean = false;
  rollups;
  statuses = [
    '',
    'scheduled',
    'completed',
    'processing'
  ];
  entities: any[] = [];
  selectedEntities: any = [];
  rollupTypes: any[] = [];
  selectedRollupTypes: any = [];
  users: any[] = [];
  selectedTriggeredByUsers: any[] = [];

  dropdownSettings = {
    singleSelection: false,
    idField: 'id',
    textField: 'display_name',
    selectAllText: angularData.railsExports.locale  == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
    unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
    searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
    noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
    itemsShowLimit: 10,
    allowSearchFilter: true
  };

  readonly ASSET = 'Asset';
  readonly DISPATCH_GROUP = 'DispatchGroup';

  @Input()
  entityType: string;


  constructor(
    private _Global: GlobalService,
    private _Rollups: RollupsProvider,
    private _Asset: AssetProvider,
    private _DispatchGroup: DispatchGroupProvider,
    private _User: UserProvider,
    public translate: TranslateService
  ) {
    this.translate.get('PAGE.CAPTIONS.HISTORY.ROLLUPS.ASSET_ROLLUPS').subscribe((res: string) => {
        this._Global.changeTitle(res);
    });
  }

  ngOnInit() {
    console.log('Initializing rollups for', this.entityType);

    this.params.entity_type = this.entityType;

    this.getRollups();
    this.getEntities();
    this.getRollupTypes();
    this.getUsers();

    let captionKey =
        'PAGE.CAPTIONS.HISTORY.ROLLUPS.' +
        (this.entityType == 'Asset' ? 'ASSET_ROLLUPS' : 'DISPATCH_GROUP_ROLLUPS');
    this.translate.get(captionKey).subscribe((res: string) => {
      this._Global.changeTitle(res);
    });
  }

  getEntities() {
    let observable;
    switch (this.entityType) {
      case this.ASSET:
        observable = this._Asset.findAllWithPagination();
        break;
      case this.DISPATCH_GROUP:
        observable = this._DispatchGroup.findAllWithPagination();
        break;
      default:
        console.error('Unexpected entity type', this.entityType);
        break;
    }
    observable.subscribe(
      result => {
        this.entities = this.entityType == this.ASSET ? result.assets : result.dispatch_groups;
        this.entities = this.entities.map((x) => {
          return {
            id: x.id,
            display_name: `#${x.id} ${x.name}`
          };
        });
      },
      error => {
        console.error('Failed to load the collection of', this.entityType, error);
      });
  }

  getRollupTypes() {
    this._Rollups.getRollupTypes({entity_type: this.params.entity_type}).subscribe(
      result => {
        this.rollupTypes = result;
        this.rollupTypes = this.rollupTypes.map((x) => {
          return {
            id: x.id,
            display_name: x.name
          };
        });
      },
      error => {
        console.error('Failed to load rollup types', error);
      });
  }

  getUsers() {
    this._User.getUsersAsIdNameEmail().subscribe(
      result => {
        this.users = result;
        this.users = this.users.map((x) => {
          return {
            id: x.id,
            display_name: `${x.name} <${x.email}>`
          };
        });
      },
      error => {
        console.error('Failed to load users', error);
      });
  }

  intervalChangedCallback(date) {
    console.log('Interval changed');
    if (date.value[0] && date.value[1]) {
      this.selectedDate = {
        startDateTime: date.value[0].startOf('minute').toISOString(),
        endDateTime: date.value[1].startOf('minute').toISOString()
      };
      this.params.start_date = this.selectedDate.startDateTime;
      this.params.end_date = this.selectedDate.endDateTime;
    } else {
      this.params.start_date = '';
      this.params.end_date = '';
    }
    this.getRollups();
  }

  clearIntervalValue() {
    this.params.start_date = '';
    this.params.end_date = '';
    this.intervalModel = null;
    this.getRollups();
  }

  filterChangedCallback() {
    console.log('Filter changed');
    this.getRollups();
  }

  selectedEntitiesChanged(values) {
    console.log('Selected entities changed');
    if (values) {
      this.params.entity_ids = this.selectedEntities ? this.selectedEntities.map(x => x.id).join(',') : '';
    } else {
      this.params.entity_ids = '';
    }
    this.getRollups();
  }

  selectedRollupTypesChanged(values) {
    console.log('Selected rollup types changed');
    if (values) {
      this.params.rollup_type_ids = this.selectedRollupTypes ? this.selectedRollupTypes.map(x => x.id).join(',') : '';
    } else {
      this.params.rollup_type_ids = '';
    }
    this.getRollups();
  }

  selectedTriggeredByUsersChanged(values) {
    console.log('Selected triggered by users changed');
    if (values) {
      this.params.triggered_by_user_ids = this.selectedTriggeredByUsers ? this.selectedTriggeredByUsers.map(x => x.id).join(',') : '';
    } else {
      this.params.triggered_by_user_ids = '';
    }
    this.getRollups();
  }

  getRollups() {
    this.showLoader = true;
    this.rollups = [];
    this._Rollups.getRollups(this.params).subscribe(
      results => {
        this.showLoader = false;
        this.rollups = results.data;
        this.collectionSize = results.total_entries;
      },
      error => {
        this.showLoader = false;
        console.log('Failed to get rollups', error);
      }
    );
  }

  pageChangedCallback(page) {
    console.log('Page changed');
    this.params.page = page;
    this.getRollups();
  }

  pageSizeChangedCallback(pageSize) {
    console.log('Page size changed');
    this.params.per_page = pageSize;
    this.params.page = 1;
    this.getRollups();
  }
}
