<div class="table-filter">
    <div class="d-flex flex-row">
        <div class="form-group col-md-4">
            <label>{{ 'HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.INTERVAL' | translate }}</label>
            <div class="input-group">
                <input
                        type="text"
                        class="form-control"
                        name="intervalFilter"
                        [owlDateTime]="dt1"
                        [selectMode]="'range'"
                        [(ngModel)]="intervalModel"
                        [owlDateTimeTrigger]="dt1"
                        (dateTimeChange)="intervalChangedCallback($event)"
                        placeholder="">
                <div class="input-group-append">
                    <button class="btn btn-default" (click)="clearIntervalValue()">
                        <i class="fa fa-times"></i>
                    </button>
                </div>
                <owl-date-time
                    #dt1
                    [pickerMode]="'popup'">
                </owl-date-time>
            </div>
        </div>
        <div class="form-group col-md-2">
            <label>{{ 'HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.STATUS' | translate }}</label>
            <select
                    class="form-control"
                    name="statusFilter"
                    [(ngModel)]="params.status"
                    (ngModelChange)="filterChangedCallback()"
            >
                <option [ngValue]="s"
                        *ngFor="let s of statuses">{{ 'HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.STATUS.' + s | uppercase | translate }}</option>
            </select>
        </div>
        <div class="form-group col-md-6">
            <label *ngIf="entityType == 'Asset'">{{ 'HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.ASSET_ROLLUP_GROUPINGS' | translate }}</label>
            <label *ngIf="entityType != 'Asset'">{{ 'HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.DISPATCH_GROUPS' | translate }}</label>
            <ng-multiselect-dropdown
                    [placeholder]="('HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.ASSET_ROLLUP_GROUPINGS' | translate )"
                    [data]="entities"
                    [(ngModel)]="selectedEntities"
                    (ngModelChange)="selectedEntitiesChanged($event)"
                    [settings]="dropdownSettings"
            ></ng-multiselect-dropdown>
        </div>
    </div>

    <div class="d-flex flex-row">
        <div class="form-group col-md-6">
            <label>{{ 'HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.ROLLUP_METRICS' | translate }}</label>
            <ng-multiselect-dropdown
                    [settings]="dropdownSettings"
                    [placeholder]="('COMPONENTS.SELECT' | translate )"
                    [data]="rollupTypes"
                    [(ngModel)]="selectedRollupTypes"
                    (ngModelChange)="selectedRollupTypesChanged($event)"
            ></ng-multiselect-dropdown>
        </div>
        <div class="form-group col-md-4">
            <label>{{ 'HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.TRIGGERED_BY' | translate }}</label>
            <ng-multiselect-dropdown
                    [settings]="dropdownSettings"
                    [placeholder]="('COMPONENTS.SELECT' | translate )"
                    [data]="users"
                    [(ngModel)]="selectedTriggeredByUsers"
                    (ngModelChange)="selectedTriggeredByUsersChanged($event)"
            ></ng-multiselect-dropdown>
        </div>
    </div>
</div>

<table class="table table-auction-results table-striped table-bg-turquoise">
    <tr>
        <th class="text-center">{{ 'HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.INTERVAL' | translate }}</th>
        <th class="text-center">{{ 'HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.STATUS' | translate }}</th>
        <th class="text-center">
            <span *ngIf="entityType == 'Asset'">{{ 'HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.ASSET_ROLLUP_GROUPINGS' | translate }}</span>
            <span *ngIf="entityType != 'Asset'">{{ 'HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.DISPATCH_GROUPS' | translate }}</span>
        </th>
        <th class="text-center">{{ 'HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.ROLLUP_METRICS' | translate }}</th>
        <th class="text-center">{{ 'HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.TRIGGERED_BY' | translate }}</th>
    </tr>
    <tr *ngIf="showLoader">
        <td colspan="5" class="text-center vertical-center">
            <vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
        <td>
    </tr>
    <ng-container *ngFor="let r of rollups">
        <tr>
            <td class="text-center">{{ r.from_time }} - {{ r.to_time }}</td>
            <td class="text-center">{{ 'HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.STATUS.' + r.status | uppercase | translate }}</td>

            <td
                    *ngIf="entityType == 'Asset'"
                    class="text-center">{{ r.grouping }}</td>

            <td
                    *ngIf="entityType != 'Asset'"
                    class="text-center">
                <ng-template ngFor let-dg [ngForOf]="r.dispatch_groups" let-isLast="last">
                    <a *ngIf="!dg.deleted"
                       href="{{ environment.routingPath + '/market_configuration_ribbon/dispatch-groups/' + dg.id }}">
                        {{ dg.name }}
                    </a>
                    <span *ngIf="dg.deleted"><del>{{ dg.name }}</del></span>
                    <br *ngIf="!isLast">
                </ng-template>
            </td>

            <td class="text-center">{{ r.rollup_metrics }}</td>
            <td class="text-center">{{ r.user_name }}<br>{{ r.user_email }}</td>
        </tr>
    </ng-container>
</table>
<div class="d-flex justify-content-between">
    <ngb-pagination
            [collectionSize]="collectionSize"
            [(page)]="page"
            [ellipses]="true"
            [maxSize]="5"
            (pageChange)="pageChangedCallback($event)"
            [pageSize]="pageSize">
    </ngb-pagination>

    <select class="custom-select" style="width: auto"
            [(ngModel)]="pageSize"
            (ngModelChange)="pageSizeChangedCallback($event)">
        <option [ngValue]="25">25 {{ 'HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.PER_PAGE' | translate }}</option>
        <option [ngValue]="50">50 {{ 'HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.PER_PAGE' | translate }}</option>
        <option [ngValue]="75">75 {{ 'HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.PER_PAGE' | translate }}</option>
    </select>
</div>
