@import "./../../../../styles/colors";

vpp-bids-list {
  .hidden {
    display: none;
  }

  .table-filter {

    .form-group {
      float: left;
      min-width: 250px;
      width: auto;
    }
    .form-check {
      float: left;
      padding-top: 36px;
      padding-right: 5px;
    }
    .auction-closed-text {
      color: red;
      align-self: flex-end;
    }
  }
  .new-row  {
    display: block;
    width: 100%;
    float: left;
  }
  .auction-closed {
    text-align: center;
    height: 52px;
    border: 0;
    padding: 0 26px;
    display: inline-block;
    width: max-content;
    box-shadow: none;
    font-weight: bold;
    font-size: 18px;
    color: #ffffff;
    background-color: transparent;
    border: 2px solid #B00402 !important;
    color: #B00402;
    line-height: 52px;
    border-radius: 16px;
  }

  .multiselect-dropdown {
    .dropdown-btn {
      border: 2px solid $eon-turquoise !important;
      background: #fff !important;
      border-radius: 3px !important;
      min-height: 52px !important;
    }
  }

  .min-width-1035 {
    min-width: 1035px;
  }

}