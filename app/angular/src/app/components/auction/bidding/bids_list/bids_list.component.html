<div class="container-fluid">
  <div class="alerts-wrapper-static" *ngIf="notifications.length">
      <ng-container *ngFor="let message of notifications; let ix = index;">
          <ngb-alert
                  [dismissible]="false"
                  [type]="message.type">
              <div [innerHTML]="message.text | translate:message.params"></div>
          </ngb-alert>
      </ng-container>
  </div>
  <div class="row min-width-1035">
      <div class="col">
          <h2 style="position: relative; z-index: -1">{{ 'BIDDING.LIST.TITLE' | translate }}</h2>
          <div class="table-filter justify-content-between">
            <div class="form-group">
              <label>{{ 'BIDDING.LIST.FILTERS.DELIVERY_DATE' | translate }}</label>
              <input
                type="text"
                class="form-control"
                [owlDateTime]="dt1"
                [selectMode]="'range'"
                [(ngModel)]="selectedDatesModel"
                [owlDateTimeTrigger]="dt1"
                (dateTimeChange)="startDateSelectedCallback($event)"
                placeholder="">
              <owl-date-time
                #dt1
                (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
                [pickerType]="'calendar'"
                [pickerMode]="'popup'"></owl-date-time>
            </div>
            <div class="form-group">
               <label for="inputAuction">{{ 'BIDDING.LIST.FILTERS.AUCTION' | translate }}</label>
                <ng-multiselect-dropdown
                  id="inputAuction"
                  [placeholder]="('COMPONENTS.SELECT' | translate )"
                  [data]="auctionConfigs"
                  [(ngModel)]="selectedAuction"
                  [settings]="dropdownSettings(true, true)"
                  (onSelect)="selectedAuctionConfigChanged()"
                  (onDeSelect)="selectedAuctionConfigChanged()">
              </ng-multiselect-dropdown>
            </div>
            <div class="form-group">
                 <label for="inputAsset">{{ 'BIDDING.LIST.FILTERS.ASSET' | translate }}</label>
                  <ng-multiselect-dropdown
                    id="inputAsset"
                    [placeholder]="('COMPONENTS.SELECT' | translate )"
                    [data]="assets"
                    [(ngModel)]="selectedAssets"
                    [settings]="dropdownSettings(false, true)"
                    (onSelect)="selectedAssetsChanged($event)"
                    (onDeSelect)="selectedAssetsChanged($event)"
                    (onSelectAll)="selectedAllAssetsChanged($event)"
                    (onDeSelectAll)="selectedAllAssetsChanged($event)"
                  >
                </ng-multiselect-dropdown>
            </div>
            <div class="form-check" style="padding-right: 10px;">
              <label class="eon-checkbox-label bg-eon-red" style="white-space: nowrap;" [ngClass]="onlyBidsWithoutAsset == true ? 'checked' : ''" (click)="toggleOnlyBidsWithoutAssetCheckbox($event)">
                {{ 'BIDDING.LIST.FILTERS.ONLY_BIDS_WITHOUT_ASSET' | translate }}
              </label>
            </div>
            <div class="form-group" style="min-width: 200px; text-align: center;" *ngIf="selectedAuction && selectedAuction.length && bids.length">
              <label>&nbsp;</label>
              <button class="eon-button bg-eon-turquoise" (click)="downloadAllBids()">
                <span>{{ 'BIDDING.LIST.LINKS.DOWNLOAD_ALL' | translate }}</span>
              </button>
            </div>
            <div class="form-group" style="min-width: 200px;" *ngIf="!auctionClosed && selectedAuction && selectedAuction.length && (selectedAssets.length || onlyBidsWithoutAsset) && bids.length">
              <label>&nbsp;</label>
              <button class="eon-button bg-eon-bordeaux" (click)="deleteBids()">
                <span>{{ 'BIDDING.LIST.LINKS.DELETE_BIDS' | translate }}</span>
              </button>
            </div>
            <div class="align-self-center" style="text-decoration:underline">
              <div class="hidden">
                <form
                  name="downloadBids"
                  #downloadBids
                  action="{{ downloadAuctionBidsUrl }}"
                  method="get"
                  target="_blank">
                  <input type="hidden" name="authenticity_token" value="{{ csr_token }}">
                  <input type="hidden" name="auction_config_id" value="{{params.auction_config_id}}"/>
                  <ng-container *ngFor="let asset_id of params.asset_ids; let ix = index;">
                    <input type="hidden" name="asset_ids[]" value="{{asset_id}}"/>
                  </ng-container>
                  <input type="hidden" name="start_delivery_date" value="{{params.start_delivery_date}}"/>
                  <input type="hidden" name="end_delivery_date" value="{{params.end_delivery_date}}"/>
                  <input type="hidden" name="only_bids_without_asset" value="{{params.only_bids_without_asset}}"/>
                  <button type="submit">{{ 'BIDDING.LIST.LINKS.EXPORT' | translate }}</button>
                </form>
              </div>
            </div>
            <div
              *ngIf="auctionClosed"
              class="form-group">
              <label>&nbsp;</label>
              <span class="auction-closed">{{ 'BIDDING.LIST.AUCTION_CLOSED' | translate }}</span>
            </div>
            <div class="new-row align-items-center" *ngIf="lastUploadTimestamp">
              <p style="margin-bottom:0;margin-right:15px;">{{ 'BIDDING.LIST.LAST_UPLOADED_FILE' | translate }}: <strong>{{ lastUploadTimestamp | localDate:'MM/DD/YYYY HH:mm' }} {{ 'BIDDING.LIST.BY' | translate }} {{ lastUploadUser.name }}</strong></p>
            </div>
            <div class="new-row align-items-center" *ngIf="deliveryInterval">
              <p style="margin-bottom:0;margin-right:15px;">{{ 'BIDDING.LIST.TENDER_DELIVERY_INTERVAL' | translate }}: <strong>{{ deliveryInterval.start }} - {{ deliveryInterval.end}}</strong></p>
            </div>
          </div>
          <table class="table table-auction-results table-striped table-bg-turquoise">
              <tr>
                  <th class="text-center">{{ 'BIDDING.LIST.TABLE.AUCTION' | translate }}</th>
                  <th class="text-center">{{ 'BIDDING.LIST.TABLE.DELIVERY_START' | translate }}</th>
                  <th class="text-center">{{ 'BIDDING.LIST.TABLE.PRODUCT' | translate }}</th>
                  <th class="text-center">{{ 'BIDDING.LIST.TABLE.TSO' | translate }}</th>
                  <th class="text-center">{{ 'BIDDING.LIST.TABLE.CAPACITY_PRICE' | translate }}</th>
                  <th class="text-center">{{ 'BIDDING.LIST.TABLE.ENERGY_PRICE' | translate }}</th>
                  <th class="text-center">{{ 'BIDDING.LIST.TABLE.CAPACITY' | translate }}</th>
                  <th class="text-center">{{ 'BIDDING.LIST.TABLE.ASSET_ID' | translate }}</th>
                  <th class="text-center">{{ 'BIDDING.LIST.TABLE.ASSET_NAME' | translate }}</th>
                  <th class="text-center">{{ 'BIDDING.LIST.TABLE.ASSET_EXTERNAL_ID' | translate }}</th>
              </tr>
              <tr *ngIf="showLoader">
                <td colspan="7" class="text-center vertical-center"><vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
                <td>
              </tr>
              <ng-container *ngIf="!bids.length && !showLoader">
                <tr>
                  <td colspan="7">{{ 'BIDDING.LIST.TABLE.NO_BIDS_UPLOADED' | translate }}</td>
                </tr>
              </ng-container>
              <ng-container *ngIf="bids.length">
                <tr *ngFor="let b of paginatedBids()">
                    <td class="text-center">{{ b.auction_config_name }}</td>
                    <td class="text-center">{{ b.start_time_in_tz }}</td>
                    <td class="text-center">{{ b.product_with_minutes }}</td>
                    <td class="text-center">{{ b.tso_name }}</td>
                    <td class="text-center">{{ b.capacity_price }}</td>
                    <td class="text-center">{{ b.energy_price }}</td>
                    <td class="text-center">{{ b.flex_volume | megawatt }}</td>
                    <td class="text-center">{{ b.asset_id }}</td>
                    <td class="text-center">{{ b.asset_name }}</td>
                    <td class="text-center">{{ b.asset_external_id }}</td>
                </tr>
              </ng-container>
          </table>
          <div class="d-flex justify-content-between">
            <ngb-pagination
                [collectionSize]="collectionSize"
                [(page)]="page"
                [ellipses]="true"
                [maxSize]="5"
                [pageSize]="pageSize">
              </ngb-pagination>

            <select
              class="custom-select"
              style="width: auto"
              [(ngModel)]="pageSize">
              <option [ngValue]="10">10 {{ 'BIDDING.LIST.TABLE.PER_PAGE' | translate }}</option>
              <option [ngValue]="20">20 {{ 'BIDDING.LIST.TABLE.PER_PAGE' | translate }}</option>
              <option [ngValue]="50">50 {{ 'BIDDING.LIST.TABLE.PER_PAGE' | translate }}</option>
            </select>
          </div>
      </div>
  </div>
</div>