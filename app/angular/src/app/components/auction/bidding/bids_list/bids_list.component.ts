import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef,
  Output,
  EventEmitter
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { UrlProvider } from "./../../../../providers/url.provider";
import { HttpClientService } from "./../../../../services/httpclient.service";
import { NotificationService } from "./../../../../services/notification.service";
import { BidProvider } from  "./../../../../providers/bid.provider";
import { TranslateService } from '@ngx-translate/core';
import * as moment from "moment-timezone";
import {Moment} from "moment-timezone/moment-timezone";
import { GlobalService } from "./../../../../services/global.service";
import {AuctionsProvider} from "../../../../providers/auctions.provider";
import {AssetProvider} from "../../../../providers/asset.provider";
import {angularData} from "../../../../../global.export";

@Component({
  selector: "vpp-bids-list",
  templateUrl: "./bids_list.component.html",
  styleUrls: ["./bids_list.component.scss"],
  encapsulation: ViewEncapsulation.None
})
export class BidsListComponent implements OnInit {
  selectedAuction: any;
  auctionConfigs: any = [];
  selectedAssets: any = [];
  assets: any = [];
  bids: any = [];
  onlyBidsWithoutAsset: boolean = false;
  auctionClosed: boolean = null;
  deliveryInterval: any;
  lastUploadUser: any;
  lastUploadTimestamp: any;
  showLoader: boolean = false;
  collectionSize = 0;
  page = 1;
  pageSize = 20;
  params = {
    auction_config_id: '',
    asset_ids: [],
    start_delivery_date: '',
    end_delivery_date: '',
    only_bids_without_asset: false,
  };
  selectedDatesModel = [
    moment().tz(angularData.railsExports.timeZone).startOf('day').add(1, 'day'),
    moment().tz(angularData.railsExports.timeZone).startOf('day').add(1, 'day')
  ];
  csr_token: string;
  downloadAuctionBidsUrl = this._UrlProvider.getDownloadBidsUrl();
  notifications: any = [];
  @ViewChild("downloadBids", { static: true }) downloadBids: any;

  constructor(
    private _BidProvider: BidProvider,
    private _UrlProvider: UrlProvider,
    private _auctionsProvider: AuctionsProvider,
    private _assetProvider: AssetProvider,
    public translate: TranslateService,
    private _HttpClientService: HttpClientService,
    public globalService: GlobalService
  ) {
    this.csr_token = this._HttpClientService.getAuthToken();
    this.downloadAuctionBidsUrl = this._UrlProvider.getDownloadBidsUrl();
  }

  ngOnInit() {
    this.params.start_delivery_date = this.formatDeliveryDate(moment().tz(angularData.railsExports.timeZone).startOf('day').add(1, 'day'))
    this.params.end_delivery_date   = this.formatDeliveryDate(moment().tz(angularData.railsExports.timeZone).startOf('day').add(1, 'day'))
    this._auctionsProvider.findAll().subscribe(
      res => {
        this.auctionConfigs = res.auctions;
        if (this.auctionConfigs.length > 0) {
          this.selectedAuction = [this.auctionConfigs[0]];
          this.params.auction_config_id = this.selectedAuction[0].id;
        }
        else {
          this.params.auction_config_id = null;
        }
        console.log("### LIST AUCTIONS CONFIG", this.auctionConfigs, this.selectedAuction)
        this.getBids();
      }, err => {
        console.log('Failed to load auction configs', err);
      }
    );
    this.getAssets();
  }

  getAssets() {
    this._assetProvider.findAllFromNominationBids().subscribe(
    result => {
        this.assets = result.assets;
    }, err => {
        console.error('Failed to load asset list', err);
    });
  }

  getBids() {
    this.showLoader = true;
    this._BidProvider.getEonNominationBids(this.params).subscribe(
      (bids) => {
        this.showLoader = false;
        this.bids = bids.nomination_bids;
        this.collectionSize = this.bids.length ? this.bids.length : 0;
        this.auctionClosed = bids.is_auction_closed;
        this.lastUploadTimestamp = bids.last_upload_timestamp;
        this.lastUploadUser = bids.last_upload_user;
        this.deliveryInterval = bids.delivery_interval
      },
      error => {
        this.showLoader = false;
      }
    )
  }

  paginatedBids() {
    return this.bids
      .map((bid, i) => ({ id: i + 1, ...bid }))
      .slice(
        (this.page - 1) * this.pageSize,
        (this.page - 1) * this.pageSize + this.pageSize
      );
  }

  selectedAuctionConfigChanged() {
    this.notifications = [];
    if (this.selectedAuction && this.selectedAuction.length > 0) {
      this.params.auction_config_id = this.selectedAuction[0].id;
    } else {
      this.params.auction_config_id = null;
    }
    this.getBids();
  }

  selectedAssetsChanged(event) {
    this.notifications = [];
    if (this.selectedAssets && this.selectedAssets.length > 0) {
      this.params.asset_ids = this.selectedAssets.map(x => x.id);
      this.onlyBidsWithoutAsset = false
      this.params.only_bids_without_asset = false
    } else {
      this.params.asset_ids = [];
    }
    console.log("#### SELECTED ASSETS:", this.params.asset_ids)
    this.getBids();
  }

  selectedAllAssetsChanged(event) {
    this.notifications = [];
    if (event && event.length > 0) {
      this.params.asset_ids = event.map(x => x.id);
      this.onlyBidsWithoutAsset = false
      this.params.only_bids_without_asset = false
    } else {
      this.params.asset_ids = [];
    }
    console.log("#### SELECTED ASSETS:", this.params.asset_ids)
    this.getBids();
  }

  startDateSelectedCallback(date) {
    console.log('date selected', date);
    if (date.value[0] && date.value[1]) {
      this.params.start_delivery_date = this.formatDeliveryDate(date.value[0].startOf('day'))
      this.params.end_delivery_date = this.formatDeliveryDate(date.value[1].startOf('day'))
    } else {
      this.params.start_delivery_date = '';
      this.params.end_delivery_date = '';
      this.selectedDatesModel = [null, null];
    }
    this.getBids();
  }

  downloadAllBids() {
    setTimeout(() => {
      this.downloadBids.nativeElement.submit();
    });
  }

  deleteBids() {
    this._BidProvider.deleteBids(this.params).subscribe(
      (result) => {
        this.showLoader = false;
        this.notifications = [];
        this.bids = [];
        this.collectionSize = 0;
        this.auctionClosed = result.is_auction_closed;
        this.lastUploadTimestamp = null;
        this.lastUploadUser = null;
        this.notifications.push({text: result.message, type: (result.success ? 'success' : 'error')})
        this.getBids();
    })
  }

  selectAuctionAndDate(startDate: Moment, endDate: Moment, auctionConfigId: string) {
    this.notifications = [];
    this.selectedDatesModel = [startDate, endDate];
    this.params.start_delivery_date = this.formatDeliveryDate(startDate)
    this.params.end_delivery_date = this.formatDeliveryDate(endDate)
    this.selectedAuction = [this.auctionConfigs.find((acfg) => acfg.id == auctionConfigId)];
    this.params.auction_config_id = auctionConfigId;
    //clear asset selection
    this.selectedAssets = [];
    this.params.asset_ids = [];
    //reload assets in filter
    this.getAssets();
    this.getBids();
  }

  clearDateValue() {
    this.params.start_delivery_date = '';
    this.params.end_delivery_date = '';
    this.selectedDatesModel = [null, null];
    this.getBids();
  }

  formatDeliveryDate(deliveryMoment) {
    return deliveryMoment.format('YYYY-MM-DD');
  }

  dropdownSettings(singleSelection, allowSearchFilter) {
    return {
      idField: 'id',
      textField: 'id_and_name',
      singleSelection: singleSelection,
      selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
      unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
      searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
      noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
      itemsShowLimit: 10,
      allowSearchFilter: allowSearchFilter
    }
  };

  toggleOnlyBidsWithoutAssetCheckbox(event) {
    event.preventDefault();
    event.stopPropagation();
    this.onlyBidsWithoutAsset = !this.onlyBidsWithoutAsset;
    if (this.onlyBidsWithoutAsset) {
      this.selectedAssets = [];
      this.params.asset_ids = [];
    }
    this.params.only_bids_without_asset = this.onlyBidsWithoutAsset
    this.getBids();
  }

}
