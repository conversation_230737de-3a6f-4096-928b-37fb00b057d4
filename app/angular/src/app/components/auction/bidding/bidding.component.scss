
@import "./../../../styles/colors";
vpp-management-bidding {
  display: block;

  .page-actions {
    a.eon-button {
      text-align: center;
      width: 270px;
    }
  }

  .tab-content{
    padding: 60px;
    margin-left: -15px;

    select.form-control, .form-control,input[type=text].form-control{
      border-color: $eon-turquoise;
    }

    &.light-blue{
      background-color: $eon-turquoise-25;
      h2{
        color: $eon-turquoise-dark;
      }
    }
    &.light-red{
      background-color: lighten($eon-red-25,5%);
      h2{
        color: $eon-red;
      }
    }
    &.light-yellow{
      background-color: $eon-limeyellow-25;
      h2{
        color: $eon-red;
      }
      a.view-more-link{
        text-decoration: underline;
        font-size: 18px;
        color: #222;
        margin-bottom: 30px;
        cursor: pointer;
      }
    }
  }
  .page-actions{
    padding: 30px 0;
    ul{
      margin-bottom: 0;
    }
    ul > li {
      margin-right: 20px;
    }
  }

  td.actions-column{
    color: $eon-red;
    ul{
      display: flex;
      li{
        margin-right: 16px;
        &:last-child{
          margin-right: 0;
        }
      }
    }
  }

  .overlay-on-top-of-list {
    position: relative;
    z-index: 999;
  }
}
