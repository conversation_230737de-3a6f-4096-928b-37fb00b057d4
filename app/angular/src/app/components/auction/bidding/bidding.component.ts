import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import * as moment from "moment-timezone";
import { Location } from "@angular/common";
import { GlobalService } from "./../../../services/global.service";
import { environment } from "../../../../environments/environment";
import { SlideRightContentAnimation } from './../../../animations/slide-right-content';
import { angularData } from "./../../../../global.export";
import { TranslateService } from '@ngx-translate/core';
import { BidsListComponent } from "./bids_list/bids_list.component";
import { UploadBidsComponent } from "./upload_bids_file/upload_bids_file.component";
import { AssetOptimizationComponent } from "./asset_optimization/asset_optimization.component";

@Component({
  selector: "vpp-management-bidding",
  templateUrl: "./bidding.component.html",
  styleUrls: ["./bidding.component.scss"],
  encapsulation: ViewEncapsulation.None,
  animations: [ SlideRightContentAnimation ]
})

export class BiddingComponent implements OnInit {
  params = {
    page: 1,
    per_page: 25
  };
  selectedSelection = "";
  permissions: any;

  @ViewChild('bidsList', {static: false}) bidsList: BidsListComponent;
  @ViewChild('uploadBidsFile', {static: true}) uploadBidsFile: UploadBidsComponent;
  @ViewChild('showBidsForTender', {static: true}) showBidsForTender: AssetOptimizationComponent;

  constructor(private _Global: GlobalService,
    private route: ActivatedRoute,
    private _Location: Location,
    public translate: TranslateService) {
    this.translate.get('PAGE.CAPTIONS.BIDDING').subscribe((res: string) => {
      this._Global.changeTitle(res);
    });
    this.permissions = angularData.permissions;
  }

  ngOnInit() {
  }

  ngAfterViewInit() {
    this.route.params.forEach((params: Params) => {
      if (params["tab"]) {
        setTimeout(() => {
          this.toggleSection(null, params["tab"]);
        })
      }
    });
  }

  pageSizeChangedCallback(pageSize) {
    this.params.per_page = pageSize;
    this.params.page = 1;
  }

  toggleSection(event, section) {
    if (event) {
      //event.preventDefault();
      //event.stopPropagation();
    }
    if (this.selectedSelection == section) {
      this.selectedSelection = '';
      this._Location.replaceState(`${environment.routingPath}/bidding/`);
    } else {
      this.selectedSelection = section;
      this._Location.replaceState(`${environment.routingPath}/bidding/${section}`);
    }
  }

  closeNominationModal() {
  }

  bidsUploadedCallback($event) {
    let startDate = moment($event.deliveryDate).startOf('day')
    let endDate   = moment($event.deliveryDate).startOf('day')
    this.bidsList.selectAuctionAndDate(startDate, endDate, $event.auctionConfigId);
  }

  showBidsForTenderCallback($event) {
    let startDate = moment($event.startDeliveryDate)
    let endDate = moment($event.endDeliveryDate)
    this.bidsList.selectAuctionAndDate(startDate, endDate, $event.auctionConfigId);
  }

  reloadAssetsFilterCallback($event) {
    this.bidsList.getAssets()
  }

}
