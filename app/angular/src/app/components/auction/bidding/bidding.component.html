<div class="container-fluid">
  <div class="row">
    <div class="col-md-8">
      <div class="page-actions">
        <ul class="list-unstyled d-flex">
          <li *ngIf="permissions.can_see_bids">
            <a href="javascript:void(0)"  (click)="toggleSection($event, 'prepare-bids')" class="eon-button bg-eon-turquoise">
            {{ 'BIDDING.TABS.PREPARE_BIDS' | translate }}
            </a>
          </li>
          <li *ngIf="permissions.can_create_bids">
            <a href="javascript:void(0)"  (click)="toggleSection($event, 'upload-bids-file')" class="eon-button bg-eon-limeyellow">
            {{ 'BIDDING.TABS.UPLOAD_BIDS' | translate }}
            </a>
          </li>
          <li *ngIf="permissions.can_see_bids">
            <a href="javascript:void(0)"  (click)="toggleSection($event, 'asset_optimization')" class="eon-button bg-eon-bordeaux">
            {{ 'BIDDING.TABS.ASSET_OPTIMIZATION' | translate }}
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>

  <div class="overlay-on-top-of-list" [@slideRightContentAnimation]="selectedSelection == 'prepare-bids' ? 'in' : 'out'" class="z1">
    <vpp-prepare-bids *ngIf="selectedSelection == 'prepare-bids'"></vpp-prepare-bids>
  </div>
  <div class="overlay-on-top-of-list" [@slideRightContentAnimation]="selectedSelection == 'upload-bids-file' ? 'in' : 'out'">
    <vpp-upload-bids-file #uploadBidsFile *ngIf="selectedSelection == 'upload-bids-file'"
      (uploaded)="bidsUploadedCallback($event)"></vpp-upload-bids-file>
  </div>
  <div class="overlay-on-top-of-list" [@slideRightContentAnimation]="selectedSelection == 'asset_optimization' ? 'in' : 'out'" class="z1">
    <vpp-asset-optimization *ngIf="selectedSelection == 'asset_optimization'"
      (tenderBids)="showBidsForTenderCallback($event)"
      (reloadAssetsFilter)="reloadAssetsFilterCallback($event)">
    </vpp-asset-optimization>
  </div>
  <section *ngIf="permissions.can_see_bids">
    <vpp-bids-list #bidsList></vpp-bids-list>
  </section>
</div>
