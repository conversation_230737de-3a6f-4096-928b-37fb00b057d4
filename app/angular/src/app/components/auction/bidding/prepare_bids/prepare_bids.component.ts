import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { Location } from "@angular/common";
import { Observable, Subject, merge } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map } from 'rxjs/operators';
import { BidProvider } from  "./../../../../providers/bid.provider";
import * as moment from "moment-timezone";
import { UrlProvider } from "./../../../../providers/url.provider";
import { GlobalService } from "./../../../../services/global.service";
import { environment } from "../../../../../environments/environment";
import { HttpClientService } from "./../../../../services/httpclient.service";
import { TranslateService } from '@ngx-translate/core';
import { angularData } from "./../../../../../global.export";

@Component({
  selector: "vpp-prepare-bids",
  templateUrl: "./prepare_bids.component.html",
  styleUrls: ["./prepare_bids.component.scss"],
  encapsulation: ViewEncapsulation.None
})

export class PrepareBidsComponent implements OnInit {
  csr_token: string;
  notifications: any = [];
  timeSlices: any = [];
  products: any = [];
  params = {
    product_intervals: null,
    market_id: null,
    date: null
  };
  selectedDate: any;
  selectedProduct: any;
  selectedTimeSlices:any = null;
  dropdownSettings = {
    singleSelection: false,
    selectAllText: angularData.railsExports.locale  == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
    unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
    searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
    noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
    itemsShowLimit: 10,
    allowSearchFilter: true
  };
  startDate = moment().add(1, 'day').startOf('day').toDate();
  prepareAuctionBidsUrl = this._UrlProvider.getPrepareActionBidsUrl();
  @ViewChild("downloadBids", { static: true }) downloadBids: any;

  constructor(
    public globalService: GlobalService,
    private route: ActivatedRoute,
    private _Location: Location,
    private _BidProvider: BidProvider,
    private _UrlProvider: UrlProvider,
    public translate: TranslateService,
    private _HttpClientService: HttpClientService,
    ) {
    this.csr_token = this._HttpClientService.getAuthToken();
    this.prepareAuctionBidsUrl = this._UrlProvider.getPrepareActionBidsUrl();
  }

  ngOnInit() {
    this._BidProvider.getTimeSlices().subscribe((timeSlices) => {
      this.timeSlices = timeSlices;
    });

    this._BidProvider.getProducts().subscribe((products) => {
      this.products = products;
      if (products && products.length > 0) {
        this.selectedProduct = this.products[0];
      }
    });

    this.selectedDate = this.startDate;
  }

  itemSelectCallback(items) {
    console.log('items', items);
  }

  selectAllCallback(items) {
    console.log('selectAllCallback', items);
  }

  startDateSelectedCallback(date) {
  }

  downloadSuggestions() {
    this.params.date = moment(this.selectedDate.toISOString()).format('YYYY-MM-DD');
    this.params.market_id = this.selectedProduct ? this.selectedProduct.id : null;
    this.params.product_intervals = this.selectedTimeSlices;
    setTimeout(() => {
      this.downloadBids.nativeElement.submit();
    });
  }

}
