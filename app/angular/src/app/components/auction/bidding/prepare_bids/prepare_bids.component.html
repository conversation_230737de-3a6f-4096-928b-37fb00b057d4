<div class="row">
  <div class="col-md-8" id="full-width-in-modal">
    <div class="tab-content light-blue">
      <h2>{{ 'BIDDING.PREPARE_BIDS.TITLE' | translate }}</h2>

      <div class="alerts-wrapper-static" *ngIf="notifications.length">
        <ng-container *ngFor="let message of notifications; let ix = index;">
          <ngb-alert
            [dismissible]="false"
            [type]="message.type"> {{ message.text | translate }}
          </ngb-alert>
        </ng-container>
      </div>

      <div class="form-row">
        <div class="col-md-6">
          <label class="required">{{ 'BIDDING.PREPARE_BIDS.DELIVERY_DATE' | translate }}</label>
          <input
            type="text"
            class="form-control"
            [owlDateTime]="dt1"
            [selectMode]="'single'"
            [(ngModel)]="selectedDate"
            [owlDateTimeTrigger]="dt1"
            (dateTimeChange)="startDateSelectedCallback($event)"
            [min]="startDate"
            placeholder="">
          <owl-date-time
            #dt1
            (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
            [startAt]="startDate"
            [pickerType]="'calendar'"
            [pickerMode]="'popup'"></owl-date-time>
        </div>
        <div class="col-md-6">
          <label class="required">{{ 'BIDDING.PREPARE_BIDS.PRODUCT' | translate }}</label>
          <select [(ngModel)]="selectedProduct" id="selectedProduct" class="form-control">
             <option *ngFor="let p of products" [ngValue]="p"> {{ p.name }} </option>
          </select>
        </div>
      </div>
      <div class="form-row">
        <div class="form-group col-md-12">
           <label class="required">{{ 'BIDDING.PREPARE_BIDS.TIME_SLICES' | translate }}</label>
           <ng-multiselect-dropdown
            [placeholder]="('BIDDING.PREPARE_BIDS.ALL_TIME_SLICES' | translate)"
            [data]="timeSlices"
            [(ngModel)]="selectedTimeSlices"
            [settings]="dropdownSettings"
            (onSelect)="itemSelectCallback($event)"
            (onSelectAll)="selectAllCallback($event)"
          >
          </ng-multiselect-dropdown>
        </div>
      </div>
      <button
        (click)="downloadSuggestions()"
        class="eon-button bg-eon-red">
        <span>{{ 'BIDDING.PREPARE_BIDS.REQUEST_BIDDING_SUGGESTIONS' | translate }}</span>
      </button>
      <div class="hidden">
        <form
          name="downloadBids"
          #downloadBids
          action="{{ prepareAuctionBidsUrl }}"
          method="get"
          target="_blank">
          <input type="hidden" name="authenticity_token" value="{{ csr_token }}">
          <input type="hidden" name="date" value="{{params.date}}"/>
          <input type="hidden" name="market_id" value="{{params.market_id}}"/>
          <input type="hidden" name="product_intervals" value="{{params.product_intervals}}"/>
          <button type="submit">{{ 'BIDDING.PREPARE_BIDS.EXPORT' | translate }}</button>
        </form>
      </div>
    </div>
  </div>
</div>