import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef, Output, EventEmitter
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import { Location } from "@angular/common";
import { Observable, Subject, merge } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map } from 'rxjs/operators';
import { AssetProvider } from "./../../../../providers/asset.provider";
import { AssetOptimizationProvider } from "./../../../../providers/asset_optimization.provider";
import {AuctionsProvider} from "../../../../providers/auctions.provider";
import * as moment from "moment-timezone";
import { UrlProvider } from "./../../../../providers/url.provider";
import { GlobalService } from "./../../../../services/global.service";
import { environment } from "../../../../../environments/environment";
import { HttpClientService } from "./../../../../services/httpclient.service";
import { NotificationService } from "./../../../../services/notification.service";
import { TranslateService } from '@ngx-translate/core';
import { angularData } from "./../../../../../global.export";
import {ConfirmService} from "../../../../services/confirm.service";

@Component({
  selector: "vpp-asset-optimization",
  templateUrl: "./asset_optimization.component.html",
  styleUrls: ["./asset_optimization.component.scss"],
  encapsulation: ViewEncapsulation.None
})

export class AssetOptimizationComponent implements OnInit {
  csr_token: string;
  notifications: any = [];
  response: any = null;
  biddingMethods: any = [
    {id: 'BatteryUk', name: 'Battery UK Optimization'}, 
    // {id: 'BatteryDe', name: 'Battery DE Optimization'}
  ];
  perspectives: any = [{id: 'EON', name: 'Market'}, {id: 'Customer', name: 'Customer'}];
  assets: any = [];
  efaBlocks: any = [
    {id: 'EFA1', name: 'EFA1'},
    {id: 'EFA2', name: 'EFA2'},
    {id: 'EFA3', name: 'EFA3'},
    {id: 'EFA4', name: 'EFA4'},
    {id: 'EFA5', name: 'EFA5'},
    {id: 'EFA6', name: 'EFA6'},
  ]
  selectedBiddingMethod: any;
  selectedPerspective: any;
  selectedAsset: any;
  assetsDropdownSettings: any;
  auctionConfigsDropdownSettings: any;
  swingLimit: boolean = false;
  swingLimitValue: any;
  auctionConfigs: any = [];
  selectedAuctionConfigs: any = [];
  tenderOptions: {
    tenderId: any,
    priceForecastType: string;
    fileUploaderReference: any;
    fileToUpload: File;
  }[] = []; // Dynamically maintains settings for each tender
  showPriceInfo: boolean = false;
  showFileFormat: boolean = false;
  uploaderOptions = {
    url: this._UrlProvider.getAssetOptimizationUrl(),
    allowedMimeType: ['text/csv']
  };
  exlcludeDcHighEfaBlocks: any;
  exlcludeDcLowEfaBlocks: any;
  exlcludeDmHighEfaBlocks: any;
  exlcludeDmLowEfaBlocks: any;
  exlcludeDrHighEfaBlocks: any;
  exlcludeDrLowEfaBlocks: any;
  disableButton: boolean = true;
  showLoader: boolean = false;
  assetOptimizations: any = [];
  assetOptimizationsTimeoutId = null;
  assetOptimizationsErrors: any = [];

  assetOptimizationUrl = this._UrlProvider.getAssetOptimizationUrl();
  marketPositionsUrl = this._UrlProvider.getMarketPositionsUrl();
  showDcEfaBlocks = false
  showDmEfaBlocks = false
  showDrEfaBlocks = false
  pleaseWait = false
  @Output() tenderBids: any = new EventEmitter();
  @Output() reloadAssetsFilter: any = new EventEmitter();
  pendingRejectOptimization = {};
  pendingValidateOptimization = {};
  confirmValidateOptimizationMessage = '';
  confirmRejectOptimizationMessage = '';

  constructor(
    public globalService: GlobalService,
    private route: ActivatedRoute,
    private _Location: Location,
    private _Asset: AssetProvider,
    private _AssetOptimizationProvider: AssetOptimizationProvider,
    private _AuctionsProvider: AuctionsProvider,
    private _UrlProvider: UrlProvider,
    public translate: TranslateService,
    private _HttpClientService: HttpClientService,
    public _NotificationService: NotificationService,
    private _confirmService: ConfirmService,
    ) {
    this.csr_token = this._HttpClientService.getAuthToken();
    this.assetsDropdownSettings = this.dropdownSettings(true, true, 'display_name');
    this.auctionConfigsDropdownSettings = this.dropdownSettings(false, true, 'name');
    this.assetOptimizationUrl = this._UrlProvider.getAssetOptimizationUrl();
    this.selectedBiddingMethod = 'BatteryUk';
    this.selectedPerspective = 'EON';
    this.translate.get('CONFIRM.MESSAGE.VALIDATE_OPTIMIZATION').subscribe((x) => {
      this.confirmValidateOptimizationMessage = x;
    });
    this.translate.get('CONFIRM.MESSAGE.REJECT_OPTIMIZATION').subscribe((x) => {
      this.confirmRejectOptimizationMessage = x;
    });
  }

  ngOnInit() {
    this.biddingMethodChanged(this.selectedBiddingMethod)
    this.getAuctionConfigs();
    this.getAssetOptimizations();
  }

  ngOnDestroy() {
    this.cancelAssetOptimizationsTimeout();
  }

  requestBiddingSuggestions(event) {
    this.response = null;
    event.preventDefault();
    event.stopPropagation();

    this.response = null; // Clear previous response
    this.pleaseWait = true; // Show loading indicator
    this.disableButton = true; // Disable the button to prevent multiple submissions

    const formData = new FormData();
    formData.append("authenticity_token", this.csr_token);
    this.appendParamsToForm(formData)
    this._HttpClientService.post(this._UrlProvider.getAssetOptimizationUrl(), formData, true)
      .toPromise()
      .then((responseData) => {
        console.log('Upload successful:', responseData);
        this.response = responseData;
          if (responseData.success) {
            console.log("Asset Optimization SUCCESS")
            this.disableButton = false
            this.selectedAuctionConfigsChanged(null);
            //reload asset optimizations
            this.cancelAssetOptimizationsTimeout()
            this.getAssetOptimizations()
          }
          else {
            console.log("Asset Optimization FAILED")
            document.getElementById("asset-optimizations-form").scrollIntoView();
          }
          this.pleaseWait = false
      })
      .catch((error) => {
        console.error('Upload failed:', error);
        this.response = {messages: [JSON.stringify(error)]};
          this.pleaseWait = false
          this.disableButton = false
          this.selectedAuctionConfigsChanged(null);
          document.getElementById("asset-optimizations-form").scrollIntoView();
      });    
  }

  appendParamsToForm(form) {
    form.append("bidding_method", this.selectedBiddingMethod);
    form.append("perspective", this.selectedPerspective);
    form.append("asset_id", this.selectedAsset[0].id);
    if (this.swingLimit) {
      form.append("swing_limit_value", this.swingLimitValue);
    }
    for (let cfg of this.selectedAuctionConfigs) {
      form.append("auction_config_id[]", cfg.id);
      let tenderOption = this.tenderOptions.find(option => option.tenderId === cfg.id);
      form.append("price_forecast_type_" + cfg.id, tenderOption.priceForecastType);
      if (tenderOption.priceForecastType == 'manual' && tenderOption.fileToUpload) {
        form.append("price_forecast_file_" + cfg.id, tenderOption.fileToUpload, tenderOption.fileToUpload.name);
      }
    }
    if (this.exlcludeDcHighEfaBlocks) {
      for (let block of this.exlcludeDcHighEfaBlocks) {
        form.append("exclude_dc_high_efa_block[]", block.id);
      }
    }
    if (this.exlcludeDcLowEfaBlocks) {
      for (let block of this.exlcludeDcLowEfaBlocks) {
        form.append("exclude_dc_low_efa_block[]", block.id);
      }
    }
    if (this.exlcludeDmHighEfaBlocks) {
      for (let block of this.exlcludeDmHighEfaBlocks) {
        form.append("exclude_dm_high_efa_block[]", block.id);
      }
    }
    if (this.exlcludeDmLowEfaBlocks) {
      for (let block of this.exlcludeDmLowEfaBlocks) {
        form.append("exclude_dm_low_efa_block[]", block.id);
      }
    }
    if (this.exlcludeDrHighEfaBlocks) {
      for (let block of this.exlcludeDrHighEfaBlocks) {
        form.append("exclude_dr_high_efa_block[]", block.id);
      }
    }
    if (this.exlcludeDrLowEfaBlocks) {
      for (let block of this.exlcludeDrLowEfaBlocks) {
        form.append("exclude_dr_low_efa_block[]", block.id);
      }
    }
  }

  dropdownSettings(singleSelection, allowSearchFilter, fieldName) {
    return {
      idField: 'id',
      textField: fieldName,
      singleSelection: singleSelection,
      selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
      unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
      searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
      noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
      itemsShowLimit: 10,
      allowSearchFilter: allowSearchFilter
    }
  };

  getAssets(customerType) {
    const noData = angularData.railsExports.locale == 'en-GB' ? 'No Asset available' : 'Keine Anlage verfügbar';
    this.assetsDropdownSettings.noDataAvailablePlaceholderText = noData;
    this._Asset.findAllForAssetOptimization({customer_type: customerType}).subscribe(
    result => {
        this.assets = result.assets;
        this.assets = this.mapAssetsAddingDisplayName(this.assets);
    }, err => {
        console.error('Failed to load asset list', err);
    });
  }

  getAssetDisplayName(a) {
    return `#${a.id} ${a.name} (${a.customer_name})`;
  }

  mapAssetsAddingDisplayName(assets) {
    return assets.map((x) => {
      x['display_name'] = this.getAssetDisplayName(x);
      return x;
    });
  }

  showBids(auction_config) {
    let tomorrow = moment().tz('Europe/London').add(1, 'day').startOf('day')
    this.tenderBids.emit({startDeliveryDate: tomorrow, endDeliveryDate: tomorrow, auctionConfigId: auction_config.id})
  }

  getAuctionConfigs() {
    const noData = angularData.railsExports.locale == 'en-GB' ? 'No open tender available' : 'Keine offen Ausschreibungskombinationen verfügbar';
    this.auctionConfigsDropdownSettings.noDataAvailablePlaceholderText = noData;
    this._AuctionsProvider.forAssetOptimization().subscribe(
      res => {
        this.auctionConfigs = res.auctions;
        console.log("LOADED AUCTION CONFIGS", this.auctionConfigs)
      }, err => {
        console.log('Failed to load auction configs', err);
      }
    );
  }

  getAssetOptimizations() {
    let params = {bidding_method: this.selectedBiddingMethod}
    this._AssetOptimizationProvider.getPortfolioOptimizations(params).subscribe(
      res => {
        this.assetOptimizations = res.optimizations;
        this.reloadAssetsFilter.emit({})
        console.log("LOADED ASSET OPTIMIZATIONS", this.assetOptimizations)
        this.assetOptimizationsTimeoutId = setTimeout(() => { this.getAssetOptimizations(); }, 8000);
      }, err => {
        console.log('Failed to load asset optimizations', err);
      }
    );
  }

  cancelAssetOptimizationsTimeout() {
    if (this.assetOptimizationsTimeoutId) {
      clearTimeout(this.assetOptimizationsTimeoutId);
    }
  }

  confirmRejectOptimization(optimization) {
    this._confirmService.confirm({message: this.confirmRejectOptimizationMessage}).then(
    () => {
        this.rejectOptimization(optimization);
    },
    () => {
    });
  }

  rejectOptimization(optimization) {
    if (this.pendingRejectOptimization[optimization.id]) {
      return;
    }
    this.pendingRejectOptimization[optimization.id] = true
    let params = {id : optimization.id}
    this._AssetOptimizationProvider.rejectBids(params).subscribe(
      res => {
        console.log("Rejected bids for:", optimization.id, res)
        this.pendingRejectOptimization[optimization.id] = false
        if (res.success) {
          this.assetOptimizationsErrors = []
        } else {
          this.assetOptimizationsErrors = res.messages
          this.scrollToAssetOptimizations()
        }
        this.cancelAssetOptimizationsTimeout()
        this.getAssetOptimizations()
      }, err => {
        this.pendingRejectOptimization[optimization.id] = false
        this.assetOptimizationsErrors = [
          "Failed to reject  bids for optimization #" + optimization.id,
          err
        ]
        this.scrollToAssetOptimizations()
      }
    );
  }

  confirmValidateOptimization(optimization) {
    this._confirmService.confirm({message: this.confirmValidateOptimizationMessage}).then(
    () => {
        this.validateOptimization(optimization);
    },
    () => {
    });
  }

  validateOptimization(optimization) {
    if (this.pendingValidateOptimization[optimization.id]) {
      return;
    }
    this.pendingValidateOptimization[optimization.id] = true
    let params = {id : optimization.id}
    this._AssetOptimizationProvider.validateBids(params).subscribe(
      res => {
        console.log("Validated bids for:", optimization.id, res)
        this.pendingValidateOptimization[optimization.id] = false
        if (res.success) {
          this.assetOptimizationsErrors = []
        } else {
          this.assetOptimizationsErrors = res.messages
          this.scrollToAssetOptimizations()
        }
        this.cancelAssetOptimizationsTimeout()
        this.getAssetOptimizations()
      }, err => {
        this.pendingValidateOptimization[optimization.id] = false
        this.assetOptimizationsErrors = [
          "Failed to validate bids for optimization #" + optimization.id,
          err
        ]
        this.scrollToAssetOptimizations()
        console.log('Failed to validate bids for:', optimization.id, err);
      }
    );
  }

  scrollToAssetOptimizations() {
    document.getElementById("asset-optimizations-list").scrollIntoView();
  }

  toggleSwingLimitCheckbox() {
    this.swingLimit = !this.swingLimit;
    this.validateInput()
  }

  fileChangedCallback(file: any, index: number) {
    this.tenderOptions[index].fileToUpload = file._file;
    this.validateInput()
  }

  uploaderChangedCallback(uploader: any, index: number) {
    this.tenderOptions[index].fileUploaderReference = uploader;
  }

  selectedAssetsChanged(event) {
    this.validateInput()
  }

  selectedAuctionConfigsChanged(event) {
    this.buildTenderOptions()
    this.validateInput()
  }

  selectedAllAuctionConfigsChanged(event) {
    this.selectedAuctionConfigs = event
    this.buildTenderOptions()
    this.validateInput()
  }

  buildTenderOptions() {
    this.tenderOptions = this.selectedAuctionConfigs.map((tender) => {
      const aCfg = this.auctionConfigs.find(aCfg => aCfg.id == tender.id)
      let tenderData =  {
        tenderId: tender.id,
        priceForecastType: 'automated', // Default value
        latestPriceForecast: aCfg.lastPriceForecast,
        fileUploaderReference: null,
        fileToUpload: null,
        tz: aCfg.tz
      };
      return tenderData
    });
  }

  biddingMethodChanged(event) {
    this.selectedAsset = []
    let customerType = null;
    if (this.selectedBiddingMethod == 'BatteryUk') {
      customerType = 'VPP_UK'
    }
    if (this.selectedBiddingMethod == 'BatteryDe') {
      customerType = 'VPP_DE'
    }
    this.getAssets(customerType)
    this.validateInput()
  }

  perspectiveChanged(event) {
    this.validateInput()
  }

  swingLimitValueChanged(event) {
    this.validateInput()
  }

  validateInput() {
    const priceFileRequired = this.tenderOptions.some((option) => {
      if (option.priceForecastType === 'manual') {
        return option.fileToUpload === null; 
      }
      return false;
    });

    this.showPriceInfo = this.tenderOptions.some((option) => {
      return option.priceForecastType === 'manual'
    });

    let valid =
      this.selectedBiddingMethod &&
      this.selectedPerspective &&
      (this.selectedAsset != null && this.selectedAsset.length > 0) &&
      (this.selectedAuctionConfigs != null && this.selectedAuctionConfigs.length > 0) &&
      !priceFileRequired
  
    if (this.swingLimit) {
      valid = valid && this.swingLimitValue
    }
    this.showDcEfaBlocks = this.isMarketSelected('DYNAMICCONTAINMENT')
    this.showDmEfaBlocks = this.isMarketSelected('DYNAMICMODERATION')
    this.showDrEfaBlocks = this.isMarketSelected('DYNAMICREGULATION')
    this.disableButton = !valid;
  }

  changePriceForecastType(type: string, index: number) {
    this.tenderOptions[index].priceForecastType = type;
    this.validateInput()
  }

  isMarketSelected(market) {
    if (this.selectedAuctionConfigs != null && this.selectedAuctionConfigs.length > 0) {
      let found = this.selectedAuctionConfigs.find(element =>
        this.auctionConfigs.find(cfg =>
          element.id == cfg.id && cfg.biddingMethodMarket && cfg.biddingMethodMarket.includes(market) ))
      return found !== undefined
    } else {
      return false
    }
  }

}
