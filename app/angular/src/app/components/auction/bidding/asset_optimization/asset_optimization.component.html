<div class="row min-width-1280">
  <div class="col-md-10" id="full-width-in-modal">
    <div class="tab-content light-red">
      <h2 id="asset-optimizations-form">{{ 'BIDDING.ASSET_OPTIMIZATION.TITLE' | translate }}</h2>
      <div class="alerts-wrapper-static" *ngIf="notifications.length">
        <ng-container *ngFor="let message of notifications; let ix = index;">
          <ngb-alert [dismissible]="false" [type]="message.type"> {{ message.text | translate }} </ngb-alert>
        </ng-container>
      </div>
      <vpp-file-upload-results [response]="response"></vpp-file-upload-results>

      <div class="form-row">
        <div class="col-md-6">
          <label class="required">{{ 'BIDDING.ASSET_OPTIMIZATION.BIDDING_METHODS' | translate }}</label>
          <select [(ngModel)]="selectedBiddingMethod" id="selectedBiddingMethod" (change)="biddingMethodChanged($event)" class="form-control">
            <option *ngFor="let m of biddingMethods" [ngValue]="m.id"> {{ m.name }} </option>
          </select>
        </div>
        <div class="col-md-6">
          <label class="required">{{ 'BIDDING.ASSET_OPTIMIZATION.PERSPECTIVE' | translate }}</label>
          <select [(ngModel)]="selectedPerspective" id="selectedPerspective" (change)="perspectiveChanged($event)" class="form-control">
            <option *ngFor="let p of perspectives" [ngValue]="p.id"> {{ p.name }} </option>
          </select>
        </div>
      </div>

      <div class="form-row">
        <div class="col-md-6">
          <label class="required">{{ 'BIDDING.ASSET_OPTIMIZATION.ASSET' | translate }}</label>
          <ng-multiselect-dropdown
            [placeholder]="('COMPONENTS.SELECT' | translate )"
            [data]="assets"
            [(ngModel)]="selectedAsset"
            [settings]="assetsDropdownSettings"
            (onSelect)="selectedAssetsChanged($event)"
            (onDeSelect)="selectedAssetsChanged($event)">
          </ng-multiselect-dropdown>
        </div>
        <div class="col-md-6">
          <label>{{ 'BIDDING.ASSET_OPTIMIZATION.SWING_LIMIT' | translate }}</label>
          <div class="form-row">
            <div class="col-sm-6">
              <div class="form-check" style="padding-top: 5px">
                <label
                  class="eon-checkbox-label bg-eon-red"
                  (click)="toggleSwingLimitCheckbox()"
                  [ngClass]="swingLimit ? 'checked': ''">{{ 'BIDDING.ASSET_OPTIMIZATION.SWING_LIMIT_YES_NO' | translate }}</label>
              </div>
            </div>
            <div class="col-sm-6">
              <div class="form-row">
                <div class="col-sm-4" style="padding-top: 5px">
                  <label style="font-weight: normal">Value</label>
                </div>
                <div class="col-sm-8">
                  <input
                    [(ngModel)]="swingLimitValue"
                    autocomplete="swingLimitValue"
                    type="number"
                    min="0"
                    oninput="validity.valid||(value='');"
                    placeholder="kW"
                    (change)="swingLimitValueChanged($event)"
                    class="form-control"
                    id="swingLimitValue">
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="form-row">
        <div class="col-md-12">
          <label class="required">{{ 'BIDDING.ASSET_OPTIMIZATION.TENDERS' | translate }}</label>
          <ng-multiselect-dropdown
            [placeholder]="('COMPONENTS.SELECT' | translate )"
            [data]="auctionConfigs"
            [(ngModel)]="selectedAuctionConfigs"
            [settings]="auctionConfigsDropdownSettings"
            (onSelect)="selectedAuctionConfigsChanged($event)"
            (onDeSelect)="selectedAuctionConfigsChanged($event)"
            (onSelectAll)="selectedAllAuctionConfigsChanged($event)"
            (onDeSelectAll)="selectedAllAuctionConfigsChanged($event)">
          </ng-multiselect-dropdown>
        </div>
      </div>

      <div class="form-row" *ngFor="let tender of selectedAuctionConfigs; let i = index">
        <input type="hidden" [(ngModel)]="tenderOptions[i].tenderId" value="{{ tender.id }}"/>
        <div class="col-md-12">
          <div class="form-row" *ngIf="i == 0">
            <div class="col-md-4">
              <label>{{ 'BIDDING.ASSET_OPTIMIZATION.TENDER' | translate }}</label>
            </div>
            <div class="col-md-4">
              <label>{{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST_TYPE' | translate }}</label>
            </div>
            <div class="col-md-4">
              <label>{{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST' | translate }}</label>
            </div>
          </div>
          <div class="form-row">
            <div class="col-md-4">
              <label class="font-weight-light">{{tender.name}}</label>
            </div>
            <!-- Price Forecast Type -->
            <div class="col-md-4">
              <div class="form-row">
                <div class="form-check form-check-inline">
                  <label class="form-check-label font-weight-light" (click)="changePriceForecastType('automated', i)">
                    <input
                      type="radio"
                      [(ngModel)]="tenderOptions[i].priceForecastType"
                      class="form-check-input"
                      name="priceForecastType_{{ i }}"
                      value="automated"
                    />
                    {{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST_TYPE_AUTOMATED' | translate }}
                  </label>
                </div>
                <div class="form-check form-check-inline">
                  <label class="form-check-label font-weight-light" (click)="changePriceForecastType('manual', i)">
                    <input
                      type="radio"
                      [(ngModel)]="tenderOptions[i].priceForecastType"
                      class="form-check-input"
                      name="priceForecastType_{{ i }}"
                      value="manual"
                    />
                    {{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST_TYPE_MANUAL' | translate }}
                  </label>
                </div>
              </div>
            </div>
      
            <!-- File Upload (Visible Only for Manual Price Forecast) -->
            <div class="col-md-4" *ngIf="tenderOptions[i].priceForecastType === 'manual'">
              <vpp-management-file-uploader
                [options]="uploaderOptions"
                (uploader)="uploaderChangedCallback($event, i)"
                (propagateFile)="fileChangedCallback($event, i)"
              ></vpp-management-file-uploader>
            </div>
            <div class="col-md-4" *ngIf="tenderOptions[i].priceForecastType === 'automated'">
               {{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.AUTOMATED.LATEST' | translate }} 
               <span *ngIf="tenderOptions[i].latestPriceForecast">
                {{ tenderOptions[i].latestPriceForecast | tzDate: tenderOptions[i].tz:'DD.MM HH:mm'}}
               </span>
               <span *ngIf="!tenderOptions[i].latestPriceForecast">
                -
               </span>                 
            </div>
          </div>
        </div>
      </div>

      <div class="form-row" *ngIf="showPriceInfo">
        <div class="col-md-12">
          <br/>
          <a (click)="showFileFormat = !showFileFormat" class="view-more-link" *ngIf="showFileFormat">- {{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.HIDE' | translate }}</a>
          <a (click)="showFileFormat = !showFileFormat" class="view-more-link" *ngIf="!showFileFormat">+ {{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.SHOW' | translate }}</a>
          <div class="file-format-box" *ngIf="showFileFormat">
            <p>{{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_FORMAT_DETAIL' | translate }}</p>
            <code [innerHtml]="'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_FORMAT' | translate"></code>
            <p>{{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_LIST_TITLE' | translate }}</p>
            <ul>
              <li>{{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_1' | translate }}</li>
              <li>{{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_2' | translate }}</li>
              <li>{{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_3' | translate }}</li>
              <li>{{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_4' | translate }}</li>
              <li>{{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_5' | translate }}</li>
              <li>{{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_6' | translate }}</li>
              <li>{{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_7' | translate }}</li>
              <li>{{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_8' | translate }}</li>
            </ul>
            <p>{{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_FORMAT_DETAIL_EPEX30MIN' | translate }}</p>
            <code [innerHtml]="'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_FORMAT_EPEX30MIN' | translate"></code>
            <ul>
              <li>{{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_1_EPEX30MIN' | translate }}</li>
              <li>{{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_2_EPEX30MIN' | translate }}</li>
            </ul>
            <p>{{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_FORMAT_DETAIL_N2EX1H' | translate }}</p>
            <code [innerHtml]="'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_FORMAT_N2EX1H' | translate"></code>
            <ul>
              <li>{{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_1_N2EX1H' | translate }}</li>
              <li>{{ 'BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_2_N2EX1H' | translate }}</li>
            </ul>            
          </div>
        </div>
      </div>

      <div class="form-row mt-2">
        <div class="col-md-8">
            <div class="form-row" *ngIf="showDcEfaBlocks">
                <div class="col-md-6">
                    <label> {{ 'BIDDING.ASSET_OPTIMIZATION.EXCLLDE_DC_HIGH_EFA_BLOCKS' | translate }} </label>
                    <ng-multiselect-dropdown
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="efaBlocks"
                            [(ngModel)]="exlcludeDcHighEfaBlocks"
                            [settings]="dropdownSettings(false, false, 'name')">
                    </ng-multiselect-dropdown>
                </div>
                <div class="col-md-6">
                    <label> {{ 'BIDDING.ASSET_OPTIMIZATION.EXCLLDE_DC_LOW_EFA_BLOCKS' | translate }} </label>
                    <ng-multiselect-dropdown
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="efaBlocks"
                            [(ngModel)]="exlcludeDcLowEfaBlocks"
                            [settings]="dropdownSettings(false, false, 'name')">
                    </ng-multiselect-dropdown>
                </div>
            </div>
            <div class="form-row" *ngIf="showDmEfaBlocks">
                <div class="col-md-6">
                    <label> {{ 'BIDDING.ASSET_OPTIMIZATION.EXCLLDE_DM_HIGH_EFA_BLOCKS' | translate }} </label>
                    <ng-multiselect-dropdown
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="efaBlocks"
                            [(ngModel)]="exlcludeDmHighEfaBlocks"
                            [settings]="dropdownSettings(false, false, 'name')">
                    </ng-multiselect-dropdown>
                </div>
                <div class="col-md-6">
                    <label> {{ 'BIDDING.ASSET_OPTIMIZATION.EXCLLDE_DM_LOW_EFA_BLOCKS' | translate }} </label>
                    <ng-multiselect-dropdown
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="efaBlocks"
                            [(ngModel)]="exlcludeDmLowEfaBlocks"
                            [settings]="dropdownSettings(false, false, 'name')">
                    </ng-multiselect-dropdown>
                </div>
            </div>
            <div class="form-row" *ngIf="showDrEfaBlocks">
                <div class="col-md-6">
                    <label> {{ 'BIDDING.ASSET_OPTIMIZATION.EXCLLDE_DR_HIGH_EFA_BLOCKS' | translate }} </label>
                    <ng-multiselect-dropdown
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="efaBlocks"
                            [(ngModel)]="exlcludeDrHighEfaBlocks"
                            [settings]="dropdownSettings(false, false, 'name')">
                    </ng-multiselect-dropdown>
                </div>
                <div class="col-md-6">
                    <label> {{ 'BIDDING.ASSET_OPTIMIZATION.EXCLLDE_DR_LOW_EFA_BLOCKS' | translate }} </label>
                    <ng-multiselect-dropdown
                            [placeholder]="('COMPONENTS.SELECT' | translate )"
                            [data]="efaBlocks"
                            [(ngModel)]="exlcludeDrLowEfaBlocks"
                            [settings]="dropdownSettings(false, false, 'name')">
                    </ng-multiselect-dropdown>
                </div>
            </div>
        </div>
      </div>

      <div class="row" style="padding-top: 20px">
        <div class="form-group col-md-6">
          <button [disabled]="disableButton" (click)="requestBiddingSuggestions($event)" class="eon-button bg-eon-red bg-eon-white-disabled">
            <span><i *ngIf="pleaseWait" class="fa fa-spinner"></i> {{ 'BIDDING.ASSET_OPTIMIZATION.REQUEST_BIDDING_SUGGESTIONS' | translate }}</span>
          </button>
        </div>
        <div class="form-group col-md-6">
          <span>{{ 'BIDDING.ASSET_OPTIMIZATION.BIDDING_SUGGESTIONS_INFO' | translate }}</span>
        </div>
      </div>

      <div class="row">
        <h4 id="asset-optimizations-list"> Asset Optimizations </h4>
        <div class="alerts-wrapper-static" *ngIf="assetOptimizationsErrors.length" style="overflow-wrap: break-word;">
            <ng-container *ngFor="let error of assetOptimizationsErrors; let ix = index;">
                <ngb-alert
                  [dismissible]="true"
                  [type]="'error'"> {{ error | translate}}
                </ngb-alert>
            </ng-container>
        </div>
        <table class="table table-optimization-results table-striped">
          <tr>
              <th class="text-left">#</th>
              <th class="text-center">{{ 'BIDDING.ASSET_OPTIMIZATION.TABLE.DATE' | translate }}</th>
              <th class="text-center">{{ 'BIDDING.ASSET_OPTIMIZATION.TABLE.STATUS' | translate }}</th>
              <th class="text-center">{{ 'BIDDING.ASSET_OPTIMIZATION.TABLE.ASSET' | translate }}</th>
              <th class="text-center">{{ 'BIDDING.ASSET_OPTIMIZATION.TABLE.TENDERS' | translate }}</th>
              <th class="text-center">{{ 'BIDDING.ASSET_OPTIMIZATION.TABLE.USER' | translate }}</th>
              <th class="text-center"></th>
          </tr>
          <tr *ngIf="showLoader">
            <td colspan="7" class="text-center vertical-center"><vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
            <td>
          </tr>
          <ng-container *ngIf="!assetOptimizations.length && !showLoader">
            <tr>
              <td colspan="5">{{ 'BIDDING.ASSET_OPTIMIZATION.TABLE.NO_ASSET_OPTIMIZATIONS' | translate }}</td>
            </tr>

          </ng-container>
          <ng-container *ngIf="assetOptimizations.length">
            <tr *ngFor="let o of assetOptimizations">
                <td class="text-left">{{ o.id }} </td>
                <td class="text-center">{{ o.created | localDate:'DD MMM, YYYY HH:mm:ss' }}</td>
                <td class="text-center">{{ o.status }}</td>
                <td class="text-center">#{{ o.asset.id }} {{ o.asset.name }}</td>
                <td class="text-center">
                  <ng-container *ngIf="o.nomination_bid_sources.length">
                    <div *ngFor="let nbsac of o.nbs_auction_configs">
                      <a href="javascript:void(0)" (click)="showBids(nbsac)">
                        #{{nbsac.id}}  {{nbsac.name}}
                      </a>
                    </div>
                  </ng-container>
                  <ng-container *ngIf="o.auction_configs.length">
                    <div *ngFor="let acfg of o.auction_configs">
                      #{{acfg.id}} {{acfg.name}}
                    </div>
                  </ng-container>
                </td>
                <td class="text-center">#{{ o.user_account.id }} {{ o.user_account.name }}</td>
                <td class="text-center actions-column">
                  <ul class="list-inline list-unstyled">
                      <li *ngIf="o.status == 'RESULT_RECEIVED'">
                        <span title="{{ 'BIDDING.ASSET_OPTIMIZATION.ACTION.REJECT_BIDS' | translate }}" (click)="confirmRejectOptimization(o)" style="display: inline-block"><i class="fa fa-2x fa-ban"></i></span> </li>
                      <li *ngIf="o.status == 'RESULT_RECEIVED'">
                        <span title="{{ 'BIDDING.ASSET_OPTIMIZATION.ACTION.VALIDATE_BIDS' | translate }}" (click)="confirmValidateOptimization(o)" style="display: inline-block"><i class="fa fa-2x fa-check-square"></i></span> </li>
                      <li *ngIf="o.status != 'NEW' && o.status != 'FAILED_OPTIMIZATION'">
                        <a href="{{marketPositionsUrl}}/{{o.id}}" target="_blank" title="{{ 'BIDDING.ASSET_OPTIMIZATION.ACTION.DOWNLOAD_MARKET_POSITIONS' | translate }}">
                          <i class="fa fa-2x fa-file"></i></a>
                  </ul>
                </td>
            </tr>
          </ng-container>
      </table>
      </div>
  </div>
</div>