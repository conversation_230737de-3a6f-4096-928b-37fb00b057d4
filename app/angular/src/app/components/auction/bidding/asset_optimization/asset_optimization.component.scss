@import "./../../../../styles/colors";
vpp-asset-optimization {
  .tab-content {
    &light-red:before {
      content: "";
      position: absolute;
      width: 0;
      height: 0;
      border-left: 15px solid transparent;
      border-right: 15px solid transparent;
      border-bottom: 15px solid lighten($eon-red-25, 5%);
      top: -15px;

      &light-red {
        a.view-more-link {
          text-decoration: underline;
          font-size: 18px;
          color: #222;
          margin-bottom: 30px;
          cursor: pointer;
        }
      }
    }
  }

  .form-control{
    border-color: $eon-red-25;
  }

  .min-width-1280 {
    min-width: 1280px;
  }

  .multiselect-dropdown {
    .dropdown-btn {
      border: 2px solid $eon-turquoise !important;
      background: #fff !important;
      border-radius: 3px !important;
      min-height: 52px !important;
    }
  }

  .hidden {
    display: none;
  }

  .file-format-box{
    padding: 30px;
    background: #fff;
    border: 1px solid $eon-darkgrey;
  }

  table.table-optimization-results {
    tr:first-child > th:first-child {
      border-left:8px solid $eon-bordeaux;
    }

    tr.validated > td:first-child {
      border-left:8px solid #08B002;
    }

    tr.received > td:first-child {
      border-left:8px solid #c3c3c3;
    }

    tr.new > td:first-child {
      border-left:8px solid #FFCC00;
    }

    tr.invalidated > td:first-child {
      border-left:8px solid #EA1C0A;
    }

    td, th {
      &.text-right {
        text-align: right;
      }
    }
  }

}