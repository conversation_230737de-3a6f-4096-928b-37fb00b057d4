<div class="row">
  <div class="col-md-10">
    <div class="tab-content light-yellow padding-50">
      <h2>{{ 'BIDDING.UPLOAD_BIDS.TITLE' | translate }}</h2>
      <p>{{ 'BIDDING.UPLOAD_BIDS.SUBTITLE' | translate }}</p>
        <vpp-file-upload-results [response]="response"></vpp-file-upload-results>
      <br><br>
      <div class="form-row">
        <div class="form-group col-md-4">
          <label>{{ 'BIDDING.UPLOAD_BIDS.AUCTION' | translate }}</label>
          <ng-multiselect-dropdown
              [placeholder]="('COMPONENTS.SELECT' | translate )"
              [data]="auctionConfigs"
              [(ngModel)]="auctionConfig"
              [settings]="dropdownSettings(true, false)">
          </ng-multiselect-dropdown>
        </div>
        <div class="form-group col-md-5">
            <label>&nbsp;</label>
            <vpp-management-file-uploader
              [options]="uploaderOptions"
              (uploader)="uploaderChangedCallback($event)"
              (propagateFile)="fileChangedCallback($event)"
              #fileUploader>>
            </vpp-management-file-uploader>
        </div>
        <div class="form-group col-md-3">
          <label>&nbsp;</label>
          <div class="upload-meta">
            <div class="row">
              <div class="col-md-2">
                <i class="fas fa-2x fa-file-excel" style="padding-top: 10px"></i>
              </div>
              <div class="col-md-10">
                <a class="nowrap" href="{{environment.apiPath}}/api/v1/bids/upload_bids_sample" target="_blank"><i class="fas fa-download"></i> {{ 'BIDDING.UPLOAD_BIDS.EXAMPLE' | translate }}</a>
                <br/>
                <a class="nowrap" href="{{environment.apiPath}}/api/v1/bids/upload_bids_sample?market=afrr" target="_blank"><i class="fas fa-download"></i> {{ 'BIDDING.UPLOAD_BIDS.EXAMPLE_AFRR' | translate }}</a>
              </div>
            </div>
          </div>
        </div>
        <div class="form-group col-md-12">
          <button [disabled]="!(fileToUpload && (auctionConfig && auctionConfig.length > 0))" (click)="uploadFile($event)" class="eon-button bg-eon-red">
            <span>{{ 'BIDDING.UPLOAD_BIDS.UPLOAD_BIDS_FILE' | translate }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>