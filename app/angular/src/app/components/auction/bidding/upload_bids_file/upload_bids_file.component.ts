import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef, Output, EventEmitter
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import {UrlProvider} from "./../../../../providers/url.provider";
import {HttpClientService} from "./../../../../services/httpclient.service";
import {NotificationService} from "./../../../../services/notification.service";
import {environment} from "../../../../../environments/environment";
import {TranslateService} from '@ngx-translate/core';
import {AuctionsProvider} from "../../../../providers/auctions.provider";
import {angularData} from "../../../../../global.export";

@Component({
  selector: "vpp-upload-bids-file",
  templateUrl: "./upload_bids_file.component.html",
  styleUrls: ["./upload_bids_file.component.scss"],
  encapsulation: ViewEncapsulation.None
})

export class UploadBidsComponent implements OnInit {
  showFileFormat: boolean = false;
  fileToUpload;
  fileUploaderReference;
  uploaderOptions = {
    url: this._UrlProvider.getUploadBidsUrl()
  };
  auctionConfig;
  auctionConfigs;
  csr_token: string = "";
  checkbox: boolean = false;
  disableButton: boolean = true;
  public notifications: any = [];
  environment = environment;
  response = null;

  @ViewChild("fileUploader", {static: true}) fileUploader: any;
  @Output() uploaded: any = new EventEmitter();

  constructor(
    private _HttpClientService: HttpClientService,
    private _UrlProvider: UrlProvider,
    public _NotificationService: NotificationService,
    public translate: TranslateService,
    private _auctionsProvider: AuctionsProvider,
  ) {
    this.uploaderOptions.url = this._UrlProvider.getUploadBidsUrl();
    this.csr_token = this._HttpClientService.getAuthToken();
  }

  ngOnInit() {
    this.getAuctionConfigs();
  }

  fileChangedCallback(file) {
    this.fileToUpload = file;
    this.disableButton = false;

    console.log('fileChangedCallback', this.fileToUpload);
  }

  uploaderChangedCallback(uploader) {
    this.fileUploaderReference = uploader;
  }

  uploadFile(event) {
    this.disableButton = true;
    this.response = null;
    event.preventDefault();
    event.stopPropagation();
    this.fileToUpload.url = this._UrlProvider.getUploadBidsUrl();
    this.fileUploaderReference.onBuildItemForm = (
      fileItem: any,
      form: any
    ) => {
      form.append("authenticity_token", this.csr_token);
      form.append("auction_config_id", this.auctionConfig[0].id);
    };
    this.fileToUpload.upload();

    this.fileUploaderReference.onCompleteItem = (item: any, response: any, status: any, headers: any) => {
      let data = JSON.parse(response);
      this.response = data;

      console.log('upload bids file response', data);

      this.fileUploader.clearFileUploaderQueue();
      this.disableButton = false;

      if (data.success) {
        this.fileToUpload = null;
        this.uploaded.emit({deliveryDate: this.response.delivery_date, auctionConfigId: this.response.auction_config_id})
      } else {

      }
    };
  }

  getAuctionConfigs() {
    this._auctionsProvider.findAll({active: true, with_bid_input: 'InternalBiddingKind', has_internal_bidding_input_format: true}).subscribe(
      res => {
        this.auctionConfigs = res.auctions;
        console.log("LOADED AUCTION CONFIGS", this.auctionConfigs)
      }, err => {
        console.log('Failed to load auction configs', err);
      }
    );
  }

  toggleCheckbox() {
    this.checkbox = !this.checkbox;
  }

  public clearNotifications() {
    this.notifications = [];
  }

  dropdownSettings(singleSelection, allowSearchFilter) {
    return {
      idField: 'id',
      textField: 'name',
      singleSelection: singleSelection,
      selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
      unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
      searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
      noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
      itemsShowLimit: 10,
      allowSearchFilter: allowSearchFilter
    }
  };

}
