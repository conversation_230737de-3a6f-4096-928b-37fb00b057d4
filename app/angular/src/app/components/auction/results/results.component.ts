import {
  Component,
  OnInit,
  ViewEncapsulation,
  ViewChild,
  ElementRef,
  Output,
  EventEmitter
} from "@angular/core";
import {
  Router,
  ActivatedRoute,
  Params,
  PRIMARY_OUTLET
} from "@angular/router";
import * as moment from "moment-timezone";
import { angularData } from "./../../../../global.export";
import { AuctionProvider } from "./../../../providers/auction.provider";
import { TranslateService } from '@ngx-translate/core';
import { GlobalService } from "./../../../services/global.service";
import {Moment} from "moment-timezone/moment-timezone";
import {AssetProvider} from "../../../providers/asset.provider";
import {HttpClientService} from "../../../services/httpclient.service";
import {UrlProvider} from "../../../providers/url.provider";

@Component({
  selector: "vpp-management-auction-results",
  templateUrl: "./results.component.html",
  styleUrls: ["./results.component.scss"],
  encapsulation: ViewEncapsulation.None
})
export class AuctionResultsComponent implements OnInit {
  resultsParms = {
    start_delivery_date: '',
    end_delivery_date: '',
  };
  bidsList = [];
  auctionConfigs = [];
  assets: any = [];
  products = [];
  tsos = [];
  directions = [];
  direction_all = 'all';
  filter = {
    tso: null,
    auctionConfig: null,
    selectedAssets: [],
    product: null,
    direction: null,
    accepted: null
  };
  page = 1;
  pageSize = 10;
  collectionSize = 0;
  showLoader: boolean = false;
  select_auction_config;
  select_products;
  select_tsos;
  selectedDatesModel = [
    moment().tz(angularData.railsExports.timeZone).startOf('day').add(1, 'day'),
    moment().tz(angularData.railsExports.timeZone).startOf('day').add(1, 'day')
  ];
  permissions: any;
  csr_token: string;
  downloadAuctionResultsUrl = this._UrlProvider.getDownloadAuctionResultsUrl();
  @ViewChild("downloadAuctionResults", { static: true }) downloadAuctionResults: any;

  constructor(
    private _Auction: AuctionProvider,
    private _UrlProvider: UrlProvider,
    private _assetProvider: AssetProvider,
    private _HttpClientService: HttpClientService,
    public translate: TranslateService,
    public globalService: GlobalService) {
    this.translate.get('AUCTIONS.RESULTS.SELECT_AUCTIONS').subscribe((res: string) => {
        this.select_auction_config = {id: "", name: res};
        this.auctionConfigs = [this.select_auction_config];
        this.filter.auctionConfig = this.select_auction_config.id;
    });

    this.translate.get('AUCTIONS.RESULTS.SELECT_PRODUCTS').subscribe((res: string) => {
        this.products = [res];
        this.select_products = res;
        this.filter.product = this.products[0];
    });

    this.translate.get('AUCTIONS.RESULTS.SELECT_TSO').subscribe((res: string) => {
        this.tsos = [res];
        this.select_tsos = res;
        this.filter.tso = this.tsos[0];
    });

    this.directions = [ this.direction_all ];
    this.filter.direction = this.directions[0];

    this.csr_token = this._HttpClientService.getAuthToken();
    this.downloadAuctionResultsUrl = this._UrlProvider.getDownloadAuctionResultsUrl();

    this.permissions = angularData.permissions;
  }

  ngOnInit() {
    this.resultsParms.start_delivery_date = this.formatDeliveryDate(moment().tz(angularData.railsExports.timeZone).startOf('day').add(1, 'day'))
    this.resultsParms.end_delivery_date = this.formatDeliveryDate(moment().tz(angularData.railsExports.timeZone).startOf('day').add(1, 'day'))
    this.getAuctionResults();
    this.getAssets();
  }

  getAuctionResults() {
    this.showLoader = true;
    this.bidsList = [];
    this._Auction.results(this.resultsParms).subscribe(
      result => {
        this.showLoader = false;
        this.bidsList = result.bids_list;
        this.auctionConfigs = [this.select_auction_config].concat(result.auction_configs);
        this.products = [this.select_products].concat(result.products);
        this.tsos = [this.select_tsos].concat(result.tsos);
        this.directions = [ this.direction_all ].concat(result.energy_directions);
        this.filterBidsTable();
      },
      error => {
        this.showLoader = false;
      }
    );
  }

  getAssets() {
    this._assetProvider.findAllFromNominationBids().subscribe(
    result => {
        this.assets = result.assets;
    }, err => {
        console.error('Failed to load asset list', err);
    });
  }

  dateSelectedCallback(date) {
    if (date.value[0] && date.value[1]) {
      this.resultsParms.start_delivery_date = this.formatDeliveryDate(date.value[0].startOf('day'))
      this.resultsParms.end_delivery_date = this.formatDeliveryDate(date.value[1].startOf('day'))
    } else {
      this.resultsParms.start_delivery_date = '';
      this.resultsParms.end_delivery_date = '';
      this.selectedDatesModel = [null, null];
      }
    this.getAuctionResults();
  }

  formatDeliveryDate(deliveryMoment) {
    return deliveryMoment.format('YYYY-MM-DD');
  }

  auctionConfigChangedCallback(data) {
    this.filterBidsTable();
  }

  tsoChangedCallback(data) {
    this.filterBidsTable();
  }

  productChangedCallback(data) {
    this.filterBidsTable();
  }

  directionChangedCallback(data) {
    this.filterBidsTable();
  }

  selectedAssetsChanged(event) {
    this.filterBidsTable();
  }

  toggleCheckbox(event) {
    event.preventDefault();
    event.stopPropagation();
    this.filter.accepted = !this.filter.accepted;

    if (!this.filter.accepted) {
      this.filter.accepted = null;
    }

    this.filterBidsTable();
  }

  filterBidsTable() {
    for (let i in this.bidsList) {
      let bid = this.bidsList[i];
      this.updateBidVisibility(bid);
    }

    let visible = this.bidsList.filter(b => b.visible == true);
    this.collectionSize = visible.length;
  }

  paginatedBids() {
    return this.bidsList
      .filter(b => b.visible == true)
      .map((bid, i) => ({ id: i + 1, ...bid }))
      .slice(
        (this.page - 1) * this.pageSize,
        (this.page - 1) * this.pageSize + this.pageSize
      );
  }

  updateBidVisibility(bid) {
    let visible = true;

    if (this.filter.tso !== "Select TSO" &&
      this.filter.tso !== "Alle" &&
      bid.tso_name != this.filter.tso) {
      visible = false;
    }
    else if (
      this.filter.auctionConfig !== "" &&
      bid.auction_config_id != this.filter.auctionConfig
    ) {
      visible = false;
    }
    else if (
      !this.matchAssetFilter(bid)
    ) {
      visible = false;
    }
    else if (
      this.filter.product !== "Select Products" &&
      this.filter.product !== "Alle" &&
      bid.product_interval != this.filter.product
    ) {
      visible = false;
    }
    else if (
      this.filter.direction != this.direction_all &&
      bid.energy_direction != this.filter.direction
    ) {
      visible = false;
    } else {
      let bid_accepted = bid.bid_status == "accepted" ? true : false;
      if (this.filter.accepted !== null && bid_accepted != this.filter.accepted) {
        visible = false;
      }
    }

    bid.visible = visible;
    return visible;
  }

  matchAssetFilter(bid) {
    if (Array.isArray(this.filter.selectedAssets) && this.filter.selectedAssets.length > 0) {
      let selAssetIds = this.filter.selectedAssets.map(x => x.id)
      console.log("#### MATCH BID ASSET ID", bid.asset_id)
      console.log("#### MATCH BID SEL ASSETs", selAssetIds)
      console.log("#### MATCH BID results", selAssetIds.includes(bid.asset_id))
      return selAssetIds.includes(bid.asset_id)
    } else {
      return true
    }
  }

  downloadAllAuctionResults() {
    setTimeout(() => {
      this.downloadAuctionResults.nativeElement.submit();
    });
  }

  dropdownSettings(singleSelection, allowSearchFilter) {
      return {
        idField: 'id',
        textField: 'id_and_name',
        singleSelection: singleSelection,
        selectAllText: angularData.railsExports.locale == 'en-GB' ? 'Select All' : 'Wählen Sie Alle',
        unSelectAllText: angularData.railsExports.locale == 'en-GB' ? 'UnSelect All' : 'Alle abwählen',
        searchPlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'Search' : 'Suche',
        noDataAvailablePlaceholderText: angularData.railsExports.locale == 'en-GB' ? 'No data available' : 'Keine Daten verfügbar',
        itemsShowLimit: 10,
        allowSearchFilter: allowSearchFilter
      }
    };

}
