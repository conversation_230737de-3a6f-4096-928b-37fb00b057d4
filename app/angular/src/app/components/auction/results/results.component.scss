@import "./../../../styles/colors";
vpp-management-auction-results {
  float: left;
  width: 100%;

  .hidden {
    display: none;
  }

  .min-width-250 {
    min-width: 250px;
  }

  .form-check {
    padding-top: 36px;
  }

  table.table-auction-results {
    tr:first-child > th:first-child {
      border-left:8px solid $eon-turquoise-dark;
    }

    tr.accepted > td:first-child {
      border-left:8px solid #08B002;
    }

    tr.pending > td:first-child {
      border-left:8px solid #c3c3c3;
    }

    tr.partial > td:first-child {
      border-left:8px solid #FFCC00;
    }

    tr.rejected > td:first-child {
      border-left:8px solid #EA1C0A;
    }

    td, th {
      &.text-right {
        text-align: right;
      }
    }
  }

  .input-group {
    .input-group-append {
      color: #343a40;
      border: 2px solid #bfbfbf;
      border-left: 0;
      border-radius: 0 4px 4px 0;
    }
  }

  .multiselect-dropdown {
    .dropdown-btn {
      border: 2px solid #bfbfbf !important;
      background: #fff !important;
      border-radius: 3px !important;
      min-height: 52px !important;
    }
  }

}
