<div class="container-fluid" *ngIf="permissions.can_see_auction_results">
    <div class="row">
        <div class="col">
            <h2>{{ 'AUCTIONS.RESULTS.TITLE' | translate }}</h2>
            <div class="table-filter d-flex">
              <div class="form-group min-width-250">
                  <label>{{ 'AUCTIONS.RESULTS.FILTERS.DELIVERY_DATE' | translate }}</label>
                  <input
                    type="text"
                    class="form-control"
                    [owlDateTime]="dt1"
                    [selectMode]="'range'"
                    [(ngModel)]="selectedDatesModel"
                    [owlDateTimeTrigger]="dt1"
                    (dateTimeChange)="dateSelectedCallback($event)"
                    placeholder="">
                  <owl-date-time
                    #dt1
                    (afterPickerOpen)="globalService.hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event)"
                    [pickerType]="'calendar'"
                    [pickerMode]="'popup'"></owl-date-time>
              </div>
              <div class="form-group">
                  <label>{{ 'AUCTIONS.RESULTS.FILTERS.AUCTION' | translate }}</label>
                  <select class="form-control"
                    [(ngModel)]="filter.auctionConfig"
                    (ngModelChange)="auctionConfigChangedCallback($event)">
                      <option [ngValue]="c.id" *ngFor="let c of auctionConfigs">{{ c.name }}</option>
                  </select>
              </div>
              <div class="form-group">
                  <label>{{ 'AUCTIONS.RESULTS.FILTERS.TSO' | translate }}</label>
                  <select class="form-control"
                    [(ngModel)]="filter.tso"
                    (ngModelChange)="tsoChangedCallback($event)">
                      <option [ngValue]="t" *ngFor="let t of tsos">{{ t }}</option>
                  </select>
              </div>
              <div class="form-group">
                  <label>{{ 'AUCTIONS.RESULTS.FILTERS.DIRECTION' | translate }}</label>
                  <select class="form-control"
                    [(ngModel)]="filter.direction"
                    (ngModelChange)="directionChangedCallback($event)">
                      <option [ngValue]="d" *ngFor="let d of directions">{{ 'AUCTIONS.RESULTS.ENERGY_DIRECTION.' + d | uppercase | translate }}</option>
                  </select>
              </div>
                <div class="form-group min-width-250">
                      <label>{{ 'AUCTIONS.RESULTS.FILTERS.ASSET' | translate }}</label>
                      <ng-multiselect-dropdown
                        id="inputAsset"
                        [placeholder]="('COMPONENTS.SELECT' | translate )"
                        [data]="assets"
                        [(ngModel)]="filter.selectedAssets"
                        [settings]="dropdownSettings(false, true)"
                        (onSelect)="selectedAssetsChanged($event)"
                        (onDeSelect)="selectedAssetsChanged($event)"
                        (onSelectAll)="selectedAssetsChanged($event)"
                        (onDeSelectAll)="selectedAssetsChanged($event)"
                      >
                      </ng-multiselect-dropdown>
                </div>
              <div class="form-check">
                <label class="eon-checkbox-label bg-eon-red" style="white-space: nowrap;" [ngClass]="filter.accepted == true ? 'checked' : ''" (click)="toggleCheckbox($event)">
                  {{ 'AUCTIONS.RESULTS.ACCEPTED_ONLY' | translate }}
                </label>
              </div>
              <div class="form-group" style="min-width: 200px; text-align: center;" *ngIf="bidsList.length">
                  <label>&nbsp;</label>
                  <button class="eon-button bg-eon-turquoise" (click)="downloadAllAuctionResults()">
                    <span>{{ 'AUCTIONS.RESULTS.DOWNLOAD' | translate }}</span>
                  </button>
              </div>
            </div>

            <table class="table table-auction-results table-striped table-bg-turquoise">
                <tr>
                    <th class="text-center">{{ 'AUCTIONS.RESULTS.TABLE.AUCTION' | translate }}</th>
                    <th class="text-center">{{ 'AUCTIONS.RESULTS.TABLE.TSO' | translate }}</th>
                    <th class="text-center">{{ 'AUCTIONS.RESULTS.TABLE.DATE' | translate }}</th>
                    <th class="text-center">{{ 'AUCTIONS.RESULTS.TABLE.PRODUCT' | translate }}</th>
                    <th class="text-center">{{ 'AUCTIONS.RESULTS.TABLE.CAPACITY_PRICE' | translate }}<br>(¤/MW)</th>
                    <th class="text-center">{{ 'AUCTIONS.RESULTS.TABLE.ENERGY_PRICE' | translate }}<br>(¤/MWh)</th>
                    <th class="text-center">{{ 'AUCTIONS.RESULTS.TABLE.CAPACITY' | translate }}<br>(MW)</th>
                    <th class="text-center">{{ 'AUCTIONS.RESULTS.TABLE.CAPACITY_ACCEPTED' | translate }}<br>(MW)</th>
                    <th class="text-center">{{ 'AUCTIONS.RESULTS.TABLE.ASSET' | translate }}</th>
                    <th class="text-center">{{ 'AUCTIONS.RESULTS.TABLE.ASSET_EXTERNAL_ID' | translate }}</th>
                </tr>
                <tr *ngIf="showLoader">
                  <td colspan="8" class="text-center vertical-center"><vpp-management-loader [showLoader]="showLoader"></vpp-management-loader>
                  <td>
                </tr>
                <ng-container *ngFor="let b of paginatedBids()">
                  <tr *ngIf="b.visible" [ngClass]="b.bid_status">
                      <td class="text-center">{{ b.auction_config_name }}</td>
                      <td class="text-center">{{ b.tso_name }}</td>
                      <td class="text-center">{{ b.start_time | localDate: 'DD.MM.YYYY' }}</td>
                      <td class="text-center">{{ b.product_interval_with_minutes }}</td>
                      <td class="text-center">{{ b.capacity_price }}</td>
                      <td class="text-center">{{ b.energy_price }}</td>
                      <td class="text-center">{{ b.flex_volume }}</td>
                      <td class="text-center">{{ b.accepted_flex_volume }}</td>
                      <td class="text-center">{{ b.asset_id_and_name }}</td>
                      <td class="text-center">{{ b.asset_external_id }}</td>
                  </tr>
                </ng-container>
            </table>
            <div class="d-flex justify-content-between">
              <ngb-pagination
                [collectionSize]="collectionSize"
                [(page)]="page"
                [pageSize]="pageSize">
              </ngb-pagination>

              <select class="custom-select" style="width: auto" [(ngModel)]="pageSize">
                <option [ngValue]="10">10 {{ 'AUCTIONS.RESULTS.TABLE.PER_PAGE' | translate }}</option>
                <option [ngValue]="20">20 {{ 'AUCTIONS.RESULTS.TABLE.PER_PAGE' | translate }}</option>
                <option [ngValue]="50">50 {{ 'AUCTIONS.RESULTS.TABLE.PER_PAGE' | translate }}</option>
              </select>
            </div>
        </div>
    </div>
</div>
<form
  #downloadAuctionResults
  name="downloadAuctionResults"
  action="{{ downloadAuctionResultsUrl }}"
  method="get"
  target="_blank">
  <input type="hidden" name="authenticity_token" value="{{ csr_token }}">
  <input type="hidden" name="auction_config_id" value="{{filter.auctionConfig}}"/>
  <input type="hidden" name="tso_id" value="{{filter.tso}}"/>
  <input type="hidden" name="direction" value="{{filter.direction}}"/>
  <ng-container *ngFor="let asset of filter.selectedAssets; let ix = index;">
      <input type="hidden" name="asset_ids[]" value="{{asset.id}}"/>
  </ng-container>
  <input type="hidden" name="start_delivery_date" value="{{resultsParms.start_delivery_date}}"/>
  <input type="hidden" name="end_delivery_date" value="{{resultsParms.end_delivery_date}}"/>
  <input type="hidden" name="accepted_only" value="{{filter.accepted}}"/>
</form>
