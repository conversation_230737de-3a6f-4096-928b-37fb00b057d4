.technical-support {
  z-index: 9999;
  padding: 0;
  position: fixed;
  top: 107px;
  right: 0;
  border-radius: 3px 0 0 3px;
  background: #1E1F23;
  opacity: 0.85;
  color: #fff !important;
  box-shadow: 0 1px 12px rgba(0, 0, 0, 0.16);

  .expand-btn.open {
    span > i {
      transform: rotate(180deg);
    }
  }

  .expand-btn {
    line-height: normal !important;
    height: 48px;
    float: left;
    padding: 3px 12px;
    text-align: center;
    border: none;
    background: none;

    &:focus {
      outline: none !important;
      box-shadow:none !important;
    }

    span {
      font-size: 11px;
      padding-bottom: 4px;
      font-weight: bold;
      display: block;
      opacity: 0.75;
      color: #fff !important;

      i {
        position: relative;
        left: 4px;
        font-size: 12px;
      }
    }
    > i {
      color: #fff !important;
      display: block;
      float: left;
      font-size: 18px;
    }
  }

  .phone-no {
    display: none;
    float: left;
    height: 48px;
    background: #fff;
    color: #f21c0a !important;
    padding: 0 20px 10px 10px;

    a {
      margin-top: 15px;
      display: block;
      font-size: 14px;
      color: #f21c0a !important;
    }

  }
}
