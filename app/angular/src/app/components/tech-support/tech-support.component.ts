import {
  Component,
  OnInit,
  ViewEncapsulation,
  ElementRef,
  HostListener
} from "@angular/core";
import { GlobalService } from "./../../services/global.service";
import { environment } from "../../../environments/environment";
import { angularData } from "./../../../global.export";
import { ToggleRightTechSupportAnimation } from './../../animations/toggle-right-tech-support';

@Component({
  selector: "vpp-management-tech-support",
  templateUrl: "./tech-support.component.html",
  styleUrls: ["./tech-support.component.scss"],
  encapsulation: ViewEncapsulation.None,
  animations: [ ToggleRightTechSupportAnimation ]
})
export class TechSupportComponent implements OnInit {
  environment = environment;
  componentActive: boolean = false;
  techSupportEmail: any;

  constructor(private _Global: GlobalService,
              private _elementRef: ElementRef) {
    this.techSupportEmail = angularData.railsExports.techSupportEmail;
  }

  @HostListener('document:click', ['$event'])
  handleGlobalClick(event) {
    if (!this._elementRef.nativeElement.contains(event.target)) {
      this.componentActive = false;
    }
  }

  ngOnInit() {
  }

  toggle() {
    this.componentActive = !this.componentActive;
  }
}
