import {Pipe, Injectable} from '@angular/core';

@Pipe({
  name: 'humanize'
})

@Injectable()
export class HumanizePipe {
  transform(text, options:any = {}, remove:any = []) {
    if (!text) { return ''; }

    text = text
      .replace(/([a-z])([A-Z])/g, '$1 $2')
      .replace(/[_]/g, ' ')
      .replace(/\./, ': ');

    if (Object.keys(options).length == 0 && remove.length == 0) {
      return text;
    }

    let words = text.split(/\s+/);

    if (options.titleize) {
      words = words.map(w => capitalize(w));
    }

    if (options.capitalize && words.length > 0) {
      words[0] = capitalize(words[0]);
    }

    words = words.filter(w => remove.indexOf(w.toLowerCase()) == -1);

    if (options.uppercase && words.length > 0) {
      words = words.map(w => w.toUpperCase());
    }

    return words.join(' ');
  }
}


function capitalize(string) {
  return string[0].toUpperCase() + string.slice(1);
}

function acronymize(string) {
  return string.split(/\s+/)
    .map(word =>
      /^[^aieouy]$/i.test(word) ? word.toUpperCase() : word
    ).join(' ');
}
