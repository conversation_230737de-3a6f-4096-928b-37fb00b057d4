import { Pipe, PipeTransform } from "@angular/core";
declare var angularData: any;
import * as moment from "moment";
import "moment-timezone";

@Pipe({ name: "localDate" })
export class LocalDatePipe implements PipeTransform {
  transform(input, format) {
    if (typeof input != "undefined" && input != null) {
      return moment(input)
        .utc()
        .tz(angularData.railsExports.timeZone)
        .format(format);
    }
  }
}
