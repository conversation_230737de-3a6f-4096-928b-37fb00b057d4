import { Pipe, PipeTransform } from "@angular/core";
declare var angularData: any;
import * as moment from "moment";
import "moment-timezone";

@Pipe({ name: "tzDate" })
export class TzDatePipe implements PipeTransform {
  transform(input, timeZone, format) {
    if (typeof input != "undefined" && input != null) {
      return moment(input)
        .utc()
        .tz(timeZone.toString())
        .format(format);
    }
  }
}
