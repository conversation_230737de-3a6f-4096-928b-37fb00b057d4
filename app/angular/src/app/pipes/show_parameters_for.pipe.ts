import { Pipe, PipeTransform } from "@angular/core";

@Pipe({ name: "showParametersFor" })
export class ShowParametersForPipe implements PipeTransform {
  transform(input: any, value: any): any {
    console.log("#### showParametersFor", input, value)
    if (!input) {
      return false;
    }
    else {
      if (Array.isArray(input)) {
        return input.some(o => o.id === value);
      } else {
        return input.id == value;
      }
    }
  }
}
