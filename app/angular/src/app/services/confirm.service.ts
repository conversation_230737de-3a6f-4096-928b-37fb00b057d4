import { Component, Injectable, TemplateRef, ElementRef, ViewChild, AfterViewInit } from '@angular/core';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';

interface ConfirmOptions {
  title?: string;
  message: string;
  isError?: boolean;
  isAlert?: boolean;
}

@Injectable()
export class ConfirmState {
  options: ConfirmOptions;
  modal: NgbModalRef;
  template: TemplateRef<any>;
}

@Injectable()
export class ConfirmService {

  constructor(
      private _modalService: NgbModal,
      private _state: ConfirmState) {
  }

  confirm(options: ConfirmOptions): Promise<any> {
    this._state.options = options;
    this._state.modal = this._modalService.open(this._state.template);
    return this._state.modal.result;
  }

  alert(options: ConfirmOptions): Promise<any> {
    this._state.options = {...options, isAlert: true};
    this._state.modal = this._modalService.open(this._state.template);
    return this._state.modal.result;
  }
}

@Component({
  selector: 'confirm-modal-component',
  templateUrl: './confirm.component.html',
  styleUrls: ['./confirm.component.sass']
})
export class ConfirmModalComponent implements AfterViewInit {
  options: ConfirmOptions;

  @ViewChild('autofocus', {static: false})
  autofocusElem: ElementRef;

  ngAfterViewInit() {
    setTimeout(() => this.autofocusElem.nativeElement.focus());
  }

  constructor(private _state: ConfirmState) {
    this.options = _state.options;
  }

  yes() {
    this._state.modal.close('confirmed');
  }

  no() {
    this._state.modal.dismiss('not confirmed');
  }
}