<div class="modal-header" *ngIf="!options.isAlert">
    <h4 class="modal-title">{{ options.title || ('CONFIRM.TITLE.GENERIC' | translate) }}</h4>
    <button *ngIf="!options.isError" type="button" class="close" aria-label="Close" (click)="no()">
        <span aria-hidden="true">&times;</span>
    </button>
</div>
<div class="modal-body">
    <p>{{ options.message }}</p>
</div>
<div class="modal-footer">
    <button *ngIf="options.isAlert" type="button" class="btn btn-full-red" (click)="no()">{{ 'CONFIRM.OK' | translate }}</button>
    <button *ngIf="!options.isAlert && options.isError" type="button" class="btn btn-full-red" (click)="no()">{{ 'CONFIRM.ACKNOWLEDGE' | translate }}</button>
    <button *ngIf="!options.isAlert && !options.isError" type="button" class="btn btn-full-red" (click)="yes()">{{ 'CONFIRM.CONFIRM' | translate }}</button>
    <button *ngIf="!options.isAlert && !options.isError" type="button" class="btn btn-outline-red" (click)="no()" #autofocus>{{ 'CONFIRM.ACKNOWLEDGE' | translate }}</button>
</div>