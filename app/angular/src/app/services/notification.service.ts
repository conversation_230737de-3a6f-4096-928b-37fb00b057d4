import { Observable } from 'rxjs/Observable';
import { UUID } from 'angular2-uuid';
import 'rxjs/add/operator/share';
import {message} from "ag-grid-community/dist/lib/utils/general";

export class NotificationService {
  _messages = [];
  _messagesObserver;
  messages$: Observable<any>;

  constructor() {
    this.messages$ = new Observable(observer => {
      this._messagesObserver = observer;
    }).share();
  }

  success(message) {
    message.type = "success";
    message.id = UUID.UUID();
    this._messages.push(message);
    if (this._messagesObserver) {
      this._messagesObserver.next(this._messages);
    }
    let index = message.id;

    this.autoCloseMessage(index);

    return index;
  }

  error(message) {
    message.type = "error";
    message.id = UUID.UUID();
    this._messages.push(message);
    if (this._messagesObserver) {
      this._messagesObserver.next(this._messages);
    }
    let index = message.id;

    this.autoCloseMessage(index)

    return index;
  }

  errors(messages) {
    if (messages != null && messages.length > 0) {
      messages.forEach((msg) => {
        this.error({text: msg});
      });
    }
  }

  popup(message) {
    message.type = "popup";
    message.id = UUID.UUID();
    this._messages.push(message);
    if (this._messagesObserver) {
      this._messagesObserver.next(this._messages);
    }
    let index = message.id;

    this.autoCloseMessage(index)

    return index;
  }

  info(message) {
    message.type = "info";
    message.id = UUID.UUID();
    this._messages.push(message);
    if (this._messagesObserver) {
      this._messagesObserver.next(this._messages);
    }
    let index = message.id;

    this.autoCloseMessage(index);

    return index;
  }

  warning(message) {
    message.type = "warning";
    message.id = UUID.UUID();
    this._messages.push(message);
    if (this._messagesObserver) {
      this._messagesObserver.next(this._messages);
    }
    let index = message.id;

    this.autoCloseMessage(index);

    return index;
  }

  popMessage(id) {
    let index = null;
    for (let i in this._messages) {
      let m = this._messages[i];
      if (m.id == id) {
        index = parseInt(i);
        break;
      }
    }

    if (index !== null) {
      this._messages.splice(index, 1)
    }

    if (this._messagesObserver) {
      this._messagesObserver.next(this._messages);
    }
  }

  messagesOnScreen() {
    return (this._messages.length == 0);
  }

  autoCloseMessage(id) {
    setTimeout(()=> {
      this.popMessage(id);
    }, 5000);
  }
}
