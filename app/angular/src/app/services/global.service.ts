import { Observable } from 'rxjs/Observable';
import 'rxjs/add/operator/share';

export class GlobalService {
  _title = '';
  _class = 'app-component';
  _titleObserver;
  _classObserver;
  _refreshAuthorizationNewObserver;
  title$: Observable<any>;
  class$: Observable<any>;
  refreshAuthorizationComponent$: Observable<any>;

  constructor() {
    this.title$ = new Observable(observer => {
      this._titleObserver = observer;
    }).share();

    this.class$ = new Observable(observer => {
      this._classObserver = observer;
    }).share();

    this.refreshAuthorizationComponent$ = new Observable(observer => {
      this._refreshAuthorizationNewObserver = observer;
    }).share();
  }

  changeTitle(title) {
    this._title = title;
    this._titleObserver.next(title);
  }

  changeClass(className) {
    this._class = className;
    this._classObserver.next(className);
  }

  refreshAuthorizationNewComponent() {
    this._refreshAuthorizationNewObserver.next(true);
  }

  public hackCallbackToCloseMenusAsThisComponentHasEventPreventDefault($event) {
    console.log('should be overwritten')
  }
}
