import { Observable } from 'rxjs/Observable';
import 'rxjs/add/operator/share';
import { Chart } from 'angular-highcharts';
import { TranslateService } from '@ngx-translate/core';
import { angularData } from "../../global.export";
import * as Moment from "moment";
import * as mTZ from "moment-timezone";


export class ChartService {
  constructor() {
    (window as any).moment = Moment;
    mTZ();
  }

  getAllocationDgChart(dgName, startTime, endTime, allocatedFlexSeries, nominatedFlexSeries, translateService) {
    let nomination = translateService.instant('ALLOCATIONS.VISUALIZE_ALLOCATIONS.NOMINATION');
    let chart = new Chart({
      chart: {
        type: 'arearange'
      },
      time: {
       timezone: angularData.railsExports.timeZone
      },
      plotOptions: {
        series: {
          step: 'left',
        },
        arearange: {
          fillOpacity: 0.2,
          lineColor: "rgba(0, 0, 0, 0)"
        }
      },
      rangeSelector: {
        selected: 2
      },
      title: {
        text: dgName,
      },
      credits: {
        enabled: false
      },
      exporting: {
        enabled: false
      },
      tooltip: {
        formatter: function() {
          const points = this.points;
          if (points && points.constructor === Array) {
            let res = "";
            for (let i = 0; i < points.length; i++) {
              const p = points[i].point;

              // this is for allocated flex
              if (p['description']) {
                res += "-" + Math.abs(p['low']) + "/ +" + p['high'] + " (MW)" +
                       " | " + p['description'] + "</span>" +
                       "<br/>";
              }
              // this is for nominated flex
              else {
                res += p.y + " MW " + nomination + "<br/>";
              }
            }

            return res;
          }
          else {
            return false;
          }
        },
        shared: true,
      },
      xAxis: {
        type: 'datetime',
        min: startTime,
        max: endTime
      },
      yAxis: {
        title: {
          text: translateService.instant('ALLOCATIONS.VISUALIZE_ALLOCATIONS.VALUE_AXIS')
        }
      },
      series: [
        {
          name: translateService.instant('ALLOCATIONS.VISUALIZE_ALLOCATIONS.ALLOCATED_FLEX'),
          data: allocatedFlexSeries || [],
          type: undefined
        },
        {
            name: translateService.instant('ALLOCATIONS.VISUALIZE_ALLOCATIONS.POSITIVE_NOMINATED_FLEX'),
            data: nominatedFlexSeries ? nominatedFlexSeries['positive'] : [],
            type: 'line',
        },
        {
            name: translateService.instant('ALLOCATIONS.VISUALIZE_ALLOCATIONS.NEGATIVE_NOMINATED_FLEX'),
            data: nominatedFlexSeries ? nominatedFlexSeries['negative'] : [],
            type: 'line',
        }
      ]
    });

    chart.ref$.subscribe(console.log);
    return chart;
  }

  getAllocationAssetChart(assetName, startTime, endTime, allocatedFlexSeries, translateService) {
    allocatedFlexSeries.forEach(function(data) {
      if (!data['exclussive']) {
        data['fillOpacity'] = 0;
        data['dashStyle'] = 'longdash';
        data['zIndex'] = 10;
        data['width'] = 2;
      }
      else {
        data['fillOpacity'] = 0.2;
        data['lineColor'] = "rgba(0, 0, 0, 0)";
      }
    });

    let chart = new Chart({
      chart: {
        type: 'arearange'
      },
      time: {
        timezone: angularData.railsExports.timeZone
      },
      plotOptions: {
        series: {
          step: 'left'
        },
      },
      rangeSelector: {
        selected: 2
      },
      title: {
        text: assetName
      },
      exporting: {
        enabled: false
      },
      tooltip: {
        formatter: function() {
          const points = this.points;
          if (points && points.constructor === Array) {
            let res = "";
            for (let i = 0; i < points.length; i++) {
              const p = points[i].point;

              res += "-" + Math.abs(p['low']) + "/ +" + p['high'] + " (MW)" +
                     " | " + p['description'] + "</span>" +
                     "<br/>";
            }
            return res;
          }
          else {
            return false;
          }
        },
        shared: true
      },
      xAxis: {
        type: 'datetime',
        min: startTime,
        max: endTime
      },
      yAxis: {
        title: {
          text: translateService.instant('ALLOCATIONS.VISUALIZE_ALLOCATIONS.VALUE_AXIS')
        }
      },
      series: allocatedFlexSeries
    });

    chart.ref$.subscribe(console.log);
    return chart;
  }
}
