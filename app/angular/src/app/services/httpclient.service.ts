import { Injectable } from "@angular/core";
import { HttpClient, HttpHeaders, HttpParams } from "@angular/common/http";
import { URLSearchParams } from "@angular/http";
import { Observable } from "rxjs/Observable";
import { environment } from "../../environments/environment";
import "rxjs/add/operator/share";
import "rxjs/add/operator/map";
import "rxjs/add/operator/finally";
import { Params } from "../lib/params";
import { SlimLoadingBarService } from "ng2-slim-loading-bar";
import { NotificationService } from "./notification.service";
declare let window: any;
declare let document: any;

@Injectable()
export class HttpClientService {
  static _idIndex = 0;
  activeRequests = [];
  failedRequests = [];
  currentRequests = 0;
  isLoading = false;

  constructor(
    public http: HttpClient,
    private _NotificationService: NotificationService,
    private slimLoadingBarService: SlimLoadingBarService
  ) {
    this.http = http;
    (<any>window).serializeParams = function(obj, prefix) {
      var str = [],
        p;
      for (p in obj) {
        if (obj.hasOwnProperty(p)) {
          var k = prefix ? prefix + (isNaN(p) ? "[" + p + "]" : "[]") : p,
            v = obj[p];
          str.push(
            v !== null && typeof v === "object"
              ? (<any>window).serializeParams(v, k)
              : encodeURIComponent(k) + "=" + encodeURIComponent(v)
          );
        }
      }
      return str.join("&");
    };
  }

  createAuthorizationHeader(headers: HttpHeaders, isMultipart = false) {
    let internalHeaders = headers;
    let token = "";
    try {
      token = document.querySelector('meta[name="csrf-token"]')["content"];
    } catch (e) {
      console.warn("Could not find CSRF token");
    }

    // headers.append('X-CSRF-Token', window.angularData.csrf_token);
    internalHeaders = internalHeaders.append("X-CSRF-Token", token);
    if (!isMultipart) {
      internalHeaders = internalHeaders.append(
        "Content-Type",
        "application/json"
      );
    }

    return internalHeaders;
  }

  send(req, url = null, type = "get") {
    this.incrementRequestCount();

    let shared = req.share();
    let ar = {
      id: ++HttpClientService._idIndex,
      request: shared
    };

    this.activeRequests.push(ar);

    shared
      .finally(() => {
        this.activeRequests.splice(this.activeRequests.indexOf(ar), 1);
      })
      .subscribe(
        r => {
          this.decrementRequestCount();

          if (!environment.production) {
            console.log("Http result:", url, r);
          }

          // this.notifier.hide(ar.id.toString())
          // this.notifier.notify( 'success', `Loaded: ${url}` );

          if (r.url && r.url.indexOf('vpp_authentication/login') > 0) {
            console.log('Redirected to login with success response', r);
            //window.alert('Session expired. Please log in again.');
            window.location.reload();
          }
        },
        e => {
          this.decrementRequestCount();

          if (!environment.production) {
            console.error("Http error:", url, e);
          }

          if (e.url && e.url.indexOf('vpp_authentication/login') > 0) {
            console.log('Redirected to login with error response', e);
            //window.alert('Session expired. Please log in again.');
            window.location.reload();
          }

          if (e.status === 401) {
            window.location.reload();
          }

          if (!environment.production) {
            this._NotificationService.error(JSON.stringify(e));
          }

          this.failedRequests.push(ar);
        }
      );
    return shared;
  }

  get(url, data = {}, remoteRequest?) {
    let type = "get";
    let headers = remoteRequest
      ? new HttpHeaders({ Accept: "application/javascript" })
      : new HttpHeaders();

    let query = (<any>window).serializeParams(data);

    if (query) {
      url = url + "?" + query;
    }

    if (environment.production) {
      url = `${environment.apiPath}${url}`;
    }

    let req = this.http.get(url, {
      headers: this.createAuthorizationHeader(headers)
    });

    return this.send(req, url, type);
  }

  delete(url) {
    let type = "delete";
    let headers = new HttpHeaders({ Accept: "application/json" });

    if (environment.production) {
      url = `${environment.apiPath}${url}`
    }

    let req = this.http.delete(url, {
      headers: this.createAuthorizationHeader(headers)
    });

    return this.send(req, url, type);
  }

  post(url, data = {}, isMultipart = false) {
    let type = "post";
    let headers = new HttpHeaders();

    if (environment.production) {
      url = `${environment.apiPath}${url}`;
    }

    let req = this.http.post(url, data, {
      headers: this.createAuthorizationHeader(headers, isMultipart)
    });

    return this.send(req, url, type);
  }

  patch(url, data = {}) {
    let headers = new HttpHeaders();

    if (environment.production) {
      url = `${environment.apiPath}${url}`;
    }

    let req = this.http.patch(url, data, {
      headers: this.createAuthorizationHeader(headers)
    });

    return this.send(req, url);
  }

  decrementRequestCount() {
    this.currentRequests--;

    if (this.currentRequests == 0) {
      this.isLoading = false;
      this.slimLoadingBarService.complete();
    }
  }

  incrementRequestCount() {
    this.currentRequests++;

    if (this.currentRequests == 1) {
      this.isLoading = true;
      this.slimLoadingBarService.start();
    }
  }

  getAuthToken() {
    try {
      return document.querySelector('meta[name="csrf-token"]')['content'];
    } catch(e) {
      return "Could not find CSRF token"
    }
  }
}
