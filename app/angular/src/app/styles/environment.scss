#environment_info_bar {
  background-color: #ffe52e;
  font-size: 0.8rem;
  padding: 3px;
  line-height: 1;
  color: black;

  a {
    color: #2774b1;
    text-decoration: underline;
  }
}

.user-agent-info {
  position: fixed;
  right: 0;
  top: 0;
  height: calc(0.8rem + 10px);
  z-index: 9999999999;
  pointer-events: none;
  line-height: 1;

  .ua-info-content {
    border-bottom-left-radius: 5px;
    background-color: rgba(#000,0.5);
    color: white;
    font-family: "Open Sans", sans-serif;
    font-size: 0.8rem;
    font-weight: normal;
    letter-spacing: 0.01em;
    padding: 5px;
    transition: 0.2s linear;
  }

  &:hover {
    .ua-info-content {
      transform: translateY(calc(-0.8rem - 10px));
    }
  }
}
