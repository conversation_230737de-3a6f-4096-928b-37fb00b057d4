@font-face {
  font-family: 'eon-icons';
  src: url('/assets/Eon-icons/eon-icons.eot?36181999');
  src: url('/assets/Eon-icons/eon-icons.eot?36181999#iefix') format('embedded-opentype'),
       url('/assets/Eon-icons/eon-icons.woff2?36181999') format('woff2'),
       url('/assets/Eon-icons/eon-icons.woff?36181999') format('woff'),
       url('/assets/Eon-icons/eon-icons.ttf?36181999') format('truetype'),
       url('/assets/Eon-icons/eon-icons.svg?36181999#eon-icons') format('svg');
  font-weight: normal;
  font-style: normal;
}

.eon-icon {
  width: 52px;
  height: 52px;
  display: inline-block;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;

  :before {
    font-family: "eon-icons";
    font-style: normal;
    font-weight: normal;
    speak: none;
    display: inline-block;
    text-decoration: inherit;
    width: 1em;
    text-align: center;

    /* For safety - reset parent styles, that can break glyph codes*/
    font-variant: normal;
    text-transform: none;

    /* fix buttons height, for twitter bootstrap */
    line-height: 52px;
    font-size: 52px;

    /* Font smoothing. That was taken from TWBS */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  &.facebook:before { content: '\e800'; }
  &.instagram:before { content: '\e801'; }
  &.twitter:before { content: '\e802'; }
  &.youtube:before { content: '\e803'; }
  &.arrow_down:before { content: '\e804'; }
  &.arrow_left:before { content: '\e805'; }
  &.arrow_right:before { content: '\e806'; }
  &.arrow_up:before { content: '\e807'; }
  &.bookmark:before { content: '\e808'; }
  &.burger_menu:before { content: '\e809'; }
  &.calendar:before { content: '\e80a'; }
  &.camera:before { content: '\e80b'; }
  &.cart:before { content: '\e80c'; }
  &.close:before { content: '\e80d'; }
  &.computer:before { content: '\e80e'; }
  &.download:before { content: '\e80f'; }
  &.drag_and_drop:before { content: '\e810'; }
  &.favourite:before { content: '\e811'; }
  &.filter:before { content: '\e812'; }
  &.forward:before { content: '\e813'; }
  &.fullcreen:before { content: '\e814'; }
  &.fullscreen_exit:before { content: '\e815'; }
  &.house:before { content: '\e816'; }
  &.letter:before { content: '\e817'; }
  &.location:before { content: '\e818'; }
  &.lock:before { content: '\e819'; }
  &.minus:before { content: '\e81a'; }
  &.pause:before { content: '\e81b'; }
  &.person:before { content: '\e81c'; }
  &.phone:before { content: '\e81d'; }
  &.play:before { content: '\e81e'; }
  &.plus:before { content: '\e81f'; }
  &.pointer_down:before { content: '\e820'; }
  &.pointer_left:before { content: '\e821'; }
  &.pointer_right:before { content: '\e822'; }
  &.pointer_s_down:before { content: '\e823'; }
  &.pointer_s_left:before { content: '\e824'; }
  &.pointer_s_right:before { content: '\e825'; }
  &.pointer_s_up:before { content: '\e826'; }
  &.pointer_up:before { content: '\e827'; }
  &.printer:before { content: '\e828'; }
  &.rewind:before { content: '\e829'; }
  &.search:before { content: '\e82a'; }
  &.settings:before { content: '\e82b'; }
  &.share:before { content: '\e82c'; }
  &.smartphone:before { content: '\e82d'; }
  &.speak_bubble:before { content: '\e82e'; }
  &.stop:before { content: '\e82f'; }
  &.tablet:before { content: '\e830'; }
  &.television:before { content: '\e831'; }
  &.trash:before { content: '\e832'; }
  &.upload:before { content: '\e833'; }
  &.warning:before { content: '\e834'; }

  &.eon-red { color: $eon-red; }
  &.eon-white { color: $eon-white; }
  &.eon-turquoise { color: $eon-turquoise; }
  &.eon-bordeaux { color: $eon-bordeaux; }
  &.eon-limeyellow { color: $eon-limeyellow; }
  &.eon-black { color: $eon-black; }
  &.eon-darkgrey { color: $eon-darkgrey; }

  &.solid.eon-red {
    color: $eon-white;
    @include background-svg('<svg width="52px" height="52px" viewBox="0 0 52 52" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M8.41491242,1.67250109 C4.99456854,2.32324671 2.32318817,4.99462047 1.67244094,8.4149559 C-0.557480315,20.1384043 -0.557480315,31.8615957 1.67244094,43.5850441 C2.32318817,47.0053795 4.99456854,49.6767533 8.41491242,50.3274989 C14.2765226,51.4423282 20.1383898,52 26,52 C31.8616102,52 37.7234774,51.4423282 43.5850876,50.3274989 C47.0054315,49.6767533 49.6768118,47.0053795 50.3275591,43.5850441 C52.5574803,31.8615957 52.5574803,20.1384043 50.3275591,8.4149559 C49.6768118,4.99462047 47.0054315,2.32324671 43.5850876,1.67250109 C37.7234774,0.557414659 31.8616102,0 26,0 C20.1383898,0 14.2765226,0.557414659 8.41491242,1.67250109 Z" id="Fill-281-Copy-10" fill="#EA1C0A"></path></svg>');
  }
  &.solid.eon-turquoise {
    color: $eon-white;
    @include background-svg('<svg width="52px" height="52px" viewBox="0 0 52 52" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M8.41491242,1.67250109 C4.99456854,2.32324671 2.32318817,4.99462047 1.67244094,8.4149559 C-0.557480315,20.1384043 -0.557480315,31.8615957 1.67244094,43.5850441 C2.32318817,47.0053795 4.99456854,49.6767533 8.41491242,50.3274989 C14.2765226,51.4423282 20.1383898,52 26,52 C31.8616102,52 37.7234774,51.4423282 43.5850876,50.3274989 C47.0054315,49.6767533 49.6768118,47.0053795 50.3275591,43.5850441 C52.5574803,31.8615957 52.5574803,20.1384043 50.3275591,8.4149559 C49.6768118,4.99462047 47.0054315,2.32324671 43.5850876,1.67250109 C37.7234774,0.557414659 31.8616102,0 26,0 C20.1383898,0 14.2765226,0.557414659 8.41491242,1.67250109 Z" id="Fill-281-Copy-10" fill="#1EA2B1"></path></svg>');
  }
  &.solid.eon-limeyellow {
    color: $eon-bordeaux;
    @include background-svg('<svg width="52px" height="52px" viewBox="0 0 52 52" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M8.41491242,1.67250109 C4.99456854,2.32324671 2.32318817,4.99462047 1.67244094,8.4149559 C-0.557480315,20.1384043 -0.557480315,31.8615957 1.67244094,43.5850441 C2.32318817,47.0053795 4.99456854,49.6767533 8.41491242,50.3274989 C14.2765226,51.4423282 20.1383898,52 26,52 C31.8616102,52 37.7234774,51.4423282 43.5850876,50.3274989 C47.0054315,49.6767533 49.6768118,47.0053795 50.3275591,43.5850441 C52.5574803,31.8615957 52.5574803,20.1384043 50.3275591,8.4149559 C49.6768118,4.99462047 47.0054315,2.32324671 43.5850876,1.67250109 C37.7234774,0.557414659 31.8616102,0 26,0 C20.1383898,0 14.2765226,0.557414659 8.41491242,1.67250109 Z" id="Fill-281-Copy-10" fill="#E3E000"></path></svg>');
  }

  &.solid.eon-bordeaux {
    color: $eon-white;
    @include background-svg('<svg width="52px" height="52px" viewBox="0 0 52 52" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M8.41491242,1.67250109 C4.99456854,2.32324671 2.32318817,4.99462047 1.67244094,8.4149559 C-0.557480315,20.1384043 -0.557480315,31.8615957 1.67244094,43.5850441 C2.32318817,47.0053795 4.99456854,49.6767533 8.41491242,50.3274989 C14.2765226,51.4423282 20.1383898,52 26,52 C31.8616102,52 37.7234774,51.4423282 43.5850876,50.3274989 C47.0054315,49.6767533 49.6768118,47.0053795 50.3275591,43.5850441 C52.5574803,31.8615957 52.5574803,20.1384043 50.3275591,8.4149559 C49.6768118,4.99462047 47.0054315,2.32324671 43.5850876,1.67250109 C37.7234774,0.557414659 31.8616102,0 26,0 C20.1383898,0 14.2765226,0.557414659 8.41491242,1.67250109 Z" id="Fill-281-Copy-10" fill="#B00402"></path></svg>');
    &.eon-limeyellow {
      color: $eon-limeyellow;
    }
  }
  &.solid.eon-black {
    color: $eon-white;
    @include background-svg('<svg width="52px" height="52px" viewBox="0 0 52 52" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M8.41491242,1.67250109 C4.99456854,2.32324671 2.32318817,4.99462047 1.67244094,8.4149559 C-0.557480315,20.1384043 -0.557480315,31.8615957 1.67244094,43.5850441 C2.32318817,47.0053795 4.99456854,49.6767533 8.41491242,50.3274989 C14.2765226,51.4423282 20.1383898,52 26,52 C31.8616102,52 37.7234774,51.4423282 43.5850876,50.3274989 C47.0054315,49.6767533 49.6768118,47.0053795 50.3275591,43.5850441 C52.5574803,31.8615957 52.5574803,20.1384043 50.3275591,8.4149559 C49.6768118,4.99462047 47.0054315,2.32324671 43.5850876,1.67250109 C37.7234774,0.557414659 31.8616102,0 26,0 C20.1383898,0 14.2765226,0.557414659 8.41491242,1.67250109 Z" id="Fill-281-Copy-10" fill="#000000"></path></svg>');
  }
  &.solid.eon-darkgrey {
    color: $eon-white;
    @include background-svg('<svg width="52px" height="52px" viewBox="0 0 52 52" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M8.41491242,1.67250109 C4.99456854,2.32324671 2.32318817,4.99462047 1.67244094,8.4149559 C-0.557480315,20.1384043 -0.557480315,31.8615957 1.67244094,43.5850441 C2.32318817,47.0053795 4.99456854,49.6767533 8.41491242,50.3274989 C14.2765226,51.4423282 20.1383898,52 26,52 C31.8616102,52 37.7234774,51.4423282 43.5850876,50.3274989 C47.0054315,49.6767533 49.6768118,47.0053795 50.3275591,43.5850441 C52.5574803,31.8615957 52.5574803,20.1384043 50.3275591,8.4149559 C49.6768118,4.99462047 47.0054315,2.32324671 43.5850876,1.67250109 C37.7234774,0.557414659 31.8616102,0 26,0 C20.1383898,0 14.2765226,0.557414659 8.41491242,1.67250109 Z" id="Fill-281-Copy-10" fill="#39393A"></path></svg>');
  }

  &.light.eon-red {
    @include background-svg('<svg version="1.1" id="Icons_red_on_red_outline" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="14px" height="14px" viewBox="1 1 14 14" enable-background="new 1 1 14 14" xml:space="preserve"><g><path fill="#EA1C0A" d="M8,1.667c1.547,0.002,3.09,0.15,4.609,0.44c0.652,0.122,1.162,0.632,1.284,1.284 c0.581,3.045,0.581,6.174,0,9.219c-0.122,0.652-0.632,1.162-1.284,1.284c-1.52,0.29-3.062,0.438-4.609,0.44 c-1.547-0.003-3.09-0.15-4.61-0.44c-0.652-0.122-1.162-0.632-1.284-1.284c-0.582-3.045-0.582-6.174,0-9.219 C2.229,2.738,2.738,2.229,3.39,2.106C4.91,1.816,6.453,1.669,8,1.667 M8,1C6.412,1.001,4.827,1.152,3.267,1.45 C2.345,1.624,1.624,2.345,1.45,3.267c-0.601,3.128-0.601,6.342,0,9.47c0.175,0.92,0.896,1.64,1.816,1.813 C4.827,14.848,6.412,14.999,8,15c1.588-0.001,3.174-0.152,4.733-0.45c0.921-0.174,1.643-0.896,1.816-1.816 c0.601-3.128,0.601-6.342,0-9.47c-0.175-0.92-0.896-1.639-1.816-1.813C11.174,1.152,9.588,1.001,8,1z"/></g></svg>');
  }
  &.light.eon-white {
    @include background-svg('<svg version="1.1" id="Icons_red_on_red_outline" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="14px" height="14px" viewBox="1 1 14 14" enable-background="new 1 1 14 14" xml:space="preserve"><g><path fill="#FFFFFF" d="M8,1.667c1.547,0.002,3.09,0.15,4.609,0.44c0.652,0.122,1.162,0.632,1.284,1.284 c0.581,3.045,0.581,6.174,0,9.219c-0.122,0.652-0.632,1.162-1.284,1.284c-1.52,0.29-3.062,0.438-4.609,0.44 c-1.547-0.003-3.09-0.15-4.61-0.44c-0.652-0.122-1.162-0.632-1.284-1.284c-0.582-3.045-0.582-6.174,0-9.219 C2.229,2.738,2.738,2.229,3.39,2.106C4.91,1.816,6.453,1.669,8,1.667 M8,1C6.412,1.001,4.827,1.152,3.267,1.45 C2.345,1.624,1.624,2.345,1.45,3.267c-0.601,3.128-0.601,6.342,0,9.47c0.175,0.92,0.896,1.64,1.816,1.813 C4.827,14.848,6.412,14.999,8,15c1.588-0.001,3.174-0.152,4.733-0.45c0.921-0.174,1.643-0.896,1.816-1.816 c0.601-3.128,0.601-6.342,0-9.47c-0.175-0.92-0.896-1.639-1.816-1.813C11.174,1.152,9.588,1.001,8,1z"/></g></svg>');
  }
  &.light.eon-turquoise {
    @include background-svg('<svg version="1.1" id="Icons_red_on_red_outline" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="14px" height="14px" viewBox="1 1 14 14" enable-background="new 1 1 14 14" xml:space="preserve"><g><path fill="#1EA2B1" d="M8,1.667c1.547,0.002,3.09,0.15,4.609,0.44c0.652,0.122,1.162,0.632,1.284,1.284 c0.581,3.045,0.581,6.174,0,9.219c-0.122,0.652-0.632,1.162-1.284,1.284c-1.52,0.29-3.062,0.438-4.609,0.44 c-1.547-0.003-3.09-0.15-4.61-0.44c-0.652-0.122-1.162-0.632-1.284-1.284c-0.582-3.045-0.582-6.174,0-9.219 C2.229,2.738,2.738,2.229,3.39,2.106C4.91,1.816,6.453,1.669,8,1.667 M8,1C6.412,1.001,4.827,1.152,3.267,1.45 C2.345,1.624,1.624,2.345,1.45,3.267c-0.601,3.128-0.601,6.342,0,9.47c0.175,0.92,0.896,1.64,1.816,1.813 C4.827,14.848,6.412,14.999,8,15c1.588-0.001,3.174-0.152,4.733-0.45c0.921-0.174,1.643-0.896,1.816-1.816 c0.601-3.128,0.601-6.342,0-9.47c-0.175-0.92-0.896-1.639-1.816-1.813C11.174,1.152,9.588,1.001,8,1z"/></g></svg>');
  }
  &.light.eon-bordeaux {
    @include background-svg('<svg version="1.1" id="Icons_red_on_red_outline" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="14px" height="14px" viewBox="1 1 14 14" enable-background="new 1 1 14 14" xml:space="preserve"><g><path fill="#B00402" d="M8,1.667c1.547,0.002,3.09,0.15,4.609,0.44c0.652,0.122,1.162,0.632,1.284,1.284 c0.581,3.045,0.581,6.174,0,9.219c-0.122,0.652-0.632,1.162-1.284,1.284c-1.52,0.29-3.062,0.438-4.609,0.44 c-1.547-0.003-3.09-0.15-4.61-0.44c-0.652-0.122-1.162-0.632-1.284-1.284c-0.582-3.045-0.582-6.174,0-9.219 C2.229,2.738,2.738,2.229,3.39,2.106C4.91,1.816,6.453,1.669,8,1.667 M8,1C6.412,1.001,4.827,1.152,3.267,1.45 C2.345,1.624,1.624,2.345,1.45,3.267c-0.601,3.128-0.601,6.342,0,9.47c0.175,0.92,0.896,1.64,1.816,1.813 C4.827,14.848,6.412,14.999,8,15c1.588-0.001,3.174-0.152,4.733-0.45c0.921-0.174,1.643-0.896,1.816-1.816 c0.601-3.128,0.601-6.342,0-9.47c-0.175-0.92-0.896-1.639-1.816-1.813C11.174,1.152,9.588,1.001,8,1z"/></g></svg>');
  }
  &.light.eon-limeyellow {
    @include background-svg('<svg version="1.1" id="Icons_red_on_red_outline" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="14px" height="14px" viewBox="1 1 14 14" enable-background="new 1 1 14 14" xml:space="preserve"><g><path fill="#E3E000" d="M8,1.667c1.547,0.002,3.09,0.15,4.609,0.44c0.652,0.122,1.162,0.632,1.284,1.284 c0.581,3.045,0.581,6.174,0,9.219c-0.122,0.652-0.632,1.162-1.284,1.284c-1.52,0.29-3.062,0.438-4.609,0.44 c-1.547-0.003-3.09-0.15-4.61-0.44c-0.652-0.122-1.162-0.632-1.284-1.284c-0.582-3.045-0.582-6.174,0-9.219 C2.229,2.738,2.738,2.229,3.39,2.106C4.91,1.816,6.453,1.669,8,1.667 M8,1C6.412,1.001,4.827,1.152,3.267,1.45 C2.345,1.624,1.624,2.345,1.45,3.267c-0.601,3.128-0.601,6.342,0,9.47c0.175,0.92,0.896,1.64,1.816,1.813 C4.827,14.848,6.412,14.999,8,15c1.588-0.001,3.174-0.152,4.733-0.45c0.921-0.174,1.643-0.896,1.816-1.816 c0.601-3.128,0.601-6.342,0-9.47c-0.175-0.92-0.896-1.639-1.816-1.813C11.174,1.152,9.588,1.001,8,1z"/></g></svg>');
  }
  &.light.eon-black {
    @include background-svg('<svg version="1.1" id="Icons_red_on_red_outline" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="14px" height="14px" viewBox="1 1 14 14" enable-background="new 1 1 14 14" xml:space="preserve"><g><path fill="#000000" d="M8,1.667c1.547,0.002,3.09,0.15,4.609,0.44c0.652,0.122,1.162,0.632,1.284,1.284 c0.581,3.045,0.581,6.174,0,9.219c-0.122,0.652-0.632,1.162-1.284,1.284c-1.52,0.29-3.062,0.438-4.609,0.44 c-1.547-0.003-3.09-0.15-4.61-0.44c-0.652-0.122-1.162-0.632-1.284-1.284c-0.582-3.045-0.582-6.174,0-9.219 C2.229,2.738,2.738,2.229,3.39,2.106C4.91,1.816,6.453,1.669,8,1.667 M8,1C6.412,1.001,4.827,1.152,3.267,1.45 C2.345,1.624,1.624,2.345,1.45,3.267c-0.601,3.128-0.601,6.342,0,9.47c0.175,0.92,0.896,1.64,1.816,1.813 C4.827,14.848,6.412,14.999,8,15c1.588-0.001,3.174-0.152,4.733-0.45c0.921-0.174,1.643-0.896,1.816-1.816 c0.601-3.128,0.601-6.342,0-9.47c-0.175-0.92-0.896-1.639-1.816-1.813C11.174,1.152,9.588,1.001,8,1z"/></g></svg>');
  }
  &.light.eon-darkgrey {
    @include background-svg('<svg version="1.1" id="Icons_red_on_red_outline" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="14px" height="14px" viewBox="1 1 14 14" enable-background="new 1 1 14 14" xml:space="preserve"><g><path fill="#39393a" d="M8,1.667c1.547,0.002,3.09,0.15,4.609,0.44c0.652,0.122,1.162,0.632,1.284,1.284 c0.581,3.045,0.581,6.174,0,9.219c-0.122,0.652-0.632,1.162-1.284,1.284c-1.52,0.29-3.062,0.438-4.609,0.44 c-1.547-0.003-3.09-0.15-4.61-0.44c-0.652-0.122-1.162-0.632-1.284-1.284c-0.582-3.045-0.582-6.174,0-9.219 C2.229,2.738,2.738,2.229,3.39,2.106C4.91,1.816,6.453,1.669,8,1.667 M8,1C6.412,1.001,4.827,1.152,3.267,1.45 C2.345,1.624,1.624,2.345,1.45,3.267c-0.601,3.128-0.601,6.342,0,9.47c0.175,0.92,0.896,1.64,1.816,1.813 C4.827,14.848,6.412,14.999,8,15c1.588-0.001,3.174-0.152,4.733-0.45c0.921-0.174,1.643-0.896,1.816-1.816 c0.601-3.128,0.601-6.342,0-9.47c-0.175-0.92-0.896-1.639-1.816-1.813C11.174,1.152,9.588,1.001,8,1z"/></g></svg>');
  }

  &.solid.eon-white {
    color: $eon-red;
    @include background-svg('<svg width="52px" height="52px" viewBox="0 0 52 52" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M8.41491242,1.67250109 C4.99456854,2.32324671 2.32318817,4.99462047 1.67244094,8.4149559 C-0.557480315,20.1384043 -0.557480315,31.8615957 1.67244094,43.5850441 C2.32318817,47.0053795 4.99456854,49.6767533 8.41491242,50.3274989 C14.2765226,51.4423282 20.1383898,52 26,52 C31.8616102,52 37.7234774,51.4423282 43.5850876,50.3274989 C47.0054315,49.6767533 49.6768118,47.0053795 50.3275591,43.5850441 C52.5574803,31.8615957 52.5574803,20.1384043 50.3275591,8.4149559 C49.6768118,4.99462047 47.0054315,2.32324671 43.5850876,1.67250109 C37.7234774,0.557414659 31.8616102,0 26,0 C20.1383898,0 14.2765226,0.557414659 8.41491242,1.67250109 Z" id="Fill-281-Copy-10" fill="#FFFFFF"></path></svg>');
    &.eon-bordeaux {
      color: $eon-bordeaux;
    }
    &.eon-turquoise {
      color: $eon-turquoise;
    }
  }
}
