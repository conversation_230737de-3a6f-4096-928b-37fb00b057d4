@mixin get-checkbox-unselected($color) {
  $colorR: red($color);
  $colorG: green($color);
  $colorB: blue($color);
  @include background-svg('<svg width="32px" height="32px" viewBox="0 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><defs><path d="M5.17840764,1.02923144 C3.07358064,1.42969028 1.42965426,3.07361259 1.02919443,5.1784344 C-0.343064809,12.3928642 -0.343064809,19.6071358 1.02919443,26.8215656 C1.42965426,28.9263874 3.07358064,30.5703097 5.17840764,30.9707686 C8.78555236,31.6568174 12.3928553,32 16,32 C19.6071447,32 23.2144476,31.6568174 26.8215924,30.9707686 C28.9264194,30.5703097 30.5703457,28.9263874 30.9708056,26.8215656 C32.3430648,19.6071358 32.3430648,12.3928642 30.9708056,5.1784344 C30.5703457,3.07361259 28.9264194,1.42969028 26.8215924,1.02923144 C23.2144476,0.343024406 19.6071447,0 16,0 C12.3928553,0 8.78555236,0.343024406 5.17840764,1.02923144 Z" id="path-1"></path><mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="32" height="32" fill="white"><use xlink:href="#path-1"></use></mask></defs><use id="Fill-281-Copy-13" stroke="rgb(#{$colorR},#{$colorG},#{$colorB})" mask="url(#mask-2)" stroke-width="4" fill="none" xlink:href="#path-1"></use></svg>');
}

@mixin get-checkbox-selected($color, $colorChecked) {
  $colorR: red($color);
  $colorG: green($color);
  $colorB: blue($color);
  $colorCheckedR: red($colorChecked);
  $colorCheckedG: green($colorChecked);
  $colorCheckedB: blue($colorChecked);
  @include background-svg('<svg width="32px" height="32px" viewBox="699 0 32 32" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="Check_box_selected-Copy-3" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" transform="translate(699.000000, 0.000000)"><path d="M5.17840764,1.02923144 C3.07358064,1.42969028 1.42965426,3.07361259 1.02919443,5.1784344 C-0.343064809,12.3928642 -0.343064809,19.6071358 1.02919443,26.8215656 C1.42965426,28.9263874 3.07358064,30.5703097 5.17840764,30.9707686 C8.78555236,31.6568174 12.3928553,32 16,32 C19.6071447,32 23.2144476,31.6568174 26.8215924,30.9707686 C28.9264194,30.5703097 30.5703457,28.9263874 30.9708056,26.8215656 C32.3430648,19.6071358 32.3430648,12.3928642 30.9708056,5.1784344 C30.5703457,3.07361259 28.9264194,1.42969028 26.8215924,1.02923144 C23.2144476,0.343024406 19.6071447,0 16,0 C12.3928553,0 8.78555236,0.343024406 5.17840764,1.02923144 Z" id="Fill-281-Copy-7" fill="rgb(#{$colorR},#{$colorG},#{$colorB})"></path><path d="M14.0347642,24.208164 C14.5057113,24.4347242 15.0761315,24.2927352 15.386936,23.8488604 L25.4565159,9.46800991 C25.8101005,8.96303885 25.6913069,8.27200817 25.199064,7.91329439 C24.7033893,7.55207973 24.024003,7.66906052 23.6739469,8.16899242 L14.3063855,21.5472565 L8.65587014,16.1339443 C8.17086414,15.6692984 7.41587575,15.6901562 6.96114541,16.1897727 L7.03424599,16.1094564 C6.58328136,16.6049355 6.6065753,17.3791492 7.09588334,17.8479165 L13.40206,23.889366 C13.584801,24.0644357 13.8058696,24.1705814 14.0347642,24.208164 Z" id="Combined-Shape-Copy" fill="rgb(#{$colorCheckedR},#{$colorCheckedG},#{$colorCheckedB})" transform="translate(16.186138, 16.008926) rotate(-1.000000) translate(-16.186138, -16.008926) "></path></g></svg>');
}

@mixin get-radio-unselected($color) {
  $colorR: red($color);
  $colorG: green($color);
  $colorB: blue($color);
  @include background-svg('<svg width="33px" height="33px" viewBox="0 0 33 33" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><defs><circle id="path-1" cx="16" cy="16" r="16"></circle><mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="32" height="32" fill="white"><use xlink:href="#path-1"></use></mask></defs><use id="Oval-3" stroke="rgb(#{$colorR},#{$colorG},#{$colorB})" mask="url(#mask-2)" stroke-width="4" fill="none" xlink:href="#path-1"></use></svg>');
}

@mixin get-radio-selected($color, $colorChecked) {
  $colorR: red($color);
  $colorG: green($color);
  $colorB: blue($color);
  $colorCheckedR: red($colorChecked);
  $colorCheckedG: green($colorChecked);
  $colorCheckedB: blue($colorChecked);
  @include background-svg('<svg width="33px" height="33px" viewBox="599 0 33 33" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="Radio_Btn_selected-Copy" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" transform="translate(599.000000, 0.000000)"><circle id="Oval-3-Copy-5" fill="rgb(#{$colorR},#{$colorG},#{$colorB})" cx="16" cy="16" r="16"></circle><circle id="Oval-4" fill="rgb(#{$colorCheckedR},#{$colorCheckedG},#{$colorCheckedB})" cx="16" cy="16" r="5"></circle></g></svg>');
}



.eon-checkbox-label,
.eon-radio-label {
  font-weight: 400;
  height: 32px;
  line-height: 32px;
  padding-left: 42px;
  font-size: 18px;
  position: relative;

  &:before {
    content: "";
    width: 32px;
    height: 32px;
    display: inline-block;
    position: absolute;
    left: 0px;
    background-size: contain;
    background-position: center center;
    background-repeat: no-repeat;
  }


  input[type=checkbox].eon-checkbox,
  input[type=radio].eon-radio {
    display: none;
  }

  &:hover {
    cursor: pointer;
  }
}

.eon-checkbox-label.non-clickable {
  cursor: default;
}


.eon-checkbox-label {
  &:before {
    @include get-checkbox-unselected($eon-middlegrey);
  }

  background-color: transparent;
  &:before {
    @include get-checkbox-unselected($eon-middlegrey);
  }
  &.bg-eon-red {
    background-color: transparent;
    &:before {
      @include get-checkbox-unselected($eon-middlegrey);
    }
  }

  &.bg-eon-bordeaux {
    background-color: transparent;
    color: $eon-bordeaux;
    &:before {
      @include get-checkbox-unselected($eon-middlegrey);
    }
    &.eon-limeyellow {
      &:before {
        @include get-checkbox-unselected($eon-middlegrey);
      }
    }
  }

  &.bg-eon-turquoise {
    background-color: transparent;
    &:before {
      @include get-checkbox-unselected($eon-middlegrey);
    }
  }

  &.bg-eon-limeyellow {
    background-color: transparent;
    &:before {
      @include get-checkbox-unselected($eon-middlegrey);
    }
  }

  &.bg-eon-white {
    background-color: transparent;
    color: $eon-white;
    &:before {
      @include get-checkbox-unselected($eon-white);
    }
    &.eon-red:before {
      @include get-checkbox-unselected($eon-white);
    }
    &.eon-bordeaux:before {
      @include get-checkbox-unselected($eon-white);
    }
    &.eon-turquoise:before {
      @include get-checkbox-unselected($eon-white);
    }
  }
  &.bg-eon-red-75 {
    background-color: transparent;
    &:before {
      @include get-checkbox-unselected($eon-red-75);
    }
  }

  &.bg-eon-bordeaux-75 {
    background-color: transparent;
    &:before {
      @include get-checkbox-unselected($eon-bordeaux-75);
    }
  }

  &.bg-eon-limeyellow-75 {
    background-color: transparent;
    &:before {
      @include get-checkbox-unselected($eon-limeyellow-75);
    }
  }

  &.bg-eon-turquoise-75 {
    background-color: transparent;
    &:before {
      @include get-checkbox-unselected($eon-turquoise-75);
    }
  }



  &.checked {
    background-color: transparent;
    &:before {
      @include get-checkbox-selected($eon-middlegrey, $eon-white);
    }
    &.bg-eon-red {
      background-color: transparent;
      &:before {
        @include get-checkbox-selected($eon-red, $eon-white);
      }
    }

    &.bg-eon-bordeaux {
      background-color: transparent;
      color: $eon-bordeaux;
      &:before {
        @include get-checkbox-selected($eon-bordeaux, $eon-white);
      }
      &.eon-limeyellow {
        &:before {
          @include get-checkbox-selected($eon-bordeaux, $eon-limeyellow);
        }
      }
    }

    &.bg-eon-turquoise {
      background-color: transparent;
      &:before {
        @include get-checkbox-selected($eon-turquoise, $eon-white);
      }
    }

    &.bg-eon-limeyellow {
      background-color: transparent;
      &:before {
        @include get-checkbox-selected($eon-limeyellow, $eon-bordeaux);
      }
    }

    &.bg-eon-white {
      background-color: transparent;
      color: $eon-white;
      &:before {
        @include get-checkbox-selected($eon-white, $eon-red);
      }
      &.eon-red:before {
        @include get-checkbox-selected($eon-white, $eon-red);
      }
      &.eon-bordeaux:before {
        @include get-checkbox-selected($eon-white, $eon-bordeaux);
      }
      &.eon-turquoise:before {
        @include get-checkbox-selected($eon-white, $eon-turquoise);
      }
    }
    &.bg-eon-red-75 {
      background-color: transparent;
      &:before {
        @include get-checkbox-selected($eon-red-75, $eon-white);
      }
    }

    &.bg-eon-bordeaux-75 {
      background-color: transparent;
      &:before {
        @include get-checkbox-selected($eon-bordeaux-75, $eon-white);
      }
    }

    &.bg-eon-limeyellow-75 {
      background-color: transparent;
      &:before {
        @include get-checkbox-selected($eon-limeyellow-75, $eon-white);
      }
    }

    &.bg-eon-turquoise-75 {
      background-color: transparent;
      &:before {
        @include get-checkbox-selected($eon-turquoise-75, $eon-white);
      }
    }
  }


  &.disabled {
    color: $eon-darkgrey-25;
    &:hover {
      cursor: default;
    }
    &:before {
      @include get-checkbox-unselected($eon-darkgrey-25);
    }
    &.eon-limeyellow-25 {
      color: $eon-limeyellow-25;
      &:before {
        @include get-checkbox-unselected($eon-limeyellow-25);
      }
    }
    &.eon-bordeaux-25 {
      color: $eon-bordeaux-25;
      &:before {
        @include get-checkbox-unselected($eon-bordeaux-25);
      }
    }
    &.eon-red-25 {
      color: $eon-red-25;
      &:before {
        @include get-checkbox-unselected($eon-red-25);
      }
    }
    &.eon-turquoise-25 {
      color: $eon-turquoise-25;
      &:before {
        @include get-checkbox-unselected($eon-turquoise-25);
      }
    }
  }

}

.eon-radio-label {
  &:before {
    @include get-radio-unselected($eon-middlegrey);
  }

  background-color: transparent;
  &:before {
    @include get-radio-unselected($eon-middlegrey);
  }
  &.bg-eon-red {
    background-color: transparent;
    &:before {
      @include get-radio-unselected($eon-middlegrey);
    }
  }

  &.bg-eon-bordeaux {
    background-color: transparent;
    &:before {
      @include get-radio-unselected($eon-middlegrey);
    }
    &.eon-limeyellow {
      &:before {
        @include get-radio-unselected($eon-middlegrey);
      }
    }
  }

  &.bg-eon-turquoise {
    background-color: transparent;
    &:before {
      @include get-radio-unselected($eon-middlegrey);
    }
  }

  &.bg-eon-limeyellow {
    background-color: transparent;
    &:before {
      @include get-radio-unselected($eon-middlegrey);
    }
  }

  &.bg-eon-white {
    background-color: transparent;
    color: $eon-white;
    &:before {
      @include get-radio-unselected($eon-white);
    }
    &.eon-red:before {
      @include get-radio-unselected($eon-white);
    }
    &.eon-bordeaux:before {
      @include get-radio-unselected($eon-white);
    }
    &.eon-turquoise:before {
      @include get-radio-unselected($eon-white);
    }
  }
  &.bg-eon-red-75 {
    background-color: transparent;
    &:before {
      @include get-radio-unselected($eon-red-75);
    }
  }

  &.bg-eon-bordeaux-75 {
    background-color: transparent;
    &:before {
      @include get-radio-unselected($eon-bordeaux-75);
    }
  }

  &.bg-eon-limeyellow-75 {
    background-color: transparent;
    &:before {
      @include get-radio-unselected($eon-limeyellow-75);
    }
  }

  &.bg-eon-turquoise-75 {
    background-color: transparent;
    &:before {
      @include get-radio-unselected($eon-turquoise-75);
    }
  }



  &.checked {
    background-color: transparent;
    &:before {
      @include get-radio-selected($eon-middlegrey, $eon-white);
    }
    &.bg-eon-red {
      background-color: transparent;
      &:before {
        @include get-radio-selected($eon-red, $eon-white);
      }
    }

    &.bg-eon-bordeaux {
      background-color: transparent;
      color: $eon-bordeaux;
      &:before {
        @include get-radio-selected($eon-bordeaux, $eon-white);
      }
      &.eon-limeyellow {
        &:before {
          @include get-radio-selected($eon-bordeaux, $eon-limeyellow);
        }
      }
    }

    &.bg-eon-turquoise {
      background-color: transparent;
      &:before {
        @include get-radio-selected($eon-turquoise, $eon-white);
      }
    }

    &.bg-eon-limeyellow {
      background-color: transparent;
      &:before {
        @include get-radio-selected($eon-limeyellow, $eon-bordeaux);
      }
    }

    &.bg-eon-white {
      background-color: transparent;
      color: $eon-white;
      &:before {
        @include get-radio-selected($eon-white, $eon-red);
      }
      &.eon-red:before {
        @include get-radio-selected($eon-white, $eon-red);
      }
      &.eon-bordeaux:before {
        @include get-radio-selected($eon-white, $eon-bordeaux);
      }
      &.eon-turquoise:before {
        @include get-radio-selected($eon-white, $eon-turquoise);
      }
    }
    &.bg-eon-red-75 {
      background-color: transparent;
      &:before {
        @include get-radio-selected($eon-red-75, $eon-white);
      }
    }

    &.bg-eon-bordeaux-75 {
      background-color: transparent;
      &:before {
        @include get-radio-selected($eon-bordeaux-75, $eon-white);
      }
    }

    &.bg-eon-limeyellow-75 {
      background-color: transparent;
      &:before {
        @include get-radio-selected($eon-limeyellow-75, $eon-white);
      }
    }

    &.bg-eon-turquoise-75 {
      background-color: transparent;
      &:before {
        @include get-radio-selected($eon-turquoise-75, $eon-white);
      }
    }
  }


  &.disabled {
    color: $eon-darkgrey-25;
    &:hover {
      cursor: default;
    }
    &:before {
      @include get-radio-unselected($eon-darkgrey-25);
    }
    &.eon-limeyellow-25 {
      color: $eon-limeyellow-25;
      &:before {
        @include get-radio-unselected($eon-limeyellow-25);
      }
    }
    &.eon-bordeaux-25 {
      color: $eon-bordeaux-25;
      &:before {
        @include get-radio-unselected($eon-bordeaux-25);
      }
    }
    &.eon-red-25 {
      color: $eon-red-25;
      &:before {
        @include get-radio-unselected($eon-red-25);
      }
    }
    &.eon-turquoise-25 {
      color: $eon-turquoise-25;
      &:before {
        @include get-radio-unselected($eon-turquoise-25);
      }
    }
  }
}
