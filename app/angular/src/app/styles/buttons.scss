$eon-button-over-red-grey: #D8D8D8;

$eon-red-active: #D51607;
$eon-grey-disabled: #E8E8E8;

@mixin get-left-side-bg($color) {
  $colorR: red($color);
  $colorG: green($color);
  $colorB: blue($color);
  @include background-svg('<svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="26px" height="52px" viewBox="0 0 26 52" enable-background="new 0 0 26 52" xml:space="preserve"><g><path fill="rgb(#{$colorR},#{$colorG},#{$colorB})" d="M8.414,1.076C4.994,1.75,2.322,4.23,1.672,7.775c-2.23,12.15-2.23,24.301,0,36.452c0.65,3.545,3.322,6.02,6.742,6.693 C14.332,51.884,26,52,26,52V0C26,0.001,13.667,0.13,8.414,1.076z"/></g></svg>');
}
@mixin get-right-side-bg($color) {
  $colorR: red($color);
  $colorG: green($color);
  $colorB: blue($color);
  @include background-svg('<svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="26px" height="52px" viewBox="0.239 0.829 26 52" enable-background="new 0.239 0.829 26 52" xml:space="preserve"><g><path fill="rgb(#{$colorR},#{$colorG},#{$colorB})" d="M17.824,51.754c3.42-0.674,6.092-3.154,6.742-6.699c2.23-12.15,2.23-24.301,0-36.451 c-0.65-3.545-3.322-6.02-6.742-6.694c-5.918-0.964-17.585-1.08-17.585-1.08v52C0.239,52.829,12.571,52.7,17.824,51.754z"/></g></svg>');
}
@mixin get-central-bg($color) {
  $colorR: red($color);
  $colorG: green($color);
  $colorB: blue($color);
  background-color: $color;
}

@mixin get-right-with-icon($color) {
  $colorR: red($color);
  $colorG: green($color);
  $colorB: blue($color);
  @include background-svg('<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" x="0px" y="0px"   width="52px" height="52px" viewBox="0 0 52 52" enable-background="new 0 0 52 52" xml:space="preserve"><g>  <path fill="rgb(#{$colorR},#{$colorG},#{$colorB})" d="M43.586,50.924c3.42-0.674,6.092-3.154,6.742-6.699c2.23-12.149,2.23-24.301,0-36.451    c-0.65-3.545-3.322-6.02-6.742-6.693C37.668,0.116,26,0,26,0v52C26,51.999,38.332,51.87,43.586,50.924z"/></g></svg>');
}

@mixin get-icon-button-bg($color, $activeColor) {
  $colorR: red($color);
  $colorG: green($color);
  $colorB: blue($color);
  $activeColorR: red($activeColor);
  $activeColorG: green($activeColor);
  $activeColorB: blue($activeColor);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: contain;
  background-color: transparent;
  @include background-svg('<svg width="52px" height="52px" viewBox="0 0 52 52" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M8.41491242,1.67250109 C4.99456854,2.32324671 2.32318817,4.99462047 1.67244094,8.4149559 C-0.557480315,20.1384043 -0.557480315,31.8615957 1.67244094,43.5850441 C2.32318817,47.0053795 4.99456854,49.6767533 8.41491242,50.3274989 C14.2765226,51.4423282 20.1383898,52 26,52 C31.8616102,52 37.7234774,51.4423282 43.5850876,50.3274989 C47.0054315,49.6767533 49.6768118,47.0053795 50.3275591,43.5850441 C52.5574803,31.8615957 52.5574803,20.1384043 50.3275591,8.4149559 C49.6768118,4.99462047 47.0054315,2.32324671 43.5850876,1.67250109 C37.7234774,0.557414659 31.8616102,0 26,0 C20.1383898,0 14.2765226,0.557414659 8.41491242,1.67250109 Z" id="Fill-281-Copy-10" fill="rgb(#{$colorR},#{$colorG},#{$colorB})"></path></svg>');
  &:hover {
    cursor: pointer;
    @include background-svg('<svg width="52px" height="52px" viewBox="0 0 52 52" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M8.41491242,1.67250109 C4.99456854,2.32324671 2.32318817,4.99462047 1.67244094,8.4149559 C-0.557480315,20.1384043 -0.557480315,31.8615957 1.67244094,43.5850441 C2.32318817,47.0053795 4.99456854,49.6767533 8.41491242,50.3274989 C14.2765226,51.4423282 20.1383898,52 26,52 C31.8616102,52 37.7234774,51.4423282 43.5850876,50.3274989 C47.0054315,49.6767533 49.6768118,47.0053795 50.3275591,43.5850441 C52.5574803,31.8615957 52.5574803,20.1384043 50.3275591,8.4149559 C49.6768118,4.99462047 47.0054315,2.32324671 43.5850876,1.67250109 C37.7234774,0.557414659 31.8616102,0 26,0 C20.1383898,0 14.2765226,0.557414659 8.41491242,1.67250109 Z" id="Fill-281-Copy-10" fill="rgb(#{$activeColorR},#{$activeColorG},#{$activeColorB})"></path></svg>');
  }
}


@mixin setBgColorsForButton($default-bg-color, $active-bg-color, $isAElement) {
  @if $isAElement {
    @include get-central-bg($default-bg-color);
  }
  &:before {
    @include get-left-side-bg($default-bg-color);
  }
  span {
    @include get-central-bg($default-bg-color);
  }
  &:after {
    @include get-right-side-bg($default-bg-color);
  }
  &:hover {
    @if $isAElement {
      @include get-central-bg($active-bg-color);
    }
    &:before {
      @include get-left-side-bg($active-bg-color);
    }
    span {
      @include get-central-bg($active-bg-color);
    }
    &:after {
      @include get-right-side-bg($active-bg-color);
    }
  }
}

button.eon-button {
  height: 52px;
  border:0;
  padding:0 26px;
  box-shadow: none;
  font-weight: bold;
  font-size: 18px;
  color: $eon-white;
  background-color: transparent;

  span {
    height: 52px;
    line-height: 52px;
    display: inline-block;
    @include get-central-bg($eon-red);
    padding: 1px;
  }

  &:before {
    content: "";
    display: inline-block;
    position: absolute;
    @include get-left-side-bg($eon-red);
    background-size: contain;
    height: 52px;
    width: 26px;
    margin-left: -25px;

  }

  &:after {
    content: "";
    display: inline-block;
    position: absolute;
    @include get-right-side-bg($eon-red);
    background-size: contain;
    height: 52px;
    width: 26px;
    margin-right: 1px;
  }
}

button.eon-button,
a.eon-button.small,
a.eon-button,
a.eon-button-icon,
a.eon-button-icon-only {
  color: $eon-white;
  &.eon-red { color: $eon-red; }
  &.eon-white { color: $eon-white; }
  &.eon-turquoise { color: $eon-turquoise; }
  &.eon-bordeaux { color: $eon-bordeaux; }
  &.eon-blueslate { color: $eon-blueslate; }
  &.eon-limeyellow { color: $eon-limeyellow; }
  &.eon-black { color: $eon-black; }
  &.eon-darkgrey { color: $eon-darkgrey; }
  &.eon-red-active { color: $eon-red-active; }
  &.eon-bordeaux-active { color: $eon-bordeaux-active; }
  &.eon-blueslate-active { color: $eon-blueslate-active; }
  &.eon-limeyellow-active { color: $eon-limeyellow-active; }
  &.eon-turquoise-active { color: $eon-turquoise-active; }
  &.eon-red-75 { color: $eon-red-75; }
  &.eon-red-50 { color: $eon-red-50; }
  &.eon-red-25 { color: $eon-red-25; }
  &.eon-bordeaux-75 { color: $eon-bordeaux-75; }
  &.eon-bordeaux-50 { color: $eon-bordeaux-50; }
  &.eon-bordeaux-25 { color: $eon-bordeaux-25; }
  &.eon-limeyellow-75 { color: $eon-limeyellow-75; }
  &.eon-limeyellow-50 { color: $eon-limeyellow-50; }
  &.eon-limeyellow-25 { color: $eon-limeyellow-25; }
  &.eon-turquoise-75 { color: $eon-turquoise-75; }
  &.eon-turquoise-50 { color: $eon-turquoise-50; }
  &.eon-turquoise-25 { color: $eon-turquoise-25; }
  &.eon-black { color: $eon-black; }
  &.eon-darkgrey { color: $eon-darkgrey; }
  &.eon-darkgrey-active { color: $eon-darkgrey-active; }
  &.eon-darkgrey-75 { color: $eon-darkgrey-75; }
  &.eon-darkgrey-25 { color: $eon-darkgrey-25; }
  &.eon-middlegrey { color: $eon-middlegrey; }
  &.eon-lightgrey { color: $eon-lightgrey; }
  &.eon-ultralightgrey { color: $eon-ultralightgrey; }
}

button.eon-button,
a.eon-button.small,
a.eon-button {
  &.bg-eon-red {
    @include setBgColorsForButton($eon-red, $eon-red-active, false);
  }

  &.bg-eon-bordeaux {
    @include setBgColorsForButton($eon-bordeaux, $eon-bordeaux-active, false);
  }

  &.bg-eon-blueslate {
    @include setBgColorsForButton($eon-blueslate, $eon-blueslate-active, false);
  }

  &.bg-eon-turquoise {
    @include setBgColorsForButton($eon-turquoise, $eon-turquoise-active, false);
  }

  &.bg-eon-limeyellow {
    @include setBgColorsForButton($eon-limeyellow, $eon-limeyellow-active, false);
    color: $eon-bordeaux;
  }
  &.bg-eon-darkgray {
    @include setBgColorsForButton($eon-darkgrey, $eon-darkgrey-active, false);
    background: $eon-darkgrey;
    color: #fff;
  }

  &.bg-eon-white {
    @include setBgColorsForButton($eon-white, $eon-lightgrey, false);
  }
  &.bg-eon-red-75 {
    @include setBgColorsForButton($eon-red-75, $eon-red-75, false);
  }

  &.bg-eon-bordeaux-75 {
    @include setBgColorsForButton($eon-bordeaux-75, $eon-bordeaux-75, false);
  }

  &.bg-eon-limeyellow-75 {
    @include setBgColorsForButton($eon-limeyellow-75, $eon-limeyellow-75, false);
    color: $eon-bordeaux;
  }

  &.bg-eon-limeyellow-50 {
    @include setBgColorsForButton($eon-limeyellow-50, $eon-limeyellow-50, false);
    color: $eon-bordeaux;
  }

  &.bg-eon-turquoise-75 {
    @include setBgColorsForButton($eon-turquoise-75, $eon-turquoise-75, false);
  }

  &.disabled, &:disabled {
    color: $eon-darkgrey-25;
    cursor: default;
    &:before {
      @include get-left-side-bg($eon-lightgrey);
    }
    span {
      @include get-central-bg($eon-lightgrey);
    }
    &:after {
      @include get-right-side-bg($eon-lightgrey);
    }
    &:hover {
      &:before {
        @include get-left-side-bg($eon-lightgrey);
      }
      span {
        @include get-central-bg($eon-lightgrey);
      }
      &:after {
        @include get-right-side-bg($eon-lightgrey);
      }
    }
    &.bg-eon-white {
      &:before {
        @include get-left-side-bg($eon-white);
      }
      span {
        @include get-central-bg($eon-white);
      }
      &:after {
        @include get-right-side-bg($eon-white);
      }
      &:hover {
        &:before {
          @include get-left-side-bg($eon-white);
        }
        span {
          @include get-central-bg($eon-white);
        }
        &:after {
          @include get-right-side-bg($eon-white);
        }
      }
    }
    &.bg-eon-white-disabled {
      &:before {
        @include get-left-side-bg($eon-white);
      }
      span {
        @include get-central-bg($eon-white);
      }
      &:after {
        @include get-right-side-bg($eon-white);
      }
      &:hover {
        &:before {
          @include get-left-side-bg($eon-white);
        }
        span {
          @include get-central-bg($eon-white);
        }
        &:after {
          @include get-right-side-bg($eon-white);
        }
      }
    }
  }
}

a.eon-button,
a.eon-button-icon {
  &.bg-eon-red {
    @include setBgColorsForButton($eon-red, $eon-red-active, true);
  }

  &.bg-eon-bordeaux {
    @include setBgColorsForButton($eon-bordeaux, $eon-bordeaux-active, true);
  }

  &.bg-eon-blueslate {
    @include setBgColorsForButton($eon-blueslate, $eon-blueslate-active, false);
  }

  &.bg-eon-turquoise {
    @include setBgColorsForButton($eon-turquoise, $eon-turquoise-active, true);
  }

  &.bg-eon-limeyellow {
    @include setBgColorsForButton($eon-limeyellow, $eon-limeyellow-active, true);
  }

  &.bg-eon-white {
    @include setBgColorsForButton($eon-white, $eon-lightgrey, true);
  }
  &.bg-eon-red-75 {
    @include setBgColorsForButton($eon-red-75, $eon-red-75, true);
  }

  &.bg-eon-bordeaux-75 {
    @include setBgColorsForButton($eon-bordeaux-75, $eon-bordeaux-75, true);
  }

  &.bg-eon-limeyellow-75 {
    @include setBgColorsForButton($eon-limeyellow-75, $eon-limeyellow-75, true);
  }
  &.bg-eon-limeyellow-50 {
    @include setBgColorsForButton($eon-limeyellow-50, $eon-limeyellow-50, true);
  }

  &.bg-eon-turquoise-75 {
    @include setBgColorsForButton($eon-turquoise-75, $eon-turquoise-75, true);
  }
}

a.back-button {
  text-align: center;
  height: 52px;
  border: 0;
  padding: 0 26px;
  display: block;
  box-shadow: none;
  font-weight: bold;
  font-size: 18px;
  color: #ffffff;
  background-color: transparent;
  border: 2px solid black !important;
  color: black;
  line-height: 52px;
  border-radius: 16px;
}

a.eon-button,
a.eon-button-icon,
a.eon-button.small {
  white-space: nowrap;
  height: 52px;
  border:0;
  padding:0 1px;
  box-shadow: none;
  font-weight: bold;
  font-size: 18px;
  color: $eon-white;
  background-color: transparent;
  height: 52px;
  line-height: 52px;
  display: inline-block;
  text-decoration: none;
  position: relative;
  //@include get-central-bg($eon-red);

  // DEFAULT MARGIN TO SHOW THE CUSTOM ROUNDED CORNERS
  margin: 0 26px;

  &:before {
    content: "";
    display: inline-block;
    position: absolute;
    @include get-left-side-bg($eon-red);
    background-size: contain;
    height: 52px;
    width: 26px;
    margin-left: -26px;
    left: 0;
  }
  &:after {
    content: "";
    display: inline-block;
    position: absolute;
    @include get-right-side-bg($eon-red);
    background-size: contain;
    height: 52px;
    width: 26px;
    margin-right: 1px;
    top:0;
    right: -27px;
  }
}

a.eon-button,
button.eon-button {
  //outline-offset: 30px;
  &:focus {
    /* Visible in the full-colour space */
    -webkit-filter: drop-shadow(0 0 5px purple);
    filter: drop-shadow(0 0 5px purple);

    /* Visible in Windows high-contrast themes */
    outline-color: transparent;
    outline-width: 2px;
    outline-style: dotted;
  }
}

a.eon-button.small {
  height: 32px;
  line-height: 32px;
  margin: 0 16px;
  &:before {
    height: 32px;
    width: 16px;
    margin-left: -16px;
  }
  &:after {
    height: 32px;
    width: 16px;
    margin-right: 1px;
    right: -17px;
  }
}

a.eon-button-icon {
  padding-right: 46px;
  position: relative;
  &:after {
    content: "";
    display: inline-block;
    position: absolute;
    height: 52px;
    width: 78px;
    right: -26px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: right;

    @include get-right-with-icon($eon-red);
    margin-right: 1px;
  }
}

a.eon-button-icon,
a.eon-button-icon-only {
  &:after {
    font-family: "eon-icons";
    font-style: normal;
    font-weight: normal;
    speak: none;
    display: inline-block;
    text-decoration: inherit;

    text-align: center;

    /* For safety - reset parent styles, that can break glyph codes*/
    font-variant: normal;
    text-transform: none;

    /* fix buttons height, for twitter bootstrap */
    line-height: 52px;
    font-size: 52px;

    /* Font smoothing. That was taken from TWBS */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  &.facebook:after { content: '\e800'; }
  &.instagram:after { content: '\e801'; }
  &.twitter:after { content: '\e802'; }
  &.youtube:after { content: '\e803'; }
  &.arrow_down:after { content: '\e804'; }
  &.arrow_left:after { content: '\e805'; }
  &.arrow_right:after { content: '\e806'; }
  &.arrow_up:after { content: '\e807'; }
  &.bookmark:after { content: '\e808'; }
  &.burger_menu:after { content: '\e809'; }
  &.calendar:after { content: '\e80a'; }
  &.camera:after { content: '\e80b'; }
  &.cart:after { content: '\e80c'; }
  &.close:after { content: '\e80d'; }
  &.computer:after { content: '\e80e'; }
  &.download:after { content: '\e80f'; }
  &.drag_and_drop:after { content: '\e810'; }
  &.favourite:after { content: '\e811'; }
  &.filter:after { content: '\e812'; }
  &.forward:after { content: '\e813'; }
  &.fullcreen:after { content: '\e814'; }
  &.fullscreen_exit:after { content: '\e815'; }
  &.house:after { content: '\e816'; }
  &.letter:after { content: '\e817'; }
  &.location:after { content: '\e818'; }
  &.lock:after { content: '\e819'; }
  &.minus:after { content: '\e81a'; }
  &.pause:after { content: '\e81b'; }
  &.person:after { content: '\e81c'; }
  &.phone:after { content: '\e81d'; }
  &.play:after { content: '\e81e'; }
  &.plus:after { content: '\e81f'; }
  &.pointer_down:after { content: '\e820'; }
  &.pointer_left:after { content: '\e821'; }
  &.pointer_right:after { content: '\e822'; }
  &.pointer_s_down:after { content: '\e823'; }
  &.pointer_s_left:after { content: '\e824'; }
  &.pointer_s_right:after { content: '\e825'; }
  &.pointer_s_up:after { content: '\e826'; }
  &.pointer_up:after { content: '\e827'; }
  &.printer:after { content: '\e828'; }
  &.rewind:after { content: '\e829'; }
  &.search:after { content: '\e82a'; }
  &.settings:after { content: '\e82b'; }
  &.share:after { content: '\e82c'; }
  &.smartphone:after { content: '\e82d'; }
  &.speak_bubble:after { content: '\e82e'; }
  &.stop:after { content: '\e82f'; }
  &.tablet:after { content: '\e830'; }
  &.television:after { content: '\e831'; }
  &.trash:after { content: '\e832'; }
  &.upload:after { content: '\e833'; }
  &.warning:after { content: '\e834'; }
}

a.eon-button,
a.eon-button-icon {
  &:hover {
    cursor: pointer;
    color: #FFF;
  }

  &.bg-eon-red {
    //@include setBgColorsForButton(#0F0, #00F, true);
    @include setBgColorsForButton($eon-red, $eon-red-active, true);
  }

  &.bg-eon-bordeaux {
    @include setBgColorsForButton($eon-bordeaux, $eon-bordeaux-active, true);
  }

  &.bg-eon-blueslate {
    @include setBgColorsForButton($eon-blueslate, $eon-blueslate-active, false);
  }

  &.bg-eon-turquoise {
    @include setBgColorsForButton($eon-turquoise, $eon-turquoise-active, true);
  }

  &.bg-eon-limeyellow {
    @include setBgColorsForButton($eon-limeyellow, $eon-limeyellow-active, true);
  }

  &.bg-eon-white {
    @include setBgColorsForButton($eon-white, $eon-lightgrey, true);
  }
  &.bg-eon-red-75 {
    @include setBgColorsForButton($eon-red-75, $eon-red-75, true);
  }

  &.bg-eon-bordeaux-75 {
    @include setBgColorsForButton($eon-bordeaux-75, $eon-bordeaux-75, true);
  }

  &.bg-eon-limeyellow-75 {
    @include setBgColorsForButton($eon-limeyellow-75, $eon-limeyellow-75, true);
    color: $eon-bordeaux;
  }

  &.bg-eon-limeyellow-50 {
    @include setBgColorsForButton($eon-limeyellow-50, $eon-limeyellow-50, true);
    color: $eon-bordeaux;
  }

  &.bg-eon-turquoise-75 {
    @include setBgColorsForButton($eon-turquoise-75, $eon-turquoise-75, true);
  }

  &.disabled, &:disabled {
    color: $eon-darkgrey-25;
    cursor: default;
    &:before {
      @include get-left-side-bg($eon-lightgrey);
    }
    @include get-central-bg($eon-lightgrey);
    &:after {
      @include get-right-side-bg($eon-lightgrey);
    }
    &:hover {

      &:before {
        @include get-left-side-bg($eon-lightgrey);
      }
      @include get-central-bg($eon-lightgrey);
      &:after {
        @include get-right-side-bg($eon-lightgrey);
      }
    }

    &.bg-eon-white {
      &:before {
        @include get-left-side-bg($eon-white);
      }
      @include get-central-bg($eon-white);
      &:after {
        @include get-right-side-bg($eon-white);
      }
      &:hover {

        &:before {
          @include get-left-side-bg($eon-white);
        }
        @include get-central-bg($eon-white);
        &:after {
          @include get-right-side-bg($eon-white);
        }
      }
    }
  }
}

a.eon-button-icon-only {
  width: 52px;
  height: 52px;
  display: inline-block;
  &:after {
    color: inherit;
  }
  &.bg-eon-red {
    @include get-icon-button-bg($eon-red, $eon-red-active);
  }

  &.bg-eon-bordeaux {
    @include get-icon-button-bg($eon-bordeaux, $eon-bordeaux-active);
  }

  &.bg-eon-blueslate {
    @include setBgColorsForButton($eon-blueslate, $eon-blueslate-active, false);
  }

  &.bg-eon-turquoise {
    @include get-icon-button-bg($eon-turquoise, $eon-turquoise-active);
  }

  &.bg-eon-limeyellow {
    @include get-icon-button-bg($eon-limeyellow, $eon-limeyellow-active);
  }

  &.bg-eon-white {
    @include get-icon-button-bg($eon-white, $eon-lightgrey);
  }
  &.bg-eon-red-75 {
    @include get-icon-button-bg($eon-red-75, $eon-red-75);
  }

  &.bg-eon-bordeaux-75 {
    @include get-icon-button-bg($eon-bordeaux-75, $eon-bordeaux-75);
  }

  &.bg-eon-limeyellow-75 {
    @include get-icon-button-bg($eon-limeyellow-75, $eon-limeyellow-75);
    color: $eon-bordeaux;
  }

  &.bg-eon-limeyellow-50 {
    @include get-icon-button-bg($eon-limeyellow-50, $eon-limeyellow-50);
    color: $eon-bordeaux;
  }

  &.bg-eon-turquoise-75 {
    @include get-icon-button-bg($eon-turquoise-75, $eon-turquoise-75);
  }

  &.disabled, &:disabled {
    color: $eon-darkgrey-25;
    @include get-icon-button-bg($eon-lightgrey, $eon-lightgrey);
    &:hover {
      cursor: default;
    }
    &.bg-eon-white {
      @include get-icon-button-bg($eon-white, $eon-white);
    }
  }
}
