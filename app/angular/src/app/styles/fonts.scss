@font-face {
    font-family: "EONBrixSans";
    src: asset_url("/assets/fonts/eon-fonts/EON_BrixSansWeb-Black.woff");
    src: url("/assets/fonts/eon-fonts/EON_BrixSansWeb-Black.eot?#iefix") format("embedded-opentype"), url("/assets/fonts/eon-fonts/EON_BrixSansWeb-Black.woff2") format("woff2"), url("/assets/fonts/eon-fonts/EON_BrixSansWeb-Black.woff") format("woff"), url("/assets/fonts/eon-fonts/EON_BrixSansWeb-Black.ttf") format("truetype");
    font-weight: 900;
    font-style: normal;
}

@font-face {
    font-family: "EONBrixSans";
    src: url("/assets/fonts/eon-fonts/EON_BrixSansWeb-Regular.eot");
    src: url("/assets/fonts/eon-fonts/EON_BrixSansWeb-Regular.eot?#iefix") format("embedded-opentype"), url("/assets/fonts/eon-fonts/EON_BrixSansWeb-Regular.woff2") format("woff2"), url("/assets/fonts/eon-fonts/EON_BrixSansWeb-Regular.woff") format("woff"), url("/assets/fonts/eon-fonts/EON_BrixSansWeb-Regular.ttf") format("truetype");
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: "EONBrixSans";
    src: url("/assets/fonts/eon-fonts/EON_BrixSansWeb-Medium.eot");
    src: url("/assets/fonts/eon-fonts/EON_BrixSansWeb-Medium.eot?#iefix") format("embedded-opentype"), url("/assets/fonts/eon-fonts/EON_BrixSansWeb-Medium.woff2") format("woff2"), url("/assets/fonts/eon-fonts/EON_BrixSansWeb-Medium.woff") format("woff"), url("/assets/fonts/eon-fonts/EON_BrixSansWeb-Medium.ttf") format("truetype");
    font-weight: 500;
    font-style: normal;
}

@font-face {
    font-family: "EONBrixSans";
    src: url("/assets/fonts/eon-fonts/ON_BrixSansWeb-Bold.eot");
    src: url("/assets/fonts/eon-fonts/EON_BrixSansWeb-Bold.eot?#iefix") format("embedded-opentype"), url("/assets/fonts/eon-fonts/EON_BrixSansWeb-Bold.woff2") format("woff2"), url("/assets/fonts/eon-fonts/EON_BrixSansWeb-Bold.woff") format("woff"), url("/assets/fonts/eon-fonts/EON_BrixSansWeb-Bold.ttf") format("truetype");
    font-weight: 700;
    font-style: normal;
}