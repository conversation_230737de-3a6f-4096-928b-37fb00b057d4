@mixin get-arrow-left($color) {
  $colorR: red($color);
  $colorG: green($color);
  $colorB: blue($color);
  @include background-svg('<svg width="19px" height="20px" viewBox="-1 0 19 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="icn_link_arrow_right" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" transform="translate(0.000000, 2.000000)" stroke-linecap="round"><path d="M0,8 L17,8" id="Stroke-84" stroke="rgb(#{$colorR},#{$colorG},#{$colorB})" stroke-width="2"></path><path d="M9,0 C11.769,2.004 14.364,4.474 16.762,7.388 C17.047,7.732 17.047,8.313 16.761,8.658 C14.375,11.551 11.752,14.006 9,16" id="Stroke-85" stroke="rgb(#{$colorR},#{$colorG},#{$colorB})" stroke-width="2" stroke-linejoin="round"></path></g></svg>');
}

@mixin get-arrow-right($color) {
  $colorR: red($color);
  $colorG: green($color);
  $colorB: blue($color);
  @include background-svg('<svg width="11px" height="18px" viewBox="77 1 11 18" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M79,3 C81.635,4.745 84.146,6.893 86.431,9.425 C86.705,9.727 86.705,10.234 86.432,10.536 C84.136,13.085 81.651,15.247 79,17" id="icn_right_small" stroke="rgb(#{$colorR},#{$colorG},#{$colorB})" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"></path></svg>');
}

.eon-link {
  color: $eon-red;
  cursor: pointer;
  font-size: 18px;
  line-height: 21px;
  height: 21px;

  &.arrow-left {
    &:before {
      content: "";
      display: inline-block;
      width: 16px;
      height: 16px;
      line-height: 21px;
      padding-right: 10px;
      margin-right: 8px;
      background-repeat: no-repeat;
      background-position: center center;
      background-size: contain;
    }

  }
  &.arrow-right {
    &:after {
      content: "";
      display: inline-block;
      width: 8px;
      height: 21px;
      line-height: 21px;
      padding-right: 10px;
      margin-left: 5px;
      vertical-align: middle;
      background-repeat: no-repeat;
      background-position: center 1px;
      background-size: contain;
    }
  }

  &.eon-red {
    color: $eon-red;
    &.arrow-left {
      &:before {
        @include get-arrow-left($eon-red);
      }
      &:hover:before {
        @include get-arrow-left($eon-red-active);
      }
    }
    &.arrow-right {
      &:after {
        @include get-arrow-right($eon-red);
      }
      &:hover:after {
        @include get-arrow-right($eon-red-active);
      }
    }
    &:hover {
      color: $eon-red-active;
    }
  }

  &.eon-bordeaux {
    color: $eon-bordeaux;
    &.arrow-left {
      &:before {
        @include get-arrow-left($eon-bordeaux);
      }
      &:hover:before {
        @include get-arrow-left($eon-bordeaux-active);
      }
    }
    &.arrow-right {
      &:after {
        @include get-arrow-right($eon-bordeaux);
      }
      &:hover:after {
        @include get-arrow-right($eon-bordeaux-active);
      }
    }
    &:hover {
      color: $eon-bordeaux-active;
    }
  }

  &.eon-limeyellow {
    color: $eon-limeyellow;
    &.arrow-left {
      &:before {
        @include get-arrow-left($eon-limeyellow);
      }
      &:hover:before {
        @include get-arrow-left($eon-limeyellow-active);
      }
    }
    &.arrow-right {
      &:after {
        @include get-arrow-right($eon-limeyellow);
      }
      &:hover:after {
        @include get-arrow-right($eon-limeyellow-active);
      }
    }
    &:hover {
      color: $eon-limeyellow-active;
    }
  }

  &.eon-turquoise {
    color: $eon-turquoise;
    &.arrow-left {
      &:before {
        @include get-arrow-left($eon-turquoise);
      }
      &:hover:before {
        @include get-arrow-left($eon-turquoise-active);
      }
    }
    &.arrow-right {
      &:after {
        @include get-arrow-right($eon-turquoise);
      }
      &:hover:after {
        @include get-arrow-right($eon-turquoise-active);
      }
    }
    &:hover {
      color: $eon-turquoise-active;
    }
  }

  &.eon-white {
    color: $eon-white;
    &.arrow-left {
      &:before {
        @include get-arrow-left($eon-white);
      }
      &:hover:before {
        @include get-arrow-left($eon-white);
      }
    }
    &.arrow-right {
      &:after {
        @include get-arrow-right($eon-white);
      }
      &:hover:after {
        @include get-arrow-right($eon-white);
      }
    }
    &:hover {
      color: $eon-white;
      text-decoration: underline;
    }
  }
}
