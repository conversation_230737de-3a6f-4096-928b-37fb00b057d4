@import 'vars';
@import 'colors';
@import 'mixins';
@import 'svg-encoder';
@import 'fonts';
@import 'icons';
@import 'buttons';
@import 'alerts';
@import 'pagination';
@import 'radio-checkbox';
@import 'select';
@import 'sliders';
@import 'switch';
@import 'typo';
@import 'input-fields';
@import 'input-fields_1';
// @import 'intro';
@import 'links';
@import 'logo';
@import 'header';
@import 'environment';
@import 'hamburger';
@import 'layout';
@import 'loadingbar';
@import 'modal';
@import 'tabset';
@import 'table';
@import 'typeahead';
@import 'font-awesome';
@import 'ng-pick-datetime';

@import '../../../node_modules/ag-grid-community/src/styles/ag-grid';
@import '../../../node_modules/ag-grid-community/src/styles/ag-theme-alpine/sass/ag-theme-alpine-mixin';

.ag-theme-alpine {
  @include ag-theme-alpine();
}

.cursor-hand {cursor:pointer;cursor:hand}
