$eon-red-form-label-disabled: #F88D84;

@mixin get-error-icon($markColor, $color) {
  $colorR: red($color);
  $colorG: green($color);
  $colorB: blue($color);
  $markColorR: red($markColor);
  $markColorG: green($markColor);
  $markColorB: blue($markColor);
  @include background-svg('<svg width="24px" height="23px" viewBox="297 39 24 23" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><g id="icn_error" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" transform="translate(297.000000, 39.000000)"><path d="M10.3034259,2.42334148 C11.3508479,0.48057371 13.0527389,0.481334305 14.1037865,2.42334148 L23.2933947,19.4028629 C24.3448539,21.3456307 23.413949,22.9205552 21.2002365,22.9205552 L3.24957081,22.9205552 C1.04209223,22.9205552 0.102090623,21.3448701 1.14910256,19.4028629 L10.3034259,2.42334148 Z" id="Triangle" fill="rgb(#{$markColorR},#{$markColorG},#{$markColorB})"></path><g id="Group-39" transform="translate(10.300049, 7.500000)"><path d="M2,0.5 L2,6.5" id="Line" stroke="rgb(#{$colorR},#{$colorG},#{$colorB})" stroke-width="2" stroke-linecap="round"></path><circle id="Oval-2" fill="rgb(#{$colorR},#{$colorG},#{$colorB})" cx="2" cy="10.7000122" r="1.5"></circle></g></g></svg>');
}

input[type=text].eon-input-text,
input[type=text].eon-input-text-no-label,
input[type=text].form-control,select.form-control,.form-control{
  width: 100%;
  display: inline-block;
  height: 52px;
  border: 2px solid $eon-darkgrey-25;
  border-radius: 4px;
  padding-left: 12px;
  font-size: 18px;
  box-shadow:none;
  &:hover{
    box-shadow:none;
  }
  &:focus {
    outline: none;
    border: 2px solid $eon-middlegrey;
    &.eon-red {
      border: 2px solid $eon-bordeaux-active;
    }
    &.eon-bordeaux {
      border: 2px solid $eon-bordeaux-dark;
    }
    &.eon-turquoise {
      border: 2px solid $eon-turquoise-dark;
    }
    &.eon-limeyellow {
      border: 2px solid $eon-bordeaux-dark;
    }
  }

  &:disabled {
    background-color: $eon-white;
    border: 2px solid $eon-lightgrey;
  }

  &.eon-red, &.eon-bordeaux, &.eon-turquoise {
    border: 2px solid $eon-white;
    color: $eon-white;

  }
  &.eon-red, &.eon-red:disabled {
    background-color: $eon-red;
    &:disabled {
      border: 2px solid $eon-red-50;
    }
  }
  &.eon-bordeaux, &.eon-bordeaux:disabled {
    background-color: $eon-bordeaux;
    &:disabled {
      border: 2px solid $eon-bordeaux-50;
    }
  }
  &.eon-turquoise, &.eon-turquoise:disabled {
    background-color: $eon-turquoise;
    &:disabled {
      border: 2px solid $eon-turquoise-50;
    }
  }
  &.eon-limeyellow, &.eon-limeyellow:disabled {
    border: 2px solid $eon-bordeaux;
    background-color: $eon-limeyellow;
    color: $eon-bordeaux;
    &:disabled {
      border: 2px solid $eon-limeyellow-50;
    }
  }
}

input[type=text].eon-input-text-no-label {
  &.eon-red, &.eon-bordeaux, &.eon-turquoise {
    &::-webkit-input-placeholder {
      color: $eon-white;
    }
    &:-moz-placeholder {
      color: $eon-white;
      opacity:  1;
    }
    &::-moz-placeholder {
      color: $eon-white;
      opacity:  1;
    }
    &:-ms-input-placeholder {
      color: $eon-white;
    }
    color: $eon-white;
  }
  &.eon-limeyellow {
    &::-webkit-input-placeholder {
      color: $eon-bordeaux;
    }
    &:-moz-placeholder {
      color: $eon-bordeaux;
      opacity:  1;
    }
    &::-moz-placeholder {
      color: $eon-bordeaux;
      opacity:  1;
    }
    &:-ms-input-placeholder {
      color: $eon-bordeaux;
    }
    color: $eon-bordeaux;
  }
}

input[type=text].eon-input-text-no-label:disabled,
input[type=text].eon-input-text:disabled,
input[type=text].form-control:disabled{
  &::-webkit-input-placeholder {
    color: $eon-lightgrey;
  }
  &:-moz-placeholder {
    color: $eon-lightgrey;
    opacity:  1;
  }
  &::-moz-placeholder {
    color: $eon-lightgrey;
    opacity:  1;
  }
  &:-ms-input-placeholder {
    color: $eon-lightgrey;
  }

  background-color: $eon-white;

  &.eon-red {
    &::-webkit-input-placeholder {
      color: $eon-red-25;
    }
    &:-moz-placeholder {
      color: $eon-red-25;
      opacity:  1;
    }
    &::-moz-placeholder {
      color: $eon-red-25;
      opacity:  1;
    }
    &:-ms-input-placeholder {
      color: $eon-red-25;
    }
  }
  &.eon-bordeaux {
    &::-webkit-input-placeholder {
      color: $eon-bordeaux-25;
    }
    &:-moz-placeholder {
      color: $eon-bordeaux-25;
      opacity:  1;
    }
    &::-moz-placeholder {
      color: $eon-bordeaux-25;
      opacity:  1;
    }
    &:-ms-input-placeholder {
      color: $eon-bordeaux-25;
    }
  }
  &.eon-turquoise {
    &::-webkit-input-placeholder {
      color: $eon-turquoise-25;
    }
    &:-moz-placeholder {
      color: $eon-turquoise-25;
      opacity:  1;
    }
    &::-moz-placeholder {
      color: $eon-turquoise-25;
      opacity:  1;
    }
    &:-ms-input-placeholder {
      color: $eon-turquoise-25;
    }
  }
  &.eon-limeyellow {
    &::-webkit-input-placeholder {
      color: $eon-limeyellow-25;
    }
    &:-moz-placeholder {
      color: $eon-limeyellow-25;
      opacity:  1;
    }
    &::-moz-placeholder {
      color: $eon-limeyellow-25;
      opacity:  1;
    }
    &:-ms-input-placeholder {
      color: $eon-limeyellow-25;
    }
  }
}

input[type=text].eon-input-text-no-label[value]:disabled,
input[type=text].eon-input-text[value]:disabled,
input[type=text].form-control[value]:disabled {
  background-color: $eon-ultralightgrey;
  border: 2px solid $eon-lightgrey;
  color: $eon-darkgrey;
  &.eon-red, &.eon-bordeaux, &.eon-turquoise {
    color: $eon-white;
  }
  &.eon-red {
    background-color: $eon-red-75;
    border: 2px solid $eon-red-active;
  }
  &.eon-bordeaux {
    background-color: $eon-bordeaux-75;
    border: 2px solid $eon-bordeaux-active;
  }
  &.eon-turquoise {
    background-color: $eon-turquoise-75;
    border: 2px solid $eon-turquoise-active;
  }
  &.eon-limeyellow {
    background-color: $eon-limeyellow-75;
    border: 2px solid $eon-limeyellow-active;
    color: $eon-bordeaux;
  }
}

input[type=text].eon-input-text-no-label.error,
input[type=text].eon-input-text.error,
input[type=text].form-control.error {
  padding-right: 52px;
  @include get-error-icon($eon-red, $eon-white);
  background-position: calc(100% - 12px) center;
  background-size: 24px;
  background-repeat: no-repeat;
  &.eon-red {
    @include get-error-icon($eon-white, $eon-red);
  }
  &.eon-bordeaux {
    @include get-error-icon($eon-white, $eon-bordeaux);
  }
  &.eon-turquoise {
    @include get-error-icon($eon-white, $eon-turquoise);
  }
  &.eon-limeyellow {
    @include get-error-icon($eon-bordeaux, $eon-limeyellow);
  }
}

label.eon-input-label,label {
  font-size: 18px;
  margin: 4px 0;
  display: block;
  width: 100%;
  font-weight: 600;
  &.required:after {
    content: "*";
    color: $eon-darkgrey;
  }
  &.eon-red, &.eon-bordeaux, &.eon-turquoise {
    color: $eon-white;
    &.required:after {
      color: $eon-white;
    }
  }
  &.eon-limeyellow {
    color: $eon-bordeaux;
    &.required:after {
      color: $eon-bordeaux;
    }
  }
  &.disabled {
    color: $eon-darkgrey-25;
    &.eon-red {
      color: $eon-red-50;
    }
    &.eon-bordeaux {
      color: $eon-bordeaux-50;
    }
    &.eon-turquoise {
      color: $eon-turquoise-50;
    }
    &.eon-limeyellow {
      color: $eon-limeyellow-25;
    }
  }
}
span.error {
  display: block;
  font-size: 14px;
  line-height: 14px;
  color: $eon-darkgrey;
  margin-top: 4px;
  &.no-label {
    color: $eon-black;
  }
  &.eon-red, &.eon-bordeaux, &.eon-turquoise {
    color: $eon-white;
    &.no-label {
      color: $eon-white;
    }
  }
  &.eon-limeyellow {
    color: $eon-bordeaux;
  }
}





.form-control[disabled], fieldset[disabled] .form-control {
  background-color:#EFF1F3;
}


select.form-control{
  box-shadow: 0 1px 0 rgba(0,0,0,0.025);
}
.search-group{
	position: relative;
	display: inline-block;
	margin:0;
	.fa{
		position: absolute;
		top:18px;
		left:10px;
		font-size: 18px;
		opacity: 0.3;
	}
	.form-control{
		background: none;
		border: 1px solid transparent;
		box-shadow:none;
		padding-left: 30px !important;
		height: 42px;
		width:200px;
		padding-left: 40px;
		font-weight: 300;
		font-size: 18px;
		@include transition(width 0.3s ease);
		@include border-radius(0);
		//@include placeholder(#aaa);
		&:hover{
			//background: darken($bg-color,3%);
			//@include placeholder($color-dark);
			width:300px;
			//border-bottom-color:$border-color;
		}
	}
}
.assets-list-options{
	form{
		margin-bottom: 0;
	}
}
input#interval{
  background: #fff;
}
.input-group-addon{
  background: #fff;
  border: 2px solid $eon-darkgrey-25;
  @include border-radius(3px 0 0 3px);
  .eon-icon{
    height:32px;
    &:before{
      line-height:32px;
    }
  }
}
.input-group.input-group-clear{
	.form-control{
		background: none;
		border: none;
		box-shadow:none;
    width: inherit;
	}
	.input-group-addon{
		background: none;
		border: none;
		padding-left: 0;
		border-right:2px solid $eon-darkgrey-25;
	}
}

.input-group {
  input[type=text].eon-input-text, input[type=text].eon-input-text-no-label, input[type=text].form-control, select.form-control, .form-control {
    width: 1%;
  }

  .calendar {
    border: 2px solid #bfbfbf;
    margin-left: -1px;
    line-height: 38px;
    padding: 5px 20px;
    border-radius: 0 4px 4px 0;

    i {
      color: $eon-red;
    }
  }
}

.form-check{
  padding-left: 0;
}
.input-group-text{
  background: $eon-turquoise;
  color: #fff;
  font-weight: 600;
}
