@mixin get-tiny-pagination-item($color) {
  $colorR: red($color);
  $colorG: green($color);
  $colorB: blue($color);
  @include background-svg('<svg width="12px" height="12px" viewBox="0 0 12 12" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><!-- Generator: Sketch 40.3 (33839) - http://www.bohemiancoding.com/sketch --><desc>Created with Sketch.</desc><defs></defs><path d="M1.94190287,0.385961789 C1.15259274,0.536133855 0.536120348,1.15260472 0.38594791,1.9419129 C-0.128649303,4.64732408 -0.128649303,7.35267592 0.38594791,10.0580871 C0.536120348,10.8473953 1.15259274,11.4638661 1.94190287,11.6140382 C3.29458213,11.8713065 4.64732073,12 6,12 C7.35267927,12 8.70541787,11.8713065 10.0580971,11.6140382 C10.8474073,11.4638661 11.4638797,10.8473953 11.6140521,10.0580871 C12.1286493,7.35267592 12.1286493,4.64732408 11.6140521,1.9419129 C11.4638797,1.15260472 10.8474073,0.536133855 10.0580971,0.385961789 C8.70541787,0.128634152 7.35267927,0 6,0 C4.64732073,0 3.29458213,0.128634152 1.94190287,0.385961789 Z" id="Fill-281-Copy-4" stroke="none" fill="rgb(#{$colorR},#{$colorG},#{$colorB})" fill-rule="evenodd"></path></svg>');
}

@mixin get-big-pagination-item($color) {
  $colorR: red($color);
  $colorG: green($color);
  $colorB: blue($color);
  @include background-svg('<svg version="1.1" id="Layer_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="50px" height="14px" viewBox="0 0 50 14" enable-background="new 0 0 50 14" xml:space="preserve"> <path fill="rgb(#{$colorR},#{$colorG},#{$colorB})" d="M47.82,1.954c-0.164-0.891-0.836-1.514-1.695-1.684C44.806,0.032,41.707,0,41.707,0H8.2 c0,0-3.099,0.032-4.419,0.27C2.922,0.439,2.25,1.063,2.087,1.954c-0.561,3.053-0.561,6.107,0,9.16 c0.164,0.891,0.835,1.513,1.694,1.682C5.268,13.037,8.2,13.066,8.2,13.066h33.507c0,0,2.932-0.029,4.418-0.271 c0.859-0.169,1.531-0.791,1.695-1.682C48.38,8.061,48.38,5.007,47.82,1.954z"/> </svg>');
}

@mixin get-full-pagination-arrow($color) {
  $colorR: red($color);
  $colorG: green($color);
  $colorB: blue($color);
  @include background-svg('<svg width="15px" height="26px" viewBox="-2 4 15 26" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M11.9998751,28 C7.85787511,25.258 3.91187511,21.883 0.321875109,17.904 C-0.107124891,17.431 -0.107124891,16.632 0.320875109,16.158 C3.92887511,12.151 7.83387511,8.755 11.9998751,6" id="icn_left" stroke="rgb(#{$colorR},#{$colorG},#{$colorB})" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"></path></svg>');
}

@mixin get-full-pagination-active-item($color) {
  $colorR: red($color);
  $colorG: green($color);
  $colorB: blue($color);
  @include background-svg('<svg width="32px" height="32px" viewBox="62 0 32 33" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M67.1784076,1.02923144 C65.0735806,1.42969028 63.4296543,3.07361259 63.0291944,5.1784344 C61.6569352,12.3928642 61.6569352,19.6071358 63.0291944,26.8215656 C63.4296543,28.9263874 65.0735806,30.5703097 67.1784076,30.9707686 C70.7855524,31.6568174 74.3928553,32 78,32 C81.6071447,32 85.2144476,31.6568174 88.8215924,30.9707686 C90.9264194,30.5703097 92.5703457,28.9263874 92.9708056,26.8215656 C94.3430648,19.6071358 94.3430648,12.3928642 92.9708056,5.1784344 C92.5703457,3.07361259 90.9264194,1.42969028 88.8215924,1.02923144 C85.2144476,0.343024406 81.6071447,0 78,0 C74.3928553,0 70.7855524,0.343024406 67.1784076,1.02923144 Z" id="Fill-281-Copy-7" stroke="none" fill="rgb(#{$colorR},#{$colorG},#{$colorB})" fill-rule="evenodd"></path></svg>');
}



ul.eon-pagination {
  list-style: none;
  padding: 0;
  display: inline-block;
  background-color: transparent !important;
  li {
    display: inline;
    float: left;
    background-repeat: no-repeat;
    background-position: center center;
    background-size: contain;
    &:hover {
      cursor: pointer;
    }
    a {
      display: block;
    }
  }
  &.tiny {
    li {
      width: 12px;
      height: 12px;
      margin-right: 4px;
      @include get-tiny-pagination-item($eon-darkgrey-25);
      &:last-of-type {
        margin-right: 0;
      }
      a {
        width: 12px;
        height: 12px;
      }
      &.active,
      &.active:hover {
        @include get-tiny-pagination-item($eon-red);
      }
    }

    &.eon-red li {
      &.active,
      &.active:hover {
        @include get-tiny-pagination-item($eon-red);
      }
    }
    &.eon-bordeaux li {
      &.active,
      &.active:hover {
        @include get-tiny-pagination-item($eon-bordeaux);
      }
    }
    &.eon-turquoise li {
      &.active,
      &.active:hover {
        @include get-tiny-pagination-item($eon-turquoise);
      }
    }
    &.eon-limeyellow li {
      &.active,
      &.active:hover {
        @include get-tiny-pagination-item($eon-limeyellow);
      }
    }

    &.eon-red-25 li {
      &.active,
      &.active:hover {
        @include get-tiny-pagination-item($eon-red-25);
      }
    }
    &.eon-bordeaux-25 li {
      &.active,
      &.active:hover {
        @include get-tiny-pagination-item($eon-bordeaux-25);
      }
    }
    &.eon-turquoise-25 li {
      &.active,
      &.active:hover {
        @include get-tiny-pagination-item($eon-turquoise-25);
      }
    }
    &.eon-limeyellow-25 li {
      &.active,
      &.active:hover {
        @include get-tiny-pagination-item($eon-limeyellow-25);
      }
    }
    &.eon-white.bg-eon-red li {
      @include get-tiny-pagination-item($eon-red-50);
      &.active,
      &.active:hover {
        @include get-tiny-pagination-item($eon-white);
      }
    }
    &.eon-white.bg-eon-bordeaux li {
      @include get-tiny-pagination-item($eon-bordeaux-50);
      &.active,
      &.active:hover {
        @include get-tiny-pagination-item($eon-white);
      }
    }
    &.eon-white.bg-eon-turquoise li {
      @include get-tiny-pagination-item($eon-turquoise-50);
      &.active,
      &.active:hover {
        @include get-tiny-pagination-item($eon-white);
      }
    }
    &.eon-white.bg-eon-limeyellow li {
      @include get-tiny-pagination-item($eon-limeyellow-50);
      &.active,
      &.active:hover {
        @include get-tiny-pagination-item($eon-bordeaux);
      }
    }
  }

  &.big {
    li {
      margin-right: 6px;
      @include get-big-pagination-item($eon-darkgrey-25);
      &:last-of-type {
        margin-right: 0;
      }
      &.active,
      &.active:hover {
        @include get-big-pagination-item($eon-red);
      }
      &:hover {
        @include get-big-pagination-item($eon-middlegrey);
      }
      a {
        width: 50px;
        height: 14px;
      }
    }

    &.eon-red {
      li {
        &.active,
        &.active:hover {
          @include get-big-pagination-item($eon-red);
        }
      }
      &.image-bg li {
        @include get-big-pagination-item($eon-white);
        &:hover {
          @include get-big-pagination-item($eon-darkgrey-25);
        }
        &.active,
        &.active:hover {
          @include get-big-pagination-item($eon-red);
        }
      }
    }
    &.eon-bordeaux {
      li {
        &.active,
        &.active:hover {
          @include get-big-pagination-item($eon-bordeaux);
        }
      }
      &.image-bg li {
        @include get-big-pagination-item($eon-white);
        &:hover {
          @include get-big-pagination-item($eon-darkgrey-25);
        }
        &.active,
        &.active:hover {
          @include get-big-pagination-item($eon-bordeaux);
        }
      }
    }
    &.eon-turquoise {
      li {
        &.active,
        &.active:hover {
          @include get-big-pagination-item($eon-turquoise);
        }
      }
      &.image-bg li {
        @include get-big-pagination-item($eon-white);
        &:hover {
          @include get-big-pagination-item($eon-darkgrey-25);
        }
        &.active,
        &.active:hover {
          @include get-big-pagination-item($eon-turquoise);
        }
      }
    }
    &.eon-limeyellow {
      li {
        &.active,
        &.active:hover {
          @include get-big-pagination-item($eon-limeyellow);
        }
      }
      &.image-bg li {
        @include get-big-pagination-item($eon-white);
        &:hover {
          @include get-big-pagination-item($eon-darkgrey-25);
        }
        &.active,
        &.active:hover {
          @include get-big-pagination-item($eon-limeyellow);
        }
      }
    }

    &.eon-red-25 li {
      &.active,
      &.active:hover {
        @include get-big-pagination-item($eon-red-25);
      }
    }
    &.eon-bordeaux-25 li {
      &.active,
      &.active:hover {
        @include get-big-pagination-item($eon-bordeaux-25);
      }
    }
    &.eon-turquoise-25 li {
      &.active,
      &.active:hover {
        @include get-big-pagination-item($eon-turquoise-25);
      }
    }
    &.eon-limeyellow-25 li {
      &.active,
      &.active:hover {
        @include get-big-pagination-item($eon-limeyellow-25);
      }
    }
    &.eon-white.bg-eon-red li {
      @include get-big-pagination-item($eon-red-50);
      &:hover {
        @include get-big-pagination-item($eon-red-75);
      }
      &.active,
      &.active:hover {
        @include get-big-pagination-item($eon-white);
      }
    }
    &.eon-white.bg-eon-bordeaux li {
      @include get-big-pagination-item($eon-bordeaux-50);
      &:hover {
        @include get-big-pagination-item($eon-bordeaux-75);
      }
      &.active,
      &.active:hover {
        @include get-big-pagination-item($eon-white);
      }
    }
    &.eon-white.bg-eon-turquoise li {
      @include get-big-pagination-item($eon-turquoise-50);
      &:hover {
        @include get-big-pagination-item($eon-turquoise-75);
      }
      &.active,
      &.active:hover {
        @include get-big-pagination-item($eon-white);
      }
    }
    &.eon-white.bg-eon-limeyellow li {
      @include get-big-pagination-item($eon-limeyellow-25);
      &:hover {
        @include get-big-pagination-item($eon-limeyellow-50);
      }
      &.active,
      &.active:hover {
        @include get-big-pagination-item($eon-bordeaux);
      }
    }
  }

  &.full {
    li {
      margin: 3px;
      &.active,
      &.active:hover {
        @include get-full-pagination-active-item($eon-red);
        a, a:hover {
          color: $eon-white;
        }
      }
      a {
        width: 32px;
        height: 32px;
        font-size: 20px;
        text-align: center;
        text-decoration: none;
        line-height: 32px;
        color: $eon-red;
        &:hover {
          color: $eon-red;
        }
      }
    }
    li.previous {
      width: 24px;
      height: 24px;
      margin: 8px 2px;
      @include get-full-pagination-arrow($eon-red);
      background-size: contain;
      background-position: center center;
      background-repeat: no-repeat;
      &:hover {
        @include get-full-pagination-arrow($eon-red);
      }
    }

    li.next {
      width: 24px;
      height: 24px;
      margin: 8px 2px;
      background-size: contain;
      background-position: center center;
      background-repeat: no-repeat;
      -webkit-transform: rotate(180deg);
         -moz-transform: rotate(180deg);
          -ms-transform: rotate(180deg);
           -o-transform: rotate(180deg);
              transform: rotate(180deg);
      @include get-full-pagination-arrow($eon-red);
      &:hover {
        @include get-full-pagination-arrow($eon-red);
      }
    }
  }

  &.eon-red {
    li {
      &.active,
      &.active:hover {
        @include get-full-pagination-active-item($eon-red);
        a, a:hover {
          color: $eon-white;
        }
      }
      a {
        color: $eon-red;
        &:hover {
          color: $eon-red;
        }
      }
    }
    li.previous {
      @include get-full-pagination-arrow($eon-red);
      &:hover {
        @include get-full-pagination-arrow($eon-red);
      }
    }
    li.next {
      @include get-full-pagination-arrow($eon-red);
      &:hover {
        @include get-full-pagination-arrow($eon-red);
      }
    }
  }

  &.eon-bordeaux {
    li {
      &.active,
      &.active:hover {
        @include get-full-pagination-active-item($eon-bordeaux);
        a, a:hover {
          color: $eon-white;
        }
      }
      a {
        color: $eon-bordeaux;
        &:hover {
          color: $eon-bordeaux;
        }
      }
    }
    li.previous {
      @include get-full-pagination-arrow($eon-bordeaux);
      &:hover {
        @include get-full-pagination-arrow($eon-bordeaux);
      }
    }
    li.next {
      @include get-full-pagination-arrow($eon-bordeaux);
      &:hover {
        @include get-full-pagination-arrow($eon-bordeaux);
      }
    }
  }
  &.eon-turquoise {
    li {
      &.active,
      &.active:hover {
        @include get-full-pagination-active-item($eon-turquoise);
        a, a:hover {
          color: $eon-white;
        }
      }
      a {
        color: $eon-turquoise;
        &:hover {
          color: $eon-turquoise;
        }
      }
    }
    li.previous {
      @include get-full-pagination-arrow($eon-turquoise);
      &:hover {
        @include get-full-pagination-arrow($eon-turquoise);
      }
    }
    li.next {
      @include get-full-pagination-arrow($eon-turquoise);
      &:hover {
        @include get-full-pagination-arrow($eon-turquoise);
      }
    }
  }
  &.eon-limeyellow {
    li {
      &.active,
      &.active:hover {
        @include get-full-pagination-active-item($eon-limeyellow);
        a, a:hover {
          color: $eon-bordeaux;
        }
      }
      a {
        color: $eon-limeyellow;
        &:hover {
          color: $eon-limeyellow;
        }
      }
    }
    li.previous {
      @include get-full-pagination-arrow($eon-limeyellow);
      &:hover {
        @include get-full-pagination-arrow($eon-limeyellow);
      }
    }
    li.next {
      @include get-full-pagination-arrow($eon-limeyellow);
      &:hover {
        @include get-full-pagination-arrow($eon-limeyellow);
      }
    }
  }

  &.eon-white.bg-eon-red {
    li {
      &.active,
      &.active:hover {
        @include get-full-pagination-active-item($eon-white);
        a, a:hover {
          color: $eon-red;
        }
      }
      a {
        color: $eon-white;
        &:hover {
          color: $eon-white;
        }
      }
    }
    li.previous {
      @include get-full-pagination-arrow($eon-white);
      &:hover {
        @include get-full-pagination-arrow($eon-white);
      }
    }
    li.next {
      @include get-full-pagination-arrow($eon-white);
      &:hover {
        @include get-full-pagination-arrow($eon-white);
      }
    }
  }

  &.eon-white.bg-eon-bordeaux {
    li {
      &.active,
      &.active:hover {
        @include get-full-pagination-active-item($eon-white);
        a, a:hover {
          color: $eon-bordeaux;
        }
      }
      a {
        color: $eon-white;
        &:hover {
          color: $eon-white;
        }
      }
    }
    li.previous {
      @include get-full-pagination-arrow($eon-white);
      &:hover {
        @include get-full-pagination-arrow($eon-white);
      }
    }
    li.next {
      @include get-full-pagination-arrow($eon-white);
      &:hover {
        @include get-full-pagination-arrow($eon-white);
      }
    }
  }

  &.eon-white.bg-eon-limeyellow {
    li {
      &.active,
      &.active:hover {
        @include get-full-pagination-active-item($eon-bordeaux);
        a, a:hover {
          color: $eon-white;
        }
      }
      a {
        color: $eon-bordeaux;
        &:hover {
          color: $eon-bordeaux;
        }
      }
    }
    li.previous {
      @include get-full-pagination-arrow($eon-bordeaux);
      &:hover {
        @include get-full-pagination-arrow($eon-bordeaux);
      }
    }
    li.next {
      @include get-full-pagination-arrow($eon-bordeaux);
      &:hover {
        @include get-full-pagination-arrow($eon-bordeaux);
      }
    }
  }

  &.eon-white.bg-eon-turquoise {
    li {
      &.active,
      &.active:hover {
        @include get-full-pagination-active-item($eon-white);
        a, a:hover {
          color: $eon-turquoise;
        }
      }
      a {
        color: $eon-white;
        &:hover {
          color: $eon-white;
        }
      }
    }
    li.previous {
      @include get-full-pagination-arrow($eon-white);
      &:hover {
        @include get-full-pagination-arrow($eon-white);
      }
    }
    li.next {
      @include get-full-pagination-arrow($eon-white);
      &:hover {
        @include get-full-pagination-arrow($eon-white);
      }
    }
  }

}
