$eon-select-border-grey: #C3C3C3;
$eon-select-border-ligthgrey: #F0F0F0;
$eon-select-default-turquoise: #77D3DA;
$eon-select-hover-turquoise: #1EA2B1;
$eon-select-open-turquoise: #128FA1;
$eon-select-active-turquoise: #C3C3C3;

@mixin get-select-button($color) {
  $colorR: red($color);
  $colorG: green($color);
  $colorB: blue($color);
  @include background-svg('<svg width="26px" height="15px" viewBox="235 18 26 15" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M259,20 C256.258,24.142 252.883,28.088 248.904,31.677 C248.431,32.107 247.632,32.107 247.158,31.679 C243.151,28.071 239.755,24.166 237,20" id="icn_down" stroke="rgb(#{$colorR},#{$colorG},#{$colorB})" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" fill="none"></path></svg>');
}

.custom-select {
  cursor: pointer;
}

.eon-select,
.eon-select.large {
  width: 100%;
  background-color: transparent;
  height: 52px;
  padding: 0;
  margin: 0;
  border: 0;
  .ui-selectmenu-button {
    width: 100%;
    height: 100%;
    border: 2px solid $eon-darkgrey-25;
    border-radius: 0;
    background-color: transparent;
    padding:0 0 0 12px;
    border-radius: 3px;
    .ui-selectmenu-text {
      height: 48px;
      line-height: 48px;
      font-size: 18px;
      border:0;
      color: $eon-darkgrey;
    }
    .ui-selectmenu-icon {
      @include get-select-button($eon-white);
      background-repeat: no-repeat;
      background-size: 22px 12px;
      background-position: center center;
      width: 44px;
      height: 44px;
      margin: 2px;
      background-color: $eon-red;
      border:0;
    }
    &:hover {
      .ui-selectmenu-icon {
        background-color: $eon-red-active;
      }
    }
    &.ui-selectmenu-button-open {
      border-bottom: 0;
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
      .ui-selectmenu-icon {
        @include get-select-button($eon-white);
        background-color: $eon-red-active;
      }
    }
  }

  &.eon-red .ui-selectmenu-button {
    .ui-selectmenu-icon {
      background-color: $eon-red;
    }
    &:hover {
      .ui-selectmenu-icon {
        background-color: $eon-red-active;
      }
    }
    &.ui-selectmenu-button-open {
      .ui-selectmenu-icon {
        background-color: $eon-red-active;
      }
    }
  }

  &.eon-bordeaux .ui-selectmenu-button {
    .ui-selectmenu-icon {
      background-color: $eon-bordeaux;
    }
    &:hover {
      .ui-selectmenu-icon {
        background-color: $eon-bordeaux-active;
      }
    }
    &.ui-selectmenu-button-open {
      .ui-selectmenu-icon {
        background-color: $eon-bordeaux-active;
      }
    }
  }

  &.eon-turquoise .ui-selectmenu-button {
    .ui-selectmenu-icon {
      background-color: $eon-turquoise;
    }
    &:hover {
      .ui-selectmenu-icon {
        background-color: $eon-turquoise-active;
      }
    }
    &.ui-selectmenu-button-open {
      .ui-selectmenu-icon {
        background-color: $eon-turquoise-active;
      }
    }
  }

  &.eon-limeyellow .ui-selectmenu-button {
    .ui-selectmenu-icon {
      background-color: $eon-limeyellow;
    }
    &:hover {
      .ui-selectmenu-icon {
        background-color: $eon-limeyellow-active;
      }
    }
    &.ui-selectmenu-button-open {
      .ui-selectmenu-icon {
        background-color: $eon-limeyellow-active;
      }
    }
  }
}
.ui-selectmenu-menu {
  .ui-menu {
    border: 2px solid $eon-darkgrey-25;
    border-top: 0;
    padding: 0;
    .ui-menu-item {
      .ui-menu-item-wrapper {
        background-color: $eon-white;
        color: $eon-darkgrey;
        height: 52px;
        border: 0;
        margin: 0;
        line-height: 52px;
        padding: 0 0 0 12px;
        font-size: 18px;
        &.ui-state-active {
          background-color: $eon-lightgrey;
        }
      }
    }
  }
}

.eon-select.small {
  height: 40px;
  .ui-selectmenu-button {
    padding:0 0 0 12px;
    .ui-selectmenu-text {
      height: 36px;
      line-height: 36px;
      font-size: 16px;
    }
    .ui-selectmenu-icon {
      @include get-select-button($eon-white);
      background-size: 14px 8px;
      width: 32px;
      height: 32px;
    }
  }
}
.ui-selectmenu-menu {
  .ui-menu.small {
    .ui-menu-item {
      .ui-menu-item-wrapper {
        height: 36px;
        line-height: 36px;
        font-size: 16px;
        padding: 0 0 0 12px;
      }
    }
  }
}
