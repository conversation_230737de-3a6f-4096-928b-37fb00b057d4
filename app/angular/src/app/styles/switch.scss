@mixin get-switch($color) {
  $colorR: red($color);
  $colorG: green($color);
  $colorB: blue($color);
  $svg-1: '<svg width="27px" height="27px" viewBox="0 4 27 27" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><!-- Generator: Sketch 41.1 (35376) - http://www.bohemiancoding.com/sketch --><desc>Created with Sketch.</desc><defs></defs><path d="M4.20745621,4.83625054 C2.49728427,5.16162335 1.16159409,6.49731023 0.836220472,8.20747795 C-0.278740157,14.0692022 -0.278740157,19.9307978 0.836220472,25.7925221 C1.16159409,27.5026898 2.49728427,28.8383766 4.20745621,29.1637495 C7.13826129,29.7211641 10.0691949,30 13,30 C15.9308051,30 18.8617387,29.7211641 21.7925438,29.1637495 C23.5027157,28.8383766 24.8384059,27.5026898 25.1637795,25.7925221 C26.2787402,19.9307978 26.2787402,14.0692022 25.1637795,8.20747795 C24.8384059,6.49731023 23.5027157,5.16162335 21.7925438,4.83625054 C18.8617387,4.27870733 15.9308051,4 13,4 C10.0691949,4 7.13826129,4.27870733 4.20745621,4.83625054 Z" id="Combined-Shape" stroke="none" fill="rgb(#{$colorR},#{$colorG},#{$colorB})" fill-rule="evenodd"></path></svg>';
  $svg-2: '<svg width="69px" height="37px" viewBox="-1 -1 69 37" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><path d="M16.3666667,34.9622307 L16.3666667,35 L49.3213034,35 C49.3213034,35 57.2039585,34.9217195 61.2024809,34.2730038 C63.5133998,33.8190549 65.3182893,32.1530195 65.7579597,29.7670525 C67.2645823,21.5889773 67.2645823,13.4110814 65.7579597,5.23300619 C65.3182893,2.84703925 63.5133998,1.17749412 61.2024809,0.723545207 C57.6536161,0.0866816226 49.3213034,0 49.3213034,0 L16.3666667,0 L16.3666667,0.0370906717 C13.6840448,0.103612837 8.89815818,0.280369391 6.428601,0.723545207 C4.11768206,1.17749412 2.31279261,2.84703925 1.87312217,5.23300619 C0.366499606,13.4110814 0.366499606,21.5889773 1.87312217,29.7670525 C2.31279261,32.1530195 4.11768206,33.8190549 6.428601,34.2730038 C9.18011526,34.7194063 13.7708701,34.8956926 16.3666667,34.9622307 Z" id="Combined-Shape" stroke="rgb(#{$colorR},#{$colorG},#{$colorB})" stroke-width="2" fill="none"></path></svg>';
  @include multi-background-svg($svg-1, $svg-2);
}

.eon-switch {
  input[type="checkbox"] {
    display: none;
  }
  width: 67px;
  height: 36px;
  @include get-switch($eon-red);
  background-size: 27px 27px, 67px 36px;
  background-position: 7px center, center center;
  background-repeat: no-repeat, no-repeat;
  -webkit-transition: background .3s ease-out;
    -moz-transition: background .3s ease-out;
    -o-transition: background .3s ease-out;
    transition: background .3s ease-out;
  &:hover {
    cursor: pointer;
  }
  &.checked {
    background-size: 27px 27px, 67px 36px;
    background-position: 34px center, center center;
    background-repeat: no-repeat, no-repeat;
  }

  &.eon-red {
    @include get-switch($eon-red);
  }
  &.eon-bordeaux {
    @include get-switch($eon-bordeaux);
  }
  &.eon-limeyellow {
    @include get-switch($eon-limeyellow);
  }
  &.eon-turquoise {
    @include get-switch($eon-turquoise);
  }
  &.eon-white {
    @include get-switch($eon-white);
  }
}

