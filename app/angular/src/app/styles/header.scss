/* @import "app/styles/vars.scss"; */
/* @import "app/styles/colors.scss"; */


@media (min-width: 1220px){
  .container {
      max-width: 1220px;
  }
}

body{
  font-family: $eon-font-family !important;
}

#app-header{
  background: white;
  height:$header-height;
  border-bottom: 1px solid #ddd;
  position: relative;

  .app-header--nav{
    padding-left: 100px;
    align-self: flex-start;

    .app-header-title{
      display: flex;
      flex-direction: column;
    }
    .app-name{
      margin: 0;
      font-size: 18px;
      color: $eon-middlegrey;
      padding: 8px 0px;
      line-height: 1;
      font-weight: 600;
    }

    .page-title {
      font-weight: 600;
      line-height: 1;
      font-size: 28px;
    }
  }
}
.app-header--menu{

}
#app-nav{
  ul{
    display: inline-block;
    margin: 0;
  }
  ul li{
    display: inline-block;
  }
  .app-logo{
    display:inline-block;
  }
  ul.list-nav{
    a{
      color: $eon-middlegrey;
      padding: 0 15px;
      vertical-align: middle;
      font-size: 21px;
      cursor: pointer;
    }
  }
}

nav#side-menu{
  display: none;
  background: $eon-red-active;
  left: 0;
  top:$header-height;
  position: absolute;
  width: 400px;
  z-index: 1000;
  a,a:link{color:#fff;}

  &.ang {
    display: block;
    transform: translateX(-100%);
    transition: all 0.5s ease;
    z-index: 1001;
    &.is-active {
      display: block;
      transform: translateX(0%);
    }
  }

  &.is-active {
    display: block;
  }

  ul{
    margin: 0;
  }
  li{
    border-bottom: 1px solid rgba(255,255,255,0.15);
    display: block;

    > a{
      display: block;
      padding: 15px;
      line-height: 1.5;
      font-size: 21px;
      cursor: pointer;

      &:hover{
        text-decoration: none;
      }
    }
  }
  ul.submenu{
    display: none;
    background:$eon-bordeaux;
    border-left: 8px solid $eon-turquoise;
    > li{
      border-bottom: 1px solid rgba(255,255,255,0.05);
    }
    &.is-active {
      display: block;
      box-shadow: 0px 2px 4px rgba(0,0,0,0.1);
    }
  }
}

#app-nav ul.list-nav .submenu-block{
    display: block;
    background: #fff;
    position: absolute;
    left: 0;
    top: $header-height;
    width: 100%;
    padding: 30px 30px 0 30px;
    z-index: 999;
    border-bottom: 1px solid rgba(0,0,0,0.125);

    li{
        text-align: right;
        padding-bottom: 10px;
    }

    button {
      color:$eon-red-active;
      font-size: 18px;
      font-weight: 600;
      border: 0;
      background: none;
    }

    a{
      color:$eon-red-active;
      font-size: 18px;
      font-weight: 600;
    }

    h3{
      padding: 50px 0;
      font-weight: 800;
      color:$eon-red-active;
    }

    form{
      align-self: center;
    }

    .container{
      border-bottom: 5px solid $eon-turquoise;
    }
}
