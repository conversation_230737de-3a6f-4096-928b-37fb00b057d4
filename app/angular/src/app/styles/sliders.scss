$eon-slider-track-darker-turquoise: #0F738A;
$eon-slider-track-darker-bordeaux: #76020C;

@mixin get-slider($color, $arrowsColor) {
  $colorR: red($color);
  $colorG: green($color);
  $colorB: blue($color);
  $arrowsColorR: red($arrowsColor);
  $arrowsColorG: green($arrowsColor);
  $arrowsColorB: blue($arrowsColor);
  @include background-svg("<svg width='53px' height='52px' viewBox='23 0 53 52' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'><g id='Group-13' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' transform='translate(23.000000, 0.000000)'><path d='M8.41491242,1.67250109 C4.99456854,2.32324671 2.32318817,4.99462047 1.67244094,8.4149559 C-0.557480315,20.1384043 -0.557480315,31.8615957 1.67244094,43.5850441 C2.32318817,47.0053795 4.99456854,49.6767533 8.41491242,50.3274989 C14.2765226,51.4423282 20.1383898,52 26,52 C31.8616102,52 37.7234774,51.4423282 43.5850876,50.3274989 C47.0054315,49.6767533 49.6768118,47.0053795 50.3275591,43.5850441 C52.5574803,31.8615957 52.5574803,20.1384043 50.3275591,8.4149559 C49.6768118,4.99462047 47.0054315,2.32324671 43.5850876,1.67250109 C37.7234774,0.557414659 31.8616102,0 26,0 C20.1383898,0 14.2765226,0.557414659 8.41491242,1.67250109 Z' id='Fill-281-Copy-4' fill='rgb(#{$colorR},#{$colorG},#{$colorB})'></path></g><g id='Group-6' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd' transform='translate(33.000000, 19.000000)' stroke-linecap='round' stroke-linejoin='round'><path d='M24,0 C26.635,1.745 29.146,3.893 31.431,6.425 C31.705,6.727 31.705,7.234 31.432,7.536 C29.136,10.085 26.651,12.247 24,14' id='icn_right_small' stroke='rgb(#{$arrowsColorR},#{$arrowsColorG},#{$arrowsColorB})' stroke-width='2'></path><path d='M7.63662517,14 C5.00062517,12.256 2.49062517,10.107 0.205625171,7.576 C-0.0683748286,7.274 -0.0683748286,6.767 0.204625171,6.465 C2.49962517,3.915 4.98562517,1.754 7.63662517,0' id='icn_left_small' stroke='rgb(#{$arrowsColorR},#{$arrowsColorG},#{$arrowsColorB})' stroke-width='2'></path></g></svg>");
}

.eon-slider {
  &.ui-slider {
    height: 8px;
    border:0;
    background-color: $eon-darkgrey-25;
    border-radius: 2px;
    margin: 22px 0;
    .ui-slider-handle {
      width: 52px;
      height: 52px;
      border:0;
      top: -22px;
      margin-left: -26px;
      border:0;
      background-color: transparent;
      @include get-slider($eon-red, $eon-white);
      &:hover {
        cursor: pointer;
        @include get-slider($eon-red-active, $eon-white);
      }
    }
  }
  &.eon-red .ui-slider-handle {
    @include get-slider($eon-red, $eon-white);
    &:hover {
      @include get-slider($eon-red-active, $eon-white);
    }
  }
  &.eon-bordeaux {
    background-color: $eon-white;
    .ui-slider-handle {
      @include get-slider($eon-bordeaux, $eon-white);
      &:hover {
        @include get-slider($eon-bordeaux-active, $eon-white);
      }
    }
  }
  &.eon-turquoise .ui-slider-handle {
    @include get-slider($eon-turquoise, $eon-white);
    &:hover {
      @include get-slider($eon-turquoise-active, $eon-white);
    }
  }
  &.eon-limeyellow .ui-slider-handle {
    @include get-slider($eon-limeyellow, $eon-bordeaux);
    &:hover {
      @include get-slider($eon-limeyellow-active, $eon-bordeaux);
    }
  }

  &.eon-white.eon-red {
    background-color: $eon-bordeaux;
    .ui-slider-handle {
      @include get-slider($eon-white, $eon-red);
      &:hover {
        @include get-slider($eon-lightgrey, $eon-red);
      }
    }
  }

  &.eon-white.eon-bordeaux {
    background-color: $eon-slider-track-darker-bordeaux;
    .ui-slider-handle {
      @include get-slider($eon-white, $eon-bordeaux);
      &:hover {
        @include get-slider($eon-lightgrey, $eon-bordeaux);
      }
    }
  }
  &.eon-white.eon-limeyellow {
    background-color: $eon-white;
    .ui-slider-handle {
      @include get-slider($eon-bordeaux, $eon-white);
      &:hover {
        @include get-slider($eon-bordeaux-active, $eon-white);
      }
    }
  }

  &.eon-white.eon-turquoise {
    background-color: $eon-slider-track-darker-turquoise;
    .ui-slider-handle {
      @include get-slider($eon-white, $eon-turquoise);
      &:hover {
        @include get-slider($eon-lightgrey, $eon-turquoise);
      }
    }
  }

}
