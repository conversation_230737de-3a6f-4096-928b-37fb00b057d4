import { Routes } from '@angular/router';
import { environment } from "../../environments/environment";
import { AssetConfigurationRibbonComponent } from "../components/configuration/asset_level/asset_configuration_ribbon.component";
import { MarketConfigurationRibbonComponent } from "../components/configuration/market_level/market_configuration_ribbon.component";
import { RollupConfigurationRibbonComponent } from "../components/configuration/rollup_level/rollup_configuration_ribbon.component";

export const configurationRoutes: Routes = [
    {
        path: `${environment.routingPath}asset_configuration_ribbon`,
        component: AssetConfigurationRibbonComponent
    },
    {
        path: `${environment.routingPath}asset_configuration_ribbon/:tab`,
        component: AssetConfigurationRibbonComponent
    },
    {
        path: `${environment.routingPath}market_configuration_ribbon/:tab/:id`,
        component: MarketConfigurationRibbonComponent
    },
    {
        path: `${environment.routingPath}market_configuration_ribbon`,
        component: MarketConfigurationRibbonComponent
    },
    {
        path: `${environment.routingPath}market_configuration_ribbon/:tab`,
        component: MarketConfigurationRibbonComponent
    },

    {
        path: `${environment.routingPath}rollup_configuration_ribbon`,
        component: RollupConfigurationRibbonComponent
    },
    {
        path: `${environment.routingPath}rollup_configuration_ribbon/:tab`,
        component: RollupConfigurationRibbonComponent
    }
];
