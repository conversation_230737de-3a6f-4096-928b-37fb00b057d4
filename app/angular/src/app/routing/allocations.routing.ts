import { Routes } from '@angular/router';
import { AllocationsComponent } from '../components/allocations/allocations.component';
import { AutomaticAllocationComponent } from '../components/allocations/automatic_allocation/automatic_allocation.component'
import { ManualAllocationComponent } from '../components/allocations/manual_allocation/manual_allocation.component'
import { environment } from "../../environments/environment";

export const allocationsRoutes: Routes = [
  {
    path: `${environment.routingPath}allocations`,
    component: AllocationsComponent
  },
  {
    path: `${environment.routingPath}allocations/automatic-allocation/:id`,
    component: AutomaticAllocationComponent
  },
  {
    path: `${environment.routingPath}allocations/manual-allocation/:id`,
    component: ManualAllocationComponent
  },
  {
    path: `${environment.routingPath}allocations/:tab`,
    component: AllocationsComponent
  },
];
