import { Routes } from '@angular/router';
import { NominationsComponent } from '../components/nominations/nominations.component';
import { NominationDetailComponent } from '../components/nominations/nomination_detail/nomination_detail.component';
import { environment } from "../../environments/environment";

export const nominationsRoutes: Routes = [
  {
    path: `${environment.routingPath}nominations`,
    component: NominationsComponent
  },
  {
    path: `${environment.routingPath}nominations/nomination_detail/:id`,
    component: NominationDetailComponent
  },
  {
    path: `${environment.routingPath}nominations/:tab`,
    component: NominationsComponent
  }


];
