import { Routes } from '@angular/router';
import { HistoryDistributedUnitComponent } from '../components/history/distributed_units/distributed_units.component';
import { DlmChargingSessionsComponent } from '../components/history/dlm_charging_sessions/dlm_charging_sessions.component';
import { BidDetailComponent } from "../components/history/distributed_units/bids_history/bid_detail.component";
import {ReportingEmailComponent} from "../components/history/reporting_email/reporting_email.component";
import { environment } from "../../environments/environment";

export const historyRoutes: Routes = [
  {
    path: `${environment.routingPath}history`,
    component: HistoryDistributedUnitComponent
  },
  {
    path: `${environment.routingPath}history/bid_detail/:id`,
    component: BidDetailComponent
  },
  {
    path: `${environment.routingPath}history/:tab`,
    component: HistoryDistributedUnitComponent
  },
  {
    path: `${environment.routingPath}reporting-emails/reporting_email_detail/:id`,
    component: ReportingEmailComponent
  },
  {
    path: `${environment.routingPath}history/dlm-charging-sessions`,
    component: DlmChargingSessionsComponent
  },


];
