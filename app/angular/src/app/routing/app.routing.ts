import { ModuleWithProviders } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';

import { managementRoutes } from './management.routing';
import { historyRoutes } from './history.routing';
import { nominationsRoutes } from './nominations.routing';
import { rollupsRoutes } from './rollups.routing';
import { allocationsRoutes } from './allocations.routing';
import { automaticReportEmailingsRoutes } from './automatic_report_emailings.routing';
import { reportsRoutes } from './reports.routing';
import { biddingRoutes } from './bidding.routing';
import { eventNotificationsRoutes } from "./event_notifications.routing";
import { configurationRoutes } from "./configuration.routing";
import {schedulingReportsRoutes} from "./scheduling_reports.routing";

const appRoutes: Routes = [
  ...managementRoutes,
  ...historyRoutes,
  ...nominationsRoutes,
  ...rollupsRoutes,
  ...allocationsRoutes,
  ...schedulingReportsRoutes,
  ...automaticReportEmailingsRoutes,
  ...eventNotificationsRoutes,
  ...reportsRoutes,
  ...biddingRoutes,
  ...configurationRoutes
];

export const appRoutingProviders: any[] = [
];

export const Routing: ModuleWithProviders = RouterModule.forRoot(appRoutes);
