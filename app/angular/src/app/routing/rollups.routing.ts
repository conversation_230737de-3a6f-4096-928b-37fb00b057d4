import { Routes } from '@angular/router';
import { RollupsComponent } from '../components/rollups/rollups.component';
import {RollupErrorDetailComponent} from "../components/rollups/rollup_error_detail/rollup_error_detail.component";
import { environment } from "../../environments/environment";

export const rollupsRoutes: Routes = [
  {
    path: `${environment.routingPath}rollups`,
    component: RollupsComponent
  },
  {
    path: `${environment.routingPath}rollups/rollup-error-detail/:id`,
    component: RollupErrorDetailComponent
  },
  {
    path: `${environment.routingPath}rollups/:tab`,
    component: RollupsComponent
  }
];
