{"PAGE.CAPTIONS.DASHBOARD": "Dashboard", "PAGE.CAPTIONS.BIDDING": "Bidding", "PAGE.CAPTIONS.NOMINATIONS": "Nominations", "PAGE.CAPTIONS.REPORTING.TSO": "TSO Reporting", "PAGE.CAPTIONS.ALLOCATIONS": "Allocations", "PAGE.CAPTIONS.REPORTING_AND_NOTIFICATIONS.EMAILS": "Reporting and Notifications – Emails", "PAGE.CAPTIONS.REPORTING_AND_NOTIFICATIONS.EMAILS.SCHEDULING_REPORT": "Reporting and Notifications – Emails - Scheduling Report", "PAGE.CAPTIONS.REPORTING_AND_NOTIFICATIONS.EMAILS.ALLOCATION": "Reporting and Notifications – Emails - Allocation", "PAGE.CAPTIONS.REPORTING_AND_NOTIFICATIONS.EMAILS.ROLLUPS": "Reporting and Notifications – Emails - Rollups", "PAGE.CAPTIONS.HISTORY.NOMINATIONS": "History – Logs - Nominations", "PAGE.CAPTIONS.HISTORY.MANUAL_ALLOCATIONS": "History – Logs – Manual Allocations", "PAGE.CAPTIONS.HISTORY.AUTOMATIC_ALLOCATIONS": "History – Logs – Automatic Allocations", "PAGE.CAPTIONS.HISTORY.AUCTIONS": "History – Logs – Auctions", "PAGE.CAPTIONS.HISTORY.REPORTING_EMAILS": "History – Logs – Reporting Emails", "PAGE.CAPTIONS.HISTORY.BIDS": "History – Logs – Bids", "PAGE.CAPTIONS.HISTORY.BID_DETAIL": "History – Logs – Bids - Detail", "PAGE.CAPTIONS.HISTORY.ROLLUPS": "History – Rollups", "PAGE.CAPTIONS.HISTORY.ROLLUPS.ASSET_ROLLUPS": "History – Rollups - <PERSON><PERSON> Rollups", "PAGE.CAPTIONS.HISTORY.ROLLUPS.DISPATCH_GROUP_ROLLUPS": "History – Rollups - Dispatch Group Rollups", "PAGE.CAPTIONS.HISTORY.ROLLUPS.ROLLUP_ERRORS": "History – Rollups - Roll<PERSON> Errors", "PAGE.CAPTIONS.HISTORY.DLM_CHARGING_SESSIONS": "History – DLM Charging Sessions", "PAGE.CAPTIONS.HISTORY.V2G": "History – Logs - V2G Optimization Jobs", "PAGE.CAPTIONS.CONFIGURATION.ASSET_LEVEL": "Configuration - Asset Level", "PAGE.CAPTIONS.CONFIGURATION.ASSET_LEVEL.BOX_TYPES": "Configuration - Asset Level - Box Types", "PAGE.CAPTIONS.CONFIGURATION.ASSET_LEVEL.GENERIC_STEERING_TYPES": "Configuration - Asset Level - Generic Steering Types", "PAGE.CAPTIONS.CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS": "Configuration - Asset Level - Signal Lists", "PAGE.CAPTIONS.CONFIGURATION.MARKET_LEVEL.SUBPOOLS": "Configuration - Market Level - Subpools", "PAGE.CAPTIONS.CONFIGURATION.MARKET_LEVEL.AUCTIONS": "Configuration - Market Level - Auctions", "PAGE.CAPTIONS.CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS": "Configuration - Market Level - Balancing Groups", "PAGE.CAPTIONS.CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS": "Configuration - Market Level - Dispatch Groups", "PAGE.CAPTIONS.CONFIGURATION.ROLLUPS_LEVEL.ASSET_ROLLUPS": "Configuration - Rollups - Asset Rollups", "PAGE.CAPTIONS.CONFIGURATION.ROLLUPS_LEVEL.DISPATCH_GROUP_ROLLUPS": "Configuration - Rollups - Dispatch Group Rollups", "BACK": "Back", "COMPONENTS.SELECT": "Select", "negative": "Negative", "positive": "Positive", "USER.LANGUAGE": "Want to view this page in another language?", "USER.CHANGE_PASSWORD": "Change Password", "USER.LOGOUT": "Logout", "HEADER.HOME": "Home", "HEADER.BIDDING": "Bidding", "HEADER.NOMINATIONS": "Nominations", "HEADER.TSO_REPORTS": "TSO Reports", "HEADER.ALLOCATIONS": "Allocations", "HEADER.REPORTING_AND_NOTIFICATIONS": "Reporting and Notifications", "HEADER.SCHEDULING_REPORTS": "Scheduling Reports", "HEADER.EMAILS": "Emails", "HEADER.NOTIFICATIONS": "Notifications", "HEADER.HISTORY": "History", "HEADER.LOGS": "Logs", "HEADER.ROLLUPS": "Rollups", "HEADER.CONFIGURATION": "Configuration", "HEADER.ASSET_LEVEL": "Asset Level", "HEADER.MARKET_LEVEL": "Market Level", "HEADER.DISPATCH_GROUP_CONFIG": "Generic DG Config", "HEADER.DGS": "DGs", "HEADER.SUBPOOLS": "Subpools", "HEADER.ASSET_CONFIGURATION": "Asset Configuration", "HEADER.USAGE": "Logs", "DASHBOARD.BIDDING": "Prepare and upload your bids for marketing.", "DASHBOARD.TSO_REPORT": "Upload the TSO day-after reports to generate the scheduling reports for internal booking.", "DASHBOARD.ALLOCATIONS": "Visualize and edit the allocations of assets to different dispatch groups.", "DASHBOARD.AUCTION_RESULTS": "Upload the auction results and individually edit the nominations entered in the system.", "DASHBOARD.BUTTONS.BIDDING": "Bidding", "DASHBOARD.BUTTONS.UPLOAD_MARKET_RESULTS": "Upload Market Results", "DASHBOARD.BUTTONS.TSO_REPORT": "TSO Report", "DASHBOARD.BUTTONS.UPLOAD_ALLOCATION_FILE": "Upload Allocation FIle", "AUCTIONS.RESULTS.TITLE": "Auction Results Overview", "AUCTIONS.RESULTS.FILTERS.DELIVERY_DATE": "Delivery Date", "AUCTIONS.RESULTS.FILTERS.AUCTION": "<PERSON>der", "AUCTIONS.RESULTS.FILTERS.ASSET": "<PERSON><PERSON>", "AUCTIONS.RESULTS.FILTERS.TSO": "TSO", "AUCTIONS.RESULTS.FILTERS.DIRECTION": "Direction", "AUCTIONS.RESULTS.SELECT_AUCTIONS": "All", "AUCTIONS.RESULTS.SELECT_PRODUCTS": "Select Products", "AUCTIONS.RESULTS.SELECT_TSO": "Select TSO", "AUCTIONS.RESULTS.SELECT_DIRECTION": "Select Direction", "AUCTIONS.RESULTS.ENERGY_DIRECTION.ALL": "Select Direction", "AUCTIONS.RESULTS.ENERGY_DIRECTION.POSITIVE": "POS", "AUCTIONS.RESULTS.ENERGY_DIRECTION.NEGATIVE": "NEG", "AUCTIONS.RESULTS.ACCEPTED_ONLY": "Accepted Only", "AUCTIONS.RESULTS.DOWNLOAD": "Download", "AUCTIONS.RESULTS.TABLE.AUCTION": "<PERSON>der", "AUCTIONS.RESULTS.TABLE.TSO": "TSO", "AUCTIONS.RESULTS.TABLE.DATE": "Date", "AUCTIONS.RESULTS.TABLE.PRODUCT": "Product", "AUCTIONS.RESULTS.TABLE.CAPACITY_PRICE": "Capacity Price", "AUCTIONS.RESULTS.TABLE.ENERGY_PRICE": "Energy Price", "AUCTIONS.RESULTS.TABLE.CAPACITY": "Capacity", "AUCTIONS.RESULTS.TABLE.CAPACITY_ACCEPTED": "Capacity Accepted", "AUCTIONS.RESULTS.TABLE.ASSET": "<PERSON><PERSON>", "AUCTIONS.RESULTS.TABLE.ASSET_EXTERNAL_ID": "Asset External ID", "AUCTIONS.RESULTS.TABLE.PER_PAGE": "per page", "BIDDING.TITLE": "Bidding", "BIDDING.TABS.PREPARE_BIDS": "Prepare Bids", "BIDDING.TABS.UPLOAD_BIDS": "Upload Bids", "BIDDING.TABS.ASSET_OPTIMIZATION": "Asset Optimization", "BIDDING.LIST.TITLE": "Bidding Overview", "BIDDING.LIST.FILTERS.DELIVERY_DATE": "Delivery Date", "BIDDING.LIST.FILTERS.AUCTION": "<PERSON>der", "BIDDING.LIST.FILTERS.ASSET": "Assets", "BIDDING.LIST.FILTERS.ONLY_BIDS_WITHOUT_ASSET": "Bids Without Asset", "BIDDING.LIST.LINKS.EXPORT": "Export", "BIDDING.LIST.LINKS.DOWNLOAD_ALL": "Download All Bids", "BIDDING.LIST.LINKS.DELETE_BIDS": "Delete Bids", "BIDDING.LIST.DELETED_SUCCESS": "Successfully deleted the bids.", "BIDDING.LIST.DELETED_ERROR": "Unable to delete the bids.", "BIDDING.LIST.AUCTION_CLOSED": "Auction Closed", "BIDDING.LIST.LAST_UPLOADED_FILE": "Last Uploaded File", "BIDDING.LIST.TENDER_DELIVERY_INTERVAL": "Tender Delivery Interval", "BIDDING.LIST.BY": "By", "BIDDING.LIST.TABLE.AUCTION": "<PERSON>der", "BIDDING.LIST.TABLE.DELIVERY_START": "Delivery Start", "BIDDING.LIST.TABLE.PRODUCT": "Product", "BIDDING.LIST.TABLE.MARKET": "Market", "BIDDING.LIST.TABLE.TSO": "TSO", "BIDDING.LIST.TABLE.CAPACITY_PRICE": "Capacity Price (¤/MW)", "BIDDING.LIST.TABLE.ENERGY_PRICE": "Energy Price (¤/MWh)", "BIDDING.LIST.TABLE.CAPACITY": "Capacity (MW)", "BIDDING.LIST.TABLE.ASSET_ID": "Asset ID", "BIDDING.LIST.TABLE.ASSET_NAME": "Asset Name", "BIDDING.LIST.TABLE.ASSET_EXTERNAL_ID": "Asset External ID", "BIDDING.LIST.TABLE.NO_BIDS_UPLOADED": "No bids uploaded for selected delivery date and tender.", "BIDDING.LIST.TABLE.PER_PAGE": "per page", "BIDDING.PREPARE_BIDS.TITLE": "Get the bidding suggestions from the system", "BIDDING.PREPARE_BIDS.DELIVERY_DATE": "Delivery Date", "BIDDING.PREPARE_BIDS.PRODUCT": "Product", "BIDDING.PREPARE_BIDS.TIME_SLICES": "Time Slices", "BIDDING.PREPARE_BIDS.ALL_TIME_SLICES": "All Time Slices", "BIDDING.PREPARE_BIDS.REQUEST_BIDDING_SUGGESTIONS": "Request Bidding Suggestions", "BIDDING.PREPARE_BIDS.EXPORT": "Export", "BIDDING.UPLOAD_BIDS.TITLE": "Select the file containing the bids", "BIDDING.UPLOAD_BIDS.SUBTITLE": "Once created, bids will be submitted to the market only after being saved, see below.", "BIDDING.UPLOAD_BIDS.EXAMPLE": "Example for Regeleistung", "BIDDING.UPLOAD_BIDS.EXAMPLE_AFRR": "Example for aFRR", "BIDDING.UPLOAD_BIDS.UPLOAD_BIDS_FILE": "Upload Bids File", "BIDDING.UPLOAD_BIDS.SUCCESS_UPLOADED": "Successfully uploaded the bids.", "BIDDING.UPLOAD_BIDS.SUCCESS_UPLOADED_AUTOMATIC": "Nomination bids were automatically created for the bids that were not previously auctioned with a nomination bid.", "BIDDING.UPLOAD_BIDS.AUCTION": "<PERSON>der", "BIDDING.ASSET_OPTIMIZATION.TITLE": "Get the bidding suggestions from the system", "BIDDING.ASSET_OPTIMIZATION.BIDDING_METHODS": "Optimizations", "BIDDING.ASSET_OPTIMIZATION.PERSPECTIVE": "Perspective", "BIDDING.ASSET_OPTIMIZATION.ASSET": "<PERSON><PERSON>", "BIDDING.ASSET_OPTIMIZATION.SWING_LIMIT": "Swing Limit", "BIDDING.ASSET_OPTIMIZATION.SWING_LIMIT_YES_NO": "Yes", "BIDDING.ASSET_OPTIMIZATION.SWING_LIMIT_VALUE": "Value", "BIDDING.ASSET_OPTIMIZATION.TENDER": "<PERSON>der", "BIDDING.ASSET_OPTIMIZATION.TENDERS": "Tenders combination", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST_TYPE": "Price Forecast Type", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST_TYPE_AUTOMATED": "Automated", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST_TYPE_MANUAL": "Manual", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.SHOW": "Show Price Forecast file format", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.HIDE": "Hide Price Forecast file format", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.AUTOMATED.LATEST": "Latest available price forecast: ", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_FORMAT_DETAIL": "For DC/DM/DR rhe CSV file should have the following format:", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_FORMAT": "datetime_from;datetime_to;Priceforecast_DCH;Priceforecast_DCL;Priceforecast_DMH;Priceforecast_DML;Priceforecast_DRH;Priceforecast_DRL", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_LIST_TITLE": "The names of the colums matters, not their order.", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_1": "\"datetime_from\" the timestamp in ISO format [yyyy-mm-ddThh:mm:ss+xx:xx]", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_2": "\"datetime_to\" the timestamp in ISO format [yyyy-mm-ddThh:mm:ss+xx:xx]", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_3": "\"Priceforecast_DCH\" decimal value of price forecast for DC high", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_4": "\"Priceforecast_DCL\" decimal value of price forecast for DC low", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_5": "\"Priceforecast_DMH\" decimal value of price forecast for DM high", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_6": "\"Priceforecast_DML\" decimal value of price forecast for DM low", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_7": "\"Priceforecast_DRH\" decimal value of price forecast for DR high", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_8": "\"Priceforecast_DRL\" decimal value of price forecast for DR low", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_FORMAT_DETAIL_EPEX30MIN": "For EPEX30MIN the CSV file should have the following format:", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_FORMAT_EPEX30MIN": "\"EPEX 30 Min FC File\",1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_1_EPEX30MIN": "\"EPEX 30 Min FC File\" column contains the day in format dd.mm.yyy e.g. 11.12.2024", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_2_EPEX30MIN": "\"1..50\" columns contains the price the corresponding settlement period", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_FORMAT_DETAIL_N2EX1H": "For N2EX1H the CSV file should have the following format:", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_FORMAT_N2EX1H": ",1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_1_N2EX1H": "The first column (with empty header) contains the day in format dd.mm.yyy e.g. 11.12.2024", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_2_N2EX1H": "\"1..50\" columns contains the price the corresponding settlement period", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST": "Price Forecast", "BIDDING.ASSET_OPTIMIZATION.EXCLLDE_DC_HIGH_EFA_BLOCKS": "Exclude DC High EFA blocks", "BIDDING.ASSET_OPTIMIZATION.EXCLLDE_DC_LOW_EFA_BLOCKS": "Exclude DC Low EFA blocks", "BIDDING.ASSET_OPTIMIZATION.EXCLLDE_DM_HIGH_EFA_BLOCKS": "Exclude DM High EFA blocks", "BIDDING.ASSET_OPTIMIZATION.EXCLLDE_DM_LOW_EFA_BLOCKS": "Exclude DM Low EFA blocks", "BIDDING.ASSET_OPTIMIZATION.EXCLLDE_DR_HIGH_EFA_BLOCKS": "Exclude DR High EFA blocks", "BIDDING.ASSET_OPTIMIZATION.EXCLLDE_DR_LOW_EFA_BLOCKS": "Exclude DR Low EFA blocks", "BIDDING.ASSET_OPTIMIZATION.REQUEST_BIDDING_SUGGESTIONS": "Request Bidding Suggestions", "BIDDING.ASSET_OPTIMIZATION.BIDDING_SUGGESTIONS_INFO": "The optimization can take some time to run. Please wait. If the result is not available after 1-2 minutes please refresh the page.", "BIDDING.ASSET_OPTIMIZATION.TABLE.DATE": "Run at", "BIDDING.ASSET_OPTIMIZATION.TABLE.STATUS": "Status", "BIDDING.ASSET_OPTIMIZATION.TABLE.ASSET": "<PERSON><PERSON>", "BIDDING.ASSET_OPTIMIZATION.TABLE.TENDERS": "Tenders combination", "BIDDING.ASSET_OPTIMIZATION.TABLE.USER": "User", "BIDDING.ASSET_OPTIMIZATION.TABLE.NO_ASSET_OPTIMIZATIONS": "No asset optimizations", "BIDDING.ASSET_OPTIMIZATION.ACTION.REJECT_BIDS": "Reject Bids", "BIDDING.ASSET_OPTIMIZATION.ACTION.VALIDATE_BIDS": "Validate Bids", "BIDDING.ASSET_OPTIMIZATION.ACTION.DOWNLOAD_MARKET_POSITIONS": "Download Market Positions", "ASSET_OPTIMIZATIONS.BIDS_VALIDATED_SUCCESS": "Successfully validated bids", "ASSET_OPTIMIZATIONS.BIDS_VALIDATED_ERROR": "Error while validating bids", "ASSET_OPTIMIZATIONS.BIDS_REJECTED_SUCCESS": "Successfully rejected bids", "ASSET_OPTIMIZATIONS.BIDS_REJECTED_ERROR": "Error while rejecting bids", "NOMINATIONS.TITLE": "Nominations", "NOMINATIONS.TABS.ENTER_NOMINATIONS": "Enter Nominations", "NOMINATIONS.TABS.UPLOAD_MARKET_RESULTS": "Upload Market Results", "NOMINATIONS.TABS.UPLOAD_NOMINATIONS_FILE": "Upload Nominations File", "NOMINATIONS.TABS.UPLOAD_OPTI_RESULTS_FILE": "Upload Opti-results", "NOMINATIONS.TABS.DOWNLOAD_OPTI_RESULTS": "Download Opti-results", "NOMINATIONS.TABS.UPLOAD_PERF_DATA_FILE": "Upload Performance Data", "NOMINATIONS.TABS.UPLOAD_BOD_FILE": "Upload BOD Price&Volumes", "NOMINATIONS.TABS.UPLOAD_MARGIN_DATA_FILE": "Upload Margin Data", "NOMINATIONS.TABS.UPLOAD_DECLARATION_OF_UNAVAILABILITY": "Declare Unavailable NG", "NOMINATIONS.LIST.TITLE": "Nominations Overview", "NOMINATIONS.LIST.FILTER.START_DATE": "Interval", "NOMINATIONS.LIST.FILTER.ENERGY_DIRECTION": "Energy Direction", "NOMINATIONS.LIST.FILTER.SELECT_ENERGY_DIRECTION": "Select Direction", "NOMINATIONS.LIST.FILTER.DISPATCH_GROUP": "Dispatch Group", "NOMINATIONS.LIST.FILTER.SHOW_DELETED": "Show Deleted", "NOMINATIONS.LIST.TABLE.DISPATCH_GROUP": "Dispatch Group", "NOMINATIONS.LIST.TABLE.START": "Start", "NOMINATIONS.LIST.TABLE.END": "End", "NOMINATIONS.LIST.TABLE.ENERGY_DIRECTION": "Energy Direction", "NOMINATIONS.LIST.TABLE.FLEX_CAPACITY": "Flex Capacity", "NOMINATIONS.LIST.TABLE.USE_FOR_ASSET_PRICE": "Use for asset price", "NOMINATIONS.LIST.TABLE.ENERGY_PRICE": "Energy Price", "NOMINATIONS.LIST.TABLE.CAPACITY_PRICE": "Capacity Price", "NOMINATIONS.LIST.TABLE.DELETED": "DELETED", "NOMINATIONS.LIST.TABLE.negative": "negative", "NOMINATIONS.LIST.TABLE.positive": "positive", "NOMINATIONS.LIST.TABLE.PER_PAGE": "per page", "NOMINATIONS.ENTER_NOMINATIONS.TITLE": "Enter Nominations", "NOMINATIONS.ENTER_NOMINATIONS.VIEW_DETAILS": "View Details", "NOMINATIONS.ENTER_NOMINATIONS.DISPATCH_GROUP": "Dispatch Group", "NOMINATIONS.ENTER_NOMINATIONS.INTERVAL": "Interval", "NOMINATIONS.ENTER_NOMINATIONS.ENERGY_DIRECTION": "Energy Direction", "NOMINATIONS.ENTER_NOMINATIONS.FLEX_CAPACITY": "Flex Capacity", "NOMINATIONS.ENTER_NOMINATIONS.ENERGY_PRICE": "Energy Price", "NOMINATIONS.ENTER_NOMINATIONS.CAPACITY_PRICE": "Capacity Price", "NOMINATIONS.ENTER_NOMINATIONS.ASSETS": "Assets", "NOMINATIONS.ENTER_NOMINATIONS.ALLOW_MODIFICATIONS_WITH_IMMEDIATE_EFFECT": "Allow modifications with immediate effect", "NOMINATIONS.ENTER_NOMINATIONS.USE_FOR_ASSET_PRICE": "Use for asset price", "NOMINATIONS.ENTER_NOMINATIONS.CREATE_NOMINATION": "Create Nomination", "NOMINATIONS.ENTER_NOMINATIONS.SAVE_NOMINATION": "Save Nomination", "NOMINATIONS.UPLOAD_MARKET_RESULTS.TITLE": "Select the auction result file:", "NOMINATIONS.UPLOAD_MARKET_RESULTS.AUCTION": "<PERSON>der", "NOMINATIONS.UPLOAD_MARKET_RESULTS.VIEW_DETAILS": "View Details", "NOMINATIONS.UPLOAD_MARKET_RESULTS.UPLOAD": "Upload", "NOMINATIONS.UPLOAD_MARKET_RESULTS.UPLOAD_INFO": "Please wait. The upload might take a while.", "NOMINATIONS.UPLOAD_MARKET_RESULTS.SUCCESS_UPLOADED": "Successfully uploaded the results.", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.TITLE": "Select the file containing the nomination", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.SHOW": "Show File Format", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.HIDE": "Hide File Format", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_FORMAT_DETAIL": "The csv file should have the following format:", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_FORMAT": "Time From [yyyy-mm-ddThh:mm:ss+xx:xx], Time To [yyyy-mm-ddThh:mm:ss+xx:xx], DG Id, Energy Direction, Flex Volume (MW), Energy Price (¤/MWh), Capacity Price (¤/MW), Default for Energy Price, Asset Ids, Auction Id", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_LIST_TITLE": "Only the order of the columns matters, not their names.", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_1": "The \"Default for Energy Price\" column has to be set to 1 if this nomination should be the default for energy price calculations otherwise 0.", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_2": "The \"Assets\" column should be inserted as a semicolon delimited list of ids (e.g. 10;11;12) if any.", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_3": "The \"Id\" column should only be used, when you want to change already existing entries. Otherwise leave it blank.", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_4": "The \"Auction Id\" column should only be used when you want to specify the auction Id. Otherwise leave it blank.", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.ALLOW_MODIFICATIONS_WITH_IMMEDIATE_EFFECT": "Allow modifications with immediate effect", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.UPLOAD_NOMINATIONS_FILE": "Upload Nomination File", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.TITLE": "Select the file containing the Opti-results", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.SHOW": "Show File Format", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.HIDE": "Hide File Format", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_FORMAT_DETAIL": "The Excel file should have the following format:", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_FORMAT": "datetime | soc | activation_dc_pos | activation_dc_neg | activation_dm_pos | activation_dm_neg | activation_dr_pos | activation_dr_neg | available_char_power | available_dischar_power", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_1": "\"datetime\" the timestamp [yyyy-mm-ddThh:mm:ss+xx:xx] for settlement period", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_2": "\"soc\" the target state of charge e.g. 0.6", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_3": "\"activation_dc_pos\" the activation range for DC positive e.g. 10000 ", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_4": "\"activation_dc_neg\" the activation range for DC negative e.g. 0", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_5": "\"activation_dm_pos\" the activation range for DM positive e.g. 10000 ", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_6": "\"activation_dm_neg\" the activation range for DM negative e.g. 0", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_7": "\"activation_dr_pos\" the activation range for DR positive e.g. 10000 ", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_8": "\"activation_dr_neg\" the activation range for DR negative e.g. 0", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_9": "\"available_charge_power\" the available charge power e.g. 10000", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_10": "\"available_discharge_power\" the available discharge power e.g. 0", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.ASSET": "<PERSON><PERSON>", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.UPLOAD_OPTI_RESULTS_FILE": "Upload Opti-results File", "NOMINATIONS.DOWNLOAD_OPTI_RESULTS.TITLE": "Select the asset and the day to download the Opti-results", "NOMINATIONS.DOWNLOAD_OPTI_RESULTS.DOWNLOAD": "Download Opti-results", "NOMINATIONS.DOWNLOAD_OPTI_RESULTS_FILE.DATE": "Day", "NOMINATIONS.DOWNLOAD_OPTI_RESULTS.FILE_WITH_GAPS": "Missing values in the document during the day. Before re-uploading you need to fill the gaps.", "NOMINATIONS.DOWNLOAD_OPTI_RESULTS.NO_FILE": "No file available today for the selected asset and date", "NOMINATIONS.UPLOAD_PERF_DATA_FILE.TITLE": "Select the file containing the performance data", "NOMINATIONS.UPLOAD_PERF_DATA_FILE.SHOW": "Show File Format", "NOMINATIONS.UPLOAD_PERF_DATA_FILE.HIDE": "Hide File Format", "NOMINATIONS.UPLOAD_PERF_DATA_FILE.FILE_FORMAT_DETAIL": "Files at the moment could be *.csv, *.txt and *.gz. The filename must match the expected format related to the asset:", "NOMINATIONS.UPLOAD_PERF_DATA_FILE.FILE_FORMAT_DETAIL_1": "For asset EONDC-01: perfdata_identifier_yyyymmddThh0000Z_yyyymmddThh0000Z_yyyymmddThhmmss.csv.gz or perfdata_identifier_yyyymmddThh0000Z_yyyymmddThh0000Z_yyyymmddThhmmss.csv", "NOMINATIONS.UPLOAD_PERF_DATA_FILE.FILE_FORMAT_DETAIL_2": "For asset EONDC-02: EONDC-02_yyyymmddhh0000_perfmonv1.csv", "NOMINATIONS.UPLOAD_PERF_DATA_FILE.FILE_FORMAT_DETAIL_3": "For asset EONDC-03: EONDC-03_yyyy-mm-dd_hh.00.00.txt", "NOMINATIONS.UPLOAD_PERF_DATA_FILE.ASSET": "<PERSON><PERSON>", "NOMINATIONS.UPLOAD_PERF_DATA_FILE.UPLOAD_FILE": "Upload Performance Data File", "NOMINATIONS.UPLOAD_BOD_FILE.TITLE": "Select the file containing the BOD prices and volumes", "NOMINATIONS.UPLOAD_BOD_FILE.SHOW": "Show File Format", "NOMINATIONS.UPLOAD_BOD_FILE.HIDE": "Hide File Format", "NOMINATIONS.UPLOAD_BOD_FILE.CSV_FORMAT_DETAIL": "The Excel file should have the following format:", "NOMINATIONS.UPLOAD_BOD_FILE.CSV_FORMAT": "Assetid | Datetime | Pair | Offer_Price | Bid_Price | Volume", "NOMINATIONS.UPLOAD_BOD_FILE.CSV_1": "\"Assetid\": asset id (e.g. external id like EONDC-01)", "NOMINATIONS.UPLOAD_BOD_FILE.CSV_2": "\"Datetime\": timestamp for the start of the settlement period", "NOMINATIONS.UPLOAD_BOD_FILE.CSV_3": "\"Pair\": identifier for the \"pair\" (+ or - integer number)", "NOMINATIONS.UPLOAD_BOD_FILE.CSV_4": "\"Offer_Price\": offer_price in GBP/MW (value with 2 decimal places)", "NOMINATIONS.UPLOAD_BOD_FILE.CSV_5": "\"Bid_Price\": bid_price in GBP/MW (value with 2 decimal places)", "NOMINATIONS.UPLOAD_BOD_FILE.CSV_6": "\"Volume\": volume in MW (integer value). Should have same sign as the \"pair\" (+ or -)", "NOMINATIONS.UPLOAD_BOD_FILE.UPLOAD_BOD_FILE": "Upload BOD File", "NOMINATIONS.UPLOAD_MARGIN_DATA_FILE.TITLE": "Select the file containing the margin data", "NOMINATIONS.UPLOAD_MARGIN_DATA_FILE.SHOW": "Show File Format", "NOMINATIONS.UPLOAD_MARGIN_DATA_FILE.HIDE": "Hide File Format", "NOMINATIONS.UPLOAD_MARGIN_DATA_FILE.CSV_FORMAT_DETAIL": "The CSV file should have the following format:", "NOMINATIONS.UPLOAD_MARGIN_DATA_FILE.CSV_FORMAT": "<PERSON>,Total Offer Mar<PERSON>,Total Bid Margin", "NOMINATIONS.UPLOAD_MARGIN_DATA_FILE.CSV_1": "\"SP\": the settlement number (1 to 50)", "NOMINATIONS.UPLOAD_MARGIN_DATA_FILE.CSV_2": "\"Total Offer Margin\": numeric value", "NOMINATIONS.UPLOAD_MARGIN_DATA_FILE.CSV_3": "\"Total Bid Margin\": numeric value", "NOMINATIONS.UPLOAD_MARGIN_DATA_FILE.UPLOAD_MARGIN_DATA_FILE": "Upload margin data file", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.TITLE": "Select the file containing the availability/unavailability declaration towards National Grid", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.SHOW": "Show File Format", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.HIDE": "Hide File Format", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.ASSET": "<PERSON><PERSON>", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT_DETAIL": "CSV files shall be named in the format UID_Timestamp_redecv1.csv. UID is the unique identifier assigned to the Response Unit. Timestamp is the file submission datetime (UTC) and is in the format YYYYMMDDHHMMSS where:", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT_DETAIL_1": "YYYY is the 4-digit year", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT_DETAIL_2": "MM is the month of year zero padded to 2 characters (00-12", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT_DETAIL_3": "DD is the day of month zero padded to 2 characters (01-31", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT_DETAIL_4": "HH is the hour of day in 24-hour format zero padded to 2 characters (00-23)", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT_DETAIL_5": "MM is the minutes past hour zero padded to 2 characters (00-59)", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT_DETAIL_6": "SS is the seconds past minute zero padded to 2 characters (00-59)", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT_DETAIL_7": "An example filename for UID 'ABCDE' and timestamp '15/09/2020 17:20:00' is: ABCDE_20200915172000_redecv1.csv", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT_DETAIL_8": "Each CSV file can contain data for multiple re-declarations. Each line would represent one line of re-declaration per response unit and service. Each CSV file may contain a maximum of 100 lines of data. If more than 100 rows of data are required to be submitted then the re-declaration file needs to be broken into multiple files.", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT_DETAIL_9": "The first line of the CSV file shall contain the header line. Notification of Availability/Outage CSV File Format | January 2021 The headers are listed below. All headers must be in lower case and must match the exact naming and order specified. All headers must be included even if some are not applicable to the unit.", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT": "unit,t_start,t_end,available_capacity,service", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_1": "\"unit\": Unique identifier assigned to the Response Unit. e.g. ABCDE)", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_2": "\"t_start\": ISO 8601 timestamp in UTC including milliseconds for when the redeclaration starts. e.g. 2021-12-04T16:00:00.000Z)", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_3": "\"t_end\": ISO 8601 timestamp in UTC including milliseconds for when the redeclaration end. e.g. 2021-12-04T17:30:00.000Z)", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_4": "\"available_capacity\": Actual capacity in MW to 2 decimal places. e.g. 27.13)", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_5": "\"service\": One of the DCL, DCH, DML, DMH, DRL or DRH service types for the availability declared. e.g. ADCH)", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.UPLOAD_FILE": "Upload declaration file", "REPORTS.TSO_REPORT.TITLE": "Upload TSO Report", "REPORTS.TSO_REPORT.TSO": "TSO", "REPORTS.TSO_REPORT.MARKET": "Market", "REPORTS.TSO_REPORT.FILE": "File", "REPORTS.TSO_REPORT.UPLOAD_TSO_REPORT": "Upload TSO Report", "REPORTS.TSO_REPORT.TABLE.TITLE": "TSO Reports Overview", "REPORTS.TSO_REPORT.TABLE.FILTER.DATE": "Date", "REPORTS.TSO_REPORT.TABLE.REPORT_DATE": "Report Date", "REPORTS.TSO_REPORT.TABLE.TSO": "TSO", "REPORTS.TSO_REPORT.TABLE.MARKET": "Market", "REPORTS.TSO_REPORT.TABLE.CREATED": "Created", "REPORTS.TSO_REPORT.TABLE.CONTROL_REPORT": "Control Report", "REPORTS.TSO_REPORT.ERRORS.CONTENTS": "Content", "ALLOCATIONS.TITLE": "Allocations", "ALLOCATIONS.TABS.ENTER_ALLOCATION": "Enter Allocations", "ALLOCATIONS.TABS.UPLOAD_ALLOCATION_FILE": "Upload Allocation File", "ALLOCATIONS.VISUALIZE_ALLOCATIONS.INTERVAL": "Interval", "ALLOCATIONS.VISUALIZE_ALLOCATIONS.DISPATCH_GROUP": "Dispatch Group", "ALLOCATIONS.VISUALIZE_ALLOCATIONS.SHOW": "Show", "ALLOCATIONS.VISUALIZE_ALLOCATIONS.DOWNLOAD": "Download", "ALLOCATIONS.VISUALIZE_ALLOCATIONS.ASSETS": "Assets", "ALLOCATIONS.VISUALIZE_ALLOCATIONS.ALLOCATED_FLEX": "Allocated Flex", "ALLOCATIONS.VISUALIZE_ALLOCATIONS.NEGATIVE_NOMINATED_FLEX": "Negative Nominated Flex", "ALLOCATIONS.VISUALIZE_ALLOCATIONS.POSITIVE_NOMINATED_FLEX": "Positive Nominated Flex", "ALLOCATIONS.VISUALIZE_ALLOCATIONS.NOMINATION": "Nomination", "ALLOCATIONS.VISUALIZE_ALLOCATIONS.VALUE_AXIS": "Values", "ALLOCATIONS.ENTER_ALLOCATION.TITLE": "Enter Allocation", "ALLOCATIONS.ENTER_ALLOCATION.VIEW_DETAILS": "View Details", "ALLOCATIONS.ENTER_ALLOCATION.INTERVAL": "Interval", "ALLOCATIONS.ENTER_ALLOCATION.ASSET": "<PERSON><PERSON>", "ALLOCATIONS.ENTER_ALLOCATION.DISPATCH_GROUP": "Dispatch Group", "ALLOCATIONS.ENTER_ALLOCATION.ALLOW_MODIFICATIONS_WITH_IMMEDIATE_EFFECT": "Allow modifications with immediate effect", "ALLOCATIONS.ENTER_ALLOCATION.CREATE_ALLOCATION": "Create Allocation", "ALLOCATIONS.ENTER_ALLOCATION.DEALLOCATE": "Deallocate", "ALLOCATIONS.UPLOAD_ALLOCATION.TITLE": "Upload Allocation File", "ALLOCATIONS.UPLOAD_ALLOCATION.VIEW_DETAILS": "View Details", "ALLOCATIONS.UPLOAD_ALLOCATION.SHOW": "Show File Format", "ALLOCATIONS.UPLOAD_ALLOCATION.HIDE": "Hide File Format", "ALLOCATIONS.UPLOAD_ALLOCATION.CSV_FORMAT": "The csv file should have the following format:", "ALLOCATIONS.UPLOAD_ALLOCATION_FILE.CSV_FORMAT_DETAIL": "Time From [yyyy-mm-ddThh:mm:ss+xx:xx], Time To [yyyy-mm-ddThh:mm:ss+xx:xx], Asset ID, DG ID, Allocate", "ALLOCATIONS.UPLOAD_ALLOCATION_FILE.CSV_LIST_TITLE": "Only the order of the columns matters, not their names.", "ALLOCATIONS.UPLOAD_ALLOCATION_FILE.CSV_1": "The \"Allocate\" column has to be set to 1 to allocate the asset. If the value is set to 0, the asset is de-allocated.", "ALLOCATIONS.UPLOAD_ALLOCATION_FILE.EXAMPLE": "Example", "ALLOCATIONS.UPLOAD_ALLOCATION_FILE.ALLOW_MODIFICATIONS_WITH_IMMEDIATE_EFFECT": "Allow modifications with immediate effect", "ALLOCATIONS.UPLOAD_ALLOCATION_FILE.UPLOAD_ALLOCATION_FILE": "Upload Allocation File", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.TRIGGERS.TYPES.AllocationType": "A new allocation has been entered manually", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.TRIGGERS.TYPES.ComputationTailed_type": "Previous allocation run has failed", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.TRIGGERS.TYPES.NominationType": "A new nomination has been entered", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.TRIGGERS.TYPES.FlexType": "Not enough flex available compared to the nominated", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.TRIGGERS.TYPES.TimeType": "Quarterly Allocation", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.TRIGGERS.TYPES.AssetBackFromFaultType": "<PERSON><PERSON> comes back from fault", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.TRIGGERS_TITLE": "Triggers", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.DISPATCH_GROUPS_TITLE": "Dispatch Groups", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.START": "Start", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.END": "End", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.FALLBACK": "Fallback Start", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.IMPORT": "Import Result", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.NOT_FOUND": "Nothing found", "AUTOMATIC_REPORT_EMAILINGS.TITLE": "Automatic Report", "AUTOMATIC_REPORT_EMAILINGS.TABS.SCHEDULING_REPORT": "Scheduling Report", "AUTOMATIC_REPORT_EMAILINGS.TABS.ALLOCATION": "Allocation", "AUTOMATIC_REPORT_EMAILINGS.TABS.ROLLUPS": "Rollups", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.TITLE": "Automatic Report Emailings Scheduling Report", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.INSTRUCTIONS": "Please use semicolons or newlines for separating multiple email addresses.", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.EDG_REPORTS_RECIPIENTS": "EDG Reports Recipients", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.EDG_REPORTS_ACTIVE": "EDG Reports Active", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.UGC_REPORTS_RECIPIENTS": "UGC Reports Recipients", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.UGC_REPORTS_ACTIVE": "UGC Reports Active", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.TSO_SCHEDULING_UPLOAD_ALARM_RECIPIENTS": "TSO Scheduling Upload Alarm Recipients", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.TSO_SCHEDULING_UPLOAD_ALARM_ACTIVE": "TSO Scheduling Upload Alarm Active", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.THIRD_PARTY_REPORTS_RECIPIENTS": "Third Party Reports Recipients", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.THIRD_PARTY_REPORTS_RECIPIENTS_ACTIVE": "Third Party Reports Active", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.ONLY_IN_CASE_OF_ACTIVATION": "Only in case of activation", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.SEND_ALL_EMAILS_TO": "Send all emails to", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.UPDATE": "Update", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.UPDATED": "Update succesful", "AUTOMATIC_REPORT_EMAILINGS.ALLOCATIONS.TITLE": "Automatic Report Emailings Allocation", "AUTOMATIC_REPORT_EMAILINGS.ALLOCATIONS.AUTOMATIC_ALLOCATIONS_NOTIFICATIONS_ACTIVE": "Automatic allocation notifications active", "AUTOMATIC_REPORT_EMAILINGS.ALLOCATIONS.AUTOMATIC_ALLOCATIONS_NOTIFICATIONS_RECIPIENTS": "Automatic allocation notifications email recipients", "AUTOMATIC_REPORT_EMAILINGS.ALLOCATIONS.INSTRUCTIONS": "Please use semicolons or newlines for separating multiple email addresses.", "AUTOMATIC_REPORT_EMAILINGS.ALLOCATIONS.UPDATE": "Update", "AUTOMATIC_REPORT_EMAILINGS.ROLLUPS.TITLE": "Automatic Report Emailings Rollup", "AUTOMATIC_REPORT_EMAILINGS.ROLLUPS.RECIPIENTS": "Recipients", "AUTOMATIC_REPORT_EMAILINGS.ROLLUPS.ROLLUP_NOTIFICATIONS_ACTIVE": "Rollup Notifications Active", "AUTOMATIC_REPORT_EMAILINGS.ROLLUPS.INSTRUCTIONS": "Please use semicolons or newlines for separating multiple email addresses.", "AUTOMATIC_REPORT_EMAILINGS.ROLLUPS.UPDATE": "Update", "HISTORY.TITLE": "History – Logs", "HISTORY.TABS.NOMINATIONS": "Nominations", "HISTORY.TABS.MANUAL_ALLOCATIONS": "Manual Allocations", "HISTORY.TABS.AUTOMATIC_ALLOCATIONS": "Automatic Allocations", "HISTORY.TABS.AUCTIONS": "Auctions", "HISTORY.TABS.REPORTING_EMAILS": "Reporting Emails", "HISTORY.TABS.DLM_CHARGING_SESSIONS": "DLM Charging Sessions", "HISTORY.NOMINATIONS.TABLE.PERFORMED_ON": "Performed On", "HISTORY.NOMINATIONS.TABLE.USER": "User", "HISTORY.NOMINATIONS.TABLE.CONTENT": "Content", "HISTORY.NOMINATIONS.TABLE.SUCCESFULLY_IMPORTED": "Successfully imported", "HISTORY.NOMINATIONS.TABLE.ERRORS": "Errors", "HISTORY.NOMINATIONS.TABLE.WARNINGS": "Warnings", "HISTORY.NOMINATIONS.TABLE.DISTRIBUTED_UNITS": "Distributed units", "HISTORY.NOMINATIONS.TABLE.UI_UPDATE": "Form", "HISTORY.NOMINATIONS.TABLE.FILE_IMPORT": "File", "HISTORY.NOMINATIONS.DETAIL.TITLE": "History - Usage - Nominations", "HISTORY.NOMINATIONS.DETAIL.TABLE.ERROR_DESCRIPTION": "Error Des<PERSON>", "HISTORY.NOMINATIONS.DETAIL.TABLE.WARNING_DESCRIPTION": "Warning Description", "HISTORY.NOMINATIONS.DETAIL.TABLE.DG": "DG", "HISTORY.NOMINATIONS.DETAIL.TABLE.DETAILS": "Details", "HISTORY.NOMINATIONS.DETAIL.TABLE.DISTRIBUTED_UNITS": "Nominations", "HISTORY.NOMINATIONS.DETAIL.TABLE.DISPATCH_GROUP": "Dispatchg Group", "HISTORY.NOMINATIONS.DETAIL.TABLE.START": "Start", "HISTORY.NOMINATIONS.DETAIL.TABLE.END": "End", "HISTORY.NOMINATIONS.DETAIL.TABLE.ENERGY_DIRECTION": "Energy Direction", "HISTORY.NOMINATIONS.DETAIL.TABLE.FLEX_CAPACITY": "Flex Capacity MW", "HISTORY.NOMINATIONS.DETAIL.TABLE.USED_FOR_ASSET_PRICE": "Use for Asset Price", "HISTORY.NOMINATIONS.DETAIL.TABLE.ENERGY_PRICE": "Energy Price (¤ / MWh)", "HISTORY.NOMINATIONS.DETAIL.TABLE.CAPACITY_PRICE": "Capacity Price (¤ / MW)", "HISTORY.NOMINATIONS.DETAIL.VALIDATION": "Validation", "HISTORY.NOMINATIONS.DETAIL.ERRORS": "Errors", "HISTORY.NOMINATIONS.DETAIL.WARNINGS": "Warnings", "HISTORY.NOMINATIONS.DETAIL.CONTENT_TYPE.NA": "N/A", "HISTORY.NOMINATIONS.DETAIL.CONTENT_TYPE.ui_update": "UI", "HISTORY.NOMINATIONS.DETAIL.CONTENT_TYPE.file_import": "File / API", "HISTORY.NOMINATIONS.DETAIL.CONTENT_TYPE.automatic_bids_conversion": "V2G", "HISTORY.NOMINATIONS.DETAIL.CONTENT_TYPE.vpp_migration": "One time", "HISTORY.NOMINATIONS.DETAIL.TYPE": "Type", "HISTORY.NOMINATIONS.DETAIL.PERFORMED_ON": "Performed On", "HISTORY.NOMINATIONS.DETAIL.USER": "User", "HISTORY.NOMINATIONS.DETAIL.FILE_IMPORT": "Nominations File", "HISTORY.NOMINATIONS.DETAIL.UI_IMPORT": "Form", "HISTORY.MANUAL_ALLOCATIONS.TABLE.PERFORMED_ON": "Performed On", "HISTORY.MANUAL_ALLOCATIONS.TABLE.USER": "User", "HISTORY.MANUAL_ALLOCATIONS.TABLE.CONTENT": "Content", "HISTORY.MANUAL_ALLOCATIONS.TABLE.SUCCESFULLY_IMPORTED": "Succesfully imported", "HISTORY.MANUAL_ALLOCATIONS.TABLE.ERRORS": "Errors", "HISTORY.MANUAL_ALLOCATIONS.TABLE.WARNINGS": "Warnings", "HISTORY.MANUAL_ALLOCATIONS.TABLE.UI_UPDATE": "Form", "HISTORY.MANUAL_ALLOCATIONS.TABLE.FILE_IMPORT": "File / API", "HISTORY.MANUAL_ALLOCATIONS.CONTENT_TYPE.NA": "N/A", "HISTORY.MANUAL_ALLOCATIONS.CONTENT_TYPE.ui_update": "UI", "HISTORY.MANUAL_ALLOCATIONS.CONTENT_TYPE.file_import": "File", "HISTORY.MANUAL_ALLOCATIONS.CONTENT_TYPE.automatic_bids_conversion": "V2G", "HISTORY.MANUAL_ALLOCATIONS.CONTENT_TYPE.vpp_migration": "One time", "HISTORY.MANUAL_ALLOCATIONS.CONTENT_TYPE.aas_update": "AAS", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.TITLE": "History - Usage - Manual Allocation", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.VALIDATION": "Validation", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.ERRORS": "Errors", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.WARNINGS": "Warnings", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.TYPE": "Type", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.ALLOCATIONS_FILE_IMPORT": "Allocations File", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.ALLOCATIONS_UI_IMPORT": "Form", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.PERFORMED_ON": "Performed On", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.PERFORMED_ON_FORMAT": "ddd, DD MMM YYYY HH:mm", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.USER": "User", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.ERROR_DESCRIPTION": "Error Des<PERSON>", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.ASSET": "<PERSON><PERSON>", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.DG": "DG", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.WARNING_DESCRIPTION": "Warning Description", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.ALLOCATIONS": "Allocations", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.START": "Start", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.END": "End", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.DISPATCH_GROUP": "Dispatch Group", "HISTORY.NOMINATIONS.FILE_IMPORT": "Nomination File", "HISTORY.MANUAL_ALLOCATIONS.UI_UPDATE": "Form", "HISTORY.MANUAL_ALLOCATIONS.FILE_IMPORT": "Allocation File", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.START": "Interval", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.TYPE": "Type", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.TYPE.CHOOSE": "<PERSON><PERSON>", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.TYPE.FALLBACK": "Fallback", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.TYPE.STANDARD": "Standard", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.STATUS": "Status", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.STATUS.CHOOSE": "<PERSON><PERSON>", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.STATUS.STARTED": "Started", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.STATUS.SUCCESS": "Success", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.STATUS.FALLBACK": "Fallback", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.STATUS.DATAPREPARATIONFAILURE": "Data Preparation Failure", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.STATUS.FAILURE": "Failure", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.STATUS.ALLOCATIONNOTACCEPTED": "Allocation Not Accepted", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.DISPATCH_GROUP": "Dispatch Group", "HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.START": "Start", "HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.END": "End", "HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.FALLBACK_START": "Fallback Start", "HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.STATUS": "Status", "HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.DISPATCH_GROUP": "Dispatch Group", "HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.TSO": "TSO", "HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.IMPORT_RESULT": "Import Result", "HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.IMPORT_RESULT_SUCCESS": "Success", "HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.PER_PAGE": "per page", "HISTORY.AUCTIONS.TABLE.PERFORMED_ON": "Performed On", "HISTORY.AUCTIONS.TABLE.USER": "User", "HISTORY.AUCTIONS.TABLE.DELIVERY_DATE": "Delivery Date", "HISTORY.AUCTIONS.TABLE.LINK.META_DATA": "Meta Data", "HISTORY.AUCTIONS.TABLE.LINK.AUCTION": "Auction", "HISTORY.AUCTIONS.TABLE.PER_PAGE": "per page", "HISTORY.REPORTING_EMAILS.FILTERS.REPORT_TYPE": "Report Type", "HISTORY.REPORTING_EMAILS.FILTERS.TIME": "Time and Date", "HISTORY.REPORTING_EMAILS.FILTERS.STATUS": "Status", "HISTORY.REPORTING_EMAILS.FILTERS.RECIPIENTS": "Recipients", "HISTORY.REPORTING_EMAILS.FILTERS.ACTIVE": "Active", "HISTORY.REPORTING_EMAILS.TABLE.TIME": "Time and Date", "HISTORY.REPORTING_EMAILS.TABLE.REPORT_DATE": "Report Date", "HISTORY.REPORTING_EMAILS.TABLE.CREATED": "Generated On", "HISTORY.REPORTING_EMAILS.TABLE.STATUS": "Status", "HISTORY.REPORTING_EMAILS.TABLE.REPORT_TYPE": "Report Type", "HISTORY.REPORTING_EMAILS.TABLE.PARAMS": "Parameters", "HISTORY.REPORTING_EMAILS.TABLE.RECIPIENTS": "Recipients", "HISTORY.REPORTING_EMAILS.TABLE.ACTIVE": "Active", "HISTORY.REPORTING_EMAILS.TABLE.DOWNLOAD": "Download", "HISTORY.REPORTING_EMAILS.TABLE.PER_PAGE": "per page", "HISTORY.REPORTING_EMAILS.DETAIL.TITLE": "History - Usage - Reporting Email", "HISTORY.ROLLUPS.TITLE": "History – Rollups", "HISTORY.ROLLUPS.TABS.ASSET_ROLLUPS": "Asset Rollups", "HISTORY.ROLLUPS.TABS.DISPATCH_GROUP_ROLLUPS": "Dispatch Group Rollups", "HISTORY.ROLLUPS.TABS.ROLLUP_ERRORS": "Rollup E<PERSON>rs", "HISTORY.ROLLUPS.ERRORS.FILTER.TIME_DATE": "Interval", "HISTORY.ROLLUPS.ERRORS.TABLE.TIME_DATE": "Time And Date", "HISTORY.ROLLUPS.ERRORS.TABLE.DESCRIPTION": "Description", "HISTORY.ROLLUPS.ERRORS.TABLE.PER_PAGE": "per page", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.INTERVAL": "Interval", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.STATUS": "Status", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.STATUS.": "", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.STATUS.PROCESSING": "Processing", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.STATUS.COMPLETED": "Completed", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.STATUS.SCHEDULED": "Scheduled", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.ASSET_ROLLUP_GROUPINGS": "Assets / Rollup Groupings", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.DISPATCH_GROUPS": "Dispatch Groups", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.ROLLUP_METRICS": "Rollup Metrics", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.TRIGGERED_BY": "Triggered By", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.INTERVAL": "Interval", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.STATUS": "Status", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.ASSET_ROLLUP_GROUPINGS": "Assets / Rollup Groupings", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.DISPATCH_GROUPS": "Dispatch Groups", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.ROLLUP_METRICS": "Rollup Metrics", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.TRIGGERED_BY": "Triggered By", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.PER_PAGE": "per page", "HISTORY.ROLLUPS.ROLLUP_ERROR_DETAIL.TITLE": "Rollup Error <PERSON>", "HISTORY.ROLLUPS.ROLLUP_ERROR_DETAIL.ON": "On", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD": "Upload DLM Charging Session File", "HISTORY.DLM_CHARGING_SESSIONS.TITLE": "DLM Charging Sessions", "HISTORY.DLM_CHARGING_SESSIONS.TABLE.TH.CUSTOMER": "Customer", "HISTORY.DLM_CHARGING_SESSIONS.TABLE.TH.CREATED_AT": "Created At", "HISTORY.DLM_CHARGING_SESSIONS.TABLE.TH.AVG_CHARGED_ENERGY": "Average Charged Energy (Wh)", "HISTORY.DLM_CHARGING_SESSIONS.TABLE.TH.AVG_CHARGED_DURATION": "Average Charged Duration (minutes)", "HISTORY.DLM_CHARGING_SESSIONS.TABLE.PER_PAGE": "per page", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.TITLE": "Select the file containing the DLM customer charging sessions", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.SHOW": "Show File Format", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.HIDE": "Hide File Format", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.CSV_FORMAT_DETAIL": "The csv file should have the following format:", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.CSV_FORMAT": "Customer ID; Average charging duration in minutes; Average charged energy in Wh\n<br>x;x;x", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.CSV_LIST_TITLE": "Only the order of the columns matters, not their names.", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.CSV_1": "The \"Customer ID\" column corresponds to the Virta Electric Vehicle Contract ID of the customer (evcoId), e.g.: FI*VIR*000000*X", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.CSV_2": "The \"Average charging duration in minutes\" must have a numeric value.", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.CSV_3": "The \"Average charged energy in Wh\" must have a numeric value.", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.UPLOAD_FILE": "Upload DLM Charging Sessions", "HISTORY.LOGS.SUBMIT": "Submit data to EE Trading Optimizer", "HISTORY.LOGS.SELECT_LABEL": "Show only:", "HISTORY.LOGS.SELECT_TRADING": "Trading", "HISTORY.LOGS.SELECT_STEERING": "Steering", "HISTORY.LOGS.TH.ID": "id", "HISTORY.LOGS.TH.TYPE": "Type", "HISTORY.LOGS.TH.CREATED": "Created At", "HISTORY.LOGS.TH.UPDATED": "Updated At", "HISTORY.LOGS.TH.START": "Start Time", "HISTORY.LOGS.TH.END": "End Time", "HISTORY.LOGS.TH.RETRIES": "Retries Count", "HISTORY.LOGS.TH.FAILED": "Failed At", "HISTORY.LOGS.TH.SENT": "<PERSON><PERSON>", "HISTORY.LOGS.TH.RESPONDED": "Response Received At", "IMPORTED_WITH_ERRORS": "File imported with errors [EN-GB]", "ERROR_INVALID_TIME_INTERVAL": "Invalid time interval. [EN-GB]", "FAILURE": "Failure", "SUCCESS": "Success", "ErrorAuctionClosed": "The auction isn't open for this delivery date and market.", "ErrorAuctionMrlClosedButSrlOpen": "The auction isn't open for this delivery date and market.", "ErrorAuctionSrlClosedButMrlOpen": "The auction isn't open for this delivery date and market.", "ErrorTSONotFound": "At least one TSO zone isn’t recognized.", "ErrorInvalidEnergyDirection": "At least one energy direction isn’t recognized.", "ErrorInvalidTSO": "At least one TSO zone isn’t recognized.", "ErrorInvalidMarket": "At least one market name isn’t recognized.", "ErrorInvalidProductName": "At least one product name isn’t recognized.", "ErrorInvalidPaymentDirection": "At least one payment direction isn’t recognized.", "ErrorFlexVolumeNotWholeValue": "At least one volume not rounded to MW.", "ErrorDGEnabledForBidsMissing": "No dispatch group in {{tsoName}} and {{marketName}} are configured in the system to accept bids. Please enable the trading functionality in the dispatch group configuration.", "ErrorBGNotFoundForBid": "No balancing group are defined at dispatch group level in {{tsoName}} and {{marketName}} for the marketed date. Please enter a balancing group in the dispatch group configuration.", "ErrorMinFlexBidRule": "The minimum size of a bid is 5 MW. A bid of less than 5 MW can be uploaded only if it is the unique bid for that day, product and market. Please check the bids in {{tsoName}}.", "ErrorInvalidFloatingEnergyPrice": "Only three numbers after the comma for the energy price are accepted.", "ErrorInvalidFloatingCapacityPrice": "Only two numbers after the comma for the capacity price are accepted.", "ErrorInvalidTimeInterval": "Invalid interval", "ErrorDGNotFound": "Dispatch Group not found", "ErrorOverlappingExistingDUWithPrice": "Overlapping existing Nomination with price", "ErrorOverlappingImportedDUWithPrice": "Overlapping existing imported Nomination with price", "ErrorOverlappingExistingDUAsset": "Overlapping existing Nomination for assets", "ErrorOverlappingImportedDUAsset": "Overlapping existing imported Nomination for assets", "ErrorPartialOverlappingExistingDU": "Partially overlapping existing Nomination", "ErrorPartialOverlappingImportedDU": "Partially overlapping existing imported Nomination", "ErrorImmediateOrPastEffect": "Lead time too short", "ErrorAuctionIdAlreadyExists": "Duplicate auction ID", "WarningZeroVolume": "Zero flex", "WarningNoMatchingBidForAuctionResult": "No corresponding bid found for some nominations", "WarningNoMatchingAuctionResultForBid": "WarningNoMatchingAuctionResultForBid", "ErrorAssetNotFound": "Asset not found", "ErrorAssetNotActive": "Asset inactive", "ErrorAssetIsNotInDG": "Asset not in Dispatch Group", "ErrorSameDGAllocationOverlap": "Overlapping existing Allocation in the same Dispatch Group", "ErrorSameMarketAllocationOverlap": "Overlapping existing Allocation in the same market", "ErrorAllocationIntervalNotCoveredByPQ": "Allocation interval not covered by prequalification", "ErrorAllocationIntervalNotCoveredByPrice": "Allocation interval not covered by price. Please also check that the asset has a valid contract for the DG's market.", "ErrorAllocationIntervalNotCoveredByBG": "Allocation interval not allowed by balancing group", "ErrorAllocationIntervalNotCoveredByInternalBG": "Allocation interval not allowed by internal balancing group", "ErrorTestAllocationReallocation": "ErrorTestAllocationReallocation", "ErrorWrongTSOAllocation": "TSO Allocation wrong", "ErrorTestAllocationReassignment": "ErrorTestAllocationReassignment", "ErrorImmediateEffect": "Lead time too short", "WarningObsoleteValidation": "Validation is obsolete", "WarningImminentExpiry": "Expiry imminent", "MISSING_START_DATE": "Start date missing", "MISSING_END_DATE": "End date missing", "INVALID_START_DATE": "Start date invalid", "INVALID_END_DATE": "End date invalid", "END_DATE_BEFORE_START_DATE": "End date before start date", "MISSING_DISPATCH_GROUP": "Dispatch Group missing", "MISSING_ASSET": "Asset missing", "MISSING_FLEX_VOLUME": "Missing Flex Volume", "MISSING_ENERGY_PRICE": "Missing Energy Price", "MISSING_ENERGY_DIRECTION": "Missing Direction", "EVENT_NOTIFICATIONS.TITLE": "Notifications", "EVENT_NOTIFICATIONS.EVENT_TYPE.DG_HEARTBEAT_MIRROR_FAILURE": "TSO line Heartbeat failure", "EVENT_NOTIFICATIONS.EVENT_TYPE.BIDDING_API_FAILURE": "Issues by bids upload on Regelleistung.net", "EVENT_NOTIFICATIONS.EVENT_TYPE.BIDDING_API_SUCCESS": "Successful bids upload to Regelleistung.net", "EVENT_NOTIFICATIONS.EVENT_TYPE.BID_DOWNLOAD_FAILURE": "Issues by bids download", "EVENT_NOTIFICATIONS.EVENT_TYPE.BID_DOWNLOAD_SUCCESS": "Successful download of the bids", "EVENT_NOTIFICATIONS.EVENT_TYPE.AUCTION_DOWNLOAD_FAILURE": "Issues by bids download", "EVENT_NOTIFICATIONS.EVENT_TYPE.AUCTION_DOWNLOAD_SUCCESS": "Successful download of the auction results", "EVENT_NOTIFICATIONS.EVENT_TYPE.AUCTION_DOWNLOAD_RESULTS_EXCEED_BIDS": "Auction results with higher volumes than bids", "EVENT_NOTIFICATIONS.EVENT_TYPE.DG_OVER_DELIVERY": "Pool overdelivery", "EVENT_NOTIFICATIONS.EVENT_TYPE.DG_AVAILABLE_FLEX_TOO_LOW": "VPP pool issues", "EVENT_NOTIFICATIONS.EVENT_TYPE.DG_MERLIN_ISSUE": "MeRLin issue", "EVENT_NOTIFICATIONS.EVENT_TYPE.V2G_OPTIMIZATION_SUBMISSION_FAILURE": "V2G-EE Optimisation Failures", "EVENT_NOTIFICATIONS.EVENT_TYPE.AUCTION_MISSING_BIDS": "Automatic creation of bids because existing bids don't match results from Regelleistung.net", "EVENT_NOTIFICATIONS.EVENT_TYPE.DLM_ASSET_FAULT": "DLM Asset in fault", "EVENT_NOTIFICATIONS.EVENT_TYPE.DLM_EXECUTION_FAILURE": "DLM generic failure", "EVENT_NOTIFICATIONS.EVENT_TYPE.VIRTA_DLM_API_FAILURE": "Virta DLM API failure", "EVENT_NOTIFICATIONS.EVENT_TYPE.VIRTA_ADMIN_API_FAILURE": "Virta Admin API failure", "EVENT_NOTIFICATIONS.EVENT_TYPE.ERROR_UPLOADING_TRADES_SPOT_OPTIMISATION": "Uploading Spot Optimization Trades Failed", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_DA_NO_SETPOINT": "NL aFRR - Day-After-Process - No Energy Delivered", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_DA_UPLOAD_FAILED": "NL aFRR - Day-After-Process - Activated Energy Document Upload Failure", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_DA_UPLOAD_SUCCESS": "NL aFRR - Day-After-Process - Activated Energy Document Upload Success", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_DA_MISSING_DATA": "NL aFRR - Day-After-Process - Asset Data Missing", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_DA_ACK_WITH_INFO": "NL aFRR - Day-After-Process - Activated Energy Document Acknowledgment contained Extra Information", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_DA_NO_ACK": "NL aFRR - Day-After-Process - Activated Energy Document no Acknowledgement", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_DA_NON_ACK": "NL aFRR - Day-After-Process - Activated Energy Document not acknowledged", "EVENT_NOTIFICATIONS.EVENT_TYPE.NLAFRR_BID_MESSAGE_NON_ACK": "NL aFRR - Bidding Process - Bid Message not acknowledged", "EVENT_NOTIFICATIONS.EVENT_TYPE.NLAFRR_BID_MESSAGE_NO_ACK": "NL aFRR - Bidding Process - Missing Acknowledgement for Bid Message", "EVENT_NOTIFICATIONS.EVENT_TYPE.NLAFRR_BID_MESSAGE_ACK_WITH_INFO": "NL aFRR - Bidding Process - Acknowledged Bid Message with extra Information", "EVENT_NOTIFICATIONS.EVENT_TYPE.NLAFRR_BID_MESSAGE_UPLOAD_FAILURE": "NL aFRR - Bidding Process - Bid Message Upload Failure", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_SIGNAL_UPLOAD_FAILURE": "NL aFRR - Delivery Process - CBP Live Data failure", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_SIGNAL_UPLOAD_FAILURE_OVER": "NL aFRR - Delivery Process - CBP Live Data failure over", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_NEGATIVE_POOL_CONFIGURATION": "NL aFRR - Pool Configuration - Negative Pool Confirmation Message Received", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_CONFIRMATION_MARKET_DOCUMENT_RECEIVED": "NL aFRR - Day-After-Process - Confirmation Market Document received", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_CONFIRMATION_MARKET_DOCUMENT_MISSING": "NL aFRR - Day-After-Process - Confirmation Market Document missing", "EVENT_NOTIFICATIONS.TOOLTIP.DG_HEARTBEAT_MIRROR_FAILURE": "A notification is sent if a communication issue on at least one TSO line between the VPP and the TSO is detected.", "EVENT_NOTIFICATIONS.TOOLTIP.BIDDING_API_FAILURE": "In case of an issue during the upload of the bids to the Regelleistung.net website, a message with the bids attached as an xml is sent.", "EVENT_NOTIFICATIONS.TOOLTIP.BIDDING_API_SUCCESS": "A message is sent to inform about the successful upload of bids to the market.", "EVENT_NOTIFICATIONS.TOOLTIP.BID_DOWNLOAD_FAILURE": "If no result is provided for all the bids stored in the system then a message is sent.", "EVENT_NOTIFICATIONS.TOOLTIP.BID_DOWNLOAD_SUCCESS": "If the auction results are downloaded successfully then a message, with the auction results attached, is sent to the recipients.", "EVENT_NOTIFICATIONS.TOOLTIP.AUCTION_DOWNLOAD_FAILURE": "If no result is provided for all the bids stored in the system then a message is sent.", "EVENT_NOTIFICATIONS.TOOLTIP.AUCTION_DOWNLOAD_SUCCESS": "If the auction results are downloaded successfully then a message, with the auction results attached, is sent to the recipients.", "EVENT_NOTIFICATIONS.TOOLTIP.AUCTION_DOWNLOAD_RESULTS_EXCEED_BIDS": "If auction results are uploaded with volumes higher than previously created bids OR auction results uploaded without creating bids before then a message is sent", "EVENT_NOTIFICATIONS.TOOLTIP.DG_OVER_DELIVERY": "A notification is sent to the recipients when a dispatch group overdelivery event is created. The overdelivery event is defined in the dispatch group configuration page.", "EVENT_NOTIFICATIONS.TOOLTIP.DG_AVAILABLE_FLEX_TOO_LOW": "Event triggered when the per-direction available flex of a DG is below the configured threshold of the nominated flex.", "EVENT_NOTIFICATIONS.TOOLTIP.DG_MERLIN_ISSUE": "In case of an issue with the Merlin Client a notification will be sent to the recipients.", "EVENT_NOTIFICATIONS.TOOLTIP.V2G_OPTIMIZATION_SUBMISSION_FAILURE": "A notification is sent to the recipients when the V2G optimisation process fails.", "EVENT_NOTIFICATIONS.TOOLTIP.AUCTION_MISSING_BIDS": "A notification is sent when bids are automatically created because no corresponding bids to the results from Regeleistung.net were found", "EVENT_NOTIFICATIONS.TOOLTIP.DLM_ASSET_FAULT": "A notification is generated when a DLM asset enters in fault while it is allocated to a DG.", "EVENT_NOTIFICATIONS.TOOLTIP.DLM_EXECUTION_FAILURE": "A notification is generated when a DLM related error occurs.", "EVENT_NOTIFICATIONS.TOOLTIP.VIRTA_DLM_API_FAILURE": "A notification is generated when communication with the Virta DLM API cannot be established or is corrupted.", "EVENT_NOTIFICATIONS.TOOLTIP.VIRTA_ADMIN_API_FAILURE": "A notification is generated when communication with the Virta Admin API cannot be established or is corrupted.", "EVENT_NOTIFICATIONS.TOOLTIP.ERROR_UPLOADING_TRADES_SPOT_OPTIMISATION": "An error occurred while uploading Spot Optimization trades back to IQCHP.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_DA_NO_SETPOINT": "A notification is sent if no Activated Energy Document was sent for the previous day because no energy was delivered.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_DA_UPLOAD_FAILED": "A notification is sent if the Activated Energy Document could not be sent to TenneT via CBP for the previous day.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_DA_UPLOAD_SUCCESS": "A notification is sent when the Activated Energy Document was sent to TenneT via CBP for the previous day.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_DA_MISSING_DATA": "A notification is sent if the Activated Energy Document is faulty because one or more asset rollups were not correctly computed for the previous day.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_DA_ACK_WITH_INFO": "A notification is sent if the acknowledgement to an Activated Energy Document is positive but contains additional information.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_DA_NON_ACK": "A notification is sent if no acknowledgement was received 30 Minutes after an Activated Energy Document has been sent.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_DA_NO_ACK": "A notification is sent if the acknowledgement to an Activated Energy Document is negative.", "EVENT_NOTIFICATIONS.TOOLTIP.NLAFRR_BID_MESSAGE_NON_ACK": "A notification is sent if a bid message was not acknowledged by the TSO.", "EVENT_NOTIFICATIONS.TOOLTIP.NLAFRR_BID_MESSAGE_NO_ACK": "A notification is sent if no acknowledgement to a bid message was received.", "EVENT_NOTIFICATIONS.TOOLTIP.NLAFRR_BID_MESSAGE_ACK_WITH_INFO": "A notification is sent if the acknowledgement to a bid message contains extra information.", "EVENT_NOTIFICATIONS.TOOLTIP.NLAFRR_BID_MESSAGE_UPLOAD_FAILURE": "A notification is sent if a bid message could not be sent.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_SIGNAL_UPLOAD_FAILURE": "A notification is sent if a communication issue is detected in the communication between the TSO in NL (CBP) and the VPP.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_SIGNAL_UPLOAD_FAILURE_OVER": "A notification is sent if a communication issue is detected in the communication between the TSO in NL (CBP) and the VPP.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_NEGATIVE_POOL_CONFIGURATION": "A notification is sent when Tennet doesn't approve the aFRR pool configuration.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_CONFIRMATION_MARKET_DOCUMENT_RECEIVED": "A notification is sent when the VPP receives a Confirmation Market Document.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_CONFIRMATION_MARKET_DOCUMENT_MISSING": "A notification is sent when the VPP didn't receive a Confirmation Market Document.", "EVENT_NOTIFICATIONS.CHANNEL.SMS": "SMS", "EVENT_NOTIFICATIONS.CHANNEL.PHONECALL": "Call", "EVENT_NOTIFICATIONS.CHANNEL.EMAIL": "E-Mail", "EVENT_NOTIFICATIONS.SUBMIT": "Update", "EVENT_NOTIFICATIONS_AUCTION_CONFIG.TITLE": "Tender Notifications", "EVENT_NOTIFICATIONS_DISPATCH_GROUP.TITLE": "Dispatch Group Notifications", "SEND_FILE_EXTERNALLY.TITLE": "Send Files Externally", "CONFIRM.TITLE.GENERIC": "Please Confirm", "CONFIRM.WARNING": "Warning Message", "CONFIRM.ACKNOWLEDGE": "Cancel", "CONFIRM.CONFIRM": "Confirm", "CONFIRM.DISMISS": "<PERSON><PERSON><PERSON>", "CONFIRM.OK": "OK", "CONFIRM.MESSAGE.DELETE_ENTRY": "Do you really want to delete this entry?", "CONFIRM.MESSAGE.VALIDATE_OPTIMIZATION": "Are you sure you want to validate the Asset Optimization?", "CONFIRM.MESSAGE.REJECT_OPTIMIZATION": "Are you sure you want to reject the Asset Optimization?", "PAGINATION.PER_PAGE": "per page", "CONFIGURATION.ASSET_LEVEL.TAB.BOX_TYPES": "Box Types", "CONFIGURATION.ASSET_LEVEL.TAB.GENERIC_STEERING_TYPES": "Generic Steering Types", "CONFIGURATION.ASSET_LEVEL.TAB.SIGNAL_LISTS": "Signal Lists", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.ENTER_BOX_TYPE": "Enter Box Type", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.CREATE_BOX_TYPE": "Save", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.SHOW_BOX_TYPE": "Box Type Configuration", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.SAVE_BOX_TYPE": "Save", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.CANCEL_EDIT": "Cancel Edit", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.EDIT": "Edit", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.DELETE": "Delete", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LIST.TITLE": "Box Types", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.BOX_TYPE": "Box Type / Steering Type", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.PROTOCOL": "Protocol", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.PORT": "Port", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.VPN_TYPE": "VPN Type", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.TYPE": "Type", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.EMAIL": "Contact Person Email", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.NAME": "Contact Person Name", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.PHONE": "Contact Person Phone", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.FIRST_NAME": "Contact Person First Name", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.LASE_NAME": "Contact Person Last Name", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.CREATE_STEERING_TYPE": "Save", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SAVE_STEERING_TYPE": "Save", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.CANCEL_EDIT": "Cancel Edit", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.EDIT": "Edit", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.DELETE": "Delete", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.LIST.TITLE": "Steering Types", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.NAME": "Steering Type", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.BASEPOINT": "Basepoint", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.SCHEDULE": "Schedule", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.FLEX": "Flex", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.SETPOINT": "Setpoint", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.DISPATCH_DEVIATED": "Dispatch Deviated", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.HEARTBEAT_VPP": "Heartbeat VPP Comm Alive", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.HEARTBEAT_ASSET": "Heartbeat Asset Comm Alive", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.ACTIONS": "", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.ENTER_STEERING_TYPE": "Enter Steering Type", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SHOW_STEERING_TYPE": "Steering Type", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.BASEPOINTS.HasBasepointAndPbp": "Preceding Basepoint from asset", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.BASEPOINTS.HasBasepoint": "Basepoint from asset", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.BASEPOINTS.NoBasepoint": "No Basepoint(-> Avg of Pist)", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SCHEDULES.Production": "Prod", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SCHEDULES.Flexibility": "Flex", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.FLEXES.Both": "Both", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.FLEXES.Positive": "Pos Only", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.FLEXES.Negative": "Neg Only", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_RELATIVITIES.Relative": "Relative", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_RELATIVITIES.Absolute": "Absolute", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_UNITS.Kilowatts": "kW", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_UNITS.Boolean": "Boolean", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_INTERVALS.Continuous": "Continuous", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_INTERVALS.OnOff": "ON/OFF", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_INTERVALS.Steps": "Steps (2 or more)", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.HEARTBEAT_AUTHORIZATIONS.Read": "Read Only", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.HEARTBEAT_AUTHORIZATIONS.Write": "Write Only", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.HEARTBEAT_AUTHORIZATIONS.ReadWrite": "R/W", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_LOCKS.true": "Setpoint activation bit", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_LOCKS.false": "No Setpoint activation bit", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.YES_NOS.true": "Yes", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.YES_NOS.false": "No", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.Y_NS.true": "Yes", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.Y_NS.false": "No", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.FEEDBACK": "<PERSON><PERSON><PERSON>", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.FREQUENCY_S": "Frequency (s)", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.DEFAULT_ABSOLUTE_SETPOINTS.ImposedPMax": "PMax", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.DEFAULT_ABSOLUTE_SETPOINTS.ImposedPMin": "PMin", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.DEFAULT_ABSOLUTE_SETPOINTS.PNorm": "PNorm", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.DEFAULT_ABSOLUTE_SETPOINTS.Default": "<PERSON><PERSON><PERSON>", "CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.ENTER_SIGNAL_LIST": "Enter Signal List", "CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.NAME": "Name", "CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.SIGNAL_DIRECTION_READ": "Read (Customer to VPP)", "CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.SIGNAL_DIRECTION_WRITE": "Write (VPP to Customer)", "CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.DIRECTION": "Direction", "CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.SAVE": "Save", "CONFIGURATION.MARKET_LEVEL.TAB.SUBPOOLS": "Subpools", "CONFIGURATION.MARKET_LEVEL.TAB.BALANCING_GROUPS": "Balancing Groups", "CONFIGURATION.MARKET_LEVEL.TAB.DISPATCH_GROUPS": "Dispatch Groups", "CONFIGURATION.MARKET_LEVEL.SUBPOOLS.ENTER_SUBPOOL": "Enter Subpool", "CONFIGURATION.MARKET_LEVEL.SUBPOOLS.LIST.TITLE": "Subpools", "CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.NAME": "Name", "CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.DISPATCH_GROUPS": "Dispatch Groups", "CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.SIGNALS": "Signals", "CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.IMPLICIT": "Implicit", "CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.ASSETS": "Assets", "CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.ACTIONS": "", "CONFIGURATION.MARKET_LEVEL.SUBPOOLS.IMPLICIT.NOTICE": "An implicit subpool will contain all assets that are not assigned to other subpools", "CONFIGURATION.MARKET_LEVEL.TAB.AUCTIONS": "Tenders", "CONFIGURATION.MARKET_LEVEL.AUCTION_CONFIGURATION.ADD": "C<PERSON> Tender", "CONFIGURATION.MARKET_LEVEL.AUCTION_CONFIGURATION.LIST": "Tenders", "CONFIGURATION.AUCTION_CONFIGURATION.SAVE": "Save", "CONFIGURATION.AUCTION_CONFIGURATION.CREATE": "Create", "CONFIGURATION.AUCTION_CONFIGURATION.EDIT": "Edit", "CONFIGURATION.AUCTION_CONFIGURATION.DELETE": "Delete", "CONFIGURATION.AUCTION_CONFIGURATION.CANCEL": "Cancel", "CONFIGURATION.AUCTION.LABEL.GENERIC": "Generic", "CONFIGURATION.AUCTION.NAME": "Name", "CONFIGURATION.AUCTION.AUCTION_VALIDITY_INTERVAL": "Validity Interval", "CONFIGURATION.AUCTION.AUCTION_VALIDITY_INTERVAL_END": "End Validity Time", "CONFIGURATION.AUCTION.TIME_ZONE": "Time Zone", "CONFIGURATION.AUCTION.DISPATCH_GROUPS": "Dispatch Groups", "CONFIGURATION.AUCTION.LABEL.BIDDING_LOGIC": "Bidding Logic", "CONFIGURATION.AUCTION.BIDDING_METHOD": "Bidding Method", "CONFIGURATION.AUCTION.BIDDING_METHOD_MARKET": "Market", "CONFIGURATION.AUCTION.ASSETS": "Assets", "CONFIGURATION.AUCTION.FREQUENCY_TIME_MINUTES": "Frequency Time (minutes)", "CONFIGURATION.AUCTION.DELIVERY_INTERVAL": "Delivery Interval", "CONFIGURATION.AUCTION.DELIVERY_INTERVAL_DAYS_SHIFTED_HOURS": "Shifted Hours", "CONFIGURATION.AUCTION.OFFERS_TIME_BLOCK": "Offer block", "CONFIGURATION.AUCTION.BIDDING_DIRECTION": "Direction", "CONFIGURATION.AUCTION.ROUNDING_DIGITS": "Volume rounding", "CONFIGURATION.AUCTION.MINIMUM_MW_VOLUME_FOR_1BID": "Minimum MW volume for 1 bid", "CONFIGURATION.AUCTION.MINIMUM_MW_VOLUME": "Minimum MW volume", "CONFIGURATION.AUCTION.PRICE_TYPE": "Prices required", "CONFIGURATION.AUCTION.ENERGY_PRICE_ROUNDING_DIGITS": "Energy price rounding", "CONFIGURATION.AUCTION.CAPACITY_PRICE_ROUNDING_DIGITS": "Capacity price rounding", "CONFIGURATION.AUCTION.SWING_LIMIT": "Swing Limit", "CONFIGURATION.AUCTION.SWING_LIMIT_VALUE": "Swing Limit Value", "CONFIGURATION.AUCTION.LABEL.OPTIMIZATION_HORIZON_CHANGE": "Optimization Horizon", "CONFIGURATION.AUCTION.OPTIMIZATION_HORIZON_CHANGE": "Optimization Horizon Change", "CONFIGURATION.AUCTION.OPTIMIZATION_HORIZON_CHANGE_DAYS_BEFORE_DELIVERY": "Days before delivery interval", "CONFIGURATION.AUCTION.OPTIMIZATION_HORIZON_CHANGE_TIME": "Time", "CONFIGURATION.AUCTION.LABEL.MARKET_AUCTION": "Market Tender", "CONFIGURATION.AUCTION.MARKET_AUCTION_START": "Auction Start", "CONFIGURATION.AUCTION.MARKET_AUCTION_START_DAYS_BEFORE_DELIVERY": "Days before delivery interval", "CONFIGURATION.AUCTION.MARKET_AUCTION_START_TIME": "Time", "CONFIGURATION.AUCTION.MARKET_AUCTION_START_MINUTES": "Minutes", "CONFIGURATION.AUCTION.MARKET_AUCTION_END": "Auction End", "CONFIGURATION.AUCTION.MARKET_AUCTION_END_DAYS_BEFORE_DELIVERY": "Days before delivery interval", "CONFIGURATION.AUCTION.MARKET_AUCTION_END_TIME": "Time", "CONFIGURATION.AUCTION.MARKET_AUCTION_END_MINUTES": "Minutes", "CONFIGURATION.AUCTION.LABEL.BID_INPUTS": "Bid Inputs", "CONFIGURATION.AUCTION.LABEL.INTERNAL_AUCTION": "Internal tender", "CONFIGURATION.AUCTION.LABEL.CUSTOMER_AUCTION": "Customer tender", "CONFIGURATION.AUCTION.LABEL.THIRD_PARTY_INTERFACE": "Third party interface", "CONFIGURATION.AUCTION.INTERNAL_BIDDING_FORMAT": "Bidding format", "CONFIGURATION.AUCTION.INTERNAL_AUCTION_START": "Auction Start", "CONFIGURATION.AUCTION.INTERNAL_AUCTION_START_DAYS_BEFORE_DELIVERY": "Days before delivery interval", "CONFIGURATION.AUCTION.INTERNAL_AUCTION_START_TIME": "Time", "CONFIGURATION.AUCTION.INTERNAL_AUCTION_START_MINUTES": "Minutes", "CONFIGURATION.AUCTION.INTERNAL_AUCTION_END": "Auction End", "CONFIGURATION.AUCTION.INTERNAL_AUCTION_END_DAYS_BEFORE_DELIVERY": "Days before delivery interval", "CONFIGURATION.AUCTION.INTERNAL_AUCTION_END_TIME": "Time", "CONFIGURATION.AUCTION.INTERNAL_AUCTION_END_MINUTES": "Minutes", "CONFIGURATION.AUCTION.CUSTOMER_BIDDING_FORMAT": "Bidding format", "CONFIGURATION.AUCTION.CUSTOMER_AUCTION_START": "Auction Start", "CONFIGURATION.AUCTION.CUSTOMER_AUCTION_START_DAYS_BEFORE_DELIVERY": "Days before delivery interval", "CONFIGURATION.AUCTION.CUSTOMER_AUCTION_START_TIME": "Time", "CONFIGURATION.AUCTION.CUSTOMER_AUCTION_START_MINUTES": "Minutes", "CONFIGURATION.AUCTION.CUSTOMER_AUCTION_MINIMUM_MW_VOLUME_FOR_1BID": "Minimum MW volume for 1 bid", "CONFIGURATION.AUCTION.CUSTOMER_AUCTION_END": "Auction End", "CONFIGURATION.AUCTION.CUSTOMER_AUCTION_END_DAYS_BEFORE_DELIVERY": "Days before delivery interval", "CONFIGURATION.AUCTION.CUSTOMER_AUCTION_END_TIME": "Time", "CONFIGURATION.AUCTION.CUSTOMER_AUCTION_END_MINUTES": "Minutes", "CONFIGURATION.AUCTION.LABEL.OPTIMIZER_BOD": "Optimizer BOD", "CONFIGURATION.AUCTION.OPTIMIZER_BOD_FREQUENCY_MINUTES": "Frequency (Minuten)", "CONFIGURATION.AUCTION.OPTIMIZER_BOD_COMPUTED_SP": "Computed SPs", "CONFIGURATION.AUCTION.OPTIMIZER_BOD_DELTA_FOR_SUBMISSION": "Delta for submission (£)", "CONFIGURATION.AUCTION.OPTIMIZER_BOD_DELTA_FOR_UNDO": "Undo delta (£)", "CONFIGURATION.AUCTION.API_AUCTION_BIDDING": "Bidding API", "CONFIGURATION.AUCTION.AUCTION_SYSTEM": "Auction System", "CONFIGURATION.AUCTION.LABEL.AUCTION_SYSTEM_UPLOAD": "Upload", "CONFIGURATION.AUCTION.LABEL.AUCTION_SYSTEM_DOWNLOAD": "Download", "CONFIGURATION.AUCTION.LABEL.AUCTION_SYSTEM_UI": "UI", "CONFIGURATION.AUCTION.LABEL.AUCTION_SYSTEM_UI_FOR_BATTERY_UK_INTRADAY": "UI For UK Batteries", "CONFIGURATION.AUCTION.LABEL.AUCTION_SYSTEM_UK_VATP": "UK VATP", "CONFIGURATION.AUCTION.LABEL.AUCTION_SYSTEM_DE_VATP": "Germany VATP", "CONFIGURATION.AUCTION.TH_AUCTION_SYSTEM_UPLOAD_BEHAVIOUR": "Upload Behaviour", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UPLOAD_BEHAVIOUR": "Behaviour", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UPLOAD_MARKET_TIME": "Minutes before market tender end", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UPLOAD_NUMBER_OF_RETRIES": "Number of retries", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UPLOAD_SECONDS_BETWEEN_RETRIES": "Seconds between retries", "CONFIGURATION.AUCTION.TH_AUCTION_SYSTEM_DOWNLOAD_BEHAVIOUR": "Download Behaviour", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_DOWNLOAD_BEHAVIOUR": "Behaviour", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_DOWNLOAD_MARKET_TIME": "Minutes after market tender end", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_DOWNLOAD_NUMBER_OF_RETRIES": "Number of retries", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_DOWNLOAD_SECONDS_BETWEEN_RETRIES": "Seconds between retries", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_TRADER_LOOK_AHEAD_HOURS": "Trader Look Ahead Time (hours)", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_PRICE_GROUPING_STEP": "Price Grouping Step Size (£/MWh)", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_BID_EXPIRY_WARNING_MINUTES": "Bid Expiry Warning (minutes)", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_CURRENCY": "<PERSON><PERSON><PERSON><PERSON>", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_SITE_ID": "Site ID", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_FOR_BATTERY_UK_INTRADAY_ASSET_ID": "Asset ID", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_FOR_BATTERY_UK_INTRADAY_PORTFOLIO_ID": "Portfolio ID", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_FOR_BATTERY_UK_INTRADAY_EXPORT_ENTRADER": "Export Entrader", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_FOR_BATTERY_UK_INTRADAY_UPLOAD_FOLDER": "Upload Folder", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UK_VATP_SMART_VOLUME_STRATEGY_TEMPLATE_ID": "Smart Volume Strategy Template ID", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UK_VATP_HIDDEN_ICEBERG_STRATEGY_TEMPLATE_ID": "Hidden Iceberg Strategy Template ID", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UK_VATP_SMART_ICEBERG_STRATEGY_TEMPLATE_ID": "Smart Iceberg Strategy Template ID", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_DE_VATP_SMART_VOLUME_STRATEGY_TEMPLATE_ID": "Smart Volume Strategy Template ID", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_DE_VATP_HIDDEN_ICEBERG_STRATEGY_TEMPLATE_ID": "Hidden Iceberg Strategy Template ID", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_DE_VATP_SMART_ICEBERG_STRATEGY_TEMPLATE_ID": "Smart Iceberg Strategy Template ID", "CONFIGURATION.AUCTION.LABEL.AUCTION_RESULTS_POST_PROCESSING": "Auction Results Post Processing", "CONFIGURATION.AUCTION.CREATE_NOMINATIONS": "Create nomination out of result?", "CONFIGURATION.AUCTION.CREATE_ALLOCATIONS": "Create allocation out of result?", "CONFIGURATION.AUCTION.ALLOCATION_START_EXTENSION_SECONDS": "Allocation Start Extension (seconds)", "CONFIGURATION.AUCTION.ALLOCATION_END_EXTENSION_SECONDS": "Allocation End Extension (seconds)", "CONFIGURATION.AUCTION.ALLOCATION_END_EXTENSION_FROM_RAMP_RATE": "Allocation End Extension From Ramp Rate", "CONFIGURATION.AUCTION.yesNoOptions.true": "Yes", "CONFIGURATION.AUCTION.yesNoOptions.false": "No", "CONFIGURATION.AUCTION.deliveryIntervalOptions.Blocks": "Blocks", "CONFIGURATION.AUCTION.deliveryIntervalOptions.Days": "Days", "CONFIGURATION.AUCTION.deliveryIntervalOptions.Weeks": "Weeks", "CONFIGURATION.AUCTION.deliveryIntervalOptions.Various": "Other", "CONFIGURATION.AUCTION.offersTimeBlockOptions.QuarterOfHour": "Quarter Of Hour", "CONFIGURATION.AUCTION.offersTimeBlockOptions.HalfHour": "Half Hour", "CONFIGURATION.AUCTION.offersTimeBlockOptions.OneHour": "One Hour", "CONFIGURATION.AUCTION.offersTimeBlockOptions.FourHours": "Four Hours", "CONFIGURATION.AUCTION.biddingDirectionOptions.PositiveAndNegative": "Positive And Negative", "CONFIGURATION.AUCTION.biddingDirectionOptions.Positive": "Positive", "CONFIGURATION.AUCTION.biddingDirectionOptions.Negative": "Negative", "CONFIGURATION.AUCTION.biddingDirectionOptions.Symmetric": "Symmetric", "CONFIGURATION.AUCTION.priceTypeOptions.EnergyOnly": "Energy Only", "CONFIGURATION.AUCTION.priceTypeOptions.CapacityOnly": "Capacity Only", "CONFIGURATION.AUCTION.priceTypeOptions.EnergyAndCapacity": "Energy And Capacity", "CONFIGURATION.AUCTION.marketAuctionStartOptions.AuctionTimeAnyTime": "Anytime before auction ends", "CONFIGURATION.AUCTION.marketAuctionStartOptions.AuctionTimeAbsolute": "Absolute: Time before delivery interval starts", "CONFIGURATION.AUCTION.marketAuctionStartOptions.AuctionTimeRelative": "Relative: Minutes before offer block starts", "CONFIGURATION.AUCTION.marketAuctionEndOptions.AuctionTimeAbsolute": "Absolute: Time before delivery interval starts", "CONFIGURATION.AUCTION.marketAuctionEndOptions.AuctionTimeRelative": "Relative: Minutes before offer block starts", "CONFIGURATION.AUCTION.marketAuctionEndOptions.AuctionTimeRelativeToBid": "Relative: Based on bid", "CONFIGURATION.AUCTION.bidInputsOptions.InternalBiddingKind": "Internal", "CONFIGURATION.AUCTION.bidInputsOptions.CustomerBiddingKind": "Customer", "CONFIGURATION.AUCTION.bidInputsOptions.V2gBiddingKind": "V2G API", "CONFIGURATION.AUCTION.bidInputsOptions.SpotOptimisationBiddingKind": "Spot Optimization Bidding API", "CONFIGURATION.AUCTION.bidInputsOptions.OptimizerBodKind": "Optimized BOD", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.LIST.TITLE": "Balancing Groups", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TH.TYPE": "Type", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TH.NAME": "Name", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TH.SCHEDULING": "Scheduling", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TH.ACTIONS": "", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TYPE.SUPPLIER_BGS": "Supplier Balancing Group", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TYPE.COLLECTING_BGS": "Collecting Balancing Group", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TYPE.INTERNAL_BGS": "E.ON Internal", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TYPE.THIRD_PARTY_BGS": "Third Party", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.ENTER_BALANCING_GROUP": "Enter Balancing Group", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.SAVE": "Save", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.ENTER_DISPATCH_GROUP": "Enter Dispatch Group", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.SHOW_DISPATCH_GROUP": "Show Dispatch Group", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.SAVE": "Save", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.CANCEL_EDIT": "Cancel", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.EDIT": "Edit", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.DELETE": "Delete", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LIST.TITLE": "Dispatch Groups", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.NAME": "Name", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.TSO": "TSO", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.MARKET": "Market", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.BALANCING_GROUP": "Balancing Group", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.GENERIC_CONFIG": "Generic Config", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.TRADING_NOMINATION_ENABLED": "Trading Nomination Enabled", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.AUTOMATIC_ALLOCATION_ENABLED": "Automatic Allocation Enabled", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.ACTIONS": "", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.GENERAL_DATA_TITLE": "Dispatch Groups Details", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.GENERIC_CONFIG_ENABLED": "Generic Config", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.PORTFOLIO_ID": "Portfolio ID", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.REACTION_TIME": "Reaction time (s)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.MINIMUM_DURATION_OF_DISPATCH": "Min. duration of dispatch (s)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.PARAMETERS_TITLE": "Local steering parameters", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.PARAMETERS_INFO_HTML": "<b>If an asset can be configured remotely and is allocated to the given dispatch group then the parameters will be sent automatically to the local box.</b>", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DEAD_BAND_POS_MHZ": "Dead band positive (mHz)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DEAD_BAND_NEG_MHZ": "Dead band negative (mHz)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.MAX_FREQUENCY_DEVIATION_POS_MHZ": "Max frequency deviation positive (mHz)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.MAX_FREQUENCY_DEVIATION_NEG_MHZ": "Max frequency deviation negative (mHz)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SIGNALS_TITLE": "Generated Signals", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SIGNALS": "Relevant Signals", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DSOS": "Subsignals for DSOs", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SUBPOOLS_VALUE_ON": "Marketed Poolindex", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SUBPOOLS_VALUE_OFF": "Non-Marketed Poolindex", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ECHO_SIGNAL_ACTIVE_POWER": "Echo ActivePower Signal", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ECHO_SIGNAL_HEARTBEAT": "Echo Heartbeat Signal", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARMS_TITLE": "Alarm Signals", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_ASSETS_WITH_NOT_RECOVERABLE_FAULTS_ENABLED": "Signal 1 (AssetAlertLevelAlarm)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_ASSETS_WITH_RECOVERABLE_FAULTS_ENABLED": "Signal 2 (AssetAlertLevelWarning)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_SETPOINT_REACHABLE_ENABLED": "Signal 3 (DUAlertLevelWarning)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_AVAILABLE_FLEX_TOO_LOW_ENABLED": "Signal 4 (VPPMissingFlexibilitySRL)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_ASSETS_FAULTS_PQ_THRESHOLD": "Assets relevant if PQ exceeds (KW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.SECTION.RCC_TITLE": "RCC (German Control Room) Alarms", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARMS_ENABLED": "RCC FlexTooLow Alarm enabled", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TOOLTIP.INDIVIDUAL_ALARMS_ENABLED_INFO_HTML": "An event will be created if the flex available is lower than the threshold percentage of the traded volume over the duration defined in seconds.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_FLEXIBILITY_THRESHOLD_FACTOR": "FlexTooLow Threshold Factor (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_FLEXIBILITY_THRESHOLD_BUFFER": "FlexTooLow Duration (s)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_MERLIN_ENABLED": "RCC MerLin Alarm enabled", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_ECHO_SIGNAL_ACTIVE_POWER_ENABLED": "RCC EchoActivePower Alarm enabled", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.NOTICE.INDIVIDUAL_ALARMS_ENABLED_HTML": "By checking any of these boxes, alarms for this DG will be sent over to the German Control Room. <br />Please contact VPP Operations in case of doubt.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_OVERDELIVERY_POS_ENABLED": "Overdelivery by pos. setpoint alarm enabled", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TOOLTIP.INDIVIDUAL_ALARM_OVERDELIVERY_POS_ENABLED_INFO_HTML": "During a positive dispatch, an event will be created if the dispatch group overdelivers longer than the accepted time in seconds by more than the entered MW.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_OVERDELIVERY_NEG_ENABLED": "Overdelivery by neg. setpoint alarm enabled", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TOOLTIP.INDIVIDUAL_ALARM_OVERDELIVERY_NEG_ENABLED_INFO_HTML": "During a negative dispatch, an event will be created if the dispatch group overdelivers longer than the accepted time in seconds by more than the entered MW.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_OVERDELIVERY_POS_THRESHOLD": "Overdelivery by pos. setpoint alarm threshold (MW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_OVERDELIVERY_NEG_THRESHOLD": "Overdelivery by neg. setpoint alarm threshold (MW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_OVERDELIVERY_POS_DELAY": "Overdelivery by pos. setpoint alarm duration (s)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_OVERDELIVERY_NEG_DELAY": "Overdelivery by neg. setpoint alarm duration (s)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.PRL_TITLE": "Pro rata activation (PRL/FFRD)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ACTIVATION_FACTOR": "Activation Buffer (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.AVAILABILITY_DEVIATION_THRESHOLD_FACTOR": "Change in Availability (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SETPOINT_REACHED_TITLE": "Setpoint-Reached detection", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SETPOINT_REACHED_DETECTION_ROLLING_AVERAGE_DURATION": "Average Window (s)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SETPOINT_REACHED_DETECTION_UPPER_TOLERANCE_FACTOR": "Upper Tolerance (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SETPOINT_REACHED_DETECTION_LOWER_TOLERANCE_FACTOR": "Lower Tolerance (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SETPOINT_REACHED_DETECTION_UPPER_TOLERANCE_MINIMUM": "Upper Tolerance Min. (KW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SETPOINT_REACHED_DETECTION_LOWER_TOLERANCE_MINIMUM": "Lower Tolerance Min. (KW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.IS_AT_SETPOINT_DETECTION_TITLE": "Is-At-Setpoint detection", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_FACTOR": "Upper Tolerance (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_FACTOR": "Lower Tolerance (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_MINIMUM": "Upper Tolerance Min. (KW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_MINIMUM": "Lower Tolerance Min. (KW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_TITLE": "Compensation", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_CHECK_INTERVAL": "Check Window (s)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_RESOLUTION": "Resolution (KW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.OVER_DELIVERY_EXCESS_COMPENSATION_FACTOR": "Over-Delivery Compensation (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.OVER_DELIVERY_COMPENSATION_LIMIT_FACTOR": "Over-Delivery Limit (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.UNDER_DELIVERY_EXCESS_COMPENSATION_FACTOR": "Under-Delivery Compensation (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.UNDER_DELIVERY_COMPENSATION_LIMIT_FACTOR": "Under-Delivery Limit (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_FACTOR": "Upper Tolerance (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_FACTOR": "Lower Tolerance (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_MINIMUM": "Upper Tolerance Min. (KW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_MINIMUM": "Lower Tolerance Min. (KW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SALLY_SETPOINT_TITLE": "Automatic (<PERSON>) Setpoint", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SALLY_SETPOINT_READ_ENABLED": "Enabled", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SALLY_SETPOINT_QUANTIZATION_RESOLUTION": "Quantization Resolution (KW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SALLY_SETPOINT_QUANTIZATION_TIMEOUT": "Quantization Timeout (s)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.MERLIN_DISPATCH_TITLE": "Me<PERSON>in Dispatch", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.MERLIN_DISPATCH_ENABLED": "Enabled", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SPOT_OPTIMIZATION_TITLE": "Spot Optimisation", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.CAPACITY_PRICE_GROUPING_STEPS": "Capacity Price Grouping Steps", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.PLACEHOLDER.CAPACITY_PRICE_GROUPING_STEPS": "Enter new Capacity Price Grouping Step", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.UI_ALERTS_TITLE": "VPP Ops Alerts", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EVENT_TYPES_ACKNOWLEDGE_ALARM": "Events to monitor with silent alarm", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EVENT_TYPES_ACKNOWLEDGE_AUDIO_ALARM": "Events to monitor with audio alarm", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_TITLE": "Trading Nomination Interface", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_ENABLED": "Trading Nomination Enabled", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_MAX_FAULT_SECONDS_PERCENTAGE": "Rollup Asset Fault Threshold (1=100%; default 0.9)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_MAX_INVALID_ROLLUPS_PERCENTAGE": "Aggregrated Asset Fault Threshold (1=100%; default 0.9)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_PAST_AVAILABILITY_HOURS": "Time window for rollups (h) (default 24)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_TARGET_BID_VOLUME_MW": "Minimum Size Bid MW (default 5)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_ADDITIONAL_BUFFER_POS_MW": "Additional Buffer POS MW (default 0)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_ADDITIONAL_BUFFER_NEG_MW": "Additional Buffer NEG MW (default 0)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_ENERGY_PRICE_TARGET_MARGIN": "Energy Price Margin (1=100%; default 0)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ROLLUPS_ENABLED_TITLE": "Enable Rollups", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ROLLUPS_ENABLED": "Enabled", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.AUTOMATIC_ALLOCATION": "Automatic Allocation", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.AUTOMATIC_ALLOCATION_ENABLED": "Automatic Allocation Enabled", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.AUTOMATIC_ALLOCATION_WEIGHT": "Weight", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.AUTOMATIC_ALLOCATION_NOMINATION_BUFFER_PERCENTAGE": "Nomination buffer (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.MAX_ASSET_REALLOCATION": "Max Asset Reallocation (Default No Constraint)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP": "External Backup", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_INFO_HTML": "<b> External backup will be activated if the functionality is marked as active and if during more than the buffer time the following assertion is true: <br/> Available flex without external backup < Total nominated capacity - MAX(Threshold MW; Threshold % of nomination * Total nominated capacity) </b>", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_ACTIVE": "Active", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_BUFFER": "Buffer (s)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_INCREMENT_POS": "Rounding steps positive (MW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_INCREMENT_NEG": "Rounding steps negative (MW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_THRESHOLD_POS": "Positive Threshold MW", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_THRESHOLD_NEG": "Negative Threshold MW", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_NOMINATION_THRESHOLD_POS": "Positive Threshold % of Nomination", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_NOMINATION_THRESHOLD_NEG": "Negative Threshold % of Nomination", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_PARAMS": "DLM Parameters", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE": "Load Management Scheme", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_SITE": "Site Based", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_SITE_INFO": "Load management is based only on the current charging situation.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_SESSION": "Session Based", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_SESSION_INFO": "Load management is based on the current charging situation as well as the durations and charged energies of individual charging sessions.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_CUSTOMER": "Customer Based", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_CUSTOMER_INFO": "Load management is based on the current charging situation as well as past customer behavior.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_IMPORT_RESTRICTIONS": "Import Restrictions", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_NOMINAL_SITE_CURRENT": "Nominal Site Current (A)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_NOMINAL_SITE_CURRENT_INFO": "Nominal current for DLM group \"blocked\"", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_REDUCTION_FACTOR": "Reduction Factor (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_REDUCTION_FACTOR_INFO": "Current reduction factor for DLM group \"reduced\".", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUP_ID": "DLM Group ID's", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUP_REDUCED_ID": "Reduced", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUP_REDUCED_ID_INFO": "Virta DLM group ID for the DLM group that is used for load management.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUP_BLOCKED_ID": "Blocked", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUP_BLOCKED_ID_INFO": "Virta DLM group ID for the DLM group that is independent of load management", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_CONTROL_WINDOWS": "Control Time Windows", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_CONTROL_WINDOW_BEGIN": "<PERSON><PERSON>", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_CONTROL_WINDOW_BEGIN_INFO": "In local time", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_CONTROL_WINDOW_END": "End", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_CONTROL_WINDOW_END_INFO": "In local time", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_CONTROL_WINDOW_ADD_INTERVAL": "Add Interval", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_CONTROL_WINDOW_REMOVE_INTERVAL": "Remove Interval", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_LOAD_MGMT_PARAMS": "Load Management Parameters", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_SESSION_DURATION_THRESHOLD_MINUTES": "Minimum Charging Time (minutes)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_SESSION_DURATION_THRESHOLD_MINUTES_INFO": "Minimum charging time before load management can be applied to a charging session in load management scheme \"Session Based\"", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_SESSION_ENERGY_THRESHOLD_WATT_HOUR": "Minimum Energy (kWh)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_SESSION_ENERGY_THRESHOLD_WATT_HOUR_INFO": "Minimum energy that needs to be charged before load management can be applied to a charging session in load management scheme \"Session Based\"", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_BOTH_THRESHOLDS_INFO": "Conjunction between left and right parameter for assigning chargers to dlm groups", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_BOTH_THRESHOLDS_AND": "and", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_BOTH_THRESHOLDS_OR": "or", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_MIN_AVERAGE_CHARGED_ENERGY_FACTOR": "Minimum Proportion of Average Energy (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_MIN_AVERAGE_CHARGED_ENERGY_FACTOR_INFO": "Minimum proportion of average charging energy of the affected customer before load management can be applied to a charging session in load manamgement scheme \"Customer Based\"", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_MIN_AVERAGE_CHARGING_DURATION_FACTOR": "Minimum Proportion of Average Charging Time (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_MIN_AVERAGE_CHARGING_DURATION_FACTOR_INFO": "Proportion of average charging time of the affected customer for which load management will be applied to a charging session in load management scheme \"Customer Based\"", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_TITLE": "Balancing Groups", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_START_DATE": "Start Date", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_END_DATE": "End Date", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_NAME": "Name", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_ADD": "Add Balancing Group", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_DIALOG_TITLE": "Enter Balancing Group", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_SAVE": "Save", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.EXPORT_ENTRADER": "Export Entrader", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ENTRADER_TRESHOLD_UPDATE": "Treshold Update (MW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ENTRADER_LEAD_TIME_UPDATE": "Lead Time Update (min)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ENTRADER_UPLOAD_FOLDER": "Upload Folder", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.BASIC_BEHAVIOR": "Basic Behavior", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.HAS_EXCLUSIVE_BEHAVIOUR": "Exclusive Behaviour", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.HAS_NOMINATION": "Nomination", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.HAS_PRECEDING_BASEPOINT": "Preceding Basepoint", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ASSET_ACTIVATION": "Asset activation", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ASSET_ACTIVATION_TYPE": "Asset Activation Type", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.FREQUENCY_LOCAL_STEERING_PARAMETERS": "Frequency Local Steering Parameters", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.AAT_AR_FLSP_DEAD_BAND_POS": "Dead Band Pos (Hz)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.AAT_AR_FLSP_DEAD_BAND_NEG": "Dead Band Neg (Hz)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.AAT_AR_FLSP_MAX_FREQUENCY_DEVIATION_POS": "Frequency Deviation Pos (Hz)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.AAT_AR_FLSP_MAX_FREQUENCY_DEVIATION_NEG": "Frequency Deviation Neg (Hz)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.AAT_AR_FLSP_HIGH_KNEE_JOINT": "High Knee Joint (Hz)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.AAT_AR_FLSP_LOW_KNEE_JOINT": "Low Knee Joint (Hz)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.STATE_OF_CHARGE_MANAGEMENT": "State of Charge Management", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DYNAMIC_CONTAINMENT_LOW": "Dynamic Containment Low", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DYNAMIC_CONTAINMENT": "Dynamic Containment", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.VARIABLE_SOE": "Variable SoE", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.FCR_NL": "FCR NL", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.OPTIMIZATION_PASS_THROUGH": "Optimization Pass Through", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_TARGET_STATE_OF_CHARGE_LOW": "Target State of Charge Low (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_TARGET_STATE_OF_CHARGE_HIGH": "Target State of Charge High (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_TARGET_STATE_OF_CHARGE_BOTH": "Target State of Charge Both (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_FOOT_ROOM": "Foot Room (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_HEAD_ROOM": "Head Room (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_RAMP_RATE_LIMIT": "Ramp Rate Limit (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_ENERGY_RESERVE": "Energy Reserve (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_MIN_ENERGY_RECOVERY": "Min. Energy Recovery (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_DEAD_BAND_FACTOR": "Dead Band Factor (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.VOLUME_BASELINE_DC": "Volume Baselining - DC", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.VOLUME_BASELINE_DM": "Volume Baselining - DM", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.VOLUME_BASELINE_DR": "Volume Baselining - DR", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DC_DELIVERY_DURATION": "Delivery Duration (sec)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DC_DELIVERY_DURATION_BUFFER": "Delivery Duration Buffer (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DC_MIN_ENERGY_RECOVERY": "Min Energy Recovery (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DM_DELIVERY_DURATION": "Delivery Duration (sec)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DM_DELIVERY_DURATION_BUFFER": "Delivery Duration Buffer (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DM_MIN_ENERGY_RECOVERY": "Min Energy Recovery (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DR_DELIVERY_DURATION": "Delivery Duration (sec)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DR_DELIVERY_DURATION_BUFFER": "Delivery Duration Buffer (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DR_MIN_ENERGY_RECOVERY": "Min Energy Recovery (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_FCR_LOWER_SOC_LIMIT": "Lower SoC Limit (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_FCR_UPPER_SOC_LIMIT": "Upper SoC Limit (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_FCR_SCM_OPT_SP_DURATION_MINUTES": "Settlement Period Duration (minutes)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ASSET_DISPATCH_STRATEGY": "Asset dispatch strategy", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_START_USING_ASSETS_ONLY_WHEN_AT_BASEPOINT": "Start using assets only when at basepoint", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_STRATEGY": "Strategy", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_STRATEGY_BY_PRICE_PRESERVE_CURRENT_DISPATCHES": "Preserve current dispatches", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_STRATEGY_BY_PRICE_AND_SOC_LIMIT_BATTERY_POWER_WINDOW": "Battery Dispatch Window (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_STRATEGY_BY_PRICE_AND_SOC_LIMIT_BATTERY_POWER_WINDOW_INFO_HTML": "Limit the dispatch power of batteries so that the dispatched power can be held for the given time in seconds before the battery is empty or full.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_STRATEGY_PRO_RATA_SYMMETRIC": "Symmetric", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_STRATEGY_ON_OFF_SIGNAL": "Signal used for on/off", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.CROSS_DG_LINKS": "Cross DG links", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.CDL_CROSS_PLAN_PROPAGATION_FREQUENCY": "Cross plan propagation frequency", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DEVIATIONS_COMPENSATION": "Deviations Compensation", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_CHECK_INTERVAL_SECONDS": "Check Interval (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_OVER_DELIVERY_EXCESS_COMPENSATION_FACTOR": "Over delivery excess compensation factor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_OVER_DELIVERY_COMPENSATION_LIMIT_FACTOR": "Over delivery compensation limit factor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_UNDER_DELIVERY_EXCESS_COMPENSATION_FACTOR": "Under delivery excess compensation factor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_UNDER_DELIVERY_COMPENSATION_LIMIT_FACTOR": "Under delivery compensation limit factor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_COMPENSATION_RESOLUTION_KW": "Compensation resolution (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_IS_AT_SETPOINT_UPPER_TOLERANCE_FACTOR": "At setpoint: upper tolerance factor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_IS_AT_SETPOINT_UPPER_TOLERANCE_MINIMUM_KW": "At setpoint: upper tolerance min (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_IS_AT_SETPOINT_LOWER_TOLERANCE_FACTOR": "At setpoint: lower tolerance factor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_IS_AT_SETPOINT_LOWER_TOLERANCE_MINIMUM_KW": "At setpoint: lower tolerance min (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DG_ACTIVATION": "Activation", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DG_ACTIVATION_TYPE": "DG Activation Type", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DISPATCH": "Dispatch", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DISPATCH_COMMANDS": "Dispatch commands", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DISPATCH_SOURCE": "Dispatch Source", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_NOMINATED_VOLUME_ACTIVATION_FACTOR": "Nominated Volume Activation Factor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_NOMINATED_VOLUME_SYMMETRIC_ACTIVATION": "Nominated Volume Symetric Activation", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_UI_EDG_SCHEDULE": "UI EDG Schedule", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_RESIDUAL_SHAPE_POSITIVE_THRESHOLD": "Positive Threshold (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_RESIDUAL_SHAPE_NEGATIVE_THRESHOLD": "Negative Threshold  (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_RESIDUAL_SHAPE_WINDOW": "Window (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_PRICE_TRIGGER_WINDOW": "Window (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_PRICE_TRIGGER_PRICE_TYPE": "Price Type", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_PRICE_TRIGGER_SETTLEMENT_PERIOD": "Settlement Period", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_PRICE_TRIGGER_PRICE_THRESHOLD_NEG": "Negative Price Threshold", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_PRICE_TRIGGER_PRICE_THRESHOLD_POS": "Positive Price Threshold", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_PRICE_TRIGGER_PRICE_EXPIRATION_SECONDS": "Price Expiration Time (sec)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_NLAFRR_BLEEDING_TIME_SECONDS": "Bleeding Time (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.EXECUTION_PLAN_WINDOW_SECONDS": "Execution Plan Window (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.EXECUTION_PLAN_FREQUENCY_SECONDS": "Execution Plan Frequency (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.EXECUTION_PLAN_FREQUENCY_INFO_HTML": "Lower limit: 1 second; upper limit: infinite; For optimal behavior the value should be as large as possible and as small as necessary.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NOMINATION_EXTENSION_TIMES": "Nomination Extension Times", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NET_EXTEND_BEFORE_NOMINATION_SECONDS": "Extend before nomination (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NET_OVERLAP_NOMINATIONS_SECONDS": "Overlap nomination (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NET_STOP_BEFORE_END_OF_NOMINATION_SECONDS": "Stop before end of nomination (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NOTIFICATIONS": "Notifications", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_FLEX_TOO_LOW": "Notifications - Flex too low", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_FLEX_TOO_LOW_THRESHOLD_FACTOR": "Threshold factor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_FLEX_TOO_LOW_THRESHOLD_BUFFER_SECONDS": "Threshold buffer (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_OVER_DELIVERY_NEGATIVE": "Notifications - Over Delivery Negative", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_OVER_DELIVERY_NEGATIVE_THRESHOLD_KW": "Negative threshold (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_OVER_DELIVERY_NEGATIVE_DELAY_SECONDS": "Negative delay (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_OVER_DELIVERY_POSITIVE": "Notifications - Over Delivery Positive", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_OVER_DELIVERY_POSITIVE_THRESHOLD_KW": "Positive threshold (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_OVER_DELIVERY_POSITIVE_DELAY_SECONDS": "Positive delay (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED": "Notifications - Setpoint Not Reached", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED_REACH_SETPOINT_IN_SECONDS": "Reach setpoint (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED_IS_AT_SETPOINT_DETECTION": "Is at Setpoint detection", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED_IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_FACTOR": "Upper tolerance factor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED_IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_MINIMUM_KW": "Upper Tolerance Minimum (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED_IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_FACTOR": "Lower tolerance factor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED_IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_MINIMUM_KW": "Lower tolerance minimum (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_REACHABLE": "Notifications - Setpoint Reachable", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_REACHABLE_IS_AT_SETPOINT_DETECTION": "Is at Setpoint detection", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_REACHABLE_IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_FACTOR": "Upper tolerance factor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_REACHABLE_IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_MINIMUM_KW": "Upper Tolerance Minimum (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_REACHABLE_IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_FACTOR": "Lower tolerance factor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_REACHABLE_IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_MINIMUM_KW": "Lower tolerance minimum (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.PERIODICITY": "Periodicity", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.PERIODICITY_DG_AGGREGATIONS_SECONDS": "DG aggregations (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.PERIODICITY_OUTPUT_SIGNAL_WRITE_SECONDS": "Output signal write (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.RAMP_ADJUSTMENT_STRATEGY": "Ramp adjustment strategy", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.REDISPATCH_TRIGGERS": "Redispatch triggers", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.RT_ASSET_AVAILABLE_FLEX_CHANGE_THRESHOLD_KW": "Av. flex change: Thresh<PERSON> (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.RT_ASSET_AVAILABLE_FLEX_CHANGE_SINCE_ACTIVATION_FACTOR": "Av. flex change since activation: Factor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.RT_DG_TOTAL_DEVIATION_BUFFER_KW": "Total dev. Buffer (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.RT_DG_TOTAL_DEVIATION_SUPPRESS_REDISPATCH_SECONDS": "Total dev. Suppress redispatch (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION": "Setpoint Validation", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION_REACTION_TIME_SECONDS": "Reaction time (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION_CAP_BY_NOMINATION": "Cap by nomination", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION_MINIMUM_DURATION_OF_DISPATCH_SECONDS": "Minimum duration of dispatch (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION_NOMINATION_INTERVAL_VALIDATION": "Nomination interval validation", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION_QUANTIZATION_FILTER_RESOLUTION_KW": "Quantization resolution (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION_QUANTIZATION_FILTER_QUANTIZATION_DURATION_SECONDS": "Quantization duration (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SIGNALS": "Signals", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SIGNALS_OUTPUT_SIGNALS": "Output Signals", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SIGNALS_SUB_POOLS_VALUES_ON": "Subpool Values On", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SIGNALS_SUB_POOLS_VALUES_OFF": "Subpool Values Off", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SIGNALS_DSO_FOR_SIGNALS": "DSO for signals", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SIGNALS_HEART_BEAT_MIRROR": "Heart beat mirror", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.BATTERY_DISPATCH_CONFIG": "Battery Dispatch Config", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.BATTERY_DISPATCH_CONFIG_MIN_POWER_FOR_DISPATCH_DISCHARGE": "Min Power For Dispatch Discharge (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.BATTERY_DISPATCH_CONFIG_MIN_POWER_FOR_DISPATCH_CHARGE": "Min Power For Dispatch Charge (%)", "CONFIGURATION.ROLLUPS.TAB.ASSET_ROLLUPS": "Asset Rollups", "CONFIGURATION.ROLLUPS.TAB.DISPATCH_GROUP_ROLLUPS": "Dispatch Group Rollups", "CONFIGURATION.ASSET_ROLLUPS.SELECT_ROLLUP_TYPE": "Select Rollup Metric", "CONFIGURATION.ASSET_ROLLUPS.SELECT_ITEM_TYPE": "Select Type", "CONFIGURATION.ASSET_ROLLUPS.SELECT_ASSET": "Select Asset", "CONFIGURATION.ASSET_ROLLUPS.SELECT_ROLLUP_GROUP": "Select Rollup Group", "CONFIGURATION.ASSET_ROLLUPS.SELECT_INTERVAL": "Select Interval", "CONFIGURATION.ASSET_ROLLUPS.BTN_REGENERATE": "Regenerate", "CONFIGURATION.ASSET_ROLLUPS.EDIT_ROLLUP_FAMILIES": "Edit Rollup Groups", "CONFIGURATION.ASSET_ROLLUPS.TITLE_ROLLUP_REGENERATION": "Rollup Regeneration", "CONFIGURATION.ASSET_ROLLUPS.TITLE_ROLLUP_MANAGEMENT": "Rollup Management Visualisation", "CONFIGURATION.ASSET_ROLLUPS.ADD": "Add", "CONFIGURATION.ASSET_ROLLUPS.SAVE": "Save", "CONFIGURATION.ASSET_ROLLUPS.ROLLUP_METRIC": "Rollup Metric", "CONFIGURATION.ASSET_ROLLUPS.PRODUCT": "Product", "CONFIGURATION.DG_ROLLUPS.SELECT_ROLLUP_TYPE": "Select Rollup Metric", "CONFIGURATION.DG_ROLLUPS.SELECT_DISPATCH_GROUP": "Select Dispatch Group", "CONFIGURATION.DG_ROLLUPS.SELECT_INTERVAL": "Select Interval", "CONFIGURATION.DG_ROLLUPS.BTN_REGENERATE": "Regenerate", "CONFIGURATION.DG_ROLLUPS.TITLE_ROLLUP_REGENERATION": "Rollup Regeneration", "CONFIGURATION.DG_ROLLUPS.TITLE_ROLLUP_MANAGEMENT": "Rollup Management Visualisation", "ISSUE_UPLOADING_FILE": "Issue uploading file.", "NO_FILE_UPLOADED": "No file uploaded.", "ISSUE_SUBMITTING_ASSET_OPTIMIZATION": "Issue Submitting Asset Optimization.", "HISTORY.BIDS.DETAIL.CONTENT_TYPE.NA": "N/A", "HISTORY.BIDS.DETAIL.CONTENT_TYPE.ui_update": "Deleted", "HISTORY.BIDS.DETAIL.CONTENT_TYPE.file_import": "File / API", "HISTORY.BIDS.DETAIL.CONTENT_TYPE.auction_import": "Automatic", "HISTORY.BIDS.DETAIL.CONTENT_TYPE.external_import": "V2G", "HISTORY.BIDS.DETAIL.CONTENT_TYPE.vpp_migration": "One time", "HISTORY.BIDS.DETAIL.CONTENT_TYPE.battery_uk_optimizer": "Battery UK Optimizer", "HISTORY.BIDS.DETAIL.VALIDATION": "Validation", "HISTORY.BIDS.DETAIL.ERRORS": "Errors", "HISTORY.BIDS.DETAIL.WARNINGS": "Warnings", "HISTORY.BIDS.DETAIL.TYPE": "Type", "HISTORY.BIDS.DETAIL.PERFORMED_ON": "Performed On", "HISTORY.BIDS.DETAIL.USER": "User", "HISTORY.BIDS.DETAIL.TABLE.ERROR_DESCRIPTION": "Error Des<PERSON>", "HISTORY.BIDS.DETAIL.TABLE.WARNING_DESCRIPTION": "Error Des<PERSON>", "HISTORY.BIDS.DETAIL.TABLE.DETAILS": "Details", "HISTORY.BIDS.TABLE.PERFORMED_ON": "Performed On", "HISTORY.BIDS.TABLE.USER": "User", "HISTORY.BIDS.TABLE.CONTENT": "Content", "HISTORY.BIDS.TABLE.SUCCESSFULLY_IMPORTED": "Successfully imported", "HISTORY.BIDS.TABLE.ERRORS": "Errors", "HISTORY.BIDS.TABLE.WARNINGS": "Warnings", "CONFIGURATION.SCHEDULING_REPORTS.DOWNLOAD_SCHEDULING": "Download Scheduling", "CONFIGURATION.SCHEDULING_REPORTS.SEND_SCHEDULING": "Send Scheduling", "CONFIGURATION.SCHEDULING_REPORTS.DOWNLOAD_EDG": "Download Scheduling to EDG", "CONFIGURATION.SCHEDULING_REPORTS.DOWNLOAD_UGC": "Download Scheduling to UGC", "CONFIGURATION.SCHEDULING_REPORTS.DOWNLOAD_TP": "Download Scheduling to Third Parties", "CONFIGURATION.SCHEDULING_REPORTS.TSO": "TSO", "CONFIGURATION.SCHEDULING_REPORTS.DATE": "Date", "CONFIGURATION.SCHEDULING_REPORTS.MARKET": "Market", "CONFIGURATION.SCHEDULING_REPORTS.DOWNLOAD": "Download", "CONFIGURATION.SCHEDULING_REPORTS.DIRECTION": "Direction", "CONFIGURATION.SCHEDULING_REPORTS.BALANCING_GROUP": "Balncing Group", "CONFIGURATION.SCHEDULING_REPORTS.SEND_TP": "Send Scheduling Third Parties", "CONFIGURATION.SCHEDULING_REPORTS.SEND_BK": "Send BK6-17-046 Reports", "CONFIGURATION.SCHEDULING_REPORTS.SEND": "Send", "SEND_FILES_EXTERNALLY.SEND_ALLOCATION_FILES": "NL FCR - Send Allocation File to TenneT", "SEND_FILES_EXTERNALLY.SEND_MEASUREMENT_FILES": "NL FCR - Send Measurement File to TenneT", "SEND_FILES_EXTERNALLY.SEND_AFRR_MEASUREMENT_FILES": "NL aFRR - Send Measurement File to TenneT", "SEND_FILES_EXTERNALLY.SEND_AFRR_ACTIVATED_ENERGY_DOCUMENTS": "NL aFRR - Send Activated Energy Document", "SEND_FILES_EXTERNALLY.SEND_AFRR_ACTIVATED_ENERGY_DOCUMENTS.GENERATE_NEW_RANDOM_MR_ID": "Generate new random mrID", "SEND_FILES_EXTERNALLY.SEND_AFRR_ACTIVATED_ENERGY_DOCUMENTS.MR_ID": "mr<PERSON>", "SEND_FILES_EXTERNALLY.DATE": "Date", "SEND_FILES_EXTERNALLY.DATE_TIME": "Date and Time", "SEND_FILES_EXTERNALLY.SEND": "Send"}