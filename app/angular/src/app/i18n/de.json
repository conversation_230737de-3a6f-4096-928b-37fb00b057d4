{"PAGE.CAPTIONS.DASHBOARD": "Dashboard", "PAGE.CAPTIONS.BIDDING": "Bidding", "PAGE.CAPTIONS.NOMINATIONS": "Nominierungen", "PAGE.CAPTIONS.REPORTING.TSO": "ÜNB-Berichte", "PAGE.CAPTIONS.ALLOCATIONS": "Z<PERSON><PERSON>nungen", "PAGE.CAPTIONS.REPORTING_AND_NOTIFICATIONS.EMAILS": "Reporting and Notifications – Emails", "PAGE.CAPTIONS.REPORTING_AND_NOTIFICATIONS.EMAILS.SCHEDULING_REPORT": "Reporting and Notifications – Emails - Scheduling Report", "PAGE.CAPTIONS.REPORTING_AND_NOTIFICATIONS.EMAILS.ALLOCATION": "Reporting and Notifications – Emails - Allocation", "PAGE.CAPTIONS.REPORTING_AND_NOTIFICATIONS.EMAILS.ROLLUPS": "Reporting and Notifications – Emails - Rollups", "PAGE.CAPTIONS.HISTORY.NOMINATIONS": "History – Benutzung - Nominierungen", "PAGE.CAPTIONS.HISTORY.MANUAL_ALLOCATIONS": "History – Ben<PERSON>ung <PERSON> <PERSON><PERSON>", "PAGE.CAPTIONS.HISTORY.AUTOMATIC_ALLOCATIONS": "History – Benutzung – Automatische Zuordnungen", "PAGE.CAPTIONS.HISTORY.AUCTIONS": "History – <PERSON><PERSON>ung – Auctions", "PAGE.CAPTIONS.HISTORY.REPORTING_EMAILS": "Historie – Benutzung – Berichts-E-Mails", "PAGE.CAPTIONS.HISTORY.BIDS": "History – Logs – Bids", "PAGE.CAPTIONS.HISTORY.BID_DETAIL": "History – Logs – Bids - Detail", "PAGE.CAPTIONS.HISTORY.ROLLUPS": "Historie <PERSON><PERSON>", "PAGE.CAPTIONS.HISTORY.ROLLUPS.ASSET_ROLLUPS": "Historie <PERSON> Rollups - <PERSON><PERSON>", "PAGE.CAPTIONS.HISTORY.ROLLUPS.DISPATCH_GROUP_ROLLUPS": "Historie – Rollups - Dispatch Group Rollups", "PAGE.CAPTIONS.HISTORY.ROLLUPS.ROLLUP_ERRORS": "Historie – Roll<PERSON> - <PERSON><PERSON> Errors", "PAGE.CAPTIONS.HISTORY.DLM_CHARGING_SESSIONS": "Historie - Benutzung - DLM Ladevorgang", "PAGE.CAPTIONS.HISTORY.V2G": "Historie – Benutzung - V2G-Optimierungsläufe", "PAGE.CAPTIONS.CONFIGURATION.ASSET_LEVEL": "Configuration - Asset Level", "PAGE.CAPTIONS.CONFIGURATION.ASSET_LEVEL.BOX_TYPES": "Configuration - Asset Level - Box Types", "PAGE.CAPTIONS.CONFIGURATION.ASSET_LEVEL.GENERIC_STEERING_TYPES": "Configuration - Asset Level - Generic Steering Types", "PAGE.CAPTIONS.CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS": "Configuration - Asset Level - Signal Lists", "PAGE.CAPTIONS.CONFIGURATION.MARKET_LEVEL.SUBPOOLS": "Configuration - Market Level - Subpools", "PAGE.CAPTIONS.CONFIGURATION.MARKET_LEVEL.AUCTIONS": "Configuration - Market Level - Auctions", "PAGE.CAPTIONS.CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS": "Configuration - Market Level - Balancing Groups", "PAGE.CAPTIONS.CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS": "Configuration - Market Level - Dispatch Groups", "PAGE.CAPTIONS.CONFIGURATION.ROLLUPS_LEVEL.ASSET_ROLLUPS": "Configuration - Rollups - Asset Rollups", "PAGE.CAPTIONS.CONFIGURATION.ROLLUPS_LEVEL.DISPATCH_GROUP_ROLLUPS": "Configuration - Rollups - Dispatch Group Rollups", "BACK": "Zurück", "COMPONENTS.SELECT": "<PERSON><PERSON><PERSON><PERSON>", "negative": "Negativ", "positive": "Positiv", "USER.LANGUAGE": "<PERSON><PERSON><PERSON><PERSON> Sie diese Seite in einer anderen Sprache sehen?", "USER.CHANGE_PASSWORD": "Passwort ändern", "USER.LOGOUT": "Abmelden", "HEADER.HOME": "Dashboard", "HEADER.BIDDING": "Auktionierung", "HEADER.NOMINATIONS": "Nominierungen", "HEADER.TSO_REPORTS": "ÜNB-Berichte", "HEADER.ALLOCATIONS": "Z<PERSON><PERSON>nungen", "HEADER.REPORTING_AND_NOTIFICATIONS": "Berichte und Benachrichtigungen", "HEADER.SCHEDULING_REPORTS": "Fahrplanberichte", "HEADER.EMAILS": "E-Mails", "HEADER.NOTIFICATIONS": "Benachrichtigungen", "HEADER.HISTORY": "Historie", "HEADER.LOGS": "Logs", "HEADER.ROLLUPS": "Rollups", "HEADER.CONFIGURATION": "Einstellungen", "HEADER.ASSET_LEVEL": "Anlagenebene", "HEADER.MARKET_LEVEL": "Marktebene", "HEADER.DGS": "DGs", "HEADER.SUBPOOLS": "Subpools", "HEADER.ASSET_CONFIGURATION": "<PERSON><PERSON>figuration", "HEADER.USAGE": "Logs", "DASHBOARD.BIDDING": "Bereiten Sie Ihre Gebote vor und laden Si<PERSON> sie für die Vermarktung hoch.", "DASHBOARD.TSO_REPORT": "Laden Sie die Abstimmungsdateien der ÜNBs hoch um die Day-After Fahrpläne für die interne Buchung zu generieren.", "DASHBOARD.ALLOCATIONS": "Visualisieren und bearbeiten Sie die Zuordnungen von Anlagen zu verschiedenen Abrufgruppen.", "DASHBOARD.AUCTION_RESULTS": "Laden Sie die Auktionsergebnisse hoch und bearbeiten Sie einzelne Nominierungen.", "DASHBOARD.BUTTONS.BIDDING": "Auktionieren", "DASHBOARD.BUTTONS.UPLOAD_MARKET_RESULTS": "Auktionsergebnisse hochladen", "DASHBOARD.BUTTONS.TSO_REPORT": "ÜNB-Berichte", "DASHBOARD.BUTTONS.UPLOAD_ALLOCATION_FILE": "Zuordnungen hochladen", "AUCTIONS.RESULTS.TITLE": "Auktionsergebnisse", "AUCTIONS.RESULTS.FILTERS.DELIVERY_DATE": "Lie<PERSON><PERSON>", "AUCTIONS.RESULTS.FILTERS.AUCTION": "Ausschreibung", "AUCTIONS.RESULTS.FILTERS.ASSET": "<PERSON><PERSON>", "AUCTIONS.RESULTS.FILTERS.TSO": "ÜNB", "AUCTIONS.RESULTS.FILTERS.DIRECTION": "<PERSON><PERSON><PERSON>", "AUCTIONS.RESULTS.SELECT_AUCTIONS": "Alle", "AUCTIONS.RESULTS.SELECT_PRODUCTS": "Alle", "AUCTIONS.RESULTS.SELECT_TSO": "Alle", "AUCTIONS.RESULTS.SELECT_DIRECTION": "Alle", "AUCTIONS.RESULTS.ENERGY_DIRECTION.ALL": "Alle", "AUCTIONS.RESULTS.ENERGY_DIRECTION.POSITIVE": "POS", "AUCTIONS.RESULTS.ENERGY_DIRECTION.NEGATIVE": "NEG", "AUCTIONS.RESULTS.ACCEPTED_ONLY": "<PERSON>ur bezuschlagte Gebote", "AUCTIONS.RESULTS.DOWNLOAD": "Download", "AUCTIONS.RESULTS.TABLE.AUCTION": "Ausschreibung", "AUCTIONS.RESULTS.TABLE.TSO": "ÜNB", "AUCTIONS.RESULTS.TABLE.DATE": "Datum", "AUCTIONS.RESULTS.TABLE.PRODUCT": "Zeitscheibe", "AUCTIONS.RESULTS.TABLE.CAPACITY_PRICE": "Leistungspreis", "AUCTIONS.RESULTS.TABLE.ENERGY_PRICE": "Arbeitspreis", "AUCTIONS.RESULTS.TABLE.CAPACITY": "Le<PERSON><PERSON>", "AUCTIONS.RESULTS.TABLE.CAPACITY_ACCEPTED": "Bezuschlagte Leistung", "AUCTIONS.RESULTS.TABLE.ASSET": "<PERSON><PERSON>", "AUCTIONS.RESULTS.TABLE.ASSET_EXTERNAL_ID": "Anlage External ID", "AUCTIONS.RESULTS.TABLE.PER_PAGE": "Einträge pro Seite", "BIDDING.TITLE": "Auktionierung", "BIDDING.TABS.PREPARE_BIDS": "Auktion vorbereiten", "BIDDING.TABS.UPLOAD_BIDS": "Angebote hochladen", "BIDDING.TABS.ASSET_OPTIMIZATION": "Anlege Optimierung", "BIDDING.LIST.TITLE": "Angebotsübersicht", "BIDDING.LIST.FILTERS.DELIVERY_DATE": "Lie<PERSON><PERSON>", "BIDDING.LIST.FILTERS.AUCTION": "Ausschreibung", "BIDDING.LIST.FILTERS.ASSET": "<PERSON><PERSON><PERSON>", "BIDDING.LIST.FILTERS.ONLY_BIDS_WITHOUT_ASSET": "Bids Without Asset", "BIDDING.LIST.LINKS.EXPORT": "Export", "BIDDING.LIST.LINKS.DOWNLOAD_ALL": "Gebotsdatei her<PERSON>n", "BIDDING.LIST.LINKS.DELETE_BIDS": "Angebote löschen", "BIDDING.LIST.DELETED_SUCCESS": "Die Angebote wurden erfolgreich gelöscht", "BIDDING.LIST.DELETED_ERROR": "<PERSON><PERSON> beim Löschen von <PERSON>", "BIDDING.LIST.AUCTION_CLOSED": "Auktion geschlossen", "BIDDING.LIST.LAST_UPLOADED_FILE": "Letzte hochgeladene Datei", "BIDDING.LIST.TENDER_DELIVERY_INTERVAL": "Tender Delivery Interval", "BIDDING.LIST.BY": "Durch", "BIDDING.LIST.TABLE.AUCTION": "Ausschreibung", "BIDDING.LIST.TABLE.DELIVERY_START": "Lieferbeginn", "BIDDING.LIST.TABLE.PRODUCT": "Zeitscheibe", "BIDDING.LIST.TABLE.MARKET": "Market", "BIDDING.LIST.TABLE.TSO": "ÜNB", "BIDDING.LIST.TABLE.CAPACITY_PRICE": "Leistungspreis (¤/MW)", "BIDDING.LIST.TABLE.ENERGY_PRICE": "Arbeitspreis (¤/MWh)", "BIDDING.LIST.TABLE.CAPACITY": "Le<PERSON><PERSON> (MW)", "BIDDING.LIST.TABLE.ASSET_ID": "Anlagen-ID", "BIDDING.LIST.TABLE.ASSET_NAME": "<PERSON><PERSON><PERSON><PERSON>", "BIDDING.LIST.TABLE.ASSET_EXTERNAL_ID": "Anlagen External ID", "BIDDING.LIST.TABLE.NO_BIDS_UPLOADED": "Es sind keine Gebote für den gewählten Zeitraum und das gewählte Ausschreibung.", "BIDDING.LIST.TABLE.PER_PAGE": "Einträge pro Seite", "BIDDING.PREPARE_BIDS.TITLE": "Auktionsvorschläge erstellen lassen", "BIDDING.PREPARE_BIDS.DELIVERY_DATE": "Lie<PERSON><PERSON>", "BIDDING.PREPARE_BIDS.PRODUCT": "Produkt", "BIDDING.PREPARE_BIDS.TIME_SLICES": "Zeitscheibe", "BIDDING.PREPARE_BIDS.ALL_TIME_SLICES": "Alle Zeitscheiben", "BIDDING.PREPARE_BIDS.REQUEST_BIDDING_SUGGESTIONS": "Auktionsvorschläge anfordern", "BIDDING.PREPARE_BIDS.EXPORT": "Export", "BIDDING.UPLOAD_BIDS.TITLE": "Angebotsdatei auswählen", "BIDDING.UPLOAD_BIDS.SUBTITLE": "", "BIDDING.UPLOAD_BIDS.EXAMPLE": "Beispieldatei für Regeleistung", "BIDDING.UPLOAD_BIDS.EXAMPLE_AFRR": "Beispieldatei für aFRR", "BIDDING.UPLOAD_BIDS.UPLOAD_BIDS_FILE": "Angebotsdatei hochladen", "BIDDING.UPLOAD_BIDS.SUCCESS_UPLOADED": "Die Gebote wurden erfolgreich hochgeladen.", "BIDDING.UPLOAD_BIDS.SUCCESS_UPLOADED_AUTOMATIC": "<PERSON><PERSON><PERSON>, die auf keinem Gebot basierten, wurden automatisch Gebote erstellt.", "BIDDING.UPLOAD_BIDS.AUCTION": "Ausschreibung", "BIDDING.ASSET_OPTIMIZATION.TITLE": "Auktionsvorschläge erstellen lassen", "BIDDING.ASSET_OPTIMIZATION.BIDDING_METHODS": "Optimierungen", "BIDDING.ASSET_OPTIMIZATION.PERSPECTIVE": "Perspektive", "BIDDING.ASSET_OPTIMIZATION.ASSET": "<PERSON><PERSON>", "BIDDING.ASSET_OPTIMIZATION.SWING_LIMIT": "Swing Limit", "BIDDING.ASSET_OPTIMIZATION.SWING_LIMIT_YES_NO": "<PERSON>a", "BIDDING.ASSET_OPTIMIZATION.SWING_LIMIT_VALUE": "Wert", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST_TYPE": "Price Forecast Type", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST_TYPE_AUTOMATED": "Automated", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST_TYPE_MANUAL": "Manual", "BIDDING.ASSET_OPTIMIZATION.TENDER": "<PERSON>der", "BIDDING.ASSET_OPTIMIZATION.TENDERS": "Ausschreibungskombinationen", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.SHOW": "Preisprognose Dateiformat anzeigen", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.HIDE": "Preisprognose Dateiformat verbergen", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.AUTOMATED.LATEST": "Latest available price forecast: ", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_FORMAT_DETAIL": "Fur DC/DM/DR die CSV Datei sollte muss folgendes Format haben:", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_FORMAT": "datetime_from;datetime_to;Priceforecast_DCH;Priceforecast_DCL;Priceforecast_DMH;Priceforecast_DML;Priceforecast_DRH;Priceforecast_DRL", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_LIST_TITLE": "Nur die Namen der Spalten zählt, nicht ihre Reihenfolge.", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_1": "\"datetime_from\" Der Zeitstempel im ISO Format [yyyy-mm-ddThh:mm:ss+xx:xx]", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_2": "\"datetime_to\" Der Zeitstempel im ISO Format [yyyy-mm-ddThh:mm:ss+xx:xx]", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_3": "\"Priceforecast_DCH\" Dezimalwert der DC High Preisprognose", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_4": "\"Priceforecast_DCL\" Dezimalwert der DC Low Preisprognose", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_5": "\"Priceforecast_DMH\" Dezimalwert der DM High Preisprognose", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_6": "\"Priceforecast_DML\" Dezimalwert der DM Low Preisprognose", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_7": "\"Priceforecast_DRH\" Dezimalwert der DR High Preisprognose", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_8": "\"Priceforecast_DRL\" Dezimalwert der DR Low Preisprognose", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_FORMAT_DETAIL_EPEX30MIN": "For EPEX30MIN the CSV file should have the following format:", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_FORMAT_EPEX30MIN": "\"EPEX 30 Min FC File\",1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_1_EPEX30MIN": "\"EPEX 30 Min FC File\" column contains the day in format dd.mm.yyy e.g. 11.12.2024", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_2_EPEX30MIN": "\"1..50\" columns contains the price the corresponding settlement period", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_FORMAT_DETAIL_N2EX1H": "For N2EX1H the CSV file should have the following format:", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_FORMAT_N2EX1H": ",1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_1_N2EX1H": "The first column contains (with empty header) the day in format dd.mm.yyy e.g. 11.12.2024", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST.CSV_2_N2EX1H": "\"1..50\" columns contains the price the corresponding settlement period", "BIDDING.ASSET_OPTIMIZATION.PRICE_FORECAST": "Preisprognose", "BIDDING.ASSET_OPTIMIZATION.EXCLLDE_DC_HIGH_EFA_BLOCKS": "DC High EFA Blöcke ausschließen", "BIDDING.ASSET_OPTIMIZATION.EXCLLDE_DC_LOW_EFA_BLOCKS": "DC Low EFA Blöcke ausschließen", "BIDDING.ASSET_OPTIMIZATION.EXCLLDE_DM_HIGH_EFA_BLOCKS": "DM High EFA Blöcke ausschließen", "BIDDING.ASSET_OPTIMIZATION.EXCLLDE_DM_LOW_EFA_BLOCKS": "DM Low EFA Blöcke ausschließen", "BIDDING.ASSET_OPTIMIZATION.EXCLLDE_DR_HIGH_EFA_BLOCKS": "DR High EFA Blöcke ausschließen", "BIDDING.ASSET_OPTIMIZATION.EXCLLDE_DR_LOW_EFA_BLOCKS": "DR Low EFA Blöcke ausschließen", "BIDDING.ASSET_OPTIMIZATION.REQUEST_BIDDING_SUGGESTIONS": "Auktionsvorschläge anfordern", "BIDDING.ASSET_OPTIMIZATION.BIDDING_SUGGESTIONS_INFO": "Die Berechnung des Optimierungsergebnisses kann einige Zeit dauern. Bitte warten Sie. Aktualisieren Sie bitte die Seite, wenn das Ergebnis nicht nach 1-2 Minuten verfügbar ist.", "BIDDING.ASSET_OPTIMIZATION.TABLE.DATE": "Ausführen um", "BIDDING.ASSET_OPTIMIZATION.TABLE.STATUS": "Status", "BIDDING.ASSET_OPTIMIZATION.TABLE.ASSET": "<PERSON><PERSON>", "BIDDING.ASSET_OPTIMIZATION.TABLE.TENDERS": "Ausschreibungskombinationen", "BIDDING.ASSET_OPTIMIZATION.TABLE.USER": "<PERSON><PERSON><PERSON>", "BIDDING.ASSET_OPTIMIZATION.TABLE.NO_ASSET_OPTIMIZATIONS": "keine Anlagenoptimierungen", "BIDDING.ASSET_OPTIMIZATION.ACTION.REJECT_BIDS": "<PERSON><PERSON><PERSON>", "BIDDING.ASSET_OPTIMIZATION.ACTION.VALIDATE_BIDS": "Angebote validieren", "BIDDING.ASSET_OPTIMIZATION.ACTION.DOWNLOAD_MARKET_POSITIONS": "<PERSON><PERSON><PERSON><PERSON> her<PERSON>", "ASSET_OPTIMIZATIONS.BIDS_VALIDATED_SUCCESS": "Erfolgreich validierte Angebote", "ASSET_OPTIMIZATIONS.BIDS_VALIDATED_ERROR": "Fehler während der Validierung von Angeboten", "ASSET_OPTIMIZATIONS.BIDS_REJECTED_SUCCESS": "Erfolgreich abgelehnte Angebote", "ASSET_OPTIMIZATIONS.BIDS_REJECTED_ERROR": "Fehler während der Ablehnung von Angeboten", "NOMINATIONS.TITLE": "Nominierungen", "NOMINATIONS.TABS.ENTER_NOMINATIONS": "Nominierung eingeben", "NOMINATIONS.TABS.UPLOAD_MARKET_RESULTS": "Auktionsergebnisse hochladen", "NOMINATIONS.TABS.UPLOAD_NOMINATIONS_FILE": "Nominierungsdatei hochladen", "NOMINATIONS.TABS.UPLOAD_OPTI_RESULTS_FILE": "Upload Opti-results", "NOMINATIONS.TABS.DOWNLOAD_OPTI_RESULTS": "Download Opti-results", "NOMINATIONS.TABS.UPLOAD_PERF_DATA_FILE": "Upload Performance Data", "NOMINATIONS.TABS.UPLOAD_BOD_FILE": "Upload BOD Price&Volumes", "NOMINATIONS.TABS.UPLOAD_MARGIN_DATA_FILE": "Upload Margin Data", "NOMINATIONS.TABS.UPLOAD_DECLARATION_OF_UNAVAILABILITY": "Declare Unavailable NG", "NOMINATIONS.LIST.TITLE": "Nominierungsübersicht", "NOMINATIONS.LIST.FILTER.START_DATE": "Zeitraum", "NOMINATIONS.LIST.FILTER.ENERGY_DIRECTION": "<PERSON><PERSON><PERSON>", "NOMINATIONS.LIST.FILTER.SELECT_ENERGY_DIRECTION": "Alle", "NOMINATIONS.LIST.FILTER.DISPATCH_GROUP": "Abrufgruppe", "NOMINATIONS.LIST.FILTER.SHOW_DELETED": "Zeige gelöschte Nominierungen", "NOMINATIONS.LIST.TABLE.DISPATCH_GROUP": "Abrufgruppe", "NOMINATIONS.LIST.TABLE.START": "Begin<PERSON>", "NOMINATIONS.LIST.TABLE.END": "<PERSON><PERSON>", "NOMINATIONS.LIST.TABLE.ENERGY_DIRECTION": "<PERSON><PERSON><PERSON>", "NOMINATIONS.LIST.TABLE.FLEX_CAPACITY": "Flex-Kapazität", "NOMINATIONS.LIST.TABLE.USE_FOR_ASSET_PRICE": "Relevant für APDU", "NOMINATIONS.LIST.TABLE.ENERGY_PRICE": "Arbeitspreis", "NOMINATIONS.LIST.TABLE.CAPACITY_PRICE": "Leistungspreis", "NOMINATIONS.LIST.TABLE.DELETED": "Gelöscht", "NOMINATIONS.LIST.TABLE.negative": "negativ", "NOMINATIONS.LIST.TABLE.positive": "positiv", "NOMINATIONS.LIST.TABLE.PER_PAGE": "Einträge pro Seite", "NOMINATIONS.ENTER_NOMINATIONS.TITLE": "Nominierung eingeben", "NOMINATIONS.ENTER_NOMINATIONS.VIEW_DETAILS": "Details anzeigen", "NOMINATIONS.ENTER_NOMINATIONS.DISPATCH_GROUP": "Abrufgruppe", "NOMINATIONS.ENTER_NOMINATIONS.INTERVAL": "Zeitraum", "NOMINATIONS.ENTER_NOMINATIONS.ENERGY_DIRECTION": "<PERSON><PERSON><PERSON>", "NOMINATIONS.ENTER_NOMINATIONS.FLEX_CAPACITY": "Flex-Kapazität", "NOMINATIONS.ENTER_NOMINATIONS.ENERGY_PRICE": "Arbeitspreis", "NOMINATIONS.ENTER_NOMINATIONS.CAPACITY_PRICE": "Leistungspreis", "NOMINATIONS.ENTER_NOMINATIONS.ASSETS": "<PERSON><PERSON><PERSON>", "NOMINATIONS.ENTER_NOMINATIONS.ALLOW_MODIFICATIONS_WITH_IMEMDIATE_EFFECT": "Änderungen mit sofortiger Gültigkeit zulassen", "NOMINATIONS.ENTER_NOMINATIONS.USE_FOR_ASSET_PRICE": "Relevant für APDU", "NOMINATIONS.ENTER_NOMINATIONS.CREATE_NOMINATION": "Nominierung erstellen", "NOMINATIONS.ENTER_NOMINATIONS.SAVE_NOMINATION": "Nominierung erstellen", "NOMINATIONS.UPLOAD_MARKET_RESULTS.TITLE": "Auktionsergebnisse auswählen", "NOMINATIONS.UPLOAD_MARKET_RESULTS.AUCTION": "Ausschreibung", "NOMINATIONS.UPLOAD_MARKET_RESULTS.VIEW_DETAILS": "Details anzeigen", "NOMINATIONS.UPLOAD_MARKET_RESULTS.UPLOAD": "Auktionsergebnisse hochladen", "NOMINATIONS.UPLOAD_MARKET_RESULTS.UPLOAD_INFO": "Please wait. The upload might take a while.", "NOMINATIONS.UPLOAD_MARKET_RESULTS.SUCCESS_UPLOADED": "Die Auktionsergebnisse wurden erfolgreich hochgeladen.", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.TITLE": "Nominierungsdatei auswählen", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.SHOW": "Dateiformat zeigen", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.HIDE": "Dateiformat zuklapen", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_FORMAT_DETAIL": "Die CSV-Datei muss das folgende Format aufweisen:", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_FORMAT": "<PERSON><PERSON> von [yyyy-mm-ddThh:mm:ss+xx:xx], <PERSON><PERSON> bis [yyyy-mm-ddThh:mm:ss+xx:xx], DG-Id, Richtung, Flex-Kapazität (MW), Arbeitspreis (¤/MWh), Leistungspreis (¤/MW), Standard für Energiepreis, Anlagen-ID, Auktions-ID", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_LIST_TITLE": "Nur die Reihenfolge der Spalten zählt, nicht ihre Namen.", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_1": "Die Spalte \"Standard für Arbeitspreis\" muss auf 1 gesetzt werden, wenn diese Nominierung der Standard für die Arbeitspreiskalkulation sein soll, ansonsten auf 0.", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_2": "Die Spalte \"An<PERSON>n\" kann eine mit Semikola getrennte Liste von Ids (z.B. 10;11;12) enthalten, falls ids vorhanden sind.", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_3": "Die Spalte \"Id\" sollte nur verwendet werden, wenn Sie bereits vorhandene Einträge ändern wollen. Andern<PERSON> lassen Sie sie leer.", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.CSV_4": "Die Spalte \"Auktions-ID\" sollte nur verwendet werden, wenn Sie die Auktions-ID angeben möchten. Andernfalls lassen Sie sie leer.", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.ALLOW_MODIFICATIONS_WITH_IMMEDIATE_EFFECT": "Änderungen mit sofortiger Gültigkeit zulassen", "NOMINATIONS.UPLOAD_NOMINATIONS_FILE.UPLOAD_NOMINATIONS_FILE": "Nominierungsdatei hochladen", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.TITLE": "Select the file containing the Opti-results", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.SHOW": "Show File Format", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.HIDE": "Hide File Format", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_FORMAT_DETAIL": "The Excel file should have the following format:", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_FORMAT": "datetime;soc | activation_dc_pos | activation_dc_neg | activation_dm_pos | activation_dm_neg | activation_dr_pos | activation_dr_neg | available_char_power | available_dischar_power", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_1": "\"datetime\" the timestamp [yyyy-mm-ddThh:mm:ss+xx:xx] for settlement period", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_2": "\"soc\" the target state of charge e.g. 0.6", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_3": "\"activation_dc_pos\" the activation range for DC positive e.g. 10000 ", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_4": "\"activation_dc_neg\" the activation range for DC negative e.g. 0", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_5": "\"activation_dc_pos\" the activation range for DM positive e.g. 10000 ", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_6": "\"activation_dc_neg\" the activation range for DM negative e.g. 0", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_7": "\"activation_dc_pos\" the activation range for DR positive e.g. 10000 ", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_8": "\"activation_dc_neg\" the activation range for DR negative e.g. 0", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_9": "\"available_charge_power\" the available charge power e.g. 10000", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.CSV_10": "\"available_discharge_power\" the available discharge power e.g. 0", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.ASSET": "<PERSON><PERSON>", "NOMINATIONS.UPLOAD_OPTI_RESULTS_FILE.UPLOAD_OPTI_RESULTS_FILE": "Upload Opti-results File", "NOMINATIONS.DOWNLOAD_OPTI_RESULTS.TITLE": "Select the asset and the day to download the Opti-results", "NOMINATIONS.DOWNLOAD_OPTI_RESULTS.DOWNLOAD": "Download Opti-results", "NOMINATIONS.DOWNLOAD_OPTI_RESULTS_FILE.DATE": "Day", "NOMINATIONS.DOWNLOAD_OPTI_RESULTS.FILE_WITH_GAPS": "An diesem Tag gibt es zeitliche Lücken in der Datei. Vor einem erneuten Hochladen müssen diese Lücken gefüllt werden.", "NOMINATIONS.DOWNLOAD_OPTI_RESULTS.NO_FILE": "Für die ausgewählte Anlage und Datum ist heute keine Datei verfügbar", "NOMINATIONS.UPLOAD_PERF_DATA_FILE.TITLE": "Select the file containing the performance data", "NOMINATIONS.UPLOAD_PERF_DATA_FILE.SHOW": "Show File Format", "NOMINATIONS.UPLOAD_PERF_DATA_FILE.HIDE": "Hide File Format", "NOMINATIONS.UPLOAD_PERF_DATA_FILE.FILE_FORMAT_DETAIL": "Files at the moment could be *.csv, *.txt and *.gz. The filename must match the expected format related to the asset:", "NOMINATIONS.UPLOAD_PERF_DATA_FILE.FILE_FORMAT_DETAIL_1": "For asset EONDC-01: perfdata_identifier_yyyymmddThh0000Z_yyyymmddThh0000Z_yyyymmddThhmmss.csv.gz or perfdata_identifier_yyyymmddThh0000Z_yyyymmddThh0000Z_yyyymmddThhmmss.csv", "NOMINATIONS.UPLOAD_PERF_DATA_FILE.FILE_FORMAT_DETAIL_2": "For asset EONDC-02: EONDC-02_yyyymmddhh0000_perfmonv1.csv", "NOMINATIONS.UPLOAD_PERF_DATA_FILE.FILE_FORMAT_DETAIL_3": "For asset EONDC-03: EONDC-03_yyyy-mm-dd_hh.00.00.txt", "NOMINATIONS.UPLOAD_PERF_DATA_FILE.ASSET": "<PERSON><PERSON>", "NOMINATIONS.UPLOAD_PERF_DATA_FILE.UPLOAD_FILE": "Upload Performance Data File", "NOMINATIONS.UPLOAD_BOD_FILE.TITLE": "Select the file containing the BOD prices and volumes", "NOMINATIONS.UPLOAD_BOD_FILE.SHOW": "Show File Format", "NOMINATIONS.UPLOAD_BOD_FILE.HIDE": "Hide File Format", "NOMINATIONS.UPLOAD_BOD_FILE.CSV_FORMAT_DETAIL": "The Excel file should have the following format:", "NOMINATIONS.UPLOAD_BOD_FILE.CSV_FORMAT": "Assetid | Datetime | Pair | Offer_Price | Bid_Price | Volume", "NOMINATIONS.UPLOAD_BOD_FILE.CSV_1": "\"Assetid\": asset id (e.g. external id like EONDC-01)", "NOMINATIONS.UPLOAD_BOD_FILE.CSV_2": "\"Datetime\": timestamp for the start of the settlement period", "NOMINATIONS.UPLOAD_BOD_FILE.CSV_3": "\"Pair\": identifier for the \"pair\" (+ or - integer number)", "NOMINATIONS.UPLOAD_BOD_FILE.CSV_4": "\"Offer_Price\": offer_price in GBP/MW (value with 2 decimal places)", "NOMINATIONS.UPLOAD_BOD_FILE.CSV_5": "\"Bid_Price\": bid_price in GBP/MW (value with 2 decimal places)", "NOMINATIONS.UPLOAD_BOD_FILE.CSV_6": "\"Volume\": volume in MW (integer value). Should have same sign as the \"pair\" (+ or -)", "NOMINATIONS.UPLOAD_BOD_FILE.UPLOAD_BOD_FILE": "Upload BOD File", "NOMINATIONS.UPLOAD_MARGIN_DATA_FILE.TITLE": "Select the file containing the margin data", "NOMINATIONS.UPLOAD_MARGIN_DATA_FILE.SHOW": "Show File Format", "NOMINATIONS.UPLOAD_MARGIN_DATA_FILE.HIDE": "Hide File Format", "NOMINATIONS.UPLOAD_MARGIN_DATA_FILE.CSV_FORMAT_DETAIL": "The CSV file should have the following format:", "NOMINATIONS.UPLOAD_MARGIN_DATA_FILE.CSV_FORMAT": "<PERSON>,Total Offer Mar<PERSON>,Total Bid Margin", "NOMINATIONS.UPLOAD_MARGIN_DATA_FILE.CSV_1": "\"SP\": the settlement number (1 to 50)", "NOMINATIONS.UPLOAD_MARGIN_DATA_FILE.CSV_2": "\"Total Offer Margin\": numeric value", "NOMINATIONS.UPLOAD_MARGIN_DATA_FILE.CSV_3": "\"Total Bid Margin\": numeric value", "NOMINATIONS.UPLOAD_MARGIN_DATA_FILE.UPLOAD_MARGIN_DATA_FILE": "Upload margin data file", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.TITLE": "Select the file containing the availability/unavailability declaration towards National Grid", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.SHOW": "Show File Format", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.HIDE": "Hide File Format", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.ASSET": "<PERSON><PERSON>", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT_DETAIL": "CSV files shall be named in the format UID_Timestamp_redecv1.csv. UID is the unique identifier assigned to the Response Unit. Timestamp is the file submission datetime (UTC) and is in the format YYYYMMDDHHMMSS where:", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT_DETAIL_1": "YYYY is the 4-digit year", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT_DETAIL_2": "MM is the month of year zero padded to 2 characters (00-12", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT_DETAIL_3": "DD is the day of month zero padded to 2 characters (01-31", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT_DETAIL_4": "HH is the hour of day in 24-hour format zero padded to 2 characters (00-23)", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT_DETAIL_5": "MM is the minutes past hour zero padded to 2 characters (00-59)", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT_DETAIL_6": "SS is the seconds past minute zero padded to 2 characters (00-59)", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT_DETAIL_7": "An example filename for UID 'ABCDE' and timestamp '15/09/2020 17:20:00' is: ABCDE_20200915172000_redecv1.csv", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT_DETAIL_8": "Each CSV file can contain data for multiple re-declarations. Each line would represent one line of re-declaration per response unit and service. Each CSV file may contain a maximum of 100 lines of data. If more than 100 rows of data are required to be submitted then the re-declaration file needs to be broken into multiple files.", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT_DETAIL_9": "The first line of the CSV file shall contain the header line. Notification of Availability/Outage CSV File Format | January 2021 The headers are listed below. All headers must be in lower case and must match the exact naming and order specified. All headers must be included even if some are not applicable to the unit.", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_FORMAT": "unit,t_start,t_end,available_capacity,service", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_1": "\"unit\": Unique identifier assigned to the Response Unit. e.g. ABCDE)", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_2": "\"t_start\": ISO 8601 timestamp in UTC including milliseconds for when the redeclaration starts. e.g. 2021-12-04T16:00:00.000Z)", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_3": "\"t_end\": ISO 8601 timestamp in UTC including milliseconds for when the redeclaration end. e.g. 2021-12-04T17:30:00.000Z)", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_4": "\"available_capacity\": Actual capacity in MW to 2 decimal places. e.g. 27.13)", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.CSV_5": "\"service\": One of the DCL, DCH, DML, DMH, DRL or DRH service types for the availability declared. e.g. ADCH)", "NOMINATIONS.UPLOAD_DECLARATION_UNAVAILABILITY.UPLOAD_FILE": "Upload declaration file", "REPORTS.TSO_REPORT.TITLE": "ÜNB-Bericht auswählen", "REPORTS.TSO_REPORT.TSO": "ÜNB", "REPORTS.TSO_REPORT.MARKET": "Produkt", "REPORTS.TSO_REPORT.FILE": "<PERSON><PERSON>", "REPORTS.TSO_REPORT.UPLOAD_TSO_REPORT": "ÜNB-Bericht hochladen", "REPORTS.TSO_REPORT.TABLE.TITLE": "ÜNB-Berichte", "REPORTS.TSO_REPORT.TABLE.FILTER.DATE": "Datum", "REPORTS.TSO_REPORT.TABLE.REPORT_DATE": "Berichtsdatum", "REPORTS.TSO_REPORT.TABLE.TSO": "ÜNB", "REPORTS.TSO_REPORT.TABLE.MARKET": "Produkt", "REPORTS.TSO_REPORT.TABLE.CREATED": "Erstellt am", "REPORTS.TSO_REPORT.TABLE.CONTROL_REPORT": "Kontrollbericht", "REPORTS.TSO_REPORT.ERRORS.CONTENTS": "Inhalt", "ALLOCATIONS.TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ALLOCATIONS.TABS.ENTER_ALLOCATION": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON>ben", "ALLOCATIONS.TABS.UPLOAD_ALLOCATION_FILE": "Zuordnungsdatei hochladen", "ALLOCATIONS.VISUALIZE_ALLOCATIONS.INTERVAL": "Zeitraum", "ALLOCATIONS.VISUALIZE_ALLOCATIONS.DISPATCH_GROUP": "Abrufgruppe", "ALLOCATIONS.VISUALIZE_ALLOCATIONS.SHOW": "Anzeigen", "ALLOCATIONS.VISUALIZE_ALLOCATIONS.DOWNLOAD": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ALLOCATIONS.VISUALIZE_ALLOCATIONS.ASSETS": "<PERSON><PERSON><PERSON>", "ALLOCATIONS.VISUALIZE_ALLOCATIONS.ALLOCATED_FLEX": "Zugewiesene Flexibilität", "ALLOCATIONS.VISUALIZE_ALLOCATIONS.NEGATIVE_NOMINATED_FLEX": "Nominierte Flexibilität (neg)", "ALLOCATIONS.VISUALIZE_ALLOCATIONS.POSITIVE_NOMINATED_FLEX": "Nominierte Flexibilität (pos)", "ALLOCATIONS.VISUALIZE_ALLOCATIONS.NOMINATION": "Nominierung", "ALLOCATIONS.VISUALIZE_ALLOCATIONS.VALUE_AXIS": "<PERSON><PERSON>", "ALLOCATIONS.ENTER_ALLOCATION.TITLE": "<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON>ben", "ALLOCATIONS.ENTER_ALLOCATION.VIEW_DETAILS": "Details anzeigen", "ALLOCATIONS.ENTER_ALLOCATION.INTERVAL": "Zeitraum", "ALLOCATIONS.ENTER_ALLOCATION.ASSET": "<PERSON><PERSON><PERSON>", "ALLOCATIONS.ENTER_ALLOCATION.DISPATCH_GROUP": "Abrufgruppe", "ALLOCATIONS.ENTER_ALLOCATION.ALLOW_MODIFICATIONS_WITH_IMMEDIATE_EFFECT": "Änderungen mit sofortiger Gültigkeit zulassen", "ALLOCATIONS.ENTER_ALLOCATION.CREATE_ALLOCATION": "<PERSON>uordnung erstellen", "ALLOCATIONS.ENTER_ALLOCATION.DEALLOCATE": "<PERSON><PERSON><PERSON><PERSON><PERSON> entfernen", "ALLOCATIONS.UPLOAD_ALLOCATION.TITLE": "Zuordnungsdatei auswählen", "ALLOCATIONS.UPLOAD_ALLOCATION.VIEW_DETAILS": "Details anzeigen", "ALLOCATIONS.UPLOAD_ALLOCATION.SHOW": "Dateiformat zuklapen", "ALLOCATIONS.UPLOAD_ALLOCATION.HIDE": "Dateiformat zeigen", "ALLOCATIONS.UPLOAD_ALLOCATION.CSV_FORMAT": "Die CSV-Datei muss das folgende Format aufweisen:", "ALLOCATIONS.UPLOAD_ALLOCATION_FILE.CSV_FORMAT_DETAIL": "<PERSON><PERSON> von [yyyy-mm-ddThh:mm:ss+xx:xx], <PERSON><PERSON> bis [yyyy-mm-ddThh:mm:ss+xx:xx], <PERSON><PERSON><PERSON><PERSON><PERSON>, DG-<PERSON>d, <PERSON><PERSON><PERSON><PERSON><PERSON>", "ALLOCATIONS.UPLOAD_ALLOCATION_FILE.CSV_LIST_TITLE": "Nur die Reihenfolge der Spalten zählt, nicht ihre Namen.", "ALLOCATIONS.UPLOAD_ALLOCATION_FILE.CSV_1": "Die Spalte \"<PERSON>uordnen\" muss auf 1 gesetzt werden, um die Anlage zuzuordnen. Wenn der Wert auf 0 gesetzt wird, wird eine vorhandene Zuordnung entfernt.", "ALLOCATIONS.UPLOAD_ALLOCATION_FILE.EXAMPLE": "Beispielda<PERSON><PERSON>", "ALLOCATIONS.UPLOAD_ALLOCATION_FILE.ALLOW_MODIFICATIONS_WITH_IMMEDIATE_EFFECT": "Änderungen mit sofortiger Gültigkeit zulassen", "ALLOCATIONS.UPLOAD_ALLOCATION_FILE.UPLOAD_ALLOCATION_FILE": "Zuordnungsdatei hochladen", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.TRIGGERS.TYPES.AllocationType": "Eine neue Zuordnung wurde manuell eingegeben", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.TRIGGERS.TYPES.ComputationTailed_type": "Vorherige Automatische Zuordnung ist fehlgeschlagen", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.TRIGGERS.TYPES.NominationType": "Eine neue Zuordnung wurde eingegeben", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.TRIGGERS.TYPES.FlexType": "Nicht genügend Flex im Vergleich zum Nominierten", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.TRIGGERS.TYPES.TimeType": "Viertelstündliche Zuordnung", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.TRIGGERS.TYPES.AssetBackFromFaultType": "Anlage kommt aus einem Fehler zurück", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.TRIGGERS_TITLE": "Triggers", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.DISPATCH_GROUPS_TITLE": "Abrufgruppen", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.START": "<PERSON>", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.END": "Bis", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.FALLBACK": "Fallback <PERSON>", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.IMPORT": "<PERSON><PERSON><PERSON>", "ALLOCATIONS.AUTOMATIC_ALLOCATIONS.NOT_FOUND": "Nichts gefunden", "AUTOMATIC_REPORT_EMAILINGS.TITLE": "Berichte und Benachrichtigungen", "AUTOMATIC_REPORT_EMAILINGS.TABS.SCHEDULING_REPORT": "Fahrplanberichte", "AUTOMATIC_REPORT_EMAILINGS.TABS.ALLOCATION": "Z<PERSON><PERSON>nungen", "AUTOMATIC_REPORT_EMAILINGS.TABS.ROLLUPS": "Rollups", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.TITLE": "Berichte und Benachrichtigungen – E-Mails", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.INSTRUCTIONS": "Mehrere E-Mail-Adressen bitte durch Semikolon oder Zeilenumbruch trennen.", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.EDG_REPORTS_RECIPIENTS": "Empfänger der EDG-Berichte", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.EDG_REPORTS_ACTIVE": "EDG-Berichte versenden", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.UGC_REPORTS_RECIPIENTS": "Empfänger der UGC-Berichte", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.UGC_REPORTS_ACTIVE": "UGC-Berichte versenden", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.TSO_SCHEDULING_UPLOAD_ALARM_RECIPIENTS": "Empfänger des ÜNB-Fahrplanalarms", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.TSO_SCHEDULING_UPLOAD_ALARM_ACTIVE": "ÜNB-Fahrplanalarm versenden", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.THIRD_PARTY_REPORTS_RECIPIENTS": "Empfänger der Berichte von Bilanzkreisen Dritter", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.THIRD_PARTY_REPORTS_RECIPIENTS_ACTIVE": "Berichte von Bilanzkreisen Dritter versenden", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.ONLY_IN_CASE_OF_ACTIVATION": "Nur bei Aktivierung", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.SEND_ALL_EMAILS_TO": "Alle E-Mails senden an", "AUTOMATIC_REPORT_EMAILINGS.SCHEDULING_REPORT.UPDATE": "Aktualisieren", "AUTOMATIC_REPORT_EMAILINGS.ALLOCATIONS.TITLE": "Berichte und Benachrichtigungen – E-Mails", "AUTOMATIC_REPORT_EMAILINGS.ALLOCATIONS.AUTOMATIC_ALLOCATIONS_NOTIFICATIONS_ACTIVE": "Bei automatischer Allokation Benachrichtigung versenden", "AUTOMATIC_REPORT_EMAILINGS.ALLOCATIONS.AUTOMATIC_ALLOCATIONS_NOTIFICATIONS_RECIPIENTS": "Empfänger der Benachrichtigung bei automatischer Allokation", "AUTOMATIC_REPORT_EMAILINGS.ALLOCATIONS.INSTRUCTIONS": "Mehrere E-Mail-Adressen bitte durch Semikolon oder Zeilenumbruch trennen.", "AUTOMATIC_REPORT_EMAILINGS.ALLOCATIONS.UPDATE": "Aktualisieren", "AUTOMATIC_REPORT_EMAILINGS.ROLLUPS.TITLE": "Berichte und Benachrichtigungen – E-Mails", "AUTOMATIC_REPORT_EMAILINGS.ROLLUPS.RECIPIENTS": "Empfänger der Rollup-Benachrichtigungen", "AUTOMATIC_REPORT_EMAILINGS.ROLLUPS.ROLLUP_NOTIFICATIONS_ACTIVE": "Rollup-Benachrichtigungen versenden", "AUTOMATIC_REPORT_EMAILINGS.ROLLUPS.INSTRUCTIONS": "Mehrere E-Mail-Adressen bitte durch Semikolon oder Zeilenumbruch trennen.", "AUTOMATIC_REPORT_EMAILINGS.ROLLUPS.UPDATE": "Aktualisieren", "AUTOMATIC_REPORT_EMAILINGS.ROLLUPS.UPDATED": "Aktualiserung Erfolgreich", "HISTORY.TITLE": "Historie – Logs", "HISTORY.TABS.NOMINATIONS": "Nominierungen", "HISTORY.TABS.MANUAL_ALLOCATIONS": "<PERSON><PERSON>", "HISTORY.TABS.AUTOMATIC_ALLOCATIONS": "Automatische Zuordnungen", "HISTORY.TABS.AUCTIONS": "Auktionen", "HISTORY.TABS.REPORTING_EMAILS": "Berichts-E-Mails", "HISTORY.TABS.DLM_CHARGING_SESSIONS": "DLM <PERSON>", "HISTORY.NOMINATIONS.TABLE.PERFORMED_ON": "Datum", "HISTORY.NOMINATIONS.TABLE.USER": "<PERSON><PERSON><PERSON>", "HISTORY.NOMINATIONS.TABLE.CONTENT": "Herkunft", "HISTORY.NOMINATIONS.TABLE.SUCCESFULLY_IMPORTED": "Erfolgreich importiert", "HISTORY.NOMINATIONS.TABLE.ERRORS": "<PERSON><PERSON>", "HISTORY.NOMINATIONS.TABLE.WARNINGS": "Warnungen", "HISTORY.NOMINATIONS.TABLE.DISTRIBUTED_UNITS": "Distributed units", "HISTORY.NOMINATIONS.TABLE.UI_UPDATE": "Beispielda<PERSON><PERSON>", "HISTORY.NOMINATIONS.TABLE.FILE_IMPORT": "File", "HISTORY.NOMINATIONS.DETAIL.TITLE": "Historie - Benutzung - Nominierungen", "HISTORY.NOMINATIONS.DETAIL.TABLE.ERROR_DESCRIPTION": "Error Des<PERSON>", "HISTORY.NOMINATIONS.DETAIL.TABLE.WARNING_DESCRIPTION": "Warning Description", "HISTORY.NOMINATIONS.DETAIL.TABLE.DG": "DG", "HISTORY.NOMINATIONS.DETAIL.TABLE.DETAILS": "Details", "HISTORY.NOMINATIONS.DETAIL.TABLE.DISTRIBUTED_UNITS": "Nominierungen", "HISTORY.NOMINATIONS.DETAIL.TABLE.DISPATCH_GROUP": "Abrufgruppe fehlt", "HISTORY.NOMINATIONS.DETAIL.TABLE.START": "Startzeitpunkt ungültig", "HISTORY.NOMINATIONS.DETAIL.TABLE.END": "Endzeitpunkt ungültig", "HISTORY.NOMINATIONS.DETAIL.TABLE.ENERGY_DIRECTION": "<PERSON><PERSON><PERSON>", "HISTORY.NOMINATIONS.DETAIL.TABLE.FLEX_CAPACITY": "Flex-Kapazität MW", "HISTORY.NOMINATIONS.DETAIL.TABLE.USED_FOR_ASSET_PRICE": "Use for Asset Price", "HISTORY.NOMINATIONS.DETAIL.TABLE.ENERGY_PRICE": "Arbeitspreis (¤ / MWh)", "HISTORY.NOMINATIONS.DETAIL.TABLE.CAPACITY_PRICE": "Leistungspreis (¤ / MW)", "HISTORY.NOMINATIONS.DETAIL.VALIDATION": "Validierung", "HISTORY.NOMINATIONS.DETAIL.ERRORS": "Fehlermeldungen", "HISTORY.NOMINATIONS.DETAIL.WARNINGS": "Warnungen", "HISTORY.NOMINATIONS.DETAIL.CONTENT_TYPE.NA": "N/A", "HISTORY.NOMINATIONS.DETAIL.CONTENT_TYPE.ui_update": "UI", "HISTORY.NOMINATIONS.DETAIL.CONTENT_TYPE.file_import": "File / API", "HISTORY.NOMINATIONS.DETAIL.CONTENT_TYPE.automatic_bids_conversion": "V2G", "HISTORY.NOMINATIONS.DETAIL.CONTENT_TYPE.vpp_migration": "Ein<PERSON>ig", "HISTORY.NOMINATIONS.DETAIL.TYPE": "<PERSON><PERSON>", "HISTORY.NOMINATIONS.DETAIL.PERFORMED_ON": "Durchgeführt am", "HISTORY.NOMINATIONS.DETAIL.USER": "<PERSON><PERSON><PERSON>", "HISTORY.NOMINATIONS.DETAIL.FILE_IMPORT": "Nominations File", "HISTORY.NOMINATIONS.DETAIL.UI_IMPORT": "Form", "HISTORY.MANUAL_ALLOCATIONS.TABLE.PERFORMED_ON": "Datum", "HISTORY.MANUAL_ALLOCATIONS.TABLE.USER": "<PERSON><PERSON><PERSON>", "HISTORY.MANUAL_ALLOCATIONS.TABLE.CONTENT": "Herkunft", "HISTORY.MANUAL_ALLOCATIONS.TABLE.SUCCESFULLY_IMPORTED": "Erfolgreich importiert", "HISTORY.MANUAL_ALLOCATIONS.TABLE.ERRORS": "<PERSON><PERSON>", "HISTORY.MANUAL_ALLOCATIONS.TABLE.WARNINGS": "Warnungen", "HISTORY.MANUAL_ALLOCATIONS.TABLE.UI_UPDATE": "Beispielda<PERSON><PERSON>", "HISTORY.MANUAL_ALLOCATIONS.TABLE.FILE_IMPORT": "File / API", "HISTORY.MANUAL_ALLOCATIONS.CONTENT_TYPE.NA": "N/A", "HISTORY.MANUAL_ALLOCATIONS.CONTENT_TYPE.ui_update": "UI", "HISTORY.MANUAL_ALLOCATIONS.CONTENT_TYPE.file_import": "<PERSON><PERSON>", "HISTORY.MANUAL_ALLOCATIONS.CONTENT_TYPE.aas_update": "AAS", "HISTORY.MANUAL_ALLOCATIONS.CONTENT_TYPE.automatic_bids_conversion": "V2G", "HISTORY.MANUAL_ALLOCATIONS.CONTENT_TYPE.vpp_migration": "Ein<PERSON>ig", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.TITLE": "Historie <PERSON> <PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON>", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.VALIDATION": "Validierung", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.ERRORS": "<PERSON><PERSON>", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.WARNINGS": "Warnungen", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.TYPE": "Herkunft", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.ALLOCATIONS_FILE_IMPORT": "File / API", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.ALLOCATIONS_UI_IMPORT": "Formular", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.PERFORMED_ON": "Datum", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.PERFORMED_ON_FORMAT": "DD.MM.YYYY HH:mm", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.USER": "<PERSON><PERSON><PERSON>", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.ERROR_DESCRIPTION": "Fehlerbeschreibung", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.ASSET": "<PERSON><PERSON>", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.DG": "DG", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.WARNING_DESCRIPTION": "Warning Description", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.ALLOCATIONS": "Z<PERSON><PERSON>nungen", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.START": "Begin<PERSON>", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.END": "<PERSON><PERSON>", "HISTORY.MANUAL_ALLOCATIONS.DETAIL.TABLE.DISPATCH_GROUP": "Abrufgruppe", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.START": "Zeitraum", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.TYPE": "<PERSON><PERSON>", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.TYPE.CHOOSE": "<PERSON><PERSON><PERSON><PERSON>", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.TYPE.FALLBACK": "<PERSON><PERSON><PERSON>", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.TYPE.STANDARD": "Standard", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.STATUS.CHOOSE": "<PERSON><PERSON><PERSON><PERSON>", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.STATUS.STARTED": "Gestartet", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.STATUS.SUCCESS": "Erfolgreich", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.STATUS.FALLBACK": "<PERSON><PERSON><PERSON>", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.STATUS.DATAPREPARATIONFAILURE": "Fehler bei Datenaufbereitung", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.STATUS.FAILURE": "<PERSON><PERSON>", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.STATUS.ALLOCATIONNOTACCEPTED": "Zuordnung nicht ak<PERSON><PERSON>iert", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.STATUS": "Status", "HISTORY.AUTOMATIC_ALLOCATIONS.FILTERS.DISPATCH_GROUP": "Abrufgruppen", "HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.START": "Begin<PERSON>", "HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.END": "<PERSON><PERSON>", "HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.FALLBACK_START": "Fallback beginn", "HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.STATUS": "Status", "HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.DISPATCH_GROUP": "Abrufgruppen", "HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.TSO": "ÜNB", "HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.IMPORT_RESULT": "Importereignisse", "HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.IMPORT_RESULT_SUCESS": "Success", "HISTORY.AUTOMATIC_ALLOCATIONS.TABLE.PER_PAGE": "Einträge pro Seite", "HISTORY.AUCTIONS.TABLE.PERFORMED_ON": "Datum", "HISTORY.AUCTIONS.TABLE.USER": "<PERSON><PERSON><PERSON>", "HISTORY.AUCTIONS.TABLE.DELIVERY_DATE": "Lieferdatum", "HISTORY.AUCTIONS.TABLE.LINK.META_DATA": "<PERSON><PERSON><PERSON>", "HISTORY.AUCTIONS.TABLE.LINK.AUCTION": "Angebotsdaten", "HISTORY.AUCTIONS.TABLE.PER_PAGE": "Einträge pro Seite", "HISTORY.REPORTING_EMAILS.FILTERS.REPORT_TYPE": "Berichtstyp", "HISTORY.REPORTING_EMAILS.FILTERS.TIME": "Zeitpunkt", "HISTORY.REPORTING_EMAILS.FILTERS.STATUS": "Status", "HISTORY.REPORTING_EMAILS.FILTERS.RECIPIENTS": "<PERSON><PERSON><PERSON><PERSON>", "HISTORY.REPORTING_EMAILS.FILTERS.ACTIVE": "Aktiv", "HISTORY.REPORTING_EMAILS.TABLE.TIME": "Zeitpunkt", "HISTORY.REPORTING_EMAILS.TABLE.REPORT_DATE": "Berichtszeitpunkt", "HISTORY.REPORTING_EMAILS.TABLE.CREATED": "Erstellt am", "HISTORY.REPORTING_EMAILS.TABLE.STATUS": "Status", "HISTORY.REPORTING_EMAILS.TABLE.REPORT_TYPE": "Berichtstyp", "HISTORY.REPORTING_EMAILS.TABLE.PARAMS": "Parameter", "HISTORY.REPORTING_EMAILS.TABLE.RECIPIENTS": "<PERSON><PERSON><PERSON><PERSON>", "HISTORY.REPORTING_EMAILS.TABLE.ACTIVE": "Aktiv", "HISTORY.REPORTING_EMAILS.TABLE.DOWNLOAD": "Download", "HISTORY.REPORTING_EMAILS.TABLE.PER_PAGE": "Einträge pro Seite", "HISTORY.REPORTING_EMAILS.DETAIL.TITLE": "Historie - Benutzung - Berichts-E-Mail", "HISTORY.ROLLUPS.TABS.ASSET_ROLLUPS": "Anlagen-Rollups", "HISTORY.ROLLUPS.TABS.DISPATCH_GROUP_ROLLUPS": "Abrufgruppen-Rollups", "HISTORY.ROLLUPS.TABS.ROLLUP_ERRORS": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HISTORY.ROLLUPS.ERRORS.FILTER.TIME_DATE": "Zeitraum", "HISTORY.ROLLUPS.ERRORS.TABLE.TIME_DATE": "Zeit und Datum", "HISTORY.ROLLUPS.ERRORS.TABLE.DESCRIPTION": "Beschreibung", "HISTORY.ROLLUPS.ERRORS.TABLE.PER_PAGE": "Einträge pro Seite", "HISTORY.NOMINATIONS.FILE_IMPORT": "Nomination File", "HISTORY.MANUAL_ALLOCATIONS.UI_UPDATE": "Form", "HISTORY.MANUAL_ALLOCATIONS.FILE_IMPORT": "Allocation File", "HISTORY.ROLLUPS.TITLE": "Historie <PERSON><PERSON>", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.INTERVAL": "Zeitraum", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.STATUS": "Status", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.STATUS.": "", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.STATUS.PROCESSING": "In Bearbeitung", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.STATUS.COMPLETED": "Abgeschlossen", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.STATUS.SCHEDULED": "<PERSON><PERSON><PERSON>", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.ASSET_ROLLUP_GROUPINGS": "Anlagen- / Rollup-Gruppierung", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.DISPATCH_GROUPS": "Abrufgruppen", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.ROLLUP_METRICS": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.FILTERS.TRIGGERED_BY": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.INTERVAL": "Zeitraum", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.STATUS": "Status", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.ASSET_ROLLUP_GROUPINGS": "Anlagen- / Rollup-Gruppierung", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.DISPATCH_GROUPS": "Abrufgruppen", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.ROLLUP_METRICS": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.TRIGGERED_BY": "<PERSON><PERSON><PERSON><PERSON><PERSON> von", "HISTORY.ROLLUPS.ROLLUPS_HISTORY.TABLE.PER_PAGE": "Einträge pro Seite", "HISTORY.ROLLUPS.ROLLUP_ERROR_DETAIL.TITLE": "Details der Rollup Fehler", "HISTORY.ROLLUPS.ROLLUP_ERROR_DETAIL.ON": "An", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD": "Hochladen der DLM Ladevorgangsdatei", "HISTORY.DLM_CHARGING_SESSIONS.TITLE": "DLM <PERSON>", "HISTORY.DLM_CHARGING_SESSIONS.TABLE.TH.CUSTOMER": "Kunde ID", "HISTORY.DLM_CHARGING_SESSIONS.TABLE.TH.CREATED_AT": "Erstellt am", "HISTORY.DLM_CHARGING_SESSIONS.TABLE.TH.AVG_CHARGED_ENERGY": "Durchschnittliche Ladenergie (Wh)", "HISTORY.DLM_CHARGING_SESSIONS.TABLE.TH.AVG_CHARGED_DURATION": "Durchschnittliche Ladezeit (Minuten)", "HISTORY.DLM_CHARGING_SESSIONS.TABLE.PER_PAGE": "Einträge pro Seite", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.TITLE": "Auswahl der Datei für die Ladevorgänge des Kunden", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.SHOW": "Dateiformat zeigen", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.HIDE": "Dateiformat zuklapen", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.CSV_FORMAT_DETAIL": "Die CSV-Datei muss das folgende Format aufweisen:", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.CSV_FORMAT": "Kunden ID; Durchschnittliche Ladezeit in Minuten; Durchschnittliche Ladenergie Wh\n<br>x;x;x", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.CSV_LIST_TITLE": "Nur die Reihenfolge der Spalten zählt, nicht ihre Namen.", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.CSV_1": "Die \"Kunden ID\" Spalte entrpricht dem Virta Electric Vehicle Contract ID (evcoId), z.B.: FI*VIR*000000*X", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.CSV_2": "Die \"Durchschnittliche Ladezeit in Minuten\" muss einen Zahlenwert haben.", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.CSV_3": "Die \"Durchschnittliche Ladenergie Wh\" muss einen Zahlenwert haben.", "HISTORY.DLM_CHARGING_SESSIONS.UPLOAD_FILE.UPLOAD_FILE": "Hochladen der DLM Ladevorgänge", "HISTORY.LOGS.SUBMIT": "Daten an EE Trading Optimizer übermitteln", "HISTORY.LOGS.SELECT_LABEL": "Zeige:", "HISTORY.LOGS.SELECT_TRADING": "Vermarktung", "HISTORY.LOGS.SELECT_STEERING": "Steuerung", "HISTORY.LOGS.TH.ID": "ID", "HISTORY.LOGS.TH.TYPE": "<PERSON><PERSON>", "HISTORY.LOGS.TH.CREATED": "<PERSON><PERSON><PERSON><PERSON>", "HISTORY.LOGS.TH.UPDATED": "G<PERSON><PERSON>ndert", "HISTORY.LOGS.TH.START": "Startzeit", "HISTORY.LOGS.TH.END": "Endzeit", "HISTORY.LOGS.TH.RETRIES": "Wiederholungen", "HISTORY.LOGS.TH.FAILED": "Gescheitert", "HISTORY.LOGS.TH.SENT": "Anfrage", "HISTORY.LOGS.TH.RESPONDED": "Antwort", "IMPORTED_WITH_ERRORS": "File imported with errors [DE]", "ERROR_INVALID_TIME_INTERVAL": "Invalid time interval. [DE]", "FAILURE": "Fehlschlag", "SUCCESS": "Erfolg", "ErrorAuctionClosed": "Die Auktion ist für diesen Zeitraum und für diesen Markt nicht offen.", "ErrorAuctionMrlClosedButSrlOpen": "Die Auktion ist für diesen Zeitraum und für diesen Markt nicht offen.", "ErrorAuctionSrlClosedButMrlOpen": "Die Auktion ist für diesen Zeitraum und für diesen Markt nicht offen.", "ErrorTSONotFound": "Mindestens eine Erbringungszone wird nicht erkannt.", "ErrorInvalidEnergyDirection": "Mindestens eine Energierichtung wird nicht erkannt.", "ErrorInvalidTSO": "Mindestens eine Erbringungszone wird nicht erkannt.", "ErrorInvalidMarket": "Mindestens ein <PERSON> wird nicht erkannt.", "ErrorInvalidProductName": "Mindestens ein Produktname wird nicht erkannt.", "ErrorInvalidPaymentDirection": "Mindestens eine AP-Zahlungsrichtung wird nicht erkannt.", "ErrorFlexVolumeNotWholeValue": "Mindestens ein volumen ist nicht auf MW gerundet.", "ErrorDGEnabledForBidsMissing": "Im System sind keine Abrufsgruppen in {{tsoName}} und {{marketName}} für die Annahme von Angeboten konfiguriert. Bitte aktivieren Sie die Trading-Funktionalität in der Konfiguration der Abrufsgruppe.", "ErrorBGNotFoundForBid": "Auf Abrufsgruppenebene sind in {{tsoName}} und {{marketName}} zum vermarkteten Datum keine Bilanzkreise definiert. Bitte tragen Sie in der Abrufsgruppenkonfiguration einen Bilanzkreis ein.", "ErrorMinFlexBidRule": "Die Mindestleistung eines Angebots beträgt 5 MW. Ein Angebot von weniger als 5 MW kann nur hochgeladen werden, wenn es sich um das einmalige Angebot für diesen Tag, dieses Produkt und diesen Markt handelt. Bitte überprüfen Sie die Gebote in {{tsoName}}.", "ErrorInvalidFloatingEnergyPrice": "Nur drei Zahlen nach dem Komma für den Energiepreis werden akzeptiert.", "ErrorInvalidFloatingCapacityPrice": "Nur zwei Zahlen nach dem Komma für den Kapazitätspreis werden akzeptiert.", "ErrorInvalidTimeInterval": "Üngültiger Zeitraum", "ErrorDGNotFound": "Abruffgruppe nicht vorhanden", "ErrorOverlappingExistingDUWithPrice": "Überschneidung mit vorhandener Nominierung mit Preis", "ErrorOverlappingImportedDUWithPrice": "Überschneidung mit vorhandener importierter Nominierung mit Preis", "ErrorOverlappingExistingDUAsset": "Überschneidung mit vorhandener Nominierung von Anlagen", "ErrorOverlappingImportedDUAsset": "Überschneidung mit vorhandener importierter Nominierung von Anlagen", "ErrorPartialOverlappingExistingDU": "Teilweise überschneidung mit vorhandener Nominierung", "ErrorPartialOverlappingImportedDU": "Teilweise überschneidung mit vorhandener importierte Nominierung", "ErrorImmediateOrPastEffect": "Vorlaufzeit zu gering", "ErrorAuctionIdAlreadyExists": "Auktions-ID bereits verwendet", "WarningZeroVolume": "Flex ist Null", "WarningNoMatchingBidForAuctionResult": "<PERSON>ür einige Nominierungen existiert kein zugehöriges Gebot", "WarningNoMatchingAuctionResultForBid": "WarningNoMatchingAuctionResultForBid", "ErrorAssetNotFound": "<PERSON>lage nicht vorhanden", "ErrorAssetNotActive": "<PERSON><PERSON> in<PERSON>", "ErrorAssetIsNotInDG": "<PERSON><PERSON> nicht in Abruffgruppe", "ErrorSameDGAllocationOverlap": "Überschneidung mit vorhandener Zuordnung in der gleichen Abruffgruppe", "ErrorSameMarketAllocationOverlap": "Überschneidung mit vorhandener Zuordnung im gleichen Markt", "ErrorAllocationIntervalNotCoveredByPQ": "Zuordnungszeitraum außerhalb der Präqualifikation", "ErrorAllocationIntervalNotCoveredByPrice": "Zuordnungszeitraum außerhalb des Preisrahmens. Bitte prüfen Si<PERSON> auch, ob ein gültiger Vertrag vorliegt.", "ErrorAllocationIntervalNotCoveredByBG": "Zuordnungszeitraum nicht zulässig für gewählten Bilanzkreis", "ErrorAllocationIntervalNotCoveredByInternalBG": "Zuordnungszeitraum nicht zulässig für gewählten internen Bilanzkreis", "ErrorTestAllocationReallocation": "ErrorTestAllocationReallocation", "ErrorWrongTSOAllocation": "ÜNB-Zuordnung falsch", "ErrorTestAllocationReassignment": "ErrorTestAllocationReassignment", "ErrorImmediateEffect": "Vorlaufzeit zu gering", "WarningObsoleteValidation": "Warning - Überprüfung ist obsolet", "WarningImminentExpiry": "<PERSON><PERSON><PERSON> steht unmittelbar bevor", "MISSING_START_DATE": "Startzeitpunkt fehlt", "MISSING_END_DATE": "Endzeitpunkt fehlt", "INVALID_START_DATE": "Startzeitpunkt ungültig", "INVALID_END_DATE": "Endzeitpunkt ungültig", "END_DATE_BEFORE_START_DATE": "Endzwitpunkt liegt vor Startzeitpunkt", "MISSING_DISPATCH_GROUP": "Abrufgruppe fehlt", "MISSING_ASSET": "<PERSON><PERSON> fehlt", "MISSING_FLEX_VOLUME": "Flex-Kapazität-Fehlt", "MISSING_ENERGY_PRICE": "Arbeitspreis fehlt", "MISSING_ENERGY_DIRECTION": "<PERSON><PERSON><PERSON> fehlt", "EVENT_NOTIFICATIONS.TITLE": "Benachrichtigungen", "EVENT_NOTIFICATIONS.EVENT_TYPE.DG_HEARTBEAT_MIRROR_FAILURE": "ÜNB-Signalstreckenfehler", "EVENT_NOTIFICATIONS.EVENT_TYPE.BIDDING_API_FAILURE": "Fehler beim Hochladen auf Regelleistung.net", "EVENT_NOTIFICATIONS.EVENT_TYPE.BIDDING_API_SUCCESS": "Erfolgreiches Gebots-Uploads auf Regelleistung.net", "EVENT_NOTIFICATIONS.EVENT_TYPE.BID_DOWNLOAD_FAILURE": "Fehler beim Runterladen der Geboten", "EVENT_NOTIFICATIONS.EVENT_TYPE.BID_DOWNLOAD_SUCCESS": "Erfolgreiches Runterladen der Gebote", "EVENT_NOTIFICATIONS.EVENT_TYPE.AUCTION_DOWNLOAD_FAILURE": "Fehler beim Runterladen der Auktionsergebnisse", "EVENT_NOTIFICATIONS.EVENT_TYPE.AUCTION_DOWNLOAD_SUCCESS": "Erfolgreiches Runterladen der Auktionsergebnisse", "EVENT_NOTIFICATIONS.EVENT_TYPE.AUCTION_DOWNLOAD_RESULTS_EXCEED_BIDS": "Auktionsergebnisse mit höheren Volumen als die vorherigen Gebote", "EVENT_NOTIFICATIONS.EVENT_TYPE.DG_OVER_DELIVERY": "Pool overdelivery", "EVENT_NOTIFICATIONS.EVENT_TYPE.DG_AVAILABLE_FLEX_TOO_LOW": "VPP Pool Probleme", "EVENT_NOTIFICATIONS.EVENT_TYPE.DG_MERLIN_ISSUE": "Me<PERSON>in <PERSON>e", "EVENT_NOTIFICATIONS.EVENT_TYPE.V2G_OPTIMIZATION_SUBMISSION_FAILURE": "V2G-EE Optimisierung Probleme", "EVENT_NOTIFICATIONS.EVENT_TYPE.AUCTION_MISSING_BIDS": "Automatische Erstellung von Geb<PERSON>n, weil vorhandene Gebote nicht mit den Ergebnissen von Regelleistung.net übereinstimmen", "EVENT_NOTIFICATIONS.EVENT_TYPE.DLM_ASSET_FAULT": "DLM Anlage im Fehler", "EVENT_NOTIFICATIONS.EVENT_TYPE.DLM_EXECUTION_FAILURE": "DL<PERSON>", "EVENT_NOTIFICATIONS.EVENT_TYPE.VIRTA_DLM_API_FAILURE": "Virta-DLM-API-Fehler", "EVENT_NOTIFICATIONS.EVENT_TYPE.VIRTA_ADMIN_API_FAILURE": "Virta-Admin-API-Fehler", "EVENT_NOTIFICATIONS.EVENT_TYPE.ERROR_UPLOADING_TRADES_SPOT_OPTIMISATION": "Fehler beim Hochladen der Handelsgeschäfte aus Spot Optimization", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_DA_NO_SETPOINT": "NL aFRR - Day-After-Process - Keine Energy geliefert", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_DA_UPLOAD_FAILED": "NL aFRR - Day-After-Process - Activated Energy Document Upload Fehler", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_DA_UPLOAD_SUCCESS": "NL aFRR - Day-After-Process - Activated Energy Document Upload erfolgt", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_DA_MISSING_DATA": "NL aFRR - Day-After-Process - Asset Daten fehlen", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_DA_ACK_WITH_INFO": "NL aFRR - Day-After-Process - Activated-Energy-Document-Acknowledgment enthält zusätzliche Informationen", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_DA_NON_ACK": "NL aFRR - Day-After-Process - Activated-Energy-Document kein Acknowledgement", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_DA_NO_ACK": "NL aFRR - Day-After-Process - Activated Energy Document nicht acknowledged", "EVENT_NOTIFICATIONS.EVENT_TYPE.NLAFRR_BID_MESSAGE_NON_ACK": "NL aFRR - Bidding Process - Bid Message nicht akzeptiert", "EVENT_NOTIFICATIONS.EVENT_TYPE.NLAFRR_BID_MESSAGE_NO_ACK": "NL aFRR - Bidding Process - Fehlende Antwort auf Bid Message", "EVENT_NOTIFICATIONS.EVENT_TYPE.NLAFRR_BID_MESSAGE_ACK_WITH_INFO": "NL aFRR - Bidding Process - Zusätzliche Information in Bid Message Rückmeldung", "EVENT_NOTIFICATIONS.EVENT_TYPE.NLAFRR_BID_MESSAGE_UPLOAD_FAILURE": "NL aFRR - Bidding Process - Bid Message Upload-Fehler", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_SIGNAL_UPLOAD_FAILURE": "NL aFRR - Delivery Process - CBP Übertragungsfehler", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_SIGNAL_UPLOAD_FAILURE_OVER": "NL aFRR - Delivery Process - CBP Übertragungsfehler behoben", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_NEGATIVE_POOL_CONFIGURATION": "NL aFRR - Pool Konfiguration - Negative Pool-Confirmation-Nachricht erhalten", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_CONFIRMATION_MARKET_DOCUMENT_RECEIVED": "NL aFRR - Day-After-Process - Confirmation Market Document erhalten", "EVENT_NOTIFICATIONS.EVENT_TYPE.AFRR_CONFIRMATION_MARKET_DOCUMENT_MISSING": "NL aFRR - Day-After-Process - Confirmation Market Document nicht erhalten", "EVENT_NOTIFICATIONS.TOOLTIP.DG_HEARTBEAT_MIRROR_FAILURE": "Eine Benachrichtigung wird gesendet, wenn ein Kommunikationsproblem auf mindestens einer TSO-Leitung zwischen dem VPP und dem TSO erkannt wird.", "EVENT_NOTIFICATIONS.TOOLTIP.BIDDING_API_FAILURE": "Im Falle eines Problems während des Hochladens der Gebote auf die Website Regelleistung.net wird eine Nachricht mit den Geboten als XML gesendet.", "EVENT_NOTIFICATIONS.TOOLTIP.BIDDING_API_SUCCESS": "Es wird eine Nachricht gesendet, die über den erfolgreichen Upload von Geboten auf den Markt informiert.", "EVENT_NOTIFICATIONS.TOOLTIP.BID_DOWNLOAD_FAILURE": "<PERSON>n kein Ergebnis für alle im System gespeicherten Gebote vorliegt, wird eine Nachricht gesendet.", "EVENT_NOTIFICATIONS.TOOLTIP.BID_DOWNLOAD_SUCCESS": "Wenn die Auktionsergebnisse erfolgreich heruntergeladen wurden, wird eine Nachricht mit den Auktionsergebnissen an die Empfänger gesendet.", "EVENT_NOTIFICATIONS.TOOLTIP.AUCTION_DOWNLOAD_FAILURE": "<PERSON>n kein Ergebnis für alle im System gespeicherten Gebote vorliegt, wird eine Nachricht gesendet.", "EVENT_NOTIFICATIONS.TOOLTIP.AUCTION_DOWNLOAD_SUCCESS": "Wenn die Auktionsergebnisse erfolgreich heruntergeladen wurden, wird eine Nachricht mit den Auktionsergebnissen an die Empfänger gesendet.", "EVENT_NOTIFICATIONS.TOOLTIP.AUCTION_DOWNLOAD_RESULTS_EXCEED_BIDS": "Eine Benachrichtigung wird gesendet, wenn Auktionsergebnisse hochgeladen werden, bei denen die Volumen höher sind als die der vorher erzeugten Bids ODER Auktionsergebnisse hochgeladen werden ohne vorher Bids erzeugt zu haben", "EVENT_NOTIFICATIONS.TOOLTIP.DG_OVER_DELIVERY": "Eine Benachrichtigung an die Empfänger wird gesendet, wenn ein Überlieferungsereignis der Abrufsgruppe angelegt wird. Das Überlieferungsereignis wird auf der Konfigurationsseite der Abrufsgruppe definiert.", "EVENT_NOTIFICATIONS.TOOLTIP.DG_AVAILABLE_FLEX_TOO_LOW": "Event triggered when the per-direction available flex of a DG is below the configured threshold of the nominated flex.", "EVENT_NOTIFICATIONS.TOOLTIP.DG_MERLIN_ISSUE": "In case of an issue with the Merlin Client a notification will be sent to the recipients.", "EVENT_NOTIFICATIONS.TOOLTIP.V2G_OPTIMIZATION_SUBMISSION_FAILURE": "Eine Benachrichtigung an die Empfänger wird gesendet, wenn Probleme beim EE V2G Optimisierungs Prozess auftreten.", "EVENT_NOTIFICATIONS.TOOLTIP.AUCTION_MISSING_BIDS": "Eine Benachrichtigung wird gesendet, wenn automatisch Gebote erstellt werden, weil keine entsprechenden Gebote zu den Ergebnissen von Regeleistung.net gefunden wurden.", "EVENT_NOTIFICATIONS.TOOLTIP.DLM_ASSET_FAULT": "Eine Benachrichtigung wird gesendet, wenn ein DLM-Asset während der Zuweisung an eine DG in einen Fehlerzustand gerät.", "EVENT_NOTIFICATIONS.TOOLTIP.DLM_EXECUTION_FAILURE": "Eine Benachrichtigung wird generiert, wenn ein allgemeiner Fehler bzgl. DLM auftritt.", "EVENT_NOTIFICATIONS.TOOLTIP.VIRTA_DLM_API_FAILURE": "Eine Benachrichtigung wird generiert, wenn die Kommunikation mit der Virta-DLM-API nicht hergestellt werden kann oder fehlerhaft ist.", "EVENT_NOTIFICATIONS.TOOLTIP.VIRTA_ADMIN_API_FAILURE": "Eine Benachrichtigung wird generiert, wenn die Kommunikation mit der Virta Admin API nicht hergestellt werden kann oder fehlerhaft ist.", "EVENT_NOTIFICATIONS.TOOLTIP.ERROR_UPLOADING_TRADES_SPOT_OPTIMISATION": "<PERSON>il hochladen der Gebote aus Spot Optimization zu IQCHP ist ein Fehler aufgetreten", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_DA_NO_SETPOINT": "Eine Benachrichtigung wird gesendet, wenn für den Vortag kein Activated Energy Document gesendet wurde, weil keine Energie geliefert wurde.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_DA_UPLOAD_FAILED": "Eine Benachrichtigung wird gesendet, wenn das Activated Energy Document für den Vortag nicht über CBP an TenneT gesendet werden konnte.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_DA_UPLOAD_SUCCESS": "Eine Benachrichtigung wird gesendet, wenn das Activated Energy Document für den Vortag über CBP an TenneT gesendet wurde.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_DA_MISSING_DATA": "Eine Benachrichtigung wird gesendet, wenn des Activated Energy Document fehlerhaft ist, weil ein oder mehrere Asset-Rollups für den Vortag nicht korrekt berechnet wurden.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_DA_ACK_WITH_INFO": "Eine Benachrichtigung wird gesendet, wenn die Bestätigung eines Activated-Energy-Documents positiv war aber zusätzliche Informationen beinhaltet.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_DA_NO_ACK": "Eine Benachrichtigung wird gesendet, wenn zu einem Activated-Energy-Document nach 30 Minuten keine Bestätigung empfangen wurde.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_DA_NON_ACK": "Eine Benachrichtigung wird gesendet, wenn die Bestätigung eines Activated-Energy-Documents negativ war.", "EVENT_NOTIFICATIONS.TOOLTIP.NLAFRR_BID_MESSAGE_NON_ACK": "Eine Benachrichtigung wird gesendet, wenn eine Bid Message vom TSO nicht akzeptiert wurde.", "EVENT_NOTIFICATIONS.TOOLTIP.NLAFRR_BID_MESSAGE_NO_ACK": "Eine Benachrichtigung wird gesendet, wenn auf eine Bid Message keine Rückmeldung vom TSO empfangen wurde.", "EVENT_NOTIFICATIONS.TOOLTIP.NLAFRR_BID_MESSAGE_ACK_WITH_INFO": "Eine Benachrichtigung wird gesendet, wenn eine Rückmeldung zu einer Bid Message zusätzliche Informationen beinhaltet.", "EVENT_NOTIFICATIONS.TOOLTIP.NLAFRR_BID_MESSAGE_UPLOAD_FAILURE": "Eine Benachrichtigung wird gesendet, wenn eine Bid Message nicht gesendet werden konnte.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_SIGNAL_UPLOAD_FAILURE": "Eine Benachrichtigung wird gesendet, wenn ein Kommunikationsproblem zwischen dem ÜNB in NL (CBP) und dem VPP erkannt wird.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_SIGNAL_UPLOAD_FAILURE_OVER": "Eine Benachrichtigung wird gesendet, wenn ein Kommunikationsproblem zwischen dem ÜNB in NL (CBP) und dem VPP erkannt wird.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_NEGATIVE_POOL_CONFIGURATION": "Eine Benachrichtigung wird gesendet, wenn Tennet die aFRR-Pool-Konfiguration nicht akzeptiert.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_CONFIRMATION_MARKET_DOCUMENT_RECEIVED": "Eine Benachrichtigung wird gesendet, wenn das VPP ein Confirmation Market Document erhalten hat.", "EVENT_NOTIFICATIONS.TOOLTIP.AFRR_CONFIRMATION_MARKET_DOCUMENT_MISSING": "Eine Benachrichtigung wird gesendet, wenn das VPP kein Confirmation Market Document erhalten hat.", "EVENT_NOTIFICATIONS.CHANNEL.SMS": "SMS", "EVENT_NOTIFICATIONS.CHANNEL.PHONECALL": "<PERSON><PERSON><PERSON>", "EVENT_NOTIFICATIONS.CHANNEL.EMAIL": "E-Mail", "EVENT_NOTIFICATIONS.SUBMIT": "Aktualisieren", "EVENT_NOTIFICATIONS_AUCTION_CONFIG.TITLE": "Tender Notifications", "EVENT_NOTIFICATIONS_DISPATCH_GROUP.TITLE": "Dispatch Group Notifications", "SEND_FILE_EXTERNALLY.TITLE": "<PERSON><PERSON> extern versenden", "CONFIRM.TITLE.GENERIC": "Bitte bestätigen", "CONFIRM.WARNING": "Warnmeldung", "CONFIRM.ACKNOWLEDGE": "Abbrechen", "CONFIRM.CONFIRM": "Bestätigen", "CONFIRM.DISMISS": "Entlassen", "CONFIRM.OK": "OK", "CONFIRM.MESSAGE.DELETE_ENTRY": "Wollen Sie diesen Eintrag wirklich löschen?", "CONFIRM.MESSAGE.VALIDATE_OPTIMIZATION": "Are you sure you want to validate the Asset Optimization?", "CONFIRM.MESSAGE.REJECT_OPTIMIZATION": "Are you sure you want to reject the Asset Optimization?", "PAGINATION.PER_PAGE": "Einträge pro Seite", "CONFIGURATION.ASSET_LEVEL.TAB.BOX_TYPES": "Boxvarianten", "CONFIGURATION.ASSET_LEVEL.TAB.GENERIC_STEERING_TYPES": "Generic Steering Types", "CONFIGURATION.ASSET_LEVEL.TAB.SIGNAL_LISTS": "Signal Lists", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.ENTER_BOX_TYPE": "Boxvariante eingeben", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.CREATE_BOX_TYPE": "Speichern", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.SHOW_BOX_TYPE": "Boxvariante Konfiguration", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.SAVE_BOX_TYPE": "Speichern", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.CANCEL_EDIT": "Abbrechen", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.EDIT": "<PERSON><PERSON><PERSON>", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.DELETE": "Löschen", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LIST.TITLE": "Boxvarianten", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.BOX_TYPE": "Boxvariante", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.PROTOCOL": "Protokoll", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.PORT": "Port", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.VPN_TYPE": "VPN Variante", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.EMAIL": "Kontaktperson E-Mail", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.NAME": "Kontaktperson Name", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.PHONE": "Kontaktperson Telefonnummer", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.FIRST_NAME": "Kontaktperson <PERSON>", "CONFIGURATION.ASSET_LEVEL.BOX_TYPES.LABEL.LASE_NAME": "Kontaktperson Familienname", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.ENTER_STEERING_TYPE": "Steuerungstyp eingeben", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.CREATE_STEERING_TYPE": "Speichern", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SHOW_STEERING_TYPE": "Steuerungstyp Konfiguration", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SAVE_STEERING_TYPE": "Speichern", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.CANCEL_EDIT": "Abbrechen", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.EDIT": "<PERSON><PERSON><PERSON>", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.DELETE": "Löschen", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.LIST.TITLE": "Steuerungstyp", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.NAME": "Steuerungstyp", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.BASEPOINT": "Arbeitspunkt", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.SCHEDULE": "Fahrplan", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.FLEX": "Flexibilität", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.SETPOINT": "Sollwert", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.DISPATCH_DEVIATED": "<PERSON><PERSON><PERSON><PERSON>", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.HEARTBEAT_VPP": "Kontrollsignal VPP Kommunikation OK", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.HEARTBEAT_ASSET": "Kontrollsignal Anlagenkommunikation OK", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.TH.ACTIONS": "", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.LABEL.BOX_TYPE": "Box Type / Steering Type", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.BASEPOINTS.HasBasepointAndPbp": "Vorauseilender Arbeitspunkt der Anlage", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.BASEPOINTS.HasBasepoint": "Arbeitspunk<PERSON> von <PERSON>lage", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.BASEPOINTS.NoBasepoint": "<PERSON><PERSON> (-> <PERSON><PERSON>)", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SCHEDULES.Production": "Prod", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SCHEDULES.Flexibility": "Flex", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.FLEXES.Both": "<PERSON><PERSON>", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.FLEXES.Positive": "Nur Positive", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.FLEXES.Negative": "Nur Negative", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_RELATIVITIES.Relative": "Relativ", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_RELATIVITIES.Absolute": "Absolut", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_UNITS.Kilowatts": "kW", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_UNITS.Boolean": "Boolean", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_INTERVALS.Continuous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_INTERVALS.OnOff": "ON/OFF", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_INTERVALS.Steps": "<PERSON><PERSON><PERSON> (2 oder mehr)", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.HEARTBEAT_AUTHORIZATIONS.Read": "<PERSON><PERSON>", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.HEARTBEAT_AUTHORIZATIONS.Write": "<PERSON><PERSON> s<PERSON>re<PERSON>", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.HEARTBEAT_AUTHORIZATIONS.ReadWrite": "L/S", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_LOCKS.true": "Sollwert Aktivierungsbit", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.SETPOINT_LOCKS.false": "Kein Sollwert Aktivierungsbit", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.YES_NOS.true": "<PERSON>a", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.YES_NOS.false": "<PERSON><PERSON>", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.Y_NS.true": "<PERSON>a", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.Y_NS.false": "<PERSON><PERSON>", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.FEEDBACK": "Rückmeldung", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.FREQUENCY_S": "<PERSON><PERSON><PERSON><PERSON> (s)", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.DEFAULT_ABSOLUTE_SETPOINTS.ImposedPMax": "Maximale Leistung", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.DEFAULT_ABSOLUTE_SETPOINTS.ImposedPMin": "Minimale Leistung", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.DEFAULT_ABSOLUTE_SETPOINTS.PNorm": "Normalleistung", "CONFIGURATION.ASSET_LEVEL.STEERING_TYPES.DEFAULT_ABSOLUTE_SETPOINTS.Default": "<PERSON><PERSON><PERSON>", "CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.ENTER_SIGNAL_LIST": "Signallist<PERSON> e<PERSON>", "CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.NAME": "Name", "CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.SIGNAL_DIRECTION_READ": "Lesen (<PERSON><PERSON> zu VPP)", "CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.SIGNAL_DIRECTION_WRITE": "<PERSON><PERSON><PERSON><PERSON> (VPP zu Kunde)", "CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.DIRECTION": "<PERSON><PERSON><PERSON>", "CONFIGURATION.ASSET_LEVEL.SIGNAL_LISTS.SAVE": "Speichern", "CONFIGURATION.MARKET_LEVEL.TAB.SUBPOOLS": "Subpools", "CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.NAME": "Name", "CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.DISPATCH_GROUPS": "Abrufgruppen", "CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.SIGNALS": "Signale", "CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.IMPLICIT": "Implizit", "CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.ASSETS": "<PERSON><PERSON><PERSON>", "CONFIGURATION.MARKET_LEVEL.SUBPOOLS.TH.ACTIONS": "", "CONFIGURATION.MARKET_LEVEL.TAB.BALANCING_GROUPS": "Bilanzkreise", "CONFIGURATION.MARKET_LEVEL.TAB.DISPATCH_GROUPS": "Abrufgruppen", "CONFIGURATION.MARKET_LEVEL.SUBPOOLS.ENTER_SUBPOOL": "Subpool eingeben", "CONFIGURATION.MARKET_LEVEL.SUBPOOLS.IMPLICIT.NOTICE": "Ein implizites Subpool enthält alle Anlagen, welche keiner anderen Subpool zugeordnet wurden.", "CONFIGURATION.MARKET_LEVEL.TAB.AUCTIONS": "Ausschreibungen", "CONFIGURATION.MARKET_LEVEL.AUCTION_CONFIGURATION.ADD": "Neue Ausschreibung", "CONFIGURATION.MARKET_LEVEL.AUCTION_CONFIGURATION.LIST": "Ausschreibungen", "CONFIGURATION.AUCTION_CONFIGURATION.SAVE": "Speichern", "CONFIGURATION.AUCTION_CONFIGURATION.CREATE": "<PERSON><PERSON><PERSON><PERSON>", "CONFIGURATION.AUCTION_CONFIGURATION.EDIT": "<PERSON><PERSON><PERSON>", "CONFIGURATION.AUCTION_CONFIGURATION.DELETE": "Löschen", "CONFIGURATION.AUCTION_CONFIGURATION.CANCEL": "Abbrechen", "CONFIGURATION.AUCTION.LABEL.GENERIC": "<PERSON><PERSON><PERSON>", "CONFIGURATION.AUCTION.NAME": "Name", "CONFIGURATION.AUCTION.AUCTION_VALIDITY_INTERVAL:": "Gültigkeitszeitraum", "CONFIGURATION.AUCTION.AUCTION_VALIDITY_INTERVAL_END": "Gültigkeitsende", "CONFIGURATION.AUCTION.TIME_ZONE": "Zeitzone", "CONFIGURATION.AUCTION.DISPATCH_GROUPS": "Abrufgruppe", "CONFIGURATION.AUCTION.LABEL.BIDDING_LOGIC": "Gebotslogik", "CONFIGURATION.AUCTION.BIDDING_METHOD": "Gebotsmethode", "CONFIGURATION.AUCTION.BIDDING_METHOD_MARKET": "Market", "CONFIGURATION.AUCTION.ASSETS": "Assets", "CONFIGURATION.AUCTION.FREQUENCY_TIME_MINUTES": "Frequency Time (minutes)", "CONFIGURATION.AUCTION.DELIVERY_INTERVAL": "Lieferintervall", "CONFIGURATION.AUCTION.DELIVERY_INTERVAL_DAYS_SHIFTED_HOURS": "Shifted Hours", "CONFIGURATION.AUCTION.OFFERS_TIME_BLOCK": "Angebotsblock", "CONFIGURATION.AUCTION.BIDDING_DIRECTION": "<PERSON><PERSON><PERSON>", "CONFIGURATION.AUCTION.ROUNDING_DIGITS": "Nachkommastellen für Angebot", "CONFIGURATION.AUCTION.MINIMUM_MW_VOLUME_FOR_1BID": "Mindestleistung bei einem Gebot (MW)", "CONFIGURATION.AUCTION.MINIMUM_MW_VOLUME": "Mindestleistung (MW)", "CONFIGURATION.AUCTION.PRICE_TYPE": "Notwendige Preise", "CONFIGURATION.AUCTION.ENERGY_PRICE_ROUNDING_DIGITS": "Energiepreis-Rundung", "CONFIGURATION.AUCTION.CAPACITY_PRICE_ROUNDING_DIGITS": "Kapazitätspreis-Rundung", "CONFIGURATION.AUCTION.SWING_LIMIT": "Swing Limit", "CONFIGURATION.AUCTION.SWING_LIMIT_VALUE": "Swing Limit Value", "CONFIGURATION.AUCTION.LABEL.OPTIMIZATION_HORIZON_CHANGE": "Optimization Horizon", "CONFIGURATION.AUCTION.OPTIMIZATION_HORIZON_CHANGE": "Optimization Horizon Change", "CONFIGURATION.AUCTION.OPTIMIZATION_HORIZON_CHANGE_DAYS_BEFORE_DELIVERY": "Tage vor Lieferintervall", "CONFIGURATION.AUCTION.OPTIMIZATION_HORIZON_CHANGE_TIME": "Zeit", "CONFIGURATION.AUCTION.LABEL.MARKET_AUCTION": "Ausschreibung", "CONFIGURATION.AUCTION.MARKET_AUCTION_START": "Ausschreibungsbeginn", "CONFIGURATION.AUCTION.MARKET_AUCTION_START_DAYS_BEFORE_DELIVERY": "Tage vor Lieferintervall", "CONFIGURATION.AUCTION.MARKET_AUCTION_START_TIME": "Zeit", "CONFIGURATION.AUCTION.MARKET_AUCTION_START_MINUTES": "Minuten", "CONFIGURATION.AUCTION.MARKET_AUCTION_END": "Ausschreibungsend", "CONFIGURATION.AUCTION.MARKET_AUCTION_END_DAYS_BEFORE_DELIVERY": "Tage vor Lieferintervall", "CONFIGURATION.AUCTION.MARKET_AUCTION_END_TIME": "Zeit", "CONFIGURATION.AUCTION.MARKET_AUCTION_END_MINUTES": "Minuten", "CONFIGURATION.AUCTION.LABEL.BID_INPUTS": "Gebotseingabe", "CONFIGURATION.AUCTION.LABEL.INTERNAL_AUCTION": "Interne Ausschreibung", "CONFIGURATION.AUCTION.LABEL.CUSTOMER_AUCTION": "Kunde Ausschreibung", "CONFIGURATION.AUCTION.LABEL.THIRD_PARTY_INTERFACE": "Schnittstelle zu Dritten", "CONFIGURATION.AUCTION.INTERNAL_BIDDING_FORMAT": "Gebotsformat", "CONFIGURATION.AUCTION.INTERNAL_AUCTION_START": "Ausschreibungsbeginn", "CONFIGURATION.AUCTION.INTERNAL_AUCTION_START_DAYS_BEFORE_DELIVERY": "Tage vor Lieferintervall", "CONFIGURATION.AUCTION.INTERNAL_AUCTION_START_TIME": "Zeit", "CONFIGURATION.AUCTION.INTERNAL_AUCTION_START_MINUTES": "Minuten", "CONFIGURATION.AUCTION.INTERNAL_AUCTION_END": "Ausschreibungsend", "CONFIGURATION.AUCTION.INTERNAL_AUCTION_END_DAYS_BEFORE_DELIVERY": "Tage vor Lieferintervall", "CONFIGURATION.AUCTION.INTERNAL_AUCTION_END_TIME": "Zeit", "CONFIGURATION.AUCTION.INTERNAL_AUCTION_END_MINUTES": "Minuten", "CONFIGURATION.AUCTION.CUSTOMER_BIDDING_FORMAT": "Gebotsformat", "CONFIGURATION.AUCTION.CUSTOMER_AUCTION_START": "Ausschreibungsbeginn", "CONFIGURATION.AUCTION.CUSTOMER_AUCTION_START_DAYS_BEFORE_DELIVERY": "Tage vor Lieferintervall", "CONFIGURATION.AUCTION.CUSTOMER_AUCTION_START_TIME": "Zeit", "CONFIGURATION.AUCTION.CUSTOMER_AUCTION_START_MINUTES": "Minuten", "CONFIGURATION.AUCTION.CUSTOMER_AUCTION_MINIMUM_MW_VOLUME_FOR_1BID": "Minimales MW für 1 Gebot", "CONFIGURATION.AUCTION.CUSTOMER_AUCTION_END": "Ausschreibungsend", "CONFIGURATION.AUCTION.CUSTOMER_AUCTION_END_DAYS_BEFORE_DELIVERY": "Tage vor Lieferintervall", "CONFIGURATION.AUCTION.CUSTOMER_AUCTION_END_TIME": "Zeit", "CONFIGURATION.AUCTION.CUSTOMER_AUCTION_END_MINUTES": "Minuten", "CONFIGURATION.AUCTION.LABEL.OPTIMIZER_BOD": "Optimizer BOD", "CONFIGURATION.AUCTION.OPTIMIZER_BOD_FREQUENCY_MINUTES": "Frequency (Minuten)", "CONFIGURATION.AUCTION.OPTIMIZER_BOD_COMPUTED_SP": "Computed SPs", "CONFIGURATION.AUCTION.OPTIMIZER_BOD_DELTA_FOR_SUBMISSION": "Delta for submission (£)", "CONFIGURATION.AUCTION.OPTIMIZER_BOD_DELTA_FOR_UNDO": "Undo delta (£)", "CONFIGURATION.AUCTION.API_AUCTION_BIDDING": "Bidding API", "CONFIGURATION.AUCTION.AUCTION_SYSTEM": "Gebotssystem", "CONFIGURATION.AUCTION.LABEL.AUCTION_SYSTEM_UPLOAD": "Upload", "CONFIGURATION.AUCTION.LABEL.AUCTION_SYSTEM_DOWNLOAD": "Download", "CONFIGURATION.AUCTION.LABEL.AUCTION_SYSTEM_UI": "UI", "CONFIGURATION.AUCTION.LABEL.AUCTION_SYSTEM_UI_FOR_BATTERY_UK_INTRADAY": "UI For UK Batteries", "CONFIGURATION.AUCTION.LABEL.AUCTION_SYSTEM_UK_VATP": "UK VATP", "CONFIGURATION.AUCTION.LABEL.AUCTION_SYSTEM_DE_VATP": "Germany VATP", "CONFIGURATION.AUCTION.TH_AUCTION_SYSTEM_UPLOAD_BEHAVIOUR": "Angebotsmethode", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UPLOAD_BEHAVIOUR": "Behaviour", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UPLOAD_MARKET_TIME": "Minuten vor Ausschreibungsende", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UPLOAD_NUMBER_OF_RETRIES": "Anzahl der Wiederholungsversuche", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UPLOAD_SECONDS_BETWEEN_RETRIES": "Sekunde zwischen Wiederholungsversuchen", "CONFIGURATION.AUCTION.TH_AUCTION_SYSTEM_DOWNLOAD_BEHAVIOUR": "Ergebnismethode", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_DOWNLOAD_BEHAVIOUR": "Behaviour", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_DOWNLOAD_MARKET_TIME": "Minuten nach Ende der Marktausschreibung", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_DOWNLOAD_NUMBER_OF_RETRIES": "Anzahl der Wiederholungsversuche", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_DOWNLOAD_SECONDS_BETWEEN_RETRIES": "Sekunde zwischen Wiederholungsversuchen", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_TRADER_LOOK_AHEAD_HOURS": "Vermarktungshorizont (Stunden)", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_PRICE_GROUPING_STEP": "Preisgruppengröße (£/MWh)", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_BID_EXPIRY_WARNING_MINUTES": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Minutes)", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_CURRENCY": "Währung", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_SITE_ID": "Site ID", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_FOR_BATTERY_UK_INTRADAY_ASSET_ID": "Asset ID", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_FOR_BATTERY_UK_INTRADAY_PORTFOLIO_ID": "Portfolio ID", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_FOR_BATTERY_UK_INTRADAY_EXPORT_ENTRADER": "Export Entrader", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UI_FOR_BATTERY_UK_INTRADAY_UPLOAD_FOLDER": "Upload Folder", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UK_VATP_SMART_VOLUME_STRATEGY_TEMPLATE_ID": "Smart Volume Strategy Template ID", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UK_VATP_HIDDEN_ICEBERG_STRATEGY_TEMPLATE_ID": "Hidden Iceberg Strategy Template ID", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_UK_VATP_SMART_ICEBERG_STRATEGY_TEMPLATE_ID": "Smart Iceberg Strategy Template ID", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_DE_VATP_SMART_VOLUME_STRATEGY_TEMPLATE_ID": "Smart Volume Strategy Template ID", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_DE_VATP_HIDDEN_ICEBERG_STRATEGY_TEMPLATE_ID": "Hidden Iceberg Strategy Template ID", "CONFIGURATION.AUCTION.AUCTION_SYSTEM_DE_VATP_SMART_ICEBERG_STRATEGY_TEMPLATE_ID": "Smart Iceberg Strategy Template ID", "CONFIGURATION.AUCTION.LABEL.AUCTION_RESULTS_POST_PROCESSING": "Auction Results Post Processing", "CONFIGURATION.AUCTION.CREATE_NOMINATIONS": "Nominierung aus dem Ergebnis erstellen?", "CONFIGURATION.AUCTION.CREATE_ALLOCATIONS": "Zuordnung aus dem Ergebnis erstellen?", "CONFIGURATION.AUCTION.ALLOCATION_START_EXTENSION_SECONDS": "Allocation Start Extension (Seconds)", "CONFIGURATION.AUCTION.ALLOCATION_END_EXTENSION_SECONDS": "Allocation End Extension (Seconds)", "CONFIGURATION.AUCTION.ALLOCATION_END_EXTENSION_FROM_RAMP_RATE": "Allocation End Extension From Ramp Rate", "CONFIGURATION.AUCTION.yesNoOptions.true": "<PERSON>a", "CONFIGURATION.AUCTION.yesNoOptions.false": "No", "CONFIGURATION.AUCTION.deliveryIntervalOptions.Blocks": "Block", "CONFIGURATION.AUCTION.deliveryIntervalOptions.Days": "Tag", "CONFIGURATION.AUCTION.deliveryIntervalOptions.Weeks": "<PERSON><PERSON><PERSON>", "CONFIGURATION.AUCTION.deliveryIntervalOptions.Various": "Sonstiges", "CONFIGURATION.AUCTION.offersTimeBlockOptions.QuarterOfHour": "Viertelstunde", "CONFIGURATION.AUCTION.offersTimeBlockOptions.HalfHour": "Halbe Stunde", "CONFIGURATION.AUCTION.offersTimeBlockOptions.OneHour": "Eine Stunde", "CONFIGURATION.AUCTION.offersTimeBlockOptions.FourHours": "Vier Stunden", "CONFIGURATION.AUCTION.biddingDirectionOptions.PositiveAndNegative": "Positiv und Negativ", "CONFIGURATION.AUCTION.biddingDirectionOptions.Positive": "Positiv", "CONFIGURATION.AUCTION.biddingDirectionOptions.Negative": "Negativ", "CONFIGURATION.AUCTION.biddingDirectionOptions.Symmetric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CONFIGURATION.AUCTION.priceTypeOptions.EnergyOnly": "Arbeit", "CONFIGURATION.AUCTION.priceTypeOptions.CapacityOnly": "Le<PERSON><PERSON>", "CONFIGURATION.AUCTION.priceTypeOptions.EnergyAndCapacity": "Arbeit und Leistung", "CONFIGURATION.AUCTION.marketAuctionStartOptions.AuctionTimeAnyTime": "Jederzeit vor Auktionsende", "CONFIGURATION.AUCTION.marketAuctionStartOptions.AuctionTimeAbsolute": "Zeit vor Lieferintervall", "CONFIGURATION.AUCTION.marketAuctionStartOptions.AuctionTimeRelative": "<PERSON>uten bevor <PERSON> beginnt", "CONFIGURATION.AUCTION.marketAuctionEndOptions.AuctionTimeAbsolute": "Zeit vor Lieferintervall", "CONFIGURATION.AUCTION.marketAuctionEndOptions.AuctionTimeRelative": "<PERSON>uten bevor <PERSON> beginnt", "CONFIGURATION.AUCTION.marketAuctionEndOptions.AuctionTimeRelativeToBid": "Relativ: basiere<PERSON> auf Gebot", "CONFIGURATION.AUCTION.bidInputsOptions.InternalBiddingKind": "Intern", "CONFIGURATION.AUCTION.bidInputsOptions.CustomerBiddingKind": "Kunde", "CONFIGURATION.AUCTION.bidInputsOptions.V2gBiddingKind": "V2G API", "CONFIGURATION.AUCTION.bidInputsOptions.SpotOptimisationBiddingKind": "Spot-Optimierungs-API", "CONFIGURATION.AUCTION.bidInputsOptions.OptimizerBodKind": "Optimized BOD", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.LIST.TITLE": "Bilanzkreise", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TH.TYPE": "<PERSON><PERSON>", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TH.NAME": "Name", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TH.SCHEDULING": "Scheduling", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TH.ACTIONS": "", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TYPE.SUPPLIER_BGS": "Anbieterbilanzkreis", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TYPE.COLLECTING_BGS": "Sammelbilanzkreis", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TYPE.INTERNAL_BGS": "E.ON Intern", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.TYPE.THIRD_PARTY_BGS": "Drittbilanzkreis", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.ENTER_BALANCING_GROUP": "Bilanzkreis eingeben", "CONFIGURATION.MARKET_LEVEL.BALANCING_GROUPS.SAVE": "Speichern", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.ENTER_DISPATCH_GROUP": "Abrufgruppe eingeben", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.SHOW_DISPATCH_GROUP": "Abrufgruppe Konfiguration", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.SAVE": "Speichern", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.CANCEL_EDIT": "Abbrechen", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.EDIT": "<PERSON><PERSON><PERSON>", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.DELETE": "Löschen", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LIST.TITLE": "Abrufgruppe", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.NAME": "Name", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.TSO": "ÜNB", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.MARKET": "Mark<PERSON>", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.BALANCING_GROUP": "Bilanzkreis", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.GENERIC_CONFIG": "Generic Config", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.TRADING_NOMINATION_ENABLED": "Nominierung Aktiviert", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.AUTOMATIC_ALLOCATION_ENABLED": "Automatisch Zuordnung Aktiv", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TH.ACTIONS": "", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.GENERAL_DATA_TITLE": "Abrufgruppe Details", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.GENERIC_CONFIG_ENABLED": "Generic Config", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.PORTFOLIO_ID": "Portfolio ID", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.REACTION_TIME": "Reaktionszeit (s)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.MINIMUM_DURATION_OF_DISPATCH": "<PERSON><PERSON> e<PERSON> Abrufes (s)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.PARAMETERS_TITLE": "Lokale Steuerungsparameter", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.PARAMETERS_INFO_HTML": "<b><PERSON><PERSON> eine Anlage aus der Ferne konfiguriert werden kann und der angegebenen Abrufsgruppe zugeordnet ist, werden die Parameter automatisch an die lokale Box gesendet.</b>", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DEAD_BAND_POS_MHZ": "Positive tote Zone (mHz)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DEAD_BAND_NEG_MHZ": "Negative tote Zone (mHz)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.MAX_FREQUENCY_DEVIATION_POS_MHZ": "Max. positive <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (mHz)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.MAX_FREQUENCY_DEVIATION_NEG_MHZ": "Max. negative <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (mHz)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SIGNALS_TITLE": "Erzeugte Signals", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SIGNALS": "Relevante Datenpunkte", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DSOS": "VNB spezifische Datenpunkte", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SUBPOOLS_VALUE_ON": "Poolindex (zugeordnet)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SUBPOOLS_VALUE_OFF": "Poolindex (nicht zugeordnet)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ECHO_SIGNAL_ACTIVE_POWER": "Echo für ActivePower Signal", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ECHO_SIGNAL_HEARTBEAT": "Echo für Heartbeat Signal", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARMS_TITLE": "Alarmsignale", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_ASSETS_WITH_NOT_RECOVERABLE_FAULTS_ENABLED": "Signal 1 (AssetAlertLevelAlarm)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_ASSETS_WITH_RECOVERABLE_FAULTS_ENABLED": "Signal 2 (AssetAlertLevelWarning)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_SETPOINT_REACHABLE_ENABLED": "Signal 3 (DUAlertLevelWarning)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_AVAILABLE_FLEX_TOO_LOW_ENABLED": "Signal 4 (VPPMissingFlexibilitySRL)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_ASSETS_FAULTS_PQ_THRESHOLD": "Anlagen relevant wenn PQ gr<PERSON> al<PERSON> (KW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.SECTION.RCC_TITLE": "RCC Hamburg Alarme", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARMS_ENABLED": "RCC Alarm aktiv für Flex Too Low", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TOOLTIP.INDIVIDUAL_ALARMS_ENABLED_INFO_HTML": "Ein Ereignis wird erzeugt, wenn der verfügbare Flex niedriger ist als der prozentuale Schwellenwert des gehandelten Volumens über die in Sekunden definierte Dauer.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_FLEXIBILITY_THRESHOLD_FACTOR": "FlexTooLow Schwellenwert-Faktor (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ALARM_FLEXIBILITY_THRESHOLD_BUFFER": "<PERSON>lex<PERSON><PERSON><PERSON><PERSON> (s)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_MERLIN_ENABLED": "RCC Alarm aktiv für MerLin", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_ECHO_SIGNAL_ACTIVE_POWER_ENABLED": "RCC Alarm aktiv für Echo für ActivePower Signal", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.NOTICE.INDIVIDUAL_ALARMS_ENABLED_HTML": "Ein Ereignis wird erzeugt, wenn der verfügbare Flex niedriger ist als der prozentuale Schwellenwert des gehandelten Volumens über die in Sekunden definierte Dauer.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_OVERDELIVERY_POS_ENABLED": "Alarm aktiv bei pos. Überlieferung", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TOOLTIP.INDIVIDUAL_ALARM_OVERDELIVERY_POS_ENABLED_INFO_HTML": "Bei einem positiven Sollwert, wird ein Ereignis er<PERSON>ug<PERSON>, wenn die Abrufsgruppe länger als die angenommene Zeit in Sekunden und um mehr als den eingegebenen MW Volumen überliefert.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_OVERDELIVERY_NEG_ENABLED": "Alarm aktiv bei neg. Überlieferung", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.TOOLTIP.INDIVIDUAL_ALARM_OVERDELIVERY_NEG_ENABLED_INFO_HTML": "Bei einem negativen Sollwert, wird ein Ereignis erzeug<PERSON>, wenn die Abrufsgruppe länger als die angenommene Zeit in Sekunden und um mehr als den eingegebenen MW Volumen überliefert.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_OVERDELIVERY_POS_THRESHOLD": "Alarm aktiv bei pos. Überlieferung Schwellenwert (MW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_OVERDELIVERY_NEG_THRESHOLD": "Alarm aktiv bei neg. Überlieferung Schwellenwert (MW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_OVERDELIVERY_POS_DELAY": "Alarm aktiv bei pos. Überlieferung Dauer (s)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.INDIVIDUAL_ALARM_OVERDELIVERY_NEG_DELAY": "Alarm aktiv bei neg. Überlieferung Dauer (s)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.PRL_TITLE": "Anteilsmäßige Aktivierung (PRL/FFRD)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ACTIVATION_FACTOR": "Aktivierungspuffer (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.AVAILABILITY_DEVIATION_THRESHOLD_FACTOR": "Verfügbarkeitsänderung (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SETPOINT_REACHED_TITLE": "Ram<PERSON><PERSON><PERSON>", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SETPOINT_REACHED_DETECTION_ROLLING_AVERAGE_DURATION": "Average Window (s)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SETPOINT_REACHED_DETECTION_UPPER_TOLERANCE_FACTOR": "Upper Tolerance (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SETPOINT_REACHED_DETECTION_LOWER_TOLERANCE_FACTOR": "Lower Tolerance (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SETPOINT_REACHED_DETECTION_UPPER_TOLERANCE_MINIMUM": "Upper Tolerance Min. (KW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SETPOINT_REACHED_DETECTION_LOWER_TOLERANCE_MINIMUM": "Lower Tolerance Min. (KW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.IS_AT_SETPOINT_DETECTION_TITLE": "Is-At-Setpoint Detektion", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_FACTOR": "Upper Tolerance (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_FACTOR": "Lower Tolerance (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_MINIMUM": "Upper Tolerance Min. (KW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_MINIMUM": "Lower Tolerance Min. (KW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_TITLE": "Kompensation", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_CHECK_INTERVAL": "Check Window (s)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_RESOLUTION": "Resolution (KW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.OVER_DELIVERY_EXCESS_COMPENSATION_FACTOR": "Over-Delivery Compensation (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.OVER_DELIVERY_COMPENSATION_LIMIT_FACTOR": "Over-Delivery Limit (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.UNDER_DELIVERY_EXCESS_COMPENSATION_FACTOR": "Under-Delivery Compensation (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.UNDER_DELIVERY_COMPENSATION_LIMIT_FACTOR": "Under-Delivery Limit (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_FACTOR": "Upper Tolerance (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_FACTOR": "Lower Tolerance (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_MINIMUM": "Upper Tolerance Min. (KW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.COMPENSATION_IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_MINIMUM": "Lower Tolerance Min. (KW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SALLY_SETPOINT_TITLE": "Auto<PERSON><PERSON><PERSON><PERSON><PERSON> (Sally)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SALLY_SETPOINT_READ_ENABLED": "Aktiv", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SALLY_SETPOINT_QUANTIZATION_RESOLUTION": "Quantization Resolution (KW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SALLY_SETPOINT_QUANTIZATION_TIMEOUT": "Quantization Timeout (s)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.MERLIN_DISPATCH_TITLE": "<PERSON><PERSON><PERSON>", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.MERLIN_DISPATCH_ENABLED": "Aktiv", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.SPOT_OPTIMIZATION_TITLE": "Spot Optimierung", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.CAPACITY_PRICE_GROUPING_STEPS": "Kapazität Preisgruppen Stufen", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.PLACEHOLDER.CAPACITY_PRICE_GROUPING_STEPS": "Geben Si<PERSON> eine neue Kapazität Preisgrup Stufe", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.UI_ALERTS_TITLE": "VPP Ops Warnungen", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EVENT_TYPES_ACKNOWLEDGE_ALARM": "Stummen Alarm bei Eintritt folgender Ereignesse", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EVENT_TYPES_ACKNOWLEDGE_AUDIO_ALARM": "Alarm mit Ton bei Eintritt folgender Ereignesse", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_TITLE": "Schnittstelle für Nominierung Trading", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_ENABLED": "Nominierung Aktiviert", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_MAX_FAULT_SECONDS_PERCENTAGE": "Rollup Anlage Fehlergrenze (1=100%; Defaultwert 0.9)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_MAX_INVALID_ROLLUPS_PERCENTAGE": "Aggregierte Anlage Fehlergrenze (1=100%; Defaultwert 0.9)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_PAST_AVAILABILITY_HOURS": "Zeitfenster für Rollups (h) (Defaultwert 24)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_TARGET_BID_VOLUME_MW": "Minimale Angebotsgröße MW (Defaultwert 5)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_ADDITIONAL_BUFFER_POS_MW": "Zusätzlicher Puffer POS MW (Defaultwert 0)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_ADDITIONAL_BUFFER_NEG_MW": "Zusätzlicher Puffer NEG MW (Defaultwert 0)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.NOMINATION_TOOL_ENERGY_PRICE_TARGET_MARGIN": "Energiepreis Gewinnmarge (1=100%; Defaultwert 0)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ROLLUPS_ENABLED_TITLE": "Rollups aktivieren", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.ROLLUPS_ENABLED": "Aktiv", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.AUTOMATIC_ALLOCATION": "Automatisch Zuordnung", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.AUTOMATIC_ALLOCATION_ENABLED": "Automatisch Zuordnung Aktiv", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.AUTOMATIC_ALLOCATION_WEIGHT": "Gewicht", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.AUTOMATIC_ALLOCATION_NOMINATION_BUFFER_PERCENTAGE": "Nominierungspuffer (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.MAX_ASSET_REALLOCATION": "Max An<PERSON>wechs<PERSON> (Default keine Beschränkung)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP": "Externe Besicherung", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_INFO_HTML": "<b>Die externe Besicherung wird aktiviert, wenn die Funktionalität als aktiv markiert ist und wenn während der Pufferzeit die folgende Behauptung wahr ist: <br/> verfügbare Flexibilität ohne externe Besicherung < Nominierte Gesamtkapazität - MAX (Schwellenwert MW; Schwellenwert% der Nominierung * Nominierte Gesamtkapazität)</b>", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_ACTIVE": "Aktiv", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_BUFFER": "<PERSON><PERSON><PERSON> (s)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_INCREMENT_POS": "Rundungsschritt Positiv MW", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_INCREMENT_NEG": "Rundungsschritt Negativ MW", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_THRESHOLD_POS": "Positives Grenzwert MW", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_THRESHOLD_NEG": "Negatives Grenzwert MW", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_NOMINATION_THRESHOLD_POS": "Positives Grenzwert % von Nominierung", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.EXT_BACKUP_NOMINATION_THRESHOLD_NEG": "Negatives Grenzwert % von Nominierung", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_PARAMS": "DLM Parameters", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE": "Lastmanagementmodus", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_SITE": "Park<PERSON>ier<PERSON>", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_SITE_INFO": "Das Lastmanagement basiert nur auf der aktuellen Ladesituation,", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_SESSION": "Ladevorgangsbasiert", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_SESSION_INFO": "Das Lastmanagement basiert auf der aktuellen Ladesituation sowie der Dauer und geladenen Energiemenge von individuellen Ladevorgängen.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_CUSTOMER": "Kundenbasier<PERSON>", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_CUSTOMER_INFO": "Das Lastmanagement basiert auf der aktuellen Ladesituation sowie dem vergangenen Kundenverhalten.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_IMPORT_RESTRICTIONS": "Bezugseinschränkungen", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_NOMINAL_SITE_CURRENT": "Nominaler Strom (A)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_NOMINAL_SITE_CURRENT_INFO": "Nominaler Ladestrom der DLM-Gruppe \"Blockiert\"", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_REDUCTION_FACTOR": "Reduktionsfaktor (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_REDUCTION_FACTOR_INFO": "Ladeleistungs-Reduzierungsfaktor der DLM-Gruppe \"Reduziert\"", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUP_ID": "DLM-Gruppen-IDs", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUP_REDUCED_ID": "<PERSON><PERSON><PERSON><PERSON>", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUP_REDUCED_ID_INFO": "Virta DLM-Gruppen-ID der für das Lastmanagement verwendeten DLM-Gruppe", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUP_BLOCKED_ID": "<PERSON><PERSON><PERSON><PERSON>", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUP_BLOCKED_ID_INFO": "Virta DLM-Gruppen-ID der nicht für das Lastmanagement verwendeten DLM-Gruppe", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_CONTROL_WINDOWS": "Regelzeitfenster", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_CONTROL_WINDOW_BEGIN": "Begin<PERSON>", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_CONTROL_WINDOW_BEGIN_INFO": "In lokaler Zeit.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_CONTROL_WINDOW_END": "<PERSON><PERSON>", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_CONTROL_WINDOW_END_INFO": "In lokaler Zeit.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_CONTROL_WINDOW_ADD_INTERVAL": "<PERSON><PERSON> hinzufügen", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_CONTROL_WINDOW_REMOVE_INTERVAL": "Interval entfernen", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_LOAD_MGMT_PARAMS": "Lastmanagementparameter", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_SESSION_DURATION_THRESHOLD_MINUTES": "Mindestladezeit (Minuten)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_SESSION_DURATION_THRESHOLD_MINUTES_INFO": "Minimale Ladezeit bevor im Lastmanagementmodus \"Ladevorgangsbasiert\" Lastmanagement auf einen Ladevorgang angewendet werden kann.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_SESSION_ENERGY_THRESHOLD_WATT_HOUR": "Mindestladeenergie (kWh)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_SESSION_ENERGY_THRESHOLD_WATT_HOUR_INFO": "Minimale Energie, die geladen werden muss, bevor im Lastmanagementmodus \"Ladevorgangsbasiert\" Lastmanagement auf einen Ladevorgang angewendet werden kann", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_BOTH_THRESHOLDS_INFO": "Verknüpfung zwischen linkem und rechtem Parameter für die Zuordnung von Ladern zu DLM-Gruppen", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_BOTH_THRESHOLDS_AND": "und", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_GROUPING_RULE_BOTH_THRESHOLDS_OR": "oder", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_MIN_AVERAGE_CHARGED_ENERGY_FACTOR": "Minimaler Anteil von durchschnittlicher Ladeenergie (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_MIN_AVERAGE_CHARGED_ENERGY_FACTOR_INFO": "Minimaler Anteil der durchschnittlichen Ladeenergie des betroffenen Kunden bevor im Lastmanagementmodus \"Kundenbasiert\" Lastmanagement auf einen Ladevorgang angewendet werden kann.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_MIN_AVERAGE_CHARGING_DURATION_FACTOR": "Minimaler Anteil von durchschnittlicher Ladezeit (1=100%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.DLM_MIN_AVERAGE_CHARGING_DURATION_FACTOR_INFO": "Anteil der durchschnittlichen Ladezeit des betroffenen Kunden während dem im Lastmanagementmodus \"Kundenbasiert\" Lastmanagement auf einen Ladevorgang angewendet werden kann", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_TITLE": "Bilanzkreise", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_START_DATE": "Startdatum", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_END_DATE": "Enddatum", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_NAME": "Name", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_ADD": "Bilanzkreise eingeben", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_DIALOG_TITLE": "Bilanzkreise eingeben", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS.LABEL.BALANCING_GROUPS_SAVE": "Speichern", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.EXPORT_ENTRADER": "Export Entrader", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ENTRADER_TRESHOLD_UPDATE": "Schwellenwert für Aktualisierungen (MW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ENTRADER_LEAD_TIME_UPDATE": "Vorlaufzeit für Aktualisierungen (min)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ENTRADER_UPLOAD_FOLDER": "Upload Folder", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.BASIC_BEHAVIOR": "Basisverhalten", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.HAS_EXCLUSIVE_BEHAVIOUR": "Exklusives Verhalten", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.HAS_NOMINATION": "Nominierung", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.HAS_PRECEDING_BASEPOINT": "<PERSON><PERSON><PERSON><PERSON> Arbeitspunkt", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ASSET_ACTIVATION": "Anlagenaktivierung", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ASSET_ACTIVATION_TYPE": "Anlagenaktivierungstyp", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.LABEL.FREQUENCY_LOCAL_STEERING_PARAMETERS": "Lokale Frequenzsteuerungsparameter", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.AAT_AR_FLSP_DEAD_BAND_POS": "positives Totband (Hz)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.AAT_AR_FLSP_DEAD_BAND_NEG": "negatives Totband (Hz)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.AAT_AR_FLSP_MAX_FREQUENCY_DEVIATION_POS": "Frequenzabweichnung Pos (Hz)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.AAT_AR_FLSP_MAX_FREQUENCY_DEVIATION_NEG": "Frequenzabweichnung Neg (Hz)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.AAT_AR_FLSP_HIGH_KNEE_JOINT": "<PERSON><PERSON><PERSON> (Hz)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.AAT_AR_FLSP_LOW_KNEE_JOINT": "<PERSON><PERSON><PERSON> (Hz)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.STATE_OF_CHARGE_MANAGEMENT": "Ladezustandsmanagement", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DYNAMIC_CONTAINMENT_LOW": "Dynamic Containment Low", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DYNAMIC_CONTAINMENT": "Dynamic Containment", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.VARIABLE_SOE": "Variable SoE", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.FCR_NL": "FCR NL", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.OPTIMIZATION_PASS_THROUGH": "Optimization Pass Through", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_TARGET_STATE_OF_CHARGE_LOW": "Zielladezustand Low (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_TARGET_STATE_OF_CHARGE_HIGH": "Zielladezustand High (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_TARGET_STATE_OF_CHARGE_BOTH": "Zielladezu<PERSON> Both (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_FOOT_ROOM": "Fußraum (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_HEAD_ROOM": "Kopfraum (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_RAMP_RATE_LIMIT": "Ramp Rate Limit (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_ENERGY_RESERVE": "Energiereserve (Sekunden)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_MIN_ENERGY_RECOVERY": "Mindestenergiewiederherstellung (Sekunden)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_DC_DEAD_BAND_FACTOR": "<PERSON><PERSON><PERSON> (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.VOLUME_BASELINE_DC": "Volumen Baselining - DC", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.VOLUME_BASELINE_DM": "Volumen Baselining - DM", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.VOLUME_BASELINE_DR": "Volumen Baselining - DR", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DC_DELIVERY_DURATION": "<PERSON><PERSON><PERSON><PERSON> (sec)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DC_DELIVERY_DURATION_BUFFER": "Puffer für Lieferdaue (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DC_MIN_ENERGY_RECOVERY": "Min. Energie für SoE Managemen (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DM_DELIVERY_DURATION": "<PERSON><PERSON><PERSON><PERSON> (sec)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DM_DELIVERY_DURATION_BUFFER": "Puffer für Lieferdaue (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DM_MIN_ENERGY_RECOVERY": "Min. Energie für SoE Managemen (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DR_DELIVERY_DURATION": "<PERSON><PERSON><PERSON><PERSON> (sec)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DR_DELIVERY_DURATION_BUFFER": "Puffer für Lieferdaue (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_VB_DR_MIN_ENERGY_RECOVERY": "Min. Energie für SoE Management (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_FCR_LOWER_SOC_LIMIT": "Unteres SoC-Limit (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_FCR_UPPER_SOC_LIMIT": "Oberes SoC-Limit (%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SCM_FCR_SCM_OPT_SP_DURATION_MINUTES": "Dauer der Abrechnungsperioden (Minuten)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ASSET_DISPATCH_STRATEGY": "Anlageneinsatzstrategie", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_START_USING_ASSETS_ONLY_WHEN_AT_BASEPOINT": "Asset nur nutzen wenn am Basepoint", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_STRATEGY": "Strategy", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_STRATEGY_BY_PRICE_PRESERVE_CURRENT_DISPATCHES": "ktuelle Einsatzplanung beibehalten", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_STRATEGY_BY_PRICE_AND_SOC_LIMIT_BATTERY_POWER_WINDOW": "Batterie Dispatch-Zeitfenster (Sekunden)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_STRATEGY_BY_PRICE_AND_SOC_LIMIT_BATTERY_POWER_WINDOW_INFO_HTML": "Begrenzt die Sollleistung von Batterien so, dass sie die Leistung über die gegebene Zeit aufrecht halten können.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_STRATEGY_PRO_RATA_SYMMETRIC": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.ADS_STRATEGY_ON_OFF_SIGNAL": "Signal für Ein- und Ausschalten", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.CROSS_DG_LINKS": "Cross DG links", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.CDL_CROSS_PLAN_PROPAGATION_FREQUENCY": "Cross plan propagation frequency", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DEVIATIONS_COMPENSATION": "Abweichungskompensation", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_CHECK_INTERVAL_SECONDS": "Prüfintervall (Sekunden)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_OVER_DELIVERY_EXCESS_COMPENSATION_FACTOR": "Überbelieferungskompensationsfaktor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_OVER_DELIVERY_COMPENSATION_LIMIT_FACTOR": "Überbelieferungsbegrenzungsfaktor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_UNDER_DELIVERY_EXCESS_COMPENSATION_FACTOR": "Unterbeliferungskompensationsfaktor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_UNDER_DELIVERY_COMPENSATION_LIMIT_FACTOR": "Unterbelieferungsbegrenzungsfaktor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_COMPENSATION_RESOLUTION_KW": "Auflösung Kompensation (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_IS_AT_SETPOINT_UPPER_TOLERANCE_FACTOR": "Am Sollwert: oberer Toleranzfaktor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_IS_AT_SETPOINT_UPPER_TOLERANCE_MINIMUM_KW": "Am Sollwert: obere Toleranz min (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_IS_AT_SETPOINT_LOWER_TOLERANCE_FACTOR": "Am Sollwert: unterer Toleranzfaktor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DC_IS_AT_SETPOINT_LOWER_TOLERANCE_MINIMUM_KW": "Am Sollwert: untere Toleranz min (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DG_ACTIVATION": "Aktivierung", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DG_ACTIVATION_TYPE": "DG Aktivierungstyp", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DISPATCH": "Einsatzplanung", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DISPATCH_COMMANDS": "Einsatzplanungsbefehle", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DISPATCH_SOURCE": "Herkunft der Einsatzplanung", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_NOMINATED_VOLUME_ACTIVATION_FACTOR": "Nominiertes Volumen Aktivierungsfaktor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_NOMINATED_VOLUME_SYMMETRIC_ACTIVATION": "Nominiertes Volumen symetrische Aktivierung", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_UI_EDG_SCHEDULE": "UI EDG Fahrplan", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_RESIDUAL_SHAPE_POSITIVE_THRESHOLD": "<PERSON><PERSON> (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_RESIDUAL_SHAPE_NEGATIVE_THRESHOLD": "negativer <PERSON><PERSON><PERSON><PERSON><PERSON> (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_RESIDUAL_SHAPE_WINDOW": "Zeitfenster (Sekunden)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_PRICE_TRIGGER_WINDOW": "Zeitfenster (Sekunden)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_PRICE_TRIGGER_PRICE_TYPE": "Preisart", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_PRICE_TRIGGER_SETTLEMENT_PERIOD": "Abrechnungsperioden", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_PRICE_TRIGGER_PRICE_THRESHOLD_NEG": "Negative Preisgrenze", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_PRICE_TRIGGER_PRICE_THRESHOLD_POS": "Positive Preisgrenze", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_PRICE_TRIGGER_PRICE_EXPIRATION_SECONDS": "Preisgültigkeitsdauer (sec)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.DS_NLAFRR_BLEEDING_TIME_SECONDS": "Bleeding Time (seconds)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.EXECUTION_PLAN_WINDOW_SECONDS": "Berechnungszeitfenster für Ausführungsplan (Sekunden)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.EXECUTION_PLAN_FREQUENCY_SECONDS": "Berechnungsfrequenz für Ausführungsplan (Sekunden)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.EXECUTION_PLAN_FREQUENCY_INFO_HTML": "Untere Grenze: 1 Sekunde; obere Grenze: unbegrenzt; Für optimales Verhalten sollte der Wert so groß wie möglich und so klein wie nötig sein.", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NOMINATION_EXTENSION_TIMES": "Nomination Extension Times", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NET_EXTEND_BEFORE_NOMINATION_SECONDS": "Extend before nomination (<PERSON><PERSON><PERSON>)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NET_OVERLAP_NOMINATIONS_SECONDS": "Overlap nomination (Se<PERSON>nden)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NET_STOP_BEFORE_END_OF_NOMINATION_SECONDS": "Stop before end of nomination (Se<PERSON>nden)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NOTIFICATIONS": "Benachrichtigungen", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_FLEX_TOO_LOW": "Benachrichtigung: <PERSON><PERSON> zu ni<PERSON>rig", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_FLEX_TOO_LOW_THRESHOLD_FACTOR": "Schwellenwertfaktor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_FLEX_TOO_LOW_THRESHOLD_BUFFER_SECONDS": "Schwellenwertpuffer (Sekunden)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_OVER_DELIVERY_NEGATIVE": "Benachrichtigung – zu hohe negative Lieferung", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_OVER_DELIVERY_NEGATIVE_THRESHOLD_KW": "negativer <PERSON><PERSON><PERSON><PERSON><PERSON> (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_OVER_DELIVERY_NEGATIVE_DELAY_SECONDS": "negative Verzögerung (Sekunden)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_OVER_DELIVERY_POSITIVE": "Benachrichtigung – zu hohe positive Lieferung", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_OVER_DELIVERY_POSITIVE_THRESHOLD_KW": "<PERSON><PERSON> (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_OVER_DELIVERY_POSITIVE_DELAY_SECONDS": "positive Verzögerung (Sekunden)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED": "Benachrichtigung – Sollwert nicht erreicht", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED_REACH_SETPOINT_IN_SECONDS": "Sollwert zu erreichen (Sekunden)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED_IS_AT_SETPOINT_DETECTION": "Feststellung: Sollwert erreicht", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED_IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_FACTOR": "Oberer Toleranzfaktor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED_IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_MINIMUM_KW": "<PERSON>beres Toleranzmini<PERSON> (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED_IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_FACTOR": "Unterer Toleranzfaktor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_NOT_REACHED_IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_MINIMUM_KW": "Unteres Toleranzminimum (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_REACHABLE": "Notifications - Setpoint Reachable", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_REACHABLE_IS_AT_SETPOINT_DETECTION": "Is at Setpoint detection", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_REACHABLE_IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_FACTOR": "Upper tolerance factor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_REACHABLE_IS_AT_SETPOINT_DETECTION_UPPER_TOLERANCE_MINIMUM_KW": "Upper Tolerance Minimum (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_REACHABLE_IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_FACTOR": "Lower tolerance factor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.NTF_SETPOINT_REACHABLE_IS_AT_SETPOINT_DETECTION_LOWER_TOLERANCE_MINIMUM_KW": "Lower tolerance minimum (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.PERIODICITY": "Periodicity", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.PERIODICITY_DG_AGGREGATIONS_SECONDS": "DG aggregations (Sekunden)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.PERIODICITY_OUTPUT_SIGNAL_WRITE_SECONDS": "Output signal write (Sekunden)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.RAMP_ADJUSTMENT_STRATEGY": "Ramp adjustment strategy", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.REDISPATCH_TRIGGERS": "Redispatch triggers", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.RT_ASSET_AVAILABLE_FLEX_CHANGE_THRESHOLD_KW": "Av. flex change: Thresh<PERSON> (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.RT_ASSET_AVAILABLE_FLEX_CHANGE_SINCE_ACTIVATION_FACTOR": "Av. flex change since activation: Factor", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.RT_DG_TOTAL_DEVIATION_BUFFER_KW": "Total dev. Buffer (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.RT_DG_TOTAL_DEVIATION_SUPPRESS_REDISPATCH_SECONDS": "Total dev. Suppress redispatch (Sekunden)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION": "Setpoint Validation", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION_REACTION_TIME_SECONDS": "Reaction time (Sekunden)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION_CAP_BY_NOMINATION": "Cap by nomination", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION_MINIMUM_DURATION_OF_DISPATCH_SECONDS": "Minimum duration of dispatch (Sekunden)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION_NOMINATION_INTERVAL_VALIDATION": "Nomination interval validation", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION_QUANTIZATION_FILTER_RESOLUTION_KW": "Quantization resolution (kW)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SETPOINT_VALIDATION_QUANTIZATION_FILTER_QUANTIZATION_DURATION_SECONDS": "Quantization duration (Sekunden)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SIGNALS": "Signals", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SIGNALS_OUTPUT_SIGNALS": "Output Signals", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SIGNALS_SUB_POOLS_VALUES_ON": "Subpool Values On", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SIGNALS_SUB_POOLS_VALUES_OFF": "Subpool Values Off", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SIGNALS_DSO_FOR_SIGNALS": "DSO for signals", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.SIGNALS_HEART_BEAT_MIRROR": "Heart beat mirror", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.BATTERY_DISPATCH_CONFIG": "Battery Dispatch Config", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.BATTERY_DISPATCH_CONFIG_MIN_POWER_FOR_DISPATCH_DISCHARGE": "Min Power For Dispatch Discharge(%)", "CONFIGURATION.MARKET_LEVEL.DISPATCH_GROUPS_CONFIG.LABEL.BATTERY_DISPATCH_CONFIG_MIN_POWER_FOR_DISPATCH_CHARGE": "Min Power For Dispatch Charge(%)", "CONFIGURATION.ROLLUPS.TAB.ASSET_ROLLUPS": "<PERSON><PERSON>", "CONFIGURATION.ROLLUPS.TAB.DISPATCH_GROUP_ROLLUPS": "Abrufgruppe Rollups", "CONFIGURATION.ASSET_ROLLUPS.SELECT_ROLLUP_TYPE": "<PERSON><PERSON> Metrik <PERSON>w<PERSON>hl<PERSON>", "CONFIGURATION.ASSET_ROLLUPS.SELECT_ITEM_TYPE": "Typ auswählen", "CONFIGURATION.ASSET_ROLLUPS.SELECT_ASSET": "<PERSON><PERSON> au<PERSON>", "CONFIGURATION.ASSET_ROLLUPS.SELECT_ROLLUP_GROUP": "Rollup Gruppierungsname auswählen", "CONFIGURATION.ASSET_ROLLUPS.SELECT_INTERVAL": "Intervall auswählen", "CONFIGURATION.ASSET_ROLLUPS.BTN_REGENERATE": "<PERSON><PERSON>", "CONFIGURATION.ASSET_ROLLUPS.EDIT_ROLLUP_FAMILIES": "<PERSON><PERSON> bearbeiten", "CONFIGURATION.ASSET_ROLLUPS.TITLE_ROLLUP_REGENERATION": "Rollup neu erstellen", "CONFIGURATION.ASSET_ROLLUPS.TITLE_ROLLUP_MANAGEMENT": "Rollup Management Visualisierung", "CONFIGURATION.ASSET_ROLLUPS.ADD": "Hinzufügen", "CONFIGURATION.ASSET_ROLLUPS.SAVE": "Speichern", "CONFIGURATION.ASSET_ROLLUPS.ROLLUP_METRIC": "<PERSON><PERSON>", "CONFIGURATION.ASSET_ROLLUPS.PRODUCT": "Produkt", "CONFIGURATION.DG_ROLLUPS.SELECT_ROLLUP_TYPE": "<PERSON><PERSON> Metrik <PERSON>w<PERSON>hl<PERSON>", "CONFIGURATION.DG_ROLLUPS.SELECT_DISPATCH_GROUP": "Abrufgruppe auswählen", "CONFIGURATION.DG_ROLLUPS.SELECT_INTERVAL": "Intervall auswählen", "CONFIGURATION.DG_ROLLUPS.BTN_REGENERATE": "<PERSON><PERSON>", "CONFIGURATION.DG_ROLLUPS.TITLE_ROLLUP_REGENERATION": "Rollup neu erstellen", "CONFIGURATION.DG_ROLLUPS.TITLE_ROLLUP_MANAGEMENT": "Rollup Management Visualisierung", "ISSUE_UPLOADING_FILE": "Problem beim <PERSON>.", "NO_FILE_UPLOADED": "<PERSON><PERSON>.", "ISSUE_SUBMITTING_ASSET_OPTIMIZATION": "Issue Submitting Asset Optimization.", "HISTORY.BIDS.DETAIL.CONTENT_TYPE.NA": "N/A", "HISTORY.BIDS.DETAIL.CONTENT_TYPE.ui_update": "Gelöscht", "HISTORY.BIDS.DETAIL.CONTENT_TYPE.file_import": "Datei / API", "HISTORY.BIDS.DETAIL.CONTENT_TYPE.auction_import": "Automatisch", "HISTORY.BIDS.DETAIL.CONTENT_TYPE.external_import": "V2G", "HISTORY.BIDS.DETAIL.CONTENT_TYPE.vpp_migration": "Ein<PERSON>ig", "HISTORY.BIDS.DETAIL.CONTENT_TYPE.battery_uk_optimizer": "Battery UK Optimizer", "HISTORY.BIDS.DETAIL.VALIDATION": "Validation", "HISTORY.BIDS.DETAIL.ERRORS": "<PERSON><PERSON>", "HISTORY.BIDS.DETAIL.WARNINGS": "Warnungen", "HISTORY.BIDS.DETAIL.TYPE": "Herkunft", "HISTORY.BIDS.DETAIL.PERFORMED_ON": "Datum", "HISTORY.BIDS.DETAIL.USER": "<PERSON><PERSON><PERSON>", "HISTORY.BIDS.DETAIL.TABLE.ERROR_DESCRIPTION": "Error Des<PERSON>", "HISTORY.BIDS.DETAIL.TABLE.WARNING_DESCRIPTION": "Error Des<PERSON>", "HISTORY.BIDS.DETAIL.TABLE.DETAILS": "Details", "HISTORY.BIDS.TABLE.PERFORMED_ON": "Datum", "HISTORY.BIDS.TABLE.USER": "<PERSON><PERSON><PERSON>", "HISTORY.BIDS.TABLE.CONTENT": "Herkunft", "HISTORY.BIDS.TABLE.SUCCESSFULLY_IMPORTED": "Erfolgreich importiert", "HISTORY.BIDS.TABLE.ERRORS": "<PERSON><PERSON>", "HISTORY.BIDS.TABLE.WARNINGS": "Warnungen", "CONFIGURATION.SCHEDULING_REPORTS.DOWNLOAD_SCHEDULING": "Fahrplanberichte herunterladen", "CONFIGURATION.SCHEDULING_REPORTS.SEND_SCHEDULING": "Fahrplanberichte senden", "CONFIGURATION.SCHEDULING_REPORTS.DOWNLOAD_EDG": "Fahrplanbericht an EDG herunterladen", "CONFIGURATION.SCHEDULING_REPORTS.DOWNLOAD_UGC": "Fahrplanbericht an UGC herunterladen", "CONFIGURATION.SCHEDULING_REPORTS.DOWNLOAD_TP": "Fahrplanbericht an dritte BKs herunterladen", "CONFIGURATION.SCHEDULING_REPORTS.TSO": "ÜNB", "CONFIGURATION.SCHEDULING_REPORTS.DATE": "Datum", "CONFIGURATION.SCHEDULING_REPORTS.MARKET": "Mark<PERSON>", "CONFIGURATION.SCHEDULING_REPORTS.DOWNLOAD": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CONFIGURATION.SCHEDULING_REPORTS.DIRECTION": "<PERSON><PERSON><PERSON>", "CONFIGURATION.SCHEDULING_REPORTS.BALANCING_GROUP": "Bilanzkreis", "CONFIGURATION.SCHEDULING_REPORTS.SEND_TP": "Fahrplanberichte an dritte BKV senden", "CONFIGURATION.SCHEDULING_REPORTS.SEND_BK": "BK6-17-046-Berichte senden", "CONFIGURATION.SCHEDULING_REPORTS.SEND": "Senden", "SEND_FILES_EXTERNALLY.SEND_ALLOCATION_FILES": "NL FCR - Allokationsdatei an TenneT senden", "SEND_FILES_EXTERNALLY.SEND_MEASUREMENT_FILES": "NL FCR - Messwertdatei an TenneT senden.", "SEND_FILES_EXTERNALLY.SEND_AFRR_MEASUREMENT_FILES": "NL aFRR - Messwertdatei an TenneT senden.", "SEND_FILES_EXTERNALLY.SEND_AFRR_ACTIVATED_ENERGY_DOCUMENTS": "NL aFRR - Activated Energy Document an TenneT senden.", "SEND_FILES_EXTERNALLY.SEND_AFRR_ACTIVATED_ENERGY_DOCUMENTS.GENERATE_NEW_RANDOM_MR_ID": "Neue zufällige mrID erzeugen", "SEND_FILES_EXTERNALLY.SEND_AFRR_ACTIVATED_ENERGY_DOCUMENTS.MR_ID": "mr<PERSON>", "SEND_FILES_EXTERNALLY.DATE": "Datum", "SEND_FILES_EXTERNALLY.DATE_TIME": "Datum und Zeit", "SEND_FILES_EXTERNALLY.SEND": "Senden"}