import { platformBrowserDynamic } from "@angular/platform-browser-dynamic";
import { NgModule } from "@angular/core";
import { BrowserModule } from "@angular/platform-browser";
import { FormsModule } from "@angular/forms";
import { HttpClientModule, HttpClient } from '@angular/common/http';
import { Router } from "@angular/router";
import { AppComponent } from "./components/app.component";
import { Routing, appRoutingProviders } from "./routing/app.routing";
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { SlimLoadingBarModule } from 'ng2-slim-loading-bar';
import { FileUploadModule } from 'ng2-file-upload';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { TranslateModule, TranslateLoader } from '@ngx-translate/core';
import { TranslateHttpLoader } from '@ngx-translate/http-loader';
import { NgxDatetimeRangePickerModule } from 'ngx-datetime-range-picker';
import { OwlDateTimeModule, OWL_DATE_TIME_FORMATS, OwlNativeDateTimeModule, OwlDateTimeIntl} from 'ng-pick-datetime';
import { OwlMomentDateTimeModule } from "ng-pick-datetime/date-time/adapter/moment-adapter/moment-date-time.module";
import { angularData } from "./../global.export";
import { environment } from "./../environments/environment";
import { ChartModule, HIGHCHARTS_MODULES } from 'angular-highcharts';
import * as more from 'highcharts/highcharts-more.src';
import * as exporting from 'highcharts/modules/exporting.src';
import * as moment from "moment-timezone";
import { AgGridModule } from "ag-grid-angular";

/* Components */
import {  ConfirmState, ConfirmModalComponent } from './services/confirm.service';
import { DashboardComponent } from './components/dashboard/dashboard.component';
import { NominationsComponent } from './components/nominations/nominations.component';
import { RollupsComponent } from './components/rollups/rollups.component';
import { EnterNominationComponent } from './components/nominations/enter_nomination/enter_nomination.component'
import { UploadNominationFileComponent } from './components/nominations/upload_nomination_file/upload_nomination_file.component'
import { UploadOptiResultsFileComponent } from './components/nominations/upload_opti_results_file/upload_opti_results_file.component'
import { DownloadOptiResultsComponent } from './components/nominations/download_opti_results/download_opti_results.component'
import { UploadPerfDataFileComponent } from "./components/nominations/upload_perf_data_file/upload_perf_data_file.component";
import { UploadBodFileComponent } from "./components/nominations/upload_bod_file/upload_bod_file.component";
import { UploadMarginDataFileComponent } from "./components/nominations/upload_margin_data_file/upload_margin_data_file.component";
import { UploadDeclarationOfUnavailabilityComponent } from "./components/nominations/upload_declaration_of_unavailability/upload_declaration_of_unavailability.component";
import { UploadMarketResultComponent } from './components/nominations/upload_market_result/upload_market_result.component'
import { AuctionResultsComponent } from './components/auction/results/results.component';
import { AllocationsComponent } from './components/allocations/allocations.component';
import { EnterAllocationComponent } from './components/allocations/enter_allocation/enter_allocation.component';
import { UploadAllocationFileComponent } from './components/allocations/upload_allocation_file/upload_allocation_file.component';
import { VisualizeAllocationsComponent } from './components/allocations/visualize_allocations/visualize_allocations.component';
import { HistoryDistributedUnitComponent } from './components/history/distributed_units/distributed_units.component';
import { NominationsHistoryComponent } from './components/history/distributed_units/nominations/nominations.component';
import { BidsHistoryComponent } from "./components/history/distributed_units/bids_history/bids_history.component";
import { BidDetailComponent } from "./components/history/distributed_units/bids_history/bid_detail.component";
import { ManualAllocationsHistoryComponent } from './components/history/distributed_units/manual_allocations/manual_allocations.component';
import { AutomaticAllocationsHistoryComponent } from './components/history/distributed_units/automatic_allocations/automatic_allocations.component';
import { AuctionsHistoryComponent } from './components/history/distributed_units/auctions/auctions.component';
import { ReportingEmailsHistoryComponent } from './components/history/reporting_emails/reporting_emails.component';
import { ReportingEmailComponent } from './components/history/reporting_email/reporting_email.component';
import { RollupHistoryComponent } from "./components/rollups/rollup_history/rollup_history.component";
import { RollupErrorsComponent } from "./components/rollups/rollup_errors/rollup_errors.component";
import { RollupErrorDetailComponent } from "./components/rollups/rollup_error_detail/rollup_error_detail.component";
import { DlmChargingSessionsComponent } from './components/history/dlm_charging_sessions/dlm_charging_sessions.component';
import { DlmChargingSessionFileComponent } from './components/history/dlm_charging_sessions/upload_dlm_charging_session_file/upload_dlm_charging_session_file.component';
import { FileUploaderComponent } from './components/common/file-uploader/file-uploader.component';
import { FileUploadResultsComponent} from "./components/common/file-upload-results/file-upload-results.component";
import { NominationsListComponent } from './components/nominations/nominations_list/nominations_list.component';
import { NominationDetailComponent } from './components/nominations/nomination_detail/nomination_detail.component';
import { HeaderComponent } from './components/header/header.component';
import { AutomaticReportEmailingsComponent } from './components/automatic_report_emailings/automatic_report_emailings.component';
import { TsoReportsComponent } from './components/reports/tso/tso.component';
import { AutomaticReportEmailingsRollupComponent } from './components/automatic_report_emailings/rollup/rollup.component';
import { AutomaticReportEmailingsAllocationComponent } from './components/automatic_report_emailings/allocation/allocation.component';
import { AutomaticReportEmailingsSchedulingReportComponent } from './components/automatic_report_emailings/scheduling-report/scheduling-report.component';
import { EventNotificationsRibbonComponent } from "./components/event_notifications/event_notifications_ribbon.component";
import { EventNotificationRecipientsComponent } from "./components/event_notifications/recipients/event_notification_recipients.component";
import { EventNotificationAuctionConfigRecipientsComponent } from "./components/event_notifications/auction_config_recipients/event_notification_auction_config_recipients.component";
import { EventNotificationDispatchGroupRecipientsComponent } from "./components/event_notifications/dispatch_group_recipients/event_notification_dispatch_group_recipients.component";
import { TechSupportComponent } from './components/tech-support/tech-support.component';
import { LoaderComponent } from './components/common/loader/loader.component';
import { AutomaticAllocationComponent } from './components/allocations/automatic_allocation/automatic_allocation.component';
import { ManualAllocationComponent } from './components/allocations/manual_allocation/manual_allocation.component';
import { BiddingComponent } from './components/auction/bidding/bidding.component';
import { UploadBidsComponent } from './components/auction/bidding/upload_bids_file/upload_bids_file.component';
import { PrepareBidsComponent } from './components/auction/bidding/prepare_bids/prepare_bids.component';
import { AssetOptimizationComponent } from './components/auction/bidding/asset_optimization/asset_optimization.component';
import { BidsListComponent } from './components/auction/bidding/bids_list/bids_list.component';
import { AssetConfigurationRibbonComponent } from "./components/configuration/asset_level/asset_configuration_ribbon.component";
import { MarketConfigurationRibbonComponent } from "./components/configuration/market_level/market_configuration_ribbon.component";
import { RollupConfigurationRibbonComponent } from "./components/configuration/rollup_level/rollup_configuration_ribbon.component";
import { BoxTypesConfigurationComponent } from "./components/configuration/asset_level/box_types/box_types_configuration.component";
import { BoxTypesListComponent } from "./components/configuration/asset_level/box_types/box_types_list.component";
import { BoxTypeFormComponent } from "./components/configuration/asset_level/box_types/box_type_form.component";
import { SteeringTypesComponent } from "./components/configuration/asset_level/steering_types/steering_types.component";
import { SteeringTypesListComponent } from "./components/configuration/asset_level/steering_types/steering_types_list.component";
import { SteeringTypeFormComponent } from "./components/configuration/asset_level/steering_types/steering_type_form.component";
import { SignalListsListComponent } from "./components/configuration/asset_level/signal_lists/signal_lists_list.component";
import { SignalListsComponent } from "./components/configuration/asset_level/signal_lists/signal_lists.component";
import { SignalListFormComponent } from "./components/configuration/asset_level/signal_lists/signal_list_form.component";
import { SubpoolsConfigurationComponent } from "./components/configuration/market_level/subpools/subpools_configuration.component";
import { SubpoolsListComponent } from "./components/configuration/market_level/subpools/subpools_list.component";
import { SubpoolFormComponent } from "./components/configuration/market_level/subpools/subpool_form.component";
import { BalancingGroupsConfigurationComponent } from "./components/configuration/market_level/balancing_groups/balancing_groups_configuration.component";
import { BalancingGroupsListComponent } from "./components/configuration/market_level/balancing_groups/balancing_groups_list.component";
import { BalancingGroupFormComponent } from "./components/configuration/market_level/balancing_groups/balancing_group_form.component";
import { DispatchGroupsConfigurationComponent } from "./components/configuration/market_level/dispatch_groups/dispatch_groups_configuration.component";
import { DispatchGroupsListComponent } from "./components/configuration/market_level/dispatch_groups/dispatch_groups_list.component";
import { DgBgFormComponent } from "./components/configuration/market_level/dispatch_groups/dg_bg_form.component";
import { DgDlmParamsComponent } from "./components/configuration/market_level/dispatch_groups/dg_dlm_params.component";
import { AssetRollupsConfigurationComponent } from "./components/configuration/rollup_level/asset_rollups/asset_rollups_configuration.component";
import { RollupFamiliesFormComponent } from "./components/configuration/rollup_level/asset_rollups/rollup_families_form.component";
import { AssetRollupRegenerationComponent } from "./components/configuration/rollup_level/asset_rollups/asset_rollup_regeneration.component";
import { DispatchGroupRollupsConfigurationComponent } from "./components/configuration/rollup_level/dispatch_group_rollups/dispatch_group_rollups_configuration.component";
import { DispatchGroupRollupRegenerationComponent } from "./components/configuration/rollup_level/dispatch_group_rollups/dispatch_group_rollup_regeneration.component";
import { AuctionsConfigurationComponent } from "./components/configuration/market_level/auctions/auctions_configuration.component";
import { AuctionListComponent } from "./components/configuration/market_level/auctions/auction_list.component";
import { AuctionFormComponent } from "./components/configuration/market_level/auctions/auction_form.component";
import { SchedulingReportsRibbonComponent } from "./components/reporting_and_notifications/scheduling_reports/scheduling_reports_ribbon.component";
import { DownloadSchedulingComponent } from "./components/reporting_and_notifications/scheduling_reports/download_scheduling.component";
import { SendSchedulingComponent } from "./components/reporting_and_notifications/scheduling_reports/send_scheduling.component";
import { V2gHistoryComponent } from "./components/history/distributed_units/v2g/v2g.component";
import { SendFilesExternallyComponent } from "./components/event_notifications/send-files-externally/send_files_externally.component";

/* AgGrid Custom Components */
import { ProductsRendererComponent } from "./components/configuration/rollup_level/asset_rollups/products_renderer.component";
import { ProductsEditorComponent } from "./components/configuration/rollup_level/asset_rollups/products_editor.component";
import { RollupFamilyHeaderComponent } from "./components/configuration/rollup_level/asset_rollups/rollup_family_header.component";

/* Services */
import { HttpClientService } from "./services/httpclient.service";
import { GlobalService } from './services/global.service';
import { NotificationService } from './services/notification.service';
import { ConfirmService } from "./services/confirm.service";
import { ChartService } from './services/chart.service';

/* Providers */
import { AuctionProvider } from './providers/auction.provider';
import { HistoryProvider } from './providers/history.provider';
import { RollupsProvider} from "./providers/rollups.provider";
import { UserProvider } from "./providers/user.provider";
import { DispatchGroupProvider } from './providers/dispatch_group.provider';
import { AssetProvider } from './providers/asset.provider';
import { AssetOptimizationProvider } from './providers/asset_optimization.provider';
import { AllocationProvider } from './providers/allocation.provider';
import { NominationProvider } from './providers/nomination.provider';
import { ReportProvider } from './providers/report.provider';
import { UrlProvider } from './providers/url.provider';
import { TsoProvider } from './providers/tso.provider';
import { MarketProvider } from './providers/market.provider';
import { EmailConfigurationProvider } from './providers/email_configuration.provider';
import { ChartProvider } from './providers/chart.provider';
import { BidProvider } from  "./providers/bid.provider";
import { EventNotificationProvider } from "./providers/event_notification.provider";
import { AssetConfigurationProvider } from "./providers/asset_configuration.provider";
import { SubpoolProvider } from "./providers/subpool.provider";
import { AuctionsProvider } from "./providers/auctions.provider";
import { BalancingGroupProvider } from "./providers/balancing_group.provider";
import { RollupConfigProvider } from "./providers/rollup_config.provider";
import { SchedulingReportProvider } from "./providers/scheduling_report.provider";
import { SendFilesExternallyProvider } from "./providers/send_files_externally.provider";

/* Pipes */
import { HumanizePipe } from './pipes/humanize.pipe';
import { LocalDatePipe } from './pipes/localdate.pipe';
import { TzDatePipe } from './pipes/tzdate.pipe';
import { MegawattPipe } from './pipes/megawatt.pipe';
import { SourceTypePipe } from './pipes/source_type.pipe';
import { BidSourceTypePipe } from "./components/history/distributed_units/bids_history/bid_source_type.pipe";
import { NominationSourceTypePipe } from './pipes/nomination_source_type.pipe';
import {DispatchGroupFormComponent} from "./components/configuration/market_level/dispatch_groups/dispatch_group_form.component";
import { FindNameByIdPipe } from './pipes/find_name_by_id.pipe';
import { ShowParametersForPipe } from './pipes/show_parameters_for.pipe';
import {GenericDgConfigComponent} from "./components/configuration/market_level/dispatch_groups/generic_dg_config.component";
import {GenericDgConfigNotificationsComponent} from "./components/configuration/market_level/dispatch_groups/generic_dg_config_notifications.component";
import {OWL_DTPICKER_SCROLL_STRATEGY} from "ng-pick-datetime/date-time/date-time-picker.component";
import {BlockScrollStrategy, Overlay, RepositionScrollStrategy} from "@angular/cdk/overlay";

export function HttpLoaderFactory(http: HttpClient) {
  return new TranslateHttpLoader(http, `${environment.apiPath}/assets/i18n/`, '.json');
}

export class DefaultIntl extends OwlDateTimeIntl {
  cancelBtnLabel= angularData.railsExports.locale == 'en-GB' ? 'Cancel': 'Aufheben';
  setBtnLabel= angularData.railsExports.locale == 'en-GB' ? 'Accept': 'Akzeptieren';
  rangeFromLabel= angularData.railsExports.locale == 'en-GB' ? 'From' : 'von';
  rangeToLabel= angularData.railsExports.locale == 'en-GB' ? 'To' : 'bis';
}

export function VPP_OWL_DTPICKER_SCROLL_STRATEGY_PROVIDER_FACTORY(
    overlay: Overlay
): () => RepositionScrollStrategy {
  const fn = () => overlay.scrollStrategies.reposition();
  return fn;
}

@NgModule({
  imports: [
    BrowserAnimationsModule,
    BrowserModule,
    FormsModule,
    HttpClientModule,
    NgbModule.forRoot(),
    SlimLoadingBarModule.forRoot(),
    FileUploadModule,
    Routing,
    NgMultiSelectDropDownModule.forRoot(),
    NgxDatetimeRangePickerModule.forRoot(),
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpClient]
      }
    }),
    ChartModule,
    OwlDateTimeModule,
    OwlMomentDateTimeModule,
    AgGridModule.withComponents([
        ProductsRendererComponent,
        ProductsEditorComponent,
        RollupFamilyHeaderComponent
    ])
  ],
  declarations: [
    AppComponent,
    ConfirmModalComponent,
    AuctionResultsComponent,
    DashboardComponent,
    NominationsComponent,
    RollupsComponent,
    EnterNominationComponent,
    UploadNominationFileComponent,
    UploadOptiResultsFileComponent,
    DownloadOptiResultsComponent,
    UploadPerfDataFileComponent,
    UploadBodFileComponent,
    UploadMarginDataFileComponent,
    UploadMarketResultComponent,
    UploadDeclarationOfUnavailabilityComponent,
    HistoryDistributedUnitComponent,
    NominationsHistoryComponent,
    BidsHistoryComponent,
    BidDetailComponent,
    ManualAllocationsHistoryComponent,
    AutomaticAllocationsHistoryComponent,
    ReportingEmailsHistoryComponent,
    ReportingEmailComponent,
    AuctionsHistoryComponent,
    RollupHistoryComponent,
    RollupErrorsComponent,
    RollupErrorDetailComponent,
    DlmChargingSessionsComponent,
    DlmChargingSessionFileComponent,
    AllocationsComponent,
    FileUploaderComponent,
    FileUploadResultsComponent,
    NominationsListComponent,
    NominationDetailComponent,
    EnterAllocationComponent,
    UploadAllocationFileComponent,
    AutomaticReportEmailingsComponent,
    AutomaticReportEmailingsRollupComponent,
    AutomaticReportEmailingsAllocationComponent,
    AutomaticReportEmailingsSchedulingReportComponent,
    EventNotificationsRibbonComponent,
    EventNotificationRecipientsComponent,
    EventNotificationAuctionConfigRecipientsComponent,
    EventNotificationDispatchGroupRecipientsComponent,
    AssetConfigurationRibbonComponent,
    MarketConfigurationRibbonComponent,
    RollupConfigurationRibbonComponent,
    BoxTypesConfigurationComponent,
    BoxTypesListComponent,
    BoxTypeFormComponent,
    SteeringTypesComponent,
    SteeringTypesListComponent,
    SteeringTypeFormComponent,
    SignalListsListComponent,
    SignalListsComponent,
    SignalListFormComponent,
    SubpoolsConfigurationComponent,
    SubpoolsListComponent,
    SubpoolFormComponent,
    BalancingGroupsConfigurationComponent,
    BalancingGroupsListComponent,
    BalancingGroupFormComponent,
    DispatchGroupsConfigurationComponent,
    DispatchGroupsListComponent,
    DispatchGroupFormComponent,
    GenericDgConfigComponent,
    GenericDgConfigNotificationsComponent,
    DgBgFormComponent,
    DgDlmParamsComponent,
    AssetRollupsConfigurationComponent,
    DispatchGroupRollupsConfigurationComponent,
    TsoReportsComponent,
    TechSupportComponent,
    LoaderComponent,
    HeaderComponent,
    AutomaticAllocationComponent,
    ManualAllocationComponent,
    VisualizeAllocationsComponent,
    BiddingComponent,
    UploadBidsComponent,
    PrepareBidsComponent,
    AssetOptimizationComponent,
    BidsListComponent,
    HumanizePipe,
    LocalDatePipe,
    TzDatePipe,
    MegawattPipe,
    SourceTypePipe,
    BidSourceTypePipe,
    NominationSourceTypePipe,
    FindNameByIdPipe,
    ShowParametersForPipe,
    AuctionsConfigurationComponent,
    AuctionListComponent,
    AuctionFormComponent,
    ProductsRendererComponent,
    ProductsEditorComponent,
    RollupFamilyHeaderComponent,
    RollupFamiliesFormComponent,
    AssetRollupRegenerationComponent,
    DispatchGroupRollupRegenerationComponent,
    SchedulingReportsRibbonComponent,
    DownloadSchedulingComponent,
    SendSchedulingComponent,
    V2gHistoryComponent,
    SendFilesExternallyComponent
  ],
  providers: [
    appRoutingProviders,
    HttpClientService,
    GlobalService,
    NotificationService,
    ConfirmService,
    ConfirmState,
    AuctionProvider,
    HistoryProvider,
    RollupsProvider,
    UserProvider,
    DispatchGroupProvider,
    AssetProvider,
    AssetOptimizationProvider,
    AllocationProvider,
    NominationProvider,
    UrlProvider,
    ReportProvider,
    TsoProvider,
    MarketProvider,
    EmailConfigurationProvider,
    BidProvider,
    ChartProvider,
    ChartService,
    {
      provide: HIGHCHARTS_MODULES,
      useFactory: () => [ more, exporting ]
    },
    {
      provide: OwlDateTimeIntl,
      useClass: DefaultIntl
    },
    {
      provide: OWL_DTPICKER_SCROLL_STRATEGY,
      deps: [Overlay],
      useFactory: VPP_OWL_DTPICKER_SCROLL_STRATEGY_PROVIDER_FACTORY
    },
    EventNotificationProvider,
    AssetConfigurationProvider,
    SubpoolProvider,
    AuctionsProvider,
    BalancingGroupProvider,
    RollupConfigProvider,
    SchedulingReportProvider,
    SendFilesExternallyProvider
  ],
  bootstrap: [
    AppComponent
  ]
})
export class AppModule {
  constructor() {
    moment.tz.setDefault(angularData.railsExports.timeZone);
    moment.locale(angularData.railsExports.locale);
  }
}
