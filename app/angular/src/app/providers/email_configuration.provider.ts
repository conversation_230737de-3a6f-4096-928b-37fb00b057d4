import { Injectable } from '@angular/core';
import { HttpClientService } from './../services/httpclient.service';

@Injectable()
export class EmailConfigurationProvider {

  constructor(private http: HttpClientService) {}

  schedulingReport() {
    return this.http.get('/api/v1/email_configuration/scheduling_report');
  }

  saveSchedulingReport(params) {
    return this.http.post('/api/v1/email_configuration/scheduling_report', params);
  }

  rolllup() {
    return this.http.get('/api/v1/email_configuration/rollup');
  }

  saveRolllup(params) {
    return this.http.post('/api/v1/email_configuration/rollup', params);
  }

  allocation() {
    return this.http.get('/api/v1/email_configuration/allocation');
  }

  saveAllocation(params) {
    return this.http.post('/api/v1/email_configuration/allocation', params);
  }


}
