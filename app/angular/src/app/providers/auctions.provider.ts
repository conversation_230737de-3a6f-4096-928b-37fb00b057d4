import { Injectable } from '@angular/core';
import { HttpClientService } from './../services/httpclient.service';

@Injectable()
export class AuctionsProvider {

  constructor(private http: HttpClientService) {}

  findAllWithPagination(params?) {
    return this.http.get('/api/v1/auction_configs', params);
  }

  findAll(params?) {
    return this.http.get('/api/v1/auction_configs_with_names', params);
  }

  forAssetOptimization(params?) {
    return this.http.get('/api/v1/auction_configs_with_names_for_asset_optimization', params);
  }

  create(params?) {
    return this.http.post('/api/v1/auction_configs/create', params);
  }

  update(params?) {
    return this.http.post(`/api/v1/auction_configs/${params.auction_config.id}/update`, params);
  }

  delete(auctionId) {
    return this.http.delete(`/api/v1/auction_configs/${auctionId}`);
  }

}
