import {Injectable} from "@angular/core";
import {HttpClientService} from "../services/httpclient.service";


@Injectable()
export class RollupConfigProvider {

    constructor(
        private _http: HttpClientService) {
    }

    getAssetRollupConfig() {
        return this._http.get('/api/v1/configuration/rollups/asset_rollups_data');
    }

    saveAssetRollupInterval(params) {
        return this._http.post('/api/v1/configuration/rollups/save_asset_rollup_interval', params);
    }

    saveAssetRollupProducts(params) {
        return this._http.post('/api/v1/configuration/rollups/save_asset_rollup_products', params);
    }

    updateAssetRollupFamilies(params) {
        return this._http.post('/api/v1/configuration/rollups/update_asset_rollup_families', params);
    }

    generateRollup(params) {
        return this._http.post('/api/v1/configuration/rollups/generate_rollup', params);
    }

    getDgRollupConfig() {
        return this._http.get('/api/v1/configuration/rollups/dg_rollups_data');
    }

    saveDgRollupInterval(params) {
        return this._http.post('/api/v1/configuration/rollups/save_dg_rollup_interval', params);
    }
}