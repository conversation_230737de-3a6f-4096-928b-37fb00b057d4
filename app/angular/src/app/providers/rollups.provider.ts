import { Injectable } from '@angular/core';
import { HttpClientService } from './../services/httpclient.service';

@Injectable()
export class RollupsProvider {

  constructor(private http: HttpClientService) {
  }

  getRollups(params) {
    console.log('Retrieving rollups for params', params);
    return this.http.get('/api/v1/rollups/rollups', params);
  }

  getRollupTypes(params) {
    return this.http.get('/api/v1/rollups/rollup_types', params);
  }

  getRollupErrors(params) {
    return this.http.get('/api/v1/rollups/rollup_errors', params);
  }

  getRollupError(id) {
    return this.http.get(`/api/v1/rollups/rollup_error/${id}`);
  }

}