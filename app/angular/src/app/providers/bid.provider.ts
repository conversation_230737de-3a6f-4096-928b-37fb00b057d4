import { Injectable } from '@angular/core';
import { HttpClientService } from './../services/httpclient.service';

@Injectable()
export class BidProvider {

  constructor(private http: HttpClientService) {}


  getTimeSlices() {
    return this.http.get(`/api/v1/bids/time_slices`);
  }

  getProducts() {
    return this.http.get('/api/v1/bids/products');
  }

  getEonNominationBids(params) {
    return this.http.get('/api/v1/bids/eon_nomination_bids', params);
  }

  deleteBids(params) {
    return this.http.get('/api/v1/bids/delete_bids', params);
  }

  deleteById(nominationId) {
    return this.http.delete(`/api/v1/nominations/distributed_unit/${nominationId}`)
  }

}
