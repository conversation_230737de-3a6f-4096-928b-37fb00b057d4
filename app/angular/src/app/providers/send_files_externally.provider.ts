import { Injectable } from "@angular/core";
import { HttpClientService } from "../services/httpclient.service";


@Injectable()
export class SendFilesExternallyProvider {

    constructor(
        private _http: HttpClientService
    ) {
    }

    sendAllocations(params) {
        return this._http.get('/api/v1/send_files_externally/send_allocations_files', params);
    }

    sendMeasurements(params) {
        return this._http.get('/api/v1/send_files_externally/send_measurements_files', params);
    }

    sendAfrrMeasurements(params) {
        return this._http.get('/api/v1/send_files_externally/send_afrr_measurements_files', params);
    }

    sendAfrrActivatedEnergyDocuments(params) {
        return this._http.get('/api/v1/send_files_externally/send_afrr_activated_energy_documents', params);
    }


}