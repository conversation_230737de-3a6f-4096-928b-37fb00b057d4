import { Injectable } from "@angular/core";
import { HttpClientService } from "../services/httpclient.service";


@Injectable()
export class SubpoolProvider {

    constructor(
        private _http: HttpClientService
    ) {
    }

    getSubpools(params) {
        return this._http.get('/api/v1/configuration/market/subpools', params);
    }

    getSubpoolSignals(params) {
        return this._http.get('/api/v1/configuration/market/subpool_signals', params);
    }

    createSubpool(params) {
        return this._http.post('/api/v1/configuration/market/create_subpool', params);
    }

    updateSubpool(params) {
        return this._http.post('/api/v1/configuration/market/update_subpool', params);
    }

    deleteSubpool(id) {
        return this._http.post('/api/v1/configuration/market/delete_subpool', {id: id});
    }

}