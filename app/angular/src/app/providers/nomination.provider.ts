import { Injectable } from '@angular/core';
import { HttpClientService } from './../services/httpclient.service';

@Injectable()
export class NominationProvider {

  constructor(private http: HttpClientService) {}

  create(params) {
    return this.http.post('/api/v1/nominations', params);
  }

  edit(params) {
    return this.http.post(`/api/v1/nomination/${params.distributed_unit.id}`, params);
  }

  findBy(params) {
    return this.http.get('/api/v1/nominations/distributed_units', params);
  }

  findById(id) {
    return this.http.get(`/api/v1/nominations/distributed_unit/${id}`);
  }

  deleteById(nominationId) {
    return this.http.delete(`/api/v1/nominations/distributed_unit/${nominationId}`)
  }

}
