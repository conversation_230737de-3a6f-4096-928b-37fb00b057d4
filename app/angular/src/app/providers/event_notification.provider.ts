import {Injectable} from "@angular/core";
import {HttpClientService} from "../services/httpclient.service";

@Injectable()
export class EventNotificationProvider {

    constructor(private _http: HttpClientService) {
    }

    eventNotificationRecipients() {
        return this._http.get('/api/v1/event_notification/event_notification_recipients');
    }

    updateNotificationRecipients(params) {
        return this._http.post('/api/v1/event_notification/update_event_notification_recipients', params);
    }

    eventNotificationAuctionConfigRecipients() {
        return this._http.get('/api/v1/event_notification/event_notification_auction_config_recipients');
    }

    updateNotificationAuctionConfigRecipients(params) {
        return this._http.post('/api/v1/event_notification/update_event_notification_auction_config_recipients', params);
    }

    eventNotificationDispatchGroupRecipients() {
        return this._http.get('/api/v1/event_notification/event_notification_dispatch_group_recipients');
    }

    updateNotificationDispatchGroupRecipients(params) {
        return this._http.post('/api/v1/event_notification/update_event_notification_dispatch_group_recipients', params);
    }
}