import { Injectable } from '@angular/core';
import { HttpClientService } from './../services/httpclient.service';

@Injectable()
export class DispatchGroupProvider {

  constructor(
      private _http: HttpClientService) {
  }

  findAllWithPagination(params?) {
    return this._http.get('/api/v1/dispatch_groups', params);
  }

  getDispatchGroupsList(params) {
    return this._http.get('/api/v1/dispatch_groups_list', params);
  }

  getDispatchGroupDetails(id) {
    return this._http.get('/api/v1/dispatch_group/dispatch_group_details', {id: id});
  }

  getFormData() {
    return this._http.get('/api/v1/dispatch_group/form_data');
  }

  createDispatchGroup(params) {
    return this._http.post('/api/v1/dispatch_group/create_dispatch_group', params);
  }

  updateDispatchGroup(params) {
    return this._http.post('/api/v1/dispatch_group/update_dispatch_group', params);
  }

  deleteDispatchGroup(id) {
    return this._http.post('/api/v1/dispatch_group/delete_dispatch_group', {id: id});
  }

  updateBalancingGroup(params) {
    return this._http.post('/api/v1/dispatch_group/update_balancing_group', params);
  }

  createBalancingGroup(params) {
    return this._http.post('/api/v1/dispatch_group/create_balancing_group', params);
  }

  deleteBalancingGroups(params) {
    return this._http.post('/api/v1/dispatch_group/delete_balancing_group', params);
  }

}
