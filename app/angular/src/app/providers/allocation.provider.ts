import { Injectable } from '@angular/core';
import { HttpClientService } from './../services/httpclient.service';

@Injectable()
export class AllocationProvider {

  constructor(private http: HttpClientService) {}

  create(params) {
    return this.http.post('/api/v1/allocations', params);
  }

  findManualAllocationById(id) {
    return this.http.get(`/api/v1/allocations/manual-allocation/${id}`);
  }

  findAutomaticAllocationById(id) {
    return this.http.get(`/api/v1/allocations/automatic-allocation/${id}`);
  }

}
