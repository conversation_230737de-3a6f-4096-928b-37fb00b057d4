import { Injectable } from '@angular/core';
import { HttpClientService } from './../services/httpclient.service';

@Injectable()
export class HistoryProvider {

  constructor(private http: HttpClientService) {}

  getAssetManualAllocations(params) {
    return this.http.get('/api/v1/history/asset_manual_allocations', params);
  }

  getAssetAutomaticAllocations(params) {
    return this.http.get('/api/v1/history/asset_automatic_allocations', params);
  }

  getNominations(params) {
    return this.http.get('/api/v1/history/nominations', params);
  }

  getAuctions(params) {
    return this.http.get('/api/v1/history/auctions', params);
  }

  getReportEmailRecipients(params) {
    return this.http.get('/api/v1/history/report_email_recipients', params);
  }

  getReportEmails(params) {
    return this.http.get('/api/v1/history/reporting-emails', params);
  }

  findReportEmailById(id) {
    return this.http.get(`/api/v1/history/report_email/${id}`);
  }

  getBids(params) {
    return this.http.get('/api/v1/history/bids', params);
  }

  getBidSource(id) {
    return this.http.get(`/api/v1/history/bid_source/${id}`);
  }

  getDlmChargingSessions(params) {
    return this.http.get('/api/v1/history/dlm_charging_sessions', params);
  }

  createV2gOptimizationJob(params) {
    return this.http.post('/api/v1/history/v2g_optimization_job_create', params);
  }

  getV2gOptimizationJobs(params) {
    return this.http.get('/api/v1/history/v2g_optimization_jobs', params);
  }

}
