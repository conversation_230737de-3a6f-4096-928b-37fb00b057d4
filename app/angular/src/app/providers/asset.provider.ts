import { Injectable } from '@angular/core';
import { HttpClientService } from './../services/httpclient.service';

@Injectable()
export class AssetProvider {

  constructor(private http: HttpClientService) {}

  findAllWithPagination(params?) {
    return this.http.get('/api/v1/assets', params);
  }

  findAllForOptiResultsUpload(params?) {
    return this.http.get('/api/v1/assets_for_opti_results_upload', params);
  }

  findAllForAssetOptimization(params?) {
    return this.http.get('/api/v1/assets_for_optimization', params);
  }

  findAllForPerfDataFilesUpload(params?) {
    return this.http.get('/api/v1/assets_for_perf_data_upload', params);
  }

  findAllWithBmu(params?) {
    return this.http.get('/api/v1/assets_for_bmu', params);
  }

  findAllFromNominationBids(params?) {
    return this.http.get('/api/v1/assets_from_nomination_bids', params);
  }

  assetOptiResultCount(params?) {
    return this.http.get('/api/v1/asset_opti_results_count', params);
  }


}
