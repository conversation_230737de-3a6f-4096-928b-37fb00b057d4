import { Injectable } from '@angular/core';
import { HttpClientService } from './../services/httpclient.service';
import { environment } from "../../environments/environment";

@Injectable()
export class UrlProvider {

  constructor(private http: HttpClientService) {}

  getUploadAllocationUrl() {
    return `${environment.apiPath}/api/v1/allocations`;
  }

  getUploadMarketResultsUrl() {
    return `${environment.apiPath}/api/v1/markets`;
  }

  getUploadNominationUrl() {
    return `${environment.apiPath}/api/v1/nominations`;
  }

  getUploadOptiResultsUrl() {
    return `${environment.apiPath}/api/v1/opti_results`;
  }

  getUploadPerfDataFilesUrl() {
    return `${environment.apiPath}/api/v1/perf_data_files`;
  }

  getUploadDeclarationOfUnavailabilityUrl() {
    return `${environment.apiPath}/api/v1/declaration_of_unavailability`;
  }

  getUploadBodFileUrl() {
    return `${environment.apiPath}/api/v1/bod_files`;
  }

  getUploadMarginDataFileUrl() {
    return `${environment.apiPath}/api/v1/margin_data_files`;
  }

  getAssetOptimizationUrl() {
    return `${environment.apiPath}/api/v1/asset_optimization`;
  }

  getMarketPositionsUrl() {
    return `${environment.apiPath}/api/v1/portfolio_optimizations/download_market_positions`;
  }

  getUploadBidsUrl() {
    return `${environment.apiPath}/api/v1/bids/upload_bids`;
  }

  getPrepareActionBidsUrl() {
    return `${environment.apiPath}/api/v1/bids/prepare_auctions_download`;
  }

  getDownloadBidsUrl() {
    return `${environment.apiPath}/api/v1/bids/auctions_bids_download`;
  }

  getDownloadOptiResultsUrl() {
    return `${environment.apiPath}/api/v1/opti_results`;
  }

  getDownloadAuctionResultsUrl() {
    return `${environment.apiPath}/api/v1/auction/download_results`;
  }

  getUploadImportedReportUrl() {
    return `${environment.apiPath}/api/v1/reports/tso`;
  }

  getUploadDlmChargingSessionUrl() {
    return `${environment.apiPath}/api/v1/dlm_charging_sessions`;
  }

  getDownloadSchedulingReportEdg() {
    return `${environment.apiPath}/api/v1/scheduling_report/download_edg_report`;
  }

  getDownloadSchedulingReportUgc() {
    return `${environment.apiPath}/api/v1/scheduling_report/download_ugc_report`;
  }

  getDownloadSchedulingReportTp() {
    return `${environment.apiPath}/api/v1/scheduling_report/download_tp_report`;
  }


}
