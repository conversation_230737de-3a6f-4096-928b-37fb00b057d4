import { Injectable } from "@angular/core";
import { HttpClientService } from "../services/httpclient.service";


@Injectable()
export class AssetConfigurationProvider {

    constructor(
        private _http: HttpClientService
    ) {

    }

    getBoxTypeConfigurations(params) {
        return this._http.get('/api/v1/configuration/asset/box_type_configurations', params);
    }

    createBoxTypeConfiguration(params) {
        return this._http.post('/api/v1/configuration/asset/create_box_type_configuration', params);
    }

    updateBoxTypeConfiguration(params) {
        return this._http.post('/api/v1/configuration/asset/update_box_type_configuration', params);
    }

    deleteBoxTypeConfiguration(id) {
        return this._http.post('/api/v1/configuration/asset/delete_box_type_configuration', {id: id});
    }

    getGenericSteeringConfigs(params) {
        return this._http.get('/api/v1/configuration/asset/generic_steering_configs', params);
    }

    createGenericSteeringConfig(params) {
        return this._http.post('/api/v1/configuration/asset/create_generic_steering_config', params);
    }

    updateGenericSteeringConfig(params) {
        return this._http.post('/api/v1/configuration/asset/update_generic_steering_config', params);
    }

    deleteGenericSteeringConfig(id) {
        return this._http.post('/api/v1/configuration/asset/delete_generic_steering_config', {id: id});
    }

    getSignalLists(params) {
        return this._http.get('/api/v1/configuration/asset/signal_lists', params);
    }

    createSignalList(params) {
        return this._http.post('/api/v1/configuration/asset/create_signal_list', params);
    }

    updateSignalList(params) {
        return this._http.post('/api/v1/configuration/asset/update_signal_list', params);
    }

    deleteSignalList(id) {
        return this._http.post('/api/v1/configuration/asset/delete_signal_list', {id: id});
    }
}
