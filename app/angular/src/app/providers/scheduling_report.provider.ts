import {HttpClientService} from "../services/httpclient.service";
import {Injectable} from "@angular/core";


@Injectable()
export class SchedulingReportProvider {

    constructor(
        private _http: HttpClientService) {
    }

    getDownloadFormData() {
        return this._http.get('/api/v1/scheduling_report/download_reports_form_data');
    }

    getEdgReport(params) {
        params.report_type = 'edg';
        return this._http.get('/api/v1/scheduling_report/generate_report', params);
    }

    getUgcReport(params) {
        params.report_type = 'ugc';
        return this._http.get('/api/v1/scheduling_report/generate_report', params);
    }

    getTpReport(params) {
        params.report_type = 'tp';
        return this._http.get('/api/v1/scheduling_report/generate_report', params);
    }

    regenerateSchedulingBalancingGroupsThirdPartyReports(params) {
        return this._http.get('/api/v1/scheduling_report/regenerate_scheduling_balancing_groups_third_party_reports', params);
    }

    regenerateBgAssetActivationsReports(params) {
        return this._http.get('/api/v1/scheduling_report/regenerate_bg_asset_activations_reports', params);
    }
}