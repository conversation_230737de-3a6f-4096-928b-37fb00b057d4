import { Injectable } from "@angular/core";
import { HttpClientService } from "../services/httpclient.service";


@Injectable()
export class BalancingGroupProvider {

    constructor(
        private _http: HttpClientService
    ) {
    }

    getBalancingGroupNames() {
        return this._http.get('/api/v1/configuration/market/bgs');
    }

    updateBalancingGroupNames(params: any) {
        return this._http.post('/api/v1/configuration/market/update_bgs', params);
    }



}