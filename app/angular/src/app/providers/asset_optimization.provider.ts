import { Injectable } from "@angular/core";
import { HttpClientService } from "../services/httpclient.service";
import {environment} from "../../environments/environment";

@Injectable()
export class AssetOptimizationProvider {

    constructor(
        private _http: HttpClientService
    ) {

    }

    create(params) {
        return this._http.post('api/v1/asset_optimization', params);
    }

    getPortfolioOptimizations(params?) {
        return this._http.get('/api/v1/portfolio_optimizations', params);
    }

    validateBids(params) {
        return this._http.post('/api/v1/portfolio_optimizations/validate_bids', params);
    }

    rejectBids(params) {
        return this._http.post('/api/v1/portfolio_optimizations/reject_bids', params);
    }
}
