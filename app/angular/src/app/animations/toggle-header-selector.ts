import {
  trigger,
  animate,
  transition,
  style,
  query,
  stagger,
  state,
  keyframes
} from '@angular/animations';

export const ToggleHeaderSelectorAnimation = trigger('toggleHeaderSelectorAnimation',
      [
        state('in', style({
          transform: 'translateY(0%)',
          opacity: 1
        })),
        state('out', style({
          transform: 'translateY(-200%)',
          opacity: 0
        })),
        transition('out <=> in', [
          animate(300),
        ])
      ]
    )