import {
  trigger,
  animate,
  transition,
  style,
  query,
  stagger,
  state,
  keyframes
} from '@angular/animations';

export const ToggleRightTechSupportAnimation = trigger('toggleRightTechSupportAnimation',
      [
        state('in', style({
          transform: 'translateX(0%)'
        })),
        state('out', style({
          transform: 'translateX(75%)'
        })),
        transition('out <=> in', [
          animate(300),
        ])
      ]
    )