rules:
  border-zero:
    - 1
    - convention: none

  brace-style:
    - 1
    - allow-single-line: true

  class-name-format:
    - 1
    - convention: hyphenatedlowercase

  clean-import-paths:
    - 1
    - filename-extension: false
      leading-underscore: false

  empty-line-between-blocks:
    - 1
    - ignore-single-line-rulesets: true

  extends-before-declarations: 1
  extends-before-mixins: 1

  final-newline:
    - 1
    - include: true

  force-attribute-nesting: 1
  force-element-nesting: 1
  force-pseudo-nesting: 1

  function-name-format:
    - 1
    - allow-leading-underscore: true
      convention: hyphenatedlowercase

  hex-length:
    - 1
    - style: long

  hex-notation:
    - 1
    - style: uppercase

  id-name-format:
    - 1
    - convention: hyphenatedlowercase

  indentation:
    - 1
    - size: 2

  leading-zero: 0

  mixin-name-format:
    - 1
    - allow-leading-underscore: true
      convention: hyphenatedlowercase

  mixins-before-declarations: 1
  nesting-depth:
    - 1
    - max-depth: 3
  
  no-warn: 0
  no-color-keywords: 0
  no-color-literals: 0

  no-css-comments: 1
  no-debug: 1
  no-duplicate-properties: 1
  no-empty-rulesets: 0
  no-extends: 0
  no-ids: 1
  no-important: 1
  no-invalid-hex: 1
  no-mergeable-selectors: 1
  no-misspelled-properties:
    - 1
    - extra-properties: []

  no-qualifying-elements:
    - 1
    - allow-element-with-attribute: false
      allow-element-with-class: false
      allow-element-with-id: false

  no-trailing-zero: 1
  no-transition-all: 0
  no-url-protocols: 1
  no-vendor-prefixes:
    - 1
    - additional-identifiers: []
      excluded-identifiers: []

  placeholder-in-extend: 1
  placeholder-name-format:
    - 1
    - convention: hyphenatedlowercase

  property-sort-order: 0

  property-units:
    - 1
    - global:
        - ch
        - em
        - ex
        - rem
        - cm
        - in
        - mm
        - pc
        - pt
        - px
        - q
        - vh
        - vw
        - vmin
        - vmax
        - deg
        - grad
        - rad
        - turn
        - ms
        - s
        - Hz
        - kHz
        - dpi
        - dpcm
        - dppx
        - '%'
      per-property: {}

  quotes: 0
    
  shorthand-values:
    - 1
    - allowed-shorthands:
        - 1
        - 2
        - 3

  single-line-per-selector: 1

  space-after-bang:
    - 1
    - include: false

  space-after-colon:
    - 1
    - include: true
    
  space-after-comma:
    - 1
    - include: true

  space-before-bang:
    - 1
    - include: true

  space-before-brace:
    - 1
    - include: true
    
  space-before-colon: 1

  space-between-parens:
    - 1
    - include: false

  trailing-semicolon: 1
  url-quotes: 1
  
  variable-for-property:
    - 0
    - properties: []

  variable-name-format:
    - 1
    - allow-leading-underscore: true
      convention: hyphenatedlowercase

  zero-unit: 0
