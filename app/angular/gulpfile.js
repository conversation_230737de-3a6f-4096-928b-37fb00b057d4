const gulp = require('gulp');
const concat = require('gulp-concat');
const sass = require('gulp-sass')(require('sass'));
const sassLint = require('gulp-sass-lint');
const sourcemaps = require('gulp-sourcemaps');
const browserSync = require('browser-sync').create();
const autoprefixer = require('gulp-autoprefixer');
const stripDebug = require('gulp-strip-debug');
const spawn = require('child_process').spawn;
const { exec } = require('child_process');
const path = require('path');

// ==========
// = Config =
// ==========

let config = {};

config.server = {
  browserSync: {
    proxy: {
      target: 'localhost:3000',
      middleware: function (req, res, next) {
        res.setHeader('Access-Control-Allow-Origin', '*');
        next();
      }
    },
    port: 3001,
    open: false
  }
};

config.styles = {
  paths: [
    // "node_modules/angular-notifier/styles.scss",
    "node_modules/bootstrap/dist/css/bootstrap.css",
    "src/app/styles/application.scss",
    "src/app/components/**/*.scss"
  ],
  concat: "styles.css",
  dest: "../../public/angular"
};

config.watch = {
  styles: [
    "src/app/styles/*.scss",
    "src/app/components/**/*.scss",
    "src/app/i18n/*.json"
  ],
  scripts: [ "../../public/angular/dist/**/*.js" ]
};

config.translations = {
  paths: [
    'src/app/i18n/*.json'
  ],
  dest: "../../public/assets/i18n"
};

config.fonts = {
  paths: [
    '../assets/fonts/**/**'
  ],
  dest: "../../public/assets/fonts"
};

config.images = {
  paths: [
     '../assets/graphics/**/**',
     '../assets/images/**/**'
  ],
  dest: "../../public/assets/images"
};

// ===========
// = angular =
// ===========

gulp.task('angular', function() {
  let process = spawn("ng build --watch", {
    shell: true
  });
  process.stdout.on('data', data => console.log(data.toString()));
  process.stderr.on('data', data => console.log(data.toString()));
});

gulp.task('translations', function() {
  return gulp.src(config.translations.paths)
    .pipe(gulp.dest(config.translations.dest))
    .pipe(browserSync.stream());
});

// gulp.task('fonts', function(done) {
//   return gulp.src(config.fonts.paths, { buffer: false, binary: true })
//     .pipe(debug({ title: 'Fonts copied:' }))
//     .pipe(gulp.dest(config.fonts.dest))
//     .on('end', done);
// });

// gulp.task('fontawesome-webfonts', function(done) {
//   return gulp.src('node_modules/font-awesome/fonts/**/*', { buffer: false, binary: true })
//     .pipe(debug({ title: 'FontAwesome copied:' }))
//     .pipe(gulp.dest('../../public/assets/fonts/font-awesome'))
//     .on('end', done);
// });

gulp.task('fonts', function(done) {
  const source = path.resolve(__dirname, '../assets/fonts');
  const target = path.resolve(__dirname, '../../public/assets/fonts');
  
  exec(`mkdir -p "${target}" && cp -a "${source}/." "${target}/"`, (err, stdout, stderr) => {
    if (err) {
      console.error(`Fonts copy failed: ${stderr}`);
      done(err);
    } else {
      console.log(`Fonts fonts copied from ${source} to ${target}`);
      done();
    }
  });
});

gulp.task('fontawesome-webfonts', function (done) {
  const source = path.resolve(__dirname, 'node_modules/font-awesome/fonts');
  const target = path.resolve(__dirname, '../../public/assets/fonts/font-awesome');

  exec(`mkdir -p "${target}" && cp -a "${source}/." "${target}/"`, (err, stdout, stderr) => {
    if (err) {
      console.error(`FontAwesome copy failed: ${stderr}`);
      done(err);
    } else {
      console.log(`FontAwesome fonts copied from ${source} to ${target}`);
      done();
    }
  });
});

gulp.task('images', function() {
  return gulp.src(config.images.paths)
    .pipe(gulp.dest(config.images.dest))
    .pipe(browserSync.stream());
});

// ==========
// = styles =
// ==========


gulp.task('styles', function() {
  return gulp.src(config.styles.paths, { allowEmpty: true })
    .pipe(sourcemaps.init())
    .pipe(sass({ errLogToConsole: true }))
      .on('error', (e) => {
        console.log(e.formatted);
        this.emit('end');
      })
    .pipe(autoprefixer())
    .pipe(concat(config.styles.concat))
    .pipe(sourcemaps.write())
    .pipe(gulp.dest(config.styles.dest))
    .pipe(browserSync.stream());
});

// ==========
// = Server =
// ==========

gulp.task('server', function() {
  browserSync.init(config.server.browserSync);
});

// =========
// = watch =
// =========

gulp.task('watch', function() {
  let opts = { interval: 500 };

  gulp.watch(config.watch.styles, opts, ['styles'])
    .on('change', function(file) {
      gulp.src(file.path)
        .pipe(sassLint())
        .pipe(sassLint.format())
        .pipe(sassLint.failOnError());
    });

  gulp.watch(config.watch.scripts, opts)
    .on('change', function(file) {
      browserSync.reload();
    });
});

gulp.task('angular-prod', async function() {
  let process = spawn("ng build --prod --aot --output-hashing=all", {
    shell: true
  });
  process.stdout.on('data', data => console.log(data.toString()));
  process.stderr.on('data', data => console.log(data.toString()));
});

// ===========
// = default =
// ===========

gulp.task('default', gulp.series(
  'translations',
  'styles',
  'fonts',
  'fontawesome-webfonts',
  'images',
  'angular',
  'server',
  'watch'
));

// ==============
// = build-prod =
// ==============

gulp.task('build:management', gulp.series(
  'styles'
));

gulp.task('prod', gulp.series(
  'styles',
  'angular-prod',
  'translations',
  'fonts',
  'fontawesome-webfonts',
  'images'
));
