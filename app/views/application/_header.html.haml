%header#header
  #main-header
    .wrapper
      #eon-logo
        = link_to root_url do
          = image_tag 'eon-logo.svg', :alt => "E.ON Logo"
      %nav#navigation
        %ul.nav-menu
          %li
            = link_to t('navigation.main.dashboard'), dashboard_path
          - if (Market.nominateable_volume_markets? && current_user.can?(:market_access)) || current_user.can_read?(DistributedUnit)
            %li
              = link_to t('navigation.main.nominations'), root_path
          - if current_user.can_read?(AssetDgAllocation)
            %li
              = link_to t('navigation.main.allocations'), asset_dg_allocations_path
          - if current_user.can_read?(DispatchGroup)
            %li
              = link_to t('navigation.main.dispatch_groups'), dispatch_groups_path
          - if current_user.can_read?(Subpool)
            %li
              = link_to t('navigation.main.subpools'), subpools_path
          - if !DisabledSections.reports_disabled? && current_user.can?(:reporting_and_notification_read)
            %li
              = link_to t('navigation.main.reports'), reports_path
          - if current_user.can_read?(DistributedUnit) || current_user.can_read?(Rollup) || current_user.can?(:reporting_and_notification_read)
            %li
              = link_to t('navigation.main.configurations'), configurations_path
          - if current_user.can_read?(AssetConfiguration)
            %li
              = link_to t('navigation.main.asset_configurations'), types_asset_configurations_path
        %ul.user-menu
          = render 'language_switch'
          %li
            = render 'vpp_authentication/logout'
  #sub-header
    .wrapper
      %ul#breadcrumb
        = yield :breadcrumb

      = yield :subheader_form

  - if content_for(:tabbed_nav)
    #tabbed-nav
      .wrapper
        %ul.tab-list
          = yield :tabbed_nav

= render 'tech_support'