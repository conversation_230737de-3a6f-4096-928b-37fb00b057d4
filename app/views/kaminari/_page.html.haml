-#  Link showing page number
-#  available local variables
-#    page:          a page object for "this" page
-#    url:           url to this page
-#    current_page:  a page object for the currently displayed page
-#    total_pages:   total number of pages
-#    per_page:      number of items to fetch per page
-#    remote:        data-remote
%li{:class => "page#{' current' if page.current?}"}
  = link_to page, url, {:remote => remote, :rel => page.next? ? 'next' : page.prev? ? 'prev' : nil}
