<% if <PERSON>NV['VPP_MANAGEMENT_DATABASE_HOST'].index('stg-').present? || ENV['VPP_MANAGEMENT_DATABASE_HOST'].index('int-').present? %>
<div id="environment_info_bar">
  <div class="user-agent-info">
    <div class="ua-info-content">
      <%= browser.name.downcase %> <%= browser.full_version %> on <%= browser.platform %> <%= browser.platform.version %>
    </div>
  </div>

  <div id="environment_info_bar" class="<%= Rails.env %>">
    You are on
    <% if ENV['VPP_MANAGEMENT_DATABASE_HOST'].index('int-').present? %>
    INT
    <% end %>
    <% if ENV['VPP_MANAGEMENT_DATABASE_HOST'].index('stg-').present? %>
    STG
    <% end %>
    running in <%= Rails.env %> mode. This environment contains the latest features and may be unstable.
  </div>
</div>
<% end %>
