%header#app-header
  .container-fluid
    .row
      .col-12
        .d-flex.justify-content-between.align-items-center
          .app-header--nav
            %button#hamburgerMenu.hamburger.hamburger--spring{:type => "button"}
              %span.hamburger-box
                %span.hamburger-inner
            %nav#side-menu
              %ul.list-unstyled
              - if (Market.nominateable_volume_markets? && current_user.can?(:market_access)) || current_user.can_read?(DistributedUnit)
                %li
                  = link_to t('navigation.main.nominations'), root_path
              - if current_user.can_read?(AssetDgAllocation)
                %li
                  = link_to t('navigation.main.allocations'), asset_dg_allocations_path
              - if current_user.can_read?(DispatchGroup)
                %li
                  = link_to t('navigation.main.dispatch_groups'), dispatch_groups_path
              - if current_user.can_read?(Subpool)
                %li
                  = link_to t('navigation.main.subpools'), subpools_path
              - if current_user.can_read?(DistributedUnit)
                %li
                  = link_to t('navigation.main.reports'), reports_path
              - if current_user.can_read?(DistributedUnit) || current_user.can_read?(Rollup)
                %li
                  = link_to t('navigation.main.configurations'), configurations_path
              - if current_user.can_read?(AssetConfiguration)
                %li
                  = link_to t('navigation.main.asset_configurations'), types_asset_configurations_path
                %li
                  %a{:href => "/dashboard"} Home
                %li
                  %a{:href => "/nominations"} Nominations
                %li
                  %a{:href => "/allocations"} Allocations
                %li
                  %a{:href => "#"} TSO Report
                %li
                  %a.has-subnav{:href => "#"} Reporting and Notifications
                  %ul.list-unstyled.submenu
                    %li
                      %a{:href => "#"} Scheduling reports
                    %li
                      %a{:href => "#"} Rollups for Assets
                    %li
                      %a{:href => "#"} Rollups for Dispatch Groups
                    %li
                      %a{:href => "#"} Automatic emailing

                %li
                  %a.has-subnav{:href => "#"} Marketing Configuration
                  %ul.list-unstyled.submenu
                    %li
                      %a{:href => "#"} Scheduling reports
                    %li
                      %a{:href => "#"} Rollups for Assets
                    %li
                      %a{:href => "#"} Rollups for Dispatch Groups
                    %li
                      %a{:href => "#"} Automatic emailing
                %li
                  %a{:href => "/history"} History
            .app-header-title
              %p.app-name
                Portfolio Management
              %h1#pageTitle.page-title
          .app-header--menu
            #app-nav
              %ul.list-inline.list-unstyled.list-nav
                %li
                  %a.fa-user-button
                    %i.fas.fa-user

                %li
                  %a.globe-button
                    %i.fas.fa-globe
                  %div#languageMenu.submenu-block
                    %div.container
                      .row
                        .col-md-6.d-flex
                          %h3.eon-title
                            Want to view this page in another language?
                        .col-md-6.d-flex
                          %form
                            %form-group
                              - available_locales.each do |locale|
                                .form-check{onclick: "document.location = '#{switch_locale_path(locale: locale)}'"}
                                  %input#languageRadios1.form-check-input{checked: I18n.locale == locale || nil, name: "languageRadios", type: "radio", value: "English"}/
                                  %label.form-check-label{:for => "languageRadios1"}
                                    =  t("angular.locale.#{locale.to_s}")
                .app-logo
                  = image_tag 'eon-logo.svg', :alt => "E.ON Logo"