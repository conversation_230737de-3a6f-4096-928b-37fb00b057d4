<!DOCTYPE html>
<html lang="en" class="<%= 'development' if Rails.env.development? %>" data-current-user-id="<%= current_user.try(:id) %>">
  <head>
    <meta charset='UTF-8'>
    <meta content='width=device-width, initial-scale=1' name='viewport'>
    <meta content='ie=edge' http-equiv='x-ua-compatible'>
    <%= favicon_link_tag %>
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.7.2/css/all.css" integrity="sha384-fnmOCqbTlWIlj8LyTjo7mOUStjsKC4pOpQbqyi7RrhN7udi9RwhKkMHpvLbHG9Sr" crossorigin="anonymous">

    <base href="/">
    <title></title>
    <%= csrf_meta_tags %>
    <link rel="stylesheet" type="text/css" href="<%= asset_url('/angular/styles.css') %>">
    <script>
      window.angularData = <%= raw @angular_data.to_json %>;
    </script>
    <%= include_gon namespace: 'railsExports', camel_case: true %>
  </head>
  <body class="<%= [controller_name.dasherize, action_name.dasherize, @body_class, yield(:body_class), "controller-#{controller_name.dasherize}"].join(" ") %>">
    <%= render partial: 'angular/layouts/environment_info_bar' %>
      <!-- ANGULAR -->
      <div id="main_content">
        <%= yield %>
        <div style="clear:both;"></div>
      </div>
      <!-- ANGULAR -->
    <!-- <%= render partial: 'footer' %>-->
    <script type="text/javascript" src="<%= ng_path 'runtime' %>"></script>
    <script type="text/javascript" src="<%= ng_path 'polyfills' %>"></script>
    <script type="text/javascript" src="<%= ng_path 'main' %>"></script>
    <% if Rails.env.development? %>
    <script type="text/javascript" src="<%= ng_path 'vendor' %>"></script>
    <% end %>

  </body>
</html>
