class BgWithTimeIntervalValidator < ActiveModel::Validator

  def validate(r)
    now = Time.now

    r.errors.add(:base, :end_date_is_in_the_past) if r.end_date.present? && r.end_date < now
    r.errors.add(:base, :start_date_after_end_date) if r.start_date.present? && r.end_date.present? && r.start_date > r.end_date
    r.errors.add(:base, :overlaps_existing_balancing_group) if r.start_date.present? && r.end_date.present? && r.overlapping_bg.any?

    if r.persisted?
      r.errors.add(:base, :cannot_change_historic_data) if r.end_date_was < now
      r.errors.add(:base, :cannot_change_start_date) if r.start_date_changed? && r.start_date_was < now
      r.errors.add(:base, :start_date_is_in_the_past) if r.start_date_changed? && r.start_date.present? && r.start_date < now
    end
  end
end
