require 'prometheus_metrics'
class ActiveRecordSubscriber < ActiveSupport::Subscriber
  attach_to :active_record

  def sql(event)
    PrometheusMetrics.counters(name: :postgres_query_count,
      docstring: "Count of the SQL queries done in Postgres",
      labels: []).increment

    PrometheusMetrics.histograms(name: :postgres_query_duration,
      docstring: "Count of the SQL queries done in Postgres",
      labels: []).observe(event.duration)
  end
end
