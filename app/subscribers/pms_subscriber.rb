require 'prometheus_metrics'
class PmsSubscriber < ActiveSupport::Subscriber
  attach_to :pms

  def event(event)
    PrometheusMetrics.counters(name: :pms_calls_count,
      docstring: "Count of the HTTP requests done in Portfolio Management Service",
      labels: []).increment

    PrometheusMetrics.histograms(name: :pms_calls_duration,
      docstring: "Duration of the HTTP requests done in Portfolio Management Service",
      labels: []).observe(event.duration)
  end
end
