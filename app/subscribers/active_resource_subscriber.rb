require 'prometheus_metrics'
class ActiveResourceSubscriber < ActiveSupport::Subscriber
  attach_to :active_resource

  def request(event)
    request_uri = event.payload[:request_uri]
    if request_uri.start_with?(ENV['VPP_REPORTING_URL'])
      PrometheusMetrics.counters(name: :vpp_reporting_calls_count,
        docstring: "Count of calls done to VPP Reporting",
        labels: []).increment
      PrometheusMetrics.histograms(name: :vpp_reporting_calls_duration,
        docstring: "Duration of calls done to VPP Reporting",
        labels: []).observe(event.duration)
    elsif request_uri.start_with?(ENV['ASSET_SCHEDULE_SERVICE_URL'])
      PrometheusMetrics.counters(name: :asset_schedule_service_calls_count,
        docstring: "Count of calls done in Asset Schedule Component",
        labels: []).increment
      PrometheusMetrics.histograms(name: :asset_schedule_service_calls_duration,
        docstring: "Duration of calls done in Asset Schedule Component",
        labels: []).observe(event.duration)
    end
  end
end
