require 'prometheus_metrics'
class VppRollupsSubscriber < ActiveSupport::Subscriber
  attach_to :vpp_rollups

  def event(event)
    PrometheusMetrics.counters(name: :vpp_rollups_calls_count,
      docstring: "Count of the HTTP requests done in VPP Rollups",
      labels: []).increment

    PrometheusMetrics.histograms(name: :vpp_rollups_calls_duration,
      docstring: "Duration of the HTTP requests done in VPP Rollups",
      labels: []).observe(event.duration)
  end
end
