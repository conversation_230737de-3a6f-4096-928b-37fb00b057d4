#
class ApplicationController < ActionController::Base
  before_action :authenticate_user!
  protect_from_forgery with: :exception
  include EventTrail::EventController unless Rails.env.test?

  before_action(except: :switch_locale) do
    I18n.locale = session[:locale] if session[:locale].present?
  end
  helper_method :available_locales

  before_action :enforce_authority, except: [:set_current_user, :set_current_user_super_admin]
  before_action :setup_gon

  def action_missing(m, *args, &block)
    if m != "index"
      redirect_to action: :index
    else
      redirect_to(root_url)
    end
  end

  def switch_locale
    I18n.locale = params[:locale]
    session[:locale] = I18n.locale
    redirect_back(fallback_location: root_path)
  end

  def switch_timezone
    if current_user && current_user.allow_timezone_change && params[:tz]
      current_user.update_column(:timezone, params[:tz])
    end
    # session[:timezone] = params[:tz]
    if request.xhr?
       head :ok
    else
      redirect_back(fallback_location: root_url)
    end
  end

  def set_time_zone(&block)
     Time.use_zone(current_user.timezone, &block)
  end

  # used by EventTrail - overload in controllers to define audited action
  def audit_actions
    {}
  end

  def home
    if Market.nominateable_volume_markets? && current_user.can?(:market_access)
      redirect_to nominateable_volumes_path
    else
      redirect_to distributed_units_path
    end
  end

  protected

  def angular_data
    @angular_data = {
      current_user: current_user.as_json(except: [:password_digest, :unencrypted_password]),
      permissions: {
        can_see_bids: current_user.can_read?(NominationBid),
        can_create_bids: current_user.can_create?(NominationBid),
        can_see_nominations: current_user.can_read?(DistributedUnit),
        can_create_nominations: current_user.can_create?(DistributedUnit),
        can_update_nominations: current_user.can_update?(DistributedUnit),
        can_delete_nominations: current_user.can_delete?(DistributedUnit),
        can_see_auction_results: current_user.can?(:auction_results_read),
        can_create_auction_results: current_user.can?(:auction_results_create),
        can_see_tso_reports: !DisabledSections.reports_disabled? && current_user.can?(:tso_reports_read),
        can_create_tso_reports: !DisabledSections.reports_disabled? && current_user.can?(:tso_reports_create),
        can_see_allocations: current_user.can_read?(AssetDgAllocation),
        can_create_allocations: current_user.can_create?(AssetDgAllocation),
        can_see_reporting_and_nofications: !DisabledSections.reports_disabled? && current_user.can?(:reporting_and_notification_read),
        can_create_reporting_and_nofications: !DisabledSections.reports_disabled? && current_user.can?(:reporting_and_notification_create),
        can_see_nofications: DisabledSections.reports_disabled? && current_user.can?(:reporting_and_notification_read),
        can_create_nofications: DisabledSections.reports_disabled? && current_user.can?(:reporting_and_notification_create),
        can_create_scheduling_reports: !DisabledSections.reports_disabled? && current_user.can?(:scheduling_reports_create),
        can_see_dlm_charging_sessions: !DisabledSections.reports_disabled? && current_user.can?(:reporting_and_notification_read),
        can_create_dlm_charging_sessions: !DisabledSections.reports_disabled? && current_user.can?(:reporting_and_notification_create),
        # can_see_history: (Market.nominateable_volume_markets? && current_user.can?(:market_access)) || current_user.can_read?(DistributedUnit) || current_user.can_read?(AssetDgAllocation) || current_user.can_read?(Rollup),
        # can_see_reports: !DisabledSections.reports_disabled? && current_user.can_read?(DistributedUnit),
        # can_create_reports: !DisabledSections.reports_disabled? && current_user.can_create?(DistributedUnit),
        can_see_configurations: current_user.can_read?(DistributedUnit) || current_user.can_read?(Rollup),
        can_see_asset_configurations: current_user.can_read?(AssetConfiguration),
        can_create_asset_configurations: current_user.can_create?(AssetConfiguration),
        can_see_generic_steering_config: current_user.can_read?(GenericSteeringConfig),
        can_create_generic_steering_config: current_user.can_create?(GenericSteeringConfig),
        can_see_rollups: current_user.can_read?(Rollup),
        can_create_rollups: current_user.can_create?(Rollup),
        can_see_subpools: current_user.can_read?(Subpool),
        can_create_subpools: current_user.can_create?(Subpool),
        can_see_balancing_groups: current_user.can_read?(DistributedUnit),
        can_create_balancing_groups: current_user.can_create?(DistributedUnit),
        can_see_dispatch_groups: current_user.can_read?(DispatchGroup),
        can_create_dispatch_groups: current_user.can_create?(DispatchGroup),
        can_update_dispatch_groups: current_user.can_update?(DispatchGroup),
        can_delete_dispatch_groups: current_user.can_delete?(DispatchGroup)
      }
    }
    @angular_data
  end

  def enforce_authority
    if self.class != VppAuthentication::SessionsController &&
      current_user.present? &&
      angular_data[:permissions].values.uniq.size == 1 &&
      angular_data[:permissions].values.uniq.first == false

      if request.headers['X-XSRF-TOKEN'].present?
        render(text: vpp_authentication.login_url, status: :unauthorized)
        false
      else
        raise Authority::SecurityViolation.new(
          current_user, action_name, controller_name)
      end
    end
  end

  def available_locales
    if ENV['VPP_LOCALES']
      ENV['VPP_LOCALES'].split(',').collect(&:to_sym)
    else
      [:'en-GB', :de]
    end
  end

  def current_page
    (params[:page] || 1).to_i - 1
  end

  def setup_gon
    gon.rails_env = Rails.env
    gon.locale = I18n.locale
    gon.time_zone = current_user ? (current_user.timezone || VppManagement::Application::TIME_ZONE) : VppManagement::Application::TIME_ZONE
    gon.allow_timezone_change = current_user && current_user.allow_timezone_change
    gon.tech_support_email = ENV['VPP_MANAGEMENT_SUPPORT_EMAIL'] || "<EMAIL>"
  end

  def order_by
    "#{params[:order_by]} #{params[:direction] || 'asc'}" if params[:order_by]
  end

  def user_owned_dg_ids
    return @user_owned_dg_ids if defined? @user_owned_dg_ids
    @user_owned_dg_ids =
      begin
        if current_user.has_privilege?(:super_admin)
          DispatchGroup.pluck(:id)
        else
          current_user.organization.try(:dispatch_group_ids) || []
        end
    end
  end

  def user_owned_auction_config_ids
    return @user_owned_auction_config_ids if defined? @user_owned_auction_config_ids
    @user_owned_auction_config_ids =
      begin
        if current_user.has_privilege?(:super_admin)
          AuctionConfig.pluck(:id)
        else
          current_user.organization.try(:auction_config_ids) || []
        end
    end
  end

  def trading_service
    if ENV['VPP_TRADING_URL'].present?
      Services::VppTradingService
    else
      Services::PortfolioManagementService
    end
  end


end
