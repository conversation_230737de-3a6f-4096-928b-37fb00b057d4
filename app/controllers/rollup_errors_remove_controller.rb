class RollupErrorsRemoveController < ActionController::Base

  before_action :authenticate

  def remove
    # delete without callbacks
    Rails.logger.info("ROLLUP ERRORS - remove errors logs older than #{ENV['ROLLUPS_ERRORS_KEEP_MONTHS'] || 3} months")
    RollupError.where('created IS NULL OR created < ?', (ENV['ROLLUPS_ERRORS_KEEP_MONTHS'] || 3).months.ago).delete_all
    render json: {success: true}
  end

  private

  def authenticate
    is_authenticated = params['token'] == ENV['ROLLUPS_SECRET_KEY']
    if !is_authenticated
      Rails.logger.info "ROLLUP ERRORS request not authenticated. Token was #{params['token']}"
      render json: { error: 'Not Authorized' }, status: 401
    end
  end

end