class AngularController < ApplicationController
  before_action :set_angular, :angular_data, :set_section, :resolve_permissions
  layout :resolve_layout

  def index
    @script_dir = "angular/dist"


    render 'index'
  end

  private

  def set_angular
    @angular = true
  end

  def resolve_layout
   "angular/layouts/application"
  end

  def resolve_permissions
    authorized = true

    case @section_name
    when "bidding"
      case @sub_section_name
      when "prepare-bids"
        authorized = @angular_data[:permissions][:can_see_bids]
      when "upload-bids-file"
        authorized = @angular_data[:permissions][:can_create_bids]
      else
      end
    when "nominations"
      case @sub_section_name
      when "enter-nominations"
        authorized = @angular_data[:permissions][:can_create_nominations]
      when "upload-nomination-file"
        authorized = @angular_data[:permissions][:can_create_nominations]
      when "upload-market-result"
        authorized = @angular_data[:permissions][:can_create_auction_results]
      else
      end
    when "allocations"
      case @sub_section_name
      when "enter-allocations"
        authorized = @angular_data[:permissions][:can_create_allocations]
      when "upload-allocation-file"
        authorized = @angular_data[:permissions][:can_create_allocations]
      else
        authorized = @angular_data[:permissions][:can_see_allocations]
      end
    when "reporting"
      authorized = @angular_data[:permissions][:can_see_tso_reports]
    when "history"
      case @sub_section_name
        when "nominations"
          authorized = @angular_data[:permissions][:can_see_nominations]
        when "manual-allocations"
          authorized = @angular_data[:permissions][:can_see_allocations]
        when "automatic-allocations"
          authorized = @angular_data[:permissions][:can_see_allocations]
        when "auctions"
          authorized = @angular_data[:permissions][:can_see_bids]
        when "bids"
          authorized = @angular_data[:permissions][:can_see_bids]
        when "dlm-charging-sessions"
          authorized = @angular_data[:permissions][:can_see_dlm_charging_sessions]
        else
        end
    when "asset_configuration_ribbon"
      authorized = @angular_data[:permissions][:can_see_configurations]
    when "market_configuration_ribbon"
      authorized = @angular_data[:permissions][:can_see_subpools] ||
        @angular_data[:permissions][:can_see_balancing_groups] ||
        @angular_data[:permissions][:can_see_dispatch_groups] ||
        @angular_data[:permissions][:can_see_bids]
    when "rollup_configuration_ribbon"
      authorized = @angular_data[:permissions][:can_see_rollups]
    when "rollups"
      case @sub_section_name
      when "rollup-errors"
        authorized = @angular_data[:permissions][:can_see_rollups]
      else
        authorized = @angular_data[:permissions][:can_see_rollups]
      end
    when "automatic-report-emailings"
      authorized = @angular_data[:permissions][:can_see_reporting_and_nofications]
    else
    end

    redirect_to(root_url) unless authorized
  end

  def set_section
    @section_name = Rails.env.development? ? request.path.split("/")[1] : request.path.split("/")[2]
    @sub_section_name = Rails.env.development? ? request.path.split("/")[2] : request.path.split("/")[3]
  end

end
