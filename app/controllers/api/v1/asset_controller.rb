module Api
  module V1
    class AssetController < Api::V1::ApiController

      def list
        if user_owned_dg_ids.empty?
          assets = []
        else
          # show all groups, no pagination
          if params[:page].present?
            assets = Asset.not_deleted.
              joins("INNER JOIN asset_dispatch_group ON asset.id = asset_dispatch_group.asset_id").
              joins(:customer).
              where('asset_dispatch_group.dispatchgroups_id': user_owned_dg_ids).
              page(params[:page]).per(params[:per_page]).
              select('assets.*, customer.name as customer_name').
              uniq
          else
            assets = Asset.not_deleted.
              joins("INNER JOIN asset_dispatch_group ON asset.id = asset_dispatch_group.asset_id").
              joins(:customer).
              where('asset_dispatch_group.dispatchgroups_id': user_owned_dg_ids).
              select('asset.*, customer.name as customer_name').order(name: :asc).
              uniq
          end
        end

        sendReply({
          assets: JSON.parse(assets.to_json(:methods => [:id_and_name]))
        })
      end

      def for_optimization
        if user_owned_dg_ids.empty?
          assets = []
        else
          # Assets having as contract type "flex optimization".
          # The contract must cover a least the time interval D and D+1
          # (the start date should be latest D and end date latest D+1 as the end date of a contract is included).
          # The asset must also be marked as active in vpp-portal (at asset level)
          # and be a battery + in UK (because "Battery UK Optimization" was selected in the previous field
          day = Time.now.beginning_of_day
          customer_type = params[:customer_type] || CustomerType::VPP_UK
          assets = Asset.
            joins(:contracts).
            joins(:customer).
            joins("INNER JOIN asset_dispatch_group ON asset.id = asset_dispatch_group.asset_id").
            where('asset_dispatch_group.dispatchgroups_id': user_owned_dg_ids).
            where("customer.customer_type = ?", customer_type).
            where("contract.contract_type = ?", Contract::TYPE_FLEX_OPTIMIZATION).
            where("contract.start_date <= :day AND contract.end_date >= :day_plus_1", day: day, day_plus_1: day + 1.day).
            where('asset.deleted IS NULL OR asset.deleted > CURRENT_TIMESTAMP').
            where('asset.active = ?', true).
            where('asset.asset_type = ?', AssetType::BATTERY).
            select('asset.id, asset.name, customer.name as customer_name').
            uniq
        end
        sendReply({
          assets: JSON.parse(assets.to_json(:methods => [:id_and_name]))
        })
      end

      def for_opti_results_upload
        # Assets from user's accessible Variable SoE DG's
        if user_owned_dg_ids.empty?
          assets = []
        else
          dispatch_groups = DispatchGroup.with_opti_lines.where(id: user_owned_dg_ids)
          if dispatch_groups.empty?
            assets = []
          else
            assets = Asset.
              joins(:customer).
              joins("INNER JOIN asset_dispatch_group ON asset.id = asset_dispatch_group.asset_id").
              select('asset.*, customer.name as customer_name').order(name: :asc).
              where('asset_dispatch_group.dispatchgroups_id': dispatch_groups.collect(&:id)).
              not_deleted.
              uniq
          end
        end

        sendReply({
          assets: JSON.parse(assets.to_json(:methods => [:id_and_name]))
        })
      end

      def for_perf_data_upload
        # Assets from user's accessible Variable SoE DG's
        if user_owned_dg_ids.empty?
          assets = []
        else
          dispatch_groups = DispatchGroup.where(id: user_owned_dg_ids)
          if dispatch_groups.empty?
            assets = []
          else
            short_names = ['EONDC-01', 'EONDC-02', 'EONDC-03', ]
            assets = Asset.with_key_value_stores.
              joins(:customer).
              joins(:asset_key_value_stores).
              joins("INNER JOIN asset_dispatch_group ON asset.id = asset_dispatch_group.asset_id").
              select('asset.*, customer.name as customer_name').order(name: :asc).
              where('asset_dispatch_group.dispatchgroups_id': dispatch_groups.collect(&:id)).
              where("asset_key_value_store.key = 'shortName' AND asset_key_value_store.value IN (?)", short_names).
              not_deleted.
              uniq
          end
        end

        sendReply({
          assets: JSON.parse(assets.to_json(:methods => [:id_and_name]))
        })
      end

      def for_bmu
        # Assets from user's accessible Variable SoE DG's
        if user_owned_dg_ids.empty?
          assets = []
        else
          dispatch_groups = DispatchGroup.where(id: user_owned_dg_ids)
          if dispatch_groups.empty?
            assets = []
          else    
            assets = Asset.with_key_value_stores.
              joins(:customer).
              joins(:asset_key_value_stores).
              joins("INNER JOIN asset_dispatch_group ON asset.id = asset_dispatch_group.asset_id").
              select('asset.*, customer.name as customer_name').order(name: :asc).
              where('asset_dispatch_group.dispatchgroups_id': dispatch_groups.collect(&:id)).
              where("asset_key_value_store.key = 'balancingMechanismUnitConfig' AND asset_key_value_store.value != 'null'").
              not_deleted.
              uniq      
          end
        end

        sendReply({
          assets: JSON.parse(assets.to_json(:methods => [:id_and_name]))
        })
      end

      def from_nomination_bids
        assets = Asset.select(:id, :name).
          joins(:nomination_bids).
          joins("INNER JOIN asset_dispatch_group ON asset.id = asset_dispatch_group.asset_id").
          where('asset_dispatch_group.dispatchgroups_id': user_owned_dg_ids).
          distinct
        sendReply({
          assets: JSON.parse(assets.to_json(:methods => [:id_and_name]))
        })
      end

      def opti_results_count
        asset = Asset.find(params[:asset_id])
        selected_date = Time.find_zone(asset.tso.time_zone).parse(params[:selected_date])
        lines_count = OptiResultLine.
          select('DISTINCT ON (datetime) *').
          where(asset_id: asset.id).
          where('datetime >= ?', selected_date.beginning_of_day).
          where('datetime <= ?', selected_date.end_of_day).
          where(autofill: false).
          order(:datetime, created: :desc).
          size

        sendReply({
          count: lines_count,
          has_gaps: lines_count != 48
        })
      end

    end
  end
end