module Api
  module V1

    class RollupsController < Api::V1::ApiController

      authorize_actions_for Rollup
      authority_actions rollup_types: :read, rollups: :read, rollup_errors: :read, rollup_error: :read

      def rollup_types
        entity_type = params[:entity_type]
        is_assets_type = entity_type == Asset.model_name.to_s

        item_types = is_assets_type ? [Asset.model_name.to_s, RollupFamily.model_name.to_s] : [entity_type]

        rollup_types = RollupType
                         .where(owner_type: item_types)
                         .order(:name)

        render json: rollup_types.to_json(only: [:id, :name]), status: 200
      end

      def rollups
        entity_type = params[:entity_type]
        is_assets_type = entity_type == Asset.model_name.to_s

        item_types = is_assets_type ? [Asset.model_name.to_s, RollupFamily.model_name.to_s] : [entity_type]

        start_date = params[:start_date]
        end_date = params[:end_date]
        status = params[:status]
        entity_ids_str = params[:entity_ids]
        rollup_type_ids_str = params[:rollup_type_ids]
        triggered_by_user_ids_str = params[:triggered_by_user_ids]

        rollups_data = RollupRegeneration
                         .where(item_type: item_types)
                         .includes(:user_account, :rollup_types, :assets, :dispatch_groups)
                         .order(created: :desc)
                         .page(params[:page])
                         .per((params[:per_page] || 25).to_i)

        if start_date.present? and end_date.present?
          rollups_data = rollups_data.where('(from_time, to_time) overlaps (?, ?)',
                                            Time.parse(start_date).to_datetime,
                                            Time.parse(end_date)).to_datetime
        end

        if status.present?
          rollups_data = rollups_data.where(status: status.downcase)
        end

        if entity_ids_str.present?
          entity_ids = entity_ids_str.split(',').collect { |x| x.to_i }
          if is_assets_type
            rollups_data = rollups_data.joins(:assets).where('rollup_regeneration_asset.asset_id in (?)', entity_ids)
          else
            rollups_data = rollups_data.joins(:dispatch_groups).where('rollup_regeneration_dispatch_group.dispatch_group_id in (?)', entity_ids)
          end
        end

        if rollup_type_ids_str.present?
          rollup_type_ids = rollup_type_ids_str.split(',').collect { |x| x.to_i }
          rollups_data = rollups_data.joins(:rollup_types).where('rollup_regeneration_rollup_type.rollup_type_id in (?)', rollup_type_ids)
        end

        if triggered_by_user_ids_str.present?
          triggered_by_user_ids = triggered_by_user_ids_str.split(',').collect { |x| x.to_i }
          rollups_data = rollups_data.where('user_id in (?)', triggered_by_user_ids)
        end

        resp_data = rollups_data.collect { |r| {
          user_name: r.user_account.name,
          user_email: r.user_account.email,
          rollup_metrics: r.rollup_types.collect { |x| x.name }.join(', '),
          from_time: I18n.localize(r.from_time, format: :long),
          to_time: I18n.localize(r.to_time, format: :long),
          status: r.status,
          grouping: (is_assets_type ? r.assets : r.dispatch_groups).collect { |x| x.name }.join(', '),
          dispatch_groups: (is_assets_type ? [] : r.dispatch_groups).collect { |x| { name: x.name, id: x.id, deleted: x.deleted?} }
        } }

        sendReply({
                    data: resp_data,
                    total_entries: rollups_data.total_count,
                    page: params[:page]})
      end

      def rollup_error
        id = params[:id]
        res = RollupError.find(id)
        sendReply({
                     rollup_error: res
                   })
      end

      def rollup_errors
        start_date = params[:start_date]
        end_date = params[:end_date]

        rollup_errors = RollupError.order('created desc').page(params[:page]).per(params[:per_page] || 25)

        if start_date.present? and end_date.present?
          rollup_errors = rollup_errors
                            .where(created: Time.parse(start_date).to_datetime..Time.parse(end_date).to_datetime)
        end

        ret_rollup_errors = rollup_errors.collect do |x|
          {
            id: x.id,
            created: x.created,
            description: x.description.try(:truncate, 150)
          }
        end

        sendReply({
                    rollup_errors: ret_rollup_errors,
                    total_entries: rollup_errors.total_count,
                    page: rollup_errors.current_page
                  })
      end


    end
  end
end