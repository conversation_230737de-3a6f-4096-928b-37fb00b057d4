module Api
  module V1
    class AllocationController < Api::V1::ApiController

      authorize_actions_for AssetDgAllocation
      authority_actions upload: :create,
                        automatic_allocation: :read,
                        download_allocation_sample: :read,
                        asset_dg_allocation_source: :read

      def new
        if params[:file].present?
          upload
        else
          create
        end
      end

      def upload
        result = {
          success: false,
          result: nil,
          error: nil,
          message: nil
        }

        if params[:file].present?
          file_name = params[:file].original_filename
          file_contents = params[:file].read
          skip_validations = (params[:skip_validations] == true || params[:skip_validations] == 'true') ? ['StartTimeValidator'] : []
          begin
            created_source_id = Services::PortfolioManagementService.allocations_file_upload(
              file_name: file_name,
              file_contents: file_contents,
              user_id: current_user.id,
              skip_validations: skip_validations
            )

            result[:success] = true
            result[:result] = if created_source_id
              AssetDgAllocationSource.where(id: created_source_id).first
            else
              nil
            end
          rescue Services::PortfolioManagementService::UnableToFileUpload => e
            result[:error] = 'ISSUE_UPLOADING_FILE'
            result[:messages] = [e.to_s]
          end
        else
          result[:error] = 'NO_FILE_UPLOADED'
        end

        sendReply(result)
      end

      def create
        result = {
          success: false,
          result: nil,
          error: nil,
          messages: []
        }

        resource_params =
          params
          .require(:asset_dg_allocation)
          .permit(:interval, :asset_id, :dispatch_group_id)

        is_allocated = (params[:asset_dg_allocation]["allocation-action"] == "Allocate")
        skip_validations = (params[:skip_validations] == true || params[:skip_validations] == 'true') ? ['StartTimeValidator'] : []
        asset_dg_allocation = Array(AssetDgAllocation.new(resource_params.merge(is_allocated: is_allocated)))

        if asset_dg_allocation.all?(&:valid?)
          begin
            created_source_id = Services::PortfolioManagementService.post_allocations(
              asset_dg_allocations: asset_dg_allocation,
              user_id: current_user.id,
              skip_validations: skip_validations
            )

            result[:success] = true
            result[:result] = if created_source_id
              AssetDgAllocationSource.where(id: created_source_id).first
            else
              nil
            end

          rescue Services::PortfolioManagementService::UnableToPostAllocations => e
            result[:error] = 'ISSUE_CREATING_ALLOCATION'
            result[:messages] = [e.to_s]
          end
        else
          result[:error] = 'VALIDATION_ERROR'
          result[:messages] = asset_dg_allocation.collect { |a| a.errors.full_messages.to_sentence }
        end

        sendReply(result)
      end

      def asset_dg_allocation_source
        allocation = {}

        if params[:id].present?
          allocation = AssetDgAllocationSource.where(id: params[:id]).first
        end

        render json: allocation.to_json(
          :include => {
            :asset_dg_allocations => {},
            :user_account => { :only => [:id, :name, :email, :username] }
        }), status: 200
      end

      def automatic_allocation
        result = {
          automatic_allocation: nil,
          asset_dg_allocation_source: nil
        }

        if params[:id].present?
          automatic_allocation_hash = AutomaticAllocation.value_hash_by_id(params[:id])
          if automatic_allocation_hash
            automatic_allocation = AutomaticAllocation.from_value_hash(automatic_allocation_hash)
            asset_dg_allocation_source = AssetDgAllocationSource.where(id: automatic_allocation.allocationSourceId.try(:[], :id)).first

            result[:automatic_allocation] = JSON.parse(automatic_allocation_hash.to_json)
            result[:automatic_allocation]['dispatch_groups'] = JSON.parse(automatic_allocation.dispatch_groups.to_json)
            result[:automatic_allocation]['trigger_events'] = JSON.parse(automatic_allocation.triggerEvents.to_json)
            result[:asset_dg_allocation_source] = asset_dg_allocation_source
          end
        end

        sendReply(result)
      end

      def download_allocation_sample
        data = [
          ["timeFrom","timeTo","assetId","dgId","allocate"],
          ["2019-02-01T23:45:00Z","2019-02-04T00:00:00Z","4","1","0"],
          ["2019-02-01T23:45:00Z","2019-02-04T00:00:00Z","7","1","0"],
          ["2019-02-01T23:45:00Z","2019-02-04T00:00:00Z","8","1","0"],
          ["2019-02-01T23:45:00Z","2019-02-04T00:00:00Z","9","1","0"],
          ["2019-02-01T23:45:00Z","2019-02-04T00:00:00Z","10","1","0"],
          ["2019-02-01T23:45:00Z","2019-02-04T00:00:00Z","11","1","0"],
          ["2019-02-01T23:45:00Z","2019-02-04T00:00:00Z","12","1","0"],
          ["2019-02-01T23:45:00Z","2019-02-04T00:00:00Z","14","1","0"],
          ["2019-02-01T23:45:00Z","2019-02-04T00:00:00Z","15","1","0"],
          ["2019-02-01T23:45:00Z","2019-02-04T00:00:00Z","20","1","0"],
          ["2019-02-01T23:45:00Z","2019-02-04T00:00:00Z","22","1","0"],
          ["2019-02-01T23:45:00Z","2019-02-04T00:00:00Z","23","1","0"],
          ["2019-02-01T23:45:00Z","2019-02-04T00:00:00Z","25","1","0"],
          ["2019-02-01T23:45:00Z","2019-02-04T00:00:00Z","26","1","0"],
          ["2019-02-01T23:45:00Z","2019-02-04T00:00:00Z","32","1","0"]
        ]
        csv_string = CSV.generate(col_sep: ",") do |csv|
          data.each do |row|
            csv << row
          end
        end

        send_data csv_string, filename: "example_allocation_file.csv"
      end


      def audit_actions
        {
          new: 'New Allocation',
          create: 'Create Allocation',
          upload: 'Upload Allocation',
        }
      end
    end
  end
end