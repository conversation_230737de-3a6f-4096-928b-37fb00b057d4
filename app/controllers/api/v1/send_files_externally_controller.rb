require 'base64'

module Api
  module V1

    class SendFilesExternallyController < Api::V1::ApiController

      include ApplicationHelper

      before_action :enforce_create_authority, only: [:send_allocations_files,
                                                      :send_measurements_files]

      def send_allocations_files
        date = Date.strptime(params[:date], '%d/%m/%Y')

        cps_msg_request = CpsAllocationMessageUploadRequest.
          new(delivery_date: date, created_at: Time.now)

        begin
          cps_msg_request.save!
          flash[:notice] =
          ret = {
              success: true,
              notice: t('send_allocations_files.success', scope: 'flash.send_files_externally', date: date)
          }
        rescue
          ret = {
              success: false,
              error: t('send_allocations_files.failure', scope: 'flash.send_files_externally', date: date)
          }
          if cps_msg_request.errors.present?
            ret[:error] += cps_msg_request.errors.full_messages.to_sentence
          end
        end

        sendReply(ret)
      end

      def send_measurements_files
        date = Date.strptime(params[:date], '%d/%m/%Y')

        upload_request = MmchubUploadRequest.
          new(delivery_date: date, created_at: Time.now, updated_at: Time.now, request_creator_id: current_user.id)

        begin
          upload_request.save!
          flash[:notice] =
          ret = {
              success: true,
              notice: t('send_measurements_files.success', scope: 'flash.send_files_externally', date: date)
          }
        rescue
          ret = {
              success: false,
              error: t('send_measurements_files.failure', scope: 'flash.send_files_externally', date: date)
          }
          if upload_request.errors.present?
            ret[:error] += upload_request.errors.full_messages.to_sentence
          end
        end

        sendReply(ret)
      end

      def send_afrr_measurements_files
        date_time = Time.parse(params[:date_time])
        # enforce full hours
        date_time = date_time.change(min: 0, sec: 0)

        upload_request = CbpDeviceUploadRequest.new(
          start_time: date_time,
          end_time: date_time + 1.hour,
          created_at: Time.now,
          updated_at: Time.now,
          request_creator_id: current_user.id
        )

        begin
          upload_request.save!
          flash[:notice] =
          ret = {
              success: true,
              notice: t('send_afrr_measurements_files.success', scope: 'flash.send_files_externally', date: date_time)
          }
        rescue
          ret = {
              success: false,
              error: t('send_afrr_measurements_files.failure', scope: 'flash.send_files_externally', date: date_time)
          }
          if upload_request.errors.present?
            ret[:error] += upload_request.errors.full_messages.to_sentence
          end
        end

        sendReply(ret)
      end
      
      def send_afrr_activated_energy_documents
        date = Date.strptime(params[:date], '%d/%m/%Y')

        upload_request = CbpActivatedEnergyDocumentUploadRequest.new(
          delivery_date: date,
          mr_id_regenerate: params[:generate_new_random_mr_id],
          mr_id_override: params[:mr_id],
          created_at: Time.now,
          updated_at: Time.now,
          request_creator_id: current_user.id
        )

        begin
          upload_request.save!
          flash[:notice] =
          ret = {
              success: true,
              notice: t('send_afrr_activated_energy_documents.success', scope: 'flash.send_files_externally', date: date)
          }
        rescue
          ret = {
              success: false,
              error: t('send_afrr_activated_energy_documents.failure', scope: 'flash.send_files_externally', date: date)
          }
          if upload_request.errors.present?
            ret[:error] += upload_request.errors.full_messages.to_sentence
          end
        end

        sendReply(ret)
      end

      def audit_actions
        {
            send_allocations_files: 'NL FCR - Send Allocation File to TenneT',
            send_measurements_files: 'NL FCR - Send Measurement File to TenneT',
            send_afrr_measurements_files: 'NL aFRR - Send Measurement File to TenneT',
            send_afrr_activated_energy_documents: 'NL aFRR - Send Activated Energy Document to TenneT',
        }
      end

      protected

      def enforce_create_authority
        if !current_user.can?(:reporting_and_notification_create)
          raise Authority::SecurityViolation.new(current_user, action_name, controller_name)
        end
      end

    end
  end
end