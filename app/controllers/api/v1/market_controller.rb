module Api
  module V1
    class MarketController < Api::V1::ApiController

      before_action :enforce_auction_results_create_authority, only: [:upload]

      def upload
        result = {
          success: false,
          result: nil,
          error: nil,
          messages: [],
          warning_msgs: []
        }

        auction_config_id = params[:auction_config_id]
        if !user_owned_auction_config_ids.include?(auction_config_id.try(:to_i))
          result[:error] = 'AUTHORIZATION_ERROR'
        elsif params[:file].present?
          file_name = params[:file].original_filename
          file_contents = params[:file].read
          auction_config = AuctionConfig.find(auction_config_id)
          if auction_config.has_internal_bidding_input_format?(["RegelleistungMRL", "RegelleistungSRL", "RegelleistungPRL"])
            skip_validations = Array(params['skip_validations'])
            contents_type = 'auctions'
            begin
              created_source_id =
                trading_service.dus_file_upload(
                  file_name: file_name,
                  file_contents: file_contents,
                  user_id: current_user.id,
                  auction_config_id: auction_config_id,
                  skip_validations: skip_validations,
                  contents_type: contents_type)

              result[:success] = true
              result[:result] = DistributedUnitSource.where(id: created_source_id).first
              result[:result_type] = 'DistributedUnitSource'
            rescue trading_service::UnableToFileUpload => e
              result[:error] = 'ISSUE_UPLOADING_FILE'
              result[:messages] = [e.to_s]
            end
          elsif auction_config.has_internal_bidding_input_format?(["N2EX1h", "EPEX30min", "DC", "DS"])
            with_market = auction_config.has_internal_bidding_input_format?(['DS'])
            xl = Services::N2exEpexDsAfrrAuctionResultImporter.new.parse_buffer(file_contents, with_market)
            err = xl[:error]
            if err.present?
              result[:error] = 'ISSUE_UPLOADING_FILE'
              result[:messages] = [err]
            else
              Rails.logger.info "Imported auction results from #{file_name} as #{xl.to_json}"
              begin
                payload = {
                  user_id: current_user.id,
                  lines: xl[:lines]
                }
                ar_resp = trading_service.post_auction_results(auction_config_id, payload)
                result[:success] = ar_resp[:success]
                result[:warning_msgs] = get_warning_messages(ar_resp[:warning_msgs])
                if ar_resp[:error].present?
                  result[:error] = ar_resp[:error]
                end  
                if ar_resp[:source_id].present?
                  nbs = NominationBidSource.where(id: ar_resp[:source_id]).first
                  result[:result] = nbs
                  result[:result_type] = 'NominationBidSource'
                  if nbs.is_success?
                    result[:messages] = ['BIDDING.UPLOAD_BIDS.SUCCESS_UPLOADED']
                  else
                    result[:messages] = get_error_messages(nomination_bid_source: nbs, is_file_upload: true)
                  end
                end
                if !result[:success] && !result[:error].present?
                  result[:error] = 'UNEXPECTED_ERROR'  
                end
              rescue trading_service::UnableToFileUpload => e
                result[:error] = 'ISSUE_UPLOADING_FILE'
                result[:messages] = [e.to_s]
              end
            end
          else
            result[:error] = 'NOT_IMPLEMENTED_BIDDING_INPUT_FORMAT'
          end
        else
          result[:error] = 'NO_FILE_UPLOADED'
        end
        sendReply(result)
      end

      def list
        markets ||= Market.all.order(name: :asc)

        if params[:page].present?
          markets = markets.page(params[:page]).per(params[:per_page])
        end

        sendReply({markets: markets})
      end


      def audit_actions
        {
            upload: 'Upload Market Auction Results',
        }
      end

      def enforce_auction_results_create_authority
        if !current_user.can?(:auction_results_create)
          raise Authority::SecurityViolation.new(current_user, action_name, controller_name)
        end
      end

      def get_error_messages(nomination_bid_source:, is_file_upload:)
        errmsg = [I18n.t('bid.err_validation')]# + ': <br/>'
        # nomination_bid_source.validation_errors.collect{|e| [e['name'], e['details']] }.uniq.each do |error_code|
        #   if error_code.last
        #     errmsg << I18n.t("bid.err_#{error_code.first}", JSON.parse(error_code.last).symbolize_keys)
        #   else
        #     errmsg << I18n.t("bid.err_#{error_code.first}")
        #   end
        #   errmsg << '<br/>'
        # end
        nomination_bid_source.validation_errors.each do |error|
          errmsg << error
        end
        if is_file_upload
          errmsg << I18n.t('bid.please_check_and_try_again')
        end
        errmsg
      end

      def get_warning_messages(warnings)
        human_readable_warns = warnings.collect do |warn|
          I18n.t("bid.warn_#{warn['code']}", description: warn['description'] )
        end
        human_readable_warns.join('<br/>').html_safe
      end

    end
  end
end
