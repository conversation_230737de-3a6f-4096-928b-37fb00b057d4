module Api
  module V1
    class NominationController < Api::V1::ApiController

      authorize_actions_for DistributedUnit
      authority_actions distributed_units: :read,
                        nomination: :read,
                        download: :read,
                        upload: :create,
                        delete_distributed_unit: :create

      def new
        if params[:file].present?
          upload
        else
          create
        end
      end

      def upload
        result = {
          success: false,
          result: nil,
          error: nil,
          messages: []
        }

        if params[:file].present?
          file_name = params[:file].original_filename
          file_contents = params[:file].read
          skip_validations = Array(params['skip_validations'])
          contents_type = 'manual'
          begin
            created_source_id =
              trading_service.dus_file_upload(
                file_name: file_name,
                file_contents: file_contents,
                user_id: current_user.id,
                auction_config_id: nil,
                skip_validations: skip_validations,
                contents_type: contents_type)

            result[:success] = true
            result[:result] = DistributedUnitSource.where(id: created_source_id).first
          rescue trading_service::UnableToFileUpload => e
            result[:error] = 'ISSUE_UPLOADING_FILE'
            result[:messages] = [e.to_s]
          end
        else
          result[:error] = 'NO_FILE_UPLOADED'
        end

        sendReply(result)
      end

      def create
        result = {
          success: false,
          result: nil,
          error: nil,
          messages: []
        }

        skip_validations = (params[:skip_validations] == true || params[:skip_validations] == 'true') ? ['StartTimeValidator'] : []

        resource_params = params.require(:distributed_unit).permit(
            :interval, :dispatch_group_id, :energy_direction, :flex_volume_mw,
            :energy_price, :capacity_price, :used_for_asset_price, asset_ids: [])

        dus = Array(DistributedUnit.new(resource_params))
        asset_ids = params[:distributed_unit][:asset_ids]

        if dus.all?(&:valid?)
          begin
            created_source_id = trading_service.post_dus(
                dus: dus,
                asset_ids: asset_ids || du.asset_ids,
                user_id: current_user.id,
                skip_validations: skip_validations)

            result[:success] = true
            result[:result] =  DistributedUnitSource.where(id: created_source_id).first

          rescue trading_service::UnableToPostDUs => e
            result[:error] = 'ISSUE_CREATING_NOMINATION'
            result[:messages] = [e.to_s]
          end
        else
          result[:error] = 'VALIDATION_ERROR'
          result[:messages] = dus.collect { |a| a.errors.full_messages.to_sentence }
        end

        sendReply(result)
      end

      def update
        result = {
          success: false,
          result: nil,
          error: nil,
          messages: []
        }

        skip_validations = (params[:skip_validations] == true || params[:skip_validations] == 'true') ? ['StartTimeValidator'] : []

        distributed_unit = DistributedUnit.find(params[:id])

        resource_params =
          params
          .require(:distributed_unit)
          .permit(:interval, :dispatch_group_id, :energy_direction, :flex_volume_mw, :energy_price, :capacity_price, :used_for_asset_price)

        puts resource_params

        distributed_unit.attributes = resource_params

        dus = Array(distributed_unit)
        asset_ids = params[:distributed_unit][:asset_ids]

        if dus.all?(&:valid?)
          begin
            created_source_id = trading_service.post_dus(
                dus: dus,
                asset_ids: asset_ids,
                user_id: current_user.id,
                skip_validations: skip_validations)

            result[:success] = true
            result[:result] = DistributedUnitSource.find(created_source_id)

          rescue trading_service::UnableToPostDUs => e
            result[:error] = 'ISSUE_CREATING_NOMINATION'
            result[:messages] = [e.to_s]
          end
        else
          result[:error] = 'VALIDATION_ERROR'
          result[:messages] = dus.collect { |a| a.errors.full_messages.to_sentence }
        end

        sendReply(result)
      end

      def distributed_units
        collection =
          DistributedUnit
          .order(start_time: :desc, created: :desc)
          .page(params[:page])
          .per((params[:per_page] || 25).to_i)
          .includes(dispatch_group: [:tso], product: [], assets: [])

        start_time = nil
        end_time = nil
        if params[:start_date].present?
          start_time = Time.parse(params[:start_date]).to_datetime
        end
        if params[:end_date].present?
          end_time = Time.parse(params[:end_date]).to_datetime
        end

        if start_time.present? && end_time.present?
          collection = begin
              if start_time <= end_time
                collection.where('(distributed_unit.start_time, distributed_unit.end_time) overlaps (?, ?)', start_time, end_time)
              else
                collection.where('true = false')
              end
            end
        elsif start_time.present?
          collection = collection.where('distributed_unit.start_time >= ? OR distributed_unit.end_time > ?', start_time, start_time)
        elsif end_time.present?
          collection = collection.where('distributed_unit.start_time < ? OR distributed_unit.end_time < ?', end_time, end_time)
        end

        if params[:dispatch_group_id].present? && params[:dispatch_group_id] != 'null'
          if user_owned_dg_ids.include?(params[:dispatch_group_id].to_i)
            collection = collection.where(dispatch_group_id: params[:dispatch_group_id])
          else
            collection = collection.where(dispatch_group_id: nil)
          end
        else
          collection = collection.where(dispatch_group_id: user_owned_dg_ids)
        end

        if params[:energy_direction].present? && params[:energy_direction] != 'Select Direction'
          m = Product.market_type(params[:energy_direction])
          collection = collection.where(product: { market_type: m })
        end

        if params[:deleted] == "false"
          collection = collection.where(deleted: nil)
        end

        sendReply(
          {
            data: {
              distributed_units: JSON.parse(collection.to_json(:include => {
                :dispatch_group => { :only => [:id, :name]},
                :product => {
                  :methods => :energy_direction
                },
                :assets => { :only => [:id, :name]}
                })
              ),
            },
            total_entries: collection.total_count,
            page: params[:page] || 1
          }
        )
      end

      def delete_distributed_unit
        result = {
          success: false,
          result: nil,
          error: nil,
          messages: []
        }

        du = DistributedUnit.where(id: params[:id]).first
        if du.present? && user_owned_dg_ids.include?(du.dispatch_group_id)
          result[:success] = true
          result[:result] = trading_service.delete_du(
            du_id: du.id,
            user_id: current_user.id)
        end

        sendReply(result)
      end

      def nomination
        distributed_unit_source = {}
        if params[:id].present?
          distributed_unit_source = DistributedUnitSource.includes(:user_account).includes(distributed_units: { dispatch_group: [:tso], product: [], assets: [] }).find_by_id(params[:id])

          dus = JSON.parse(distributed_unit_source.to_json(
            :include => {
              :user_account => { :only => [:id, :name, :email, :username] }
            })
          ) || {}
          dus['distributed_units'] = []

          (distributed_unit_source.try(:distributed_units) || []).each do |d|
            item = JSON.parse(d.to_json)
            item['dispatch_group'] = JSON.parse(d.dispatch_group.to_json)
            item['dispatch_group']['tso'] = {
              id: d.dispatch_group.tso.id,
              name: d.dispatch_group.tso.name
            }
            item['energy_direction'] = d.energy_direction
            item['product'] = JSON.parse(d.product.to_json)
            item['assets'] = JSON.parse(d.assets.to_json)
            dus['distributed_units'] << item
          end

          sendReply(dus)
        else
          sendReply(distributed_unit_source)
        end
      end

      def download
        source =
          DistributedUnitSource
          .filter_file_uploads
          .find(params[:id])

        send_data(
          source.content_file_contents,
          type: 'text/csv',
          disposition: 'attachment',
          filename: source.content_file_name.presence)
      end

      def audit_actions
        {
          new: 'Upload Nominations',
          upload: 'Upload Nominations',
          create: 'Create Nomination',
          update: 'Update Nomination',
          delete_distributed_unit: 'Update Nomination',
        }
      end

    end
  end
end
