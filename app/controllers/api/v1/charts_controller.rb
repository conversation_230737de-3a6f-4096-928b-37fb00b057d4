module Api
  module V1
    class ChartsController < Api::V1::ApiController
      INTERVAL_TIME_FORMAT = '%Y-%m-%d %H:%M'.freeze
      INTERVAL_SEPPARATOR = ' - '.freeze

      before_action :enforce_read_authority, only: [:allocations, :download_allocations_csv]

      def allocations
        if params[:interval].present? && (params[:asset_id].present? || params[:dispatchgroup_ids].present?)
          allocated_flex = nil
          puts query_params
          if params[:asset_id].present?
            allocated_flex = Services::PortfolioManagementService.asset_allocated_flex(
              start_time: query_params[:start_time],
              end_time: query_params[:end_time],
              asset_id: query_params[:asset_id],
              dg_ids: query_params[:dg_ids]
            )
            allocated_flex_series = prepare_asset_allocated_flex_series(allocated_flex)
            sendReply({
              allocated_flex_series: allocated_flex_series
            })
          elsif params[:dispatchgroup_ids].present?
            dg_aggregations = Services::PortfolioManagementService.dg_aggregations(
              start_time: query_params[:start_time],
              end_time: query_params[:end_time],
              dg_ids: query_params[:dg_ids]
            )

            dgs = DispatchGroup.not_deleted.includes([:tso, :market]).order(name: :asc).group_by(&:id)

            allocated_flex_series = prepare_allocated_flex_series(dg_aggregations['allocatedFlex'], dgs)
            nominated_flex_series = prepare_nominated_flex_series(dg_aggregations['nominatedFlex'])
            sendReply({
              allocated_flex_series: allocated_flex_series,
              nominated_flex_series: nominated_flex_series
            })
          end
        else
           sendReply(nil)
        end
      end

      def download_allocations_csv
        if params[:interval].present? && params[:dispatchgroup_ids].present?
          allocated_flex = nil
          puts download_query_params
          allocations = Services::PortfolioManagementService.allocations(
            start_time: download_query_params[:start_time],
            end_time: download_query_params[:end_time],
            dg_ids: download_query_params[:dg_ids]
          )

          data = Services::AllocationsCsvExporter.export(allocations)
          filename =
            [
              'allocations',
              I18n.l(download_query_params[:start_time], format: '%d %b %H:%M'),
              I18n.l(download_query_params[:end_time], format: '%d %b %H:%M'),
              download_query_params[:dg_ids].present? ? (["DGs"] + download_query_params[:dg_ids]) : nil
            ].compact.join('_')
          send_data(data, filename: "#{filename}.csv")
        else
           sendReply(nil)
        end
      end

      private

      def prepare_allocated_flex_series(flex, dgs)
        flex.each_with_object({}) do |(dg_id_string, entries), acc|
          parsed_entries = entries.each_with_object({}) do |entry, in_acc|
            start_time = Time.parse(entry['startTime']).in_time_zone(Time.zone.name)
            end_time = Time.parse(entry['endTime']).in_time_zone(Time.zone.name)
            dg_name = "#{dgs[dg_id_string.to_i].try(:first).try(:name)}"
            in_acc[start_time] = {
              x: start_time.to_i * 1_000,
              low: entry['flexInterval']['negativeFlex'].to_f / 1_000,
              high: entry['flexInterval']['positiveFlex'].to_f / 1_000,
              description:
                dg_name + " | " +
                I18n.l(start_time, format: :short) + ' - ' +
                I18n.l(end_time, format: :short)
            }
            in_acc[end_time] = {
              x: end_time.to_i * 1_000,
              low: 0,
              high: 0,
              description: dg_name
            }
          end
          acc[dg_id_string.to_i] = parsed_entries.values
        end
      end

      def prepare_asset_allocated_flex_series(allocated_flex)
        dgs = DispatchGroup.not_deleted.includes([:tso, :market]).order(name: :asc).group_by(&:id)
        grouped = allocated_flex.group_by { |a| a['dispatchGroupId'] }
        prepare_allocated_flex_series(grouped, dgs)
          .each_with_object([]) do |(k, v), acc|
            acc << {
              'data' => v,
              'name' => dgs[k].try(:first).try(:name),
              'exclussive' => dgs[k].try(:first).market.exclussive_market?
            }
          end
      end

      def prepare_nominated_flex_series(flex)
        flex.each_with_object({}) do |(dg_id_string, entries), acc|
          parsed_entries = entries.each_with_object({}) do |entry, in_acc|
            start_time = Time.parse(entry['startTime']).in_time_zone(Time.zone.name)
            end_time = Time.parse(entry['endTime']).in_time_zone(Time.zone.name)
            in_acc['positive'] ||= {}
            in_acc['negative'] ||= {}

            in_acc['positive'][start_time] = [
              start_time.to_i * 1_000,
              entry['flexInterval']['positiveFlex'].to_f / 1_000
            ]
            in_acc['positive'][end_time] = [
              end_time.to_i * 1_000,
              0
            ]

            in_acc['negative'][start_time] = [
              start_time.to_i * 1_000,
              entry['flexInterval']['negativeFlex'].to_f / 1_000
            ]
            in_acc['negative'][end_time] = [
              end_time.to_i * 1_000,
              0
            ]
          end
          acc[dg_id_string.to_i] = {
            positive: parsed_entries['positive'].try(:values) || [],
            negative: parsed_entries['negative'].try(:values) || []
          }
        end
      end

      def query_params
        start_time, end_time = params[:interval].split(INTERVAL_SEPPARATOR)

        opts = {
          start_time: Time.parse(start_time),
          end_time: Time.parse(end_time)
        }

        if params[:asset_id].present?
          opts[:asset_id] = params[:asset_id].presence.try(&:to_i)
        end

        if params[:dispatchgroup_ids].present?
          opts[:dg_ids] = params[:dispatchgroup_ids].presence.to_a.collect(&:to_i)
        end

        opts
      end

      def download_query_params
        dgIds = params[:dispatchgroup_ids].present? ? params[:dispatchgroup_ids].split(',') : nil
        start_time, end_time = params[:interval].split(INTERVAL_SEPPARATOR)

        opts = {
          start_time: Time.parse(start_time),
          end_time: Time.parse(end_time)
        }

        if params[:dispatchgroup_ids].present?
          opts[:dg_ids] = dgIds.map{|i| i.to_s.to_i}
        end

        opts
      end

      def enforce_read_authority
        if !current_user.can_read?(AssetDgAllocation)
          raise Authority::SecurityViolation.new(current_user, action_name, controller_name)
        end
      end

    end
  end
end
