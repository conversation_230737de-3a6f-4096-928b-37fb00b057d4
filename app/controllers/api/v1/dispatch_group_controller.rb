module Api
  module V1
    class DispatchGroupController < Api::V1::ApiController

      authorize_actions_for DispatchGroup
      authority_actions list: :read,
                        dispatch_groups_list: :read,
                        dispatch_group_details: :read,
                        form_data: :read,
                        update_dispatch_group: :update,
                        create_dispatch_group: :create,
                        delete_dispatch_group: :delete,
                        update_balancing_group: :update,
                        create_balancing_group: :update,
                        delete_balancing_group: :update

      def list

        dispatch_groups = DispatchGroup
                                .not_deleted
                                .includes(:tso, :market)
                                .order(name: :asc)
        dispatch_groups = dispatch_groups.where(id: user_owned_dg_ids)

        if params[:page].present?
          dispatch_groups = dispatch_groups.page(params[:page]).per(params[:per_page])
        end

        dispatch_groups_json = dispatch_groups.to_json(
            :only => [:id, :name, :market_id],
            :include => {
                :tso => {only: [:id, :name]},
                :market => {only: [:id, :name]}
            },
            :methods => [
              :generic_config_enabled,
              :has_generic_config,
            ])
        sendReply({dispatch_groups: JSON.parse(dispatch_groups_json)})
      end

      def new_dispatch_group_details
        default_params = {
            scm_dc_foot_room: 110,
            scm_dc_head_room: 110,
            scm_dc_ramp_rate_limit: 5,
            scm_dc_energy_reserve: 900,
            scm_dc_min_energy_recovery: 200,
            ext_backup_buffer: 120,
            ext_backup_threshold_pos: 5000,
            ext_backup_threshold_neg: 5000,
            ext_backup_nomination_threshold_pos: 10,
            ext_backup_nomination_threshold_neg: 10,
            ext_backup_increment_pos: 10000,
            ext_backup_increment_neg: 10000
        }
        DispatchGroup.new(default_params)
      end

      def dispatch_group_details
        id = params[:id]
        if (!id || !(id.try(&:to_i) > 0))
          dg = new_dispatch_group_details
          bgs = []
        else
          dg = DispatchGroup.not_deleted.includes(:tso, :market)
          dg = dg.where(id: user_owned_dg_ids).find(params[:id])

          # bgs = BalancingGroupDg
          #           .overlap_date_interval(Date.today..Date.today)
          #           .where(dispatch_group_id: id)
          dg.set_generic_config_attrs_from_json if dg.has_generic_config
          dg.set_dlm_params_attrs_from_json if dg.dlm_parameters_enabled? && dg.has_dlm_market?
          bgs = BalancingGroupDg
                    .where(dispatch_group_id: id)
        end

        sendReply(
            {
                dispatch_group:
                    JSON.parse(dg.to_json(
                        :include => {
                            :tso => {:only => [:id, :name]},
                            :market => {:ony => [:id, :name]},
                            :dsos => {:ony => [:id, :name]}
                        },
                        :methods => [
                            :generic_config_enabled,
                            :has_generic_config,
                            :signals_list,
                            :capacity_price_grouping_steps_list,
                            :event_types_acknowledge_alarm_list,
                            :event_types_acknowledge_audio_alarm_list,

                            :export_entrader,
                            :entrader_treshold_update,
                            :entrader_lead_time_update,
                            :entrader_upload_folder,

                            :has_exclusive_behaviour,
                            :has_nomination,
                            :has_preceding_basepoint,

                            :asset_activation_type,
                            :aat_ar_flsp_dead_band_pos,
                            :aat_ar_flsp_dead_band_neg,
                            :aat_ar_flsp_max_frequency_deviation_pos,
                            :aat_ar_flsp_max_frequency_deviation_neg,
                            :aat_ar_flsp_high_knee_joint,
                            :aat_ar_flsp_low_knee_joint,

                            :state_of_charge_management,
                            :scm_dc_target_state_of_charge_low,
                            :scm_dc_target_state_of_charge_high,
                            :scm_dc_target_state_of_charge_both,
                            :scm_dc_foot_room,
                            :scm_dc_head_room,
                            :scm_dc_ramp_rate_limit,
                            :scm_dc_energy_reserve,
                            :scm_dc_min_energy_recovery,
                            :scm_dc_dead_band_factor,
                            :scm_vb_dc_delivery_duration,
                            :scm_vb_dc_delivery_duration_buffer,
                            :scm_vb_dc_min_energy_recovery,
                            :scm_vb_dm_delivery_duration,
                            :scm_vb_dm_delivery_duration_buffer,
                            :scm_vb_dm_min_energy_recovery,
                            :scm_vb_dr_delivery_duration,
                            :scm_vb_dr_delivery_duration_buffer,
                            :scm_vb_dr_min_energy_recovery,
                            :scm_fcr_lower_soc_limit,
                            :scm_fcr_upper_soc_limit,
                            :scm_opt_sp_duration_minutes,

                            :ads_start_using_assets_only_when_at_basepoint,
                            :ads_strategy,
                            :ads_strategy_by_price_preserve_current_dispatches,
                            :ads_strategy_by_price_and_soc_limit_battery_power_window,
                            :ads_strategy_pro_rata_symmetric,
                            :ads_strategy_on_off_signal,

                            :cross_dg_links,
                            :cdl_cross_plan_propagation_frequency,

                            :dc_check_interval_seconds,
                            :dc_over_delivery_excess_compensation_factor,
                            :dc_over_delivery_compensation_limit_factor,
                            :dc_under_delivery_excess_compensation_factor,
                            :dc_under_delivery_compensation_limit_factor,
                            :dc_compensation_resolution_kw,
                            :dc_is_at_setpoint_upper_tolerance_factor,
                            :dc_is_at_setpoint_upper_tolerance_minimum_kw,
                            :dc_is_at_setpoint_lower_tolerance_factor,
                            :dc_is_at_setpoint_lower_tolerance_minimum_kw,

                            :dg_activation_type,
                            :dispatch_commands,
                            :dispatch_source,
                            :ds_nominated_volume_activation_factor,
                            :ds_nominated_volume_symmetric_activation,
                            :ds_ui_edg_schedule,
                            :ds_residual_shape_positive_threshold,
                            :ds_residual_shape_negative_threshold,
                            :ds_residual_shape_window,
                            :ds_price_trigger_window,
                            :ds_price_trigger_price_type,
                            :ds_price_trigger_settlement_period,
                            :ds_price_trigger_price_threshold_neg,
                            :ds_price_trigger_price_threshold_pos,
                            :ds_price_trigger_price_expiration_seconds,
                            :ds_nlafrr_bleeding_time_seconds,

                            :execution_plan_window_seconds,
                            :execution_plan_frequency_seconds,

                            :dpb_min_power_for_dispatch_discharge,
                            :dpb_min_power_for_dispatch_charge,

                            :net_extend_before_nomination_seconds,
                            :net_overlap_nominations_seconds,
                            :net_stop_before_end_of_nomination_seconds,

                            :notifications,
                            :ntf_flex_too_low_threshold_factor,
                            :ntf_flex_too_low_threshold_buffer_seconds,
                            :ntf_over_delivery_negative_threshold_kw,
                            :ntf_over_delivery_negative_delay_seconds,
                            :ntf_over_delivery_positive_threshold_kw,
                            :ntf_over_delivery_positive_delay_seconds,
                            :ntf_setpoint_not_reached_reach_setpoint_in_seconds,
                            :ntf_setpoint_not_reached_is_at_setpoint_detection_upper_tolerance_factor,
                            :ntf_setpoint_not_reached_is_at_setpoint_detection_upper_tolerance_minimum_kw,
                            :ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_factor,
                            :ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_minimum_kw,
                            :ntf_setpoint_reachable_is_at_setpoint_detection_upper_tolerance_factor,
                            :ntf_setpoint_reachable_is_at_setpoint_detection_upper_tolerance_minimum_kw,
                            :ntf_setpoint_reachable_is_at_setpoint_detection_lower_tolerance_factor,
                            :ntf_setpoint_reachable_is_at_setpoint_detection_lower_tolerance_minimum_kw,

                            :periodicity_dg_aggregations_seconds,
                            :periodicity_output_signal_write_seconds,

                            :ramp_adjustment_strategy,
                            :redispatch_triggers,
                            :rt_asset_available_flex_change_threshold_kw,
                            :rt_asset_available_flex_change_since_activation_factor,
                            :rt_dg_total_deviation_buffer_kw,
                            :rt_dg_total_deviation_suppress_redispatch_seconds,

                            :setpoint_validation_reaction_time_seconds,
                            :setpoint_validation_cap_by_nomination,
                            :setpoint_validation_minimum_duration_of_dispatch_seconds,
                            :setpoint_validation_nomination_interval_validation,
                            :setpoint_validation_quantization_filter_resolution_kw,
                            :setpoint_validation_quantization_filter_quantization_duration_seconds,

                            :signals_output_signals,
                            :signals_sub_pools_values_on,
                            :signals_sub_pools_values_off,
                            :signals_dso_for_signals,
                            :signals_heart_beat_mirror,

                            :ext_backup_increment_pos_mw,
                            :ext_backup_increment_neg_mw,
                            :ext_backup_threshold_pos_mw,
                            :ext_backup_threshold_neg_mw,
                            :nomination_tool_additional_buffer_pos_mw,
                            :nomination_tool_additional_buffer_neg_mw,
                            :nomination_tool_target_bid_volume_mw,

                            #DLM parameters
                            :grouping_rule,
                            :nominal_site_current,
                            :reduction_factor,
                            :dlm_group_reduced_id,
                            :dlm_group_blocked_id,
                            :control_windows,
                            :session_duration_threshold_minutes,
                            :session_energy_threshold_watt_hour,
                            :grouping_rule_both_thresholds,
                            :min_average_charged_energy_factor,
                            :min_average_charging_duration_factor
                        ]
                    )),
                bgs:
                    JSON.parse(bgs.to_json(
                        :only => [:id, :name, :start_date, :end_date],
                        :methods => [:can_delete]
                    ))
            }
        )
      end

      def form_data
        signals = DispatchGroup.available_signals
        dsos = Dso.all.order(name: :asc)
        event_types =
            EventTypeDecorator
                .decorate_collection(EventType.all)
                .sort { |a, b| a.name.downcase <=> b.name.downcase }
        markets = Market.all.order(name: :asc)
        tsos = Tso.all.order(name: :asc)
        available_bg_names = Db::DbBgNameSupplier.pluck(:name)

        sendReply(
            {
                signals: signals,
                dsos: dsos.map { |x| {id: x.id, name: x.name} },
                event_types: event_types,
                markets: markets.map { |x| {id: x.id, name: x.name, allows_nomination_tool_enablement: x.allows_nomination_tool_enablement?} },
                tsos: tsos.map { |x| {id: x.id, name: x.name} },
                available_bg_names: available_bg_names
            }
        )
      end

      def create_dispatch_group
        result = {
            success: false,
            messages: ['not implemented']
        }

        p = dispatch_group_params

        dg = DispatchGroup.create(p)

        if dg.errors.any?
          result[:messages] = dg.errors.full_messages
        else
          if !current_user.has_privilege?(:super_admin) && current_user.organization.present?
            org = current_user.organization
            org.dispatch_groups << dg
            org.save
          end
          result[:messages] = []
          result[:success] = true
          result[:id] = dg.id
        end

        sendReply(result)
      end

      def update_dispatch_group
        result = {
            success: false,
            messages: []
        }

        p = dispatch_group_params
        if user_owned_dg_ids.include?(p[:id].try(:to_i))
          x = DispatchGroup.update(p[:id], p)

          if x.errors.any?
            result[:messages] = x.errors.full_messages
          else
            result[:messages] = []
            result[:success] = true
            result[:id] = x.id
          end
        else
          result[:messages] = 'AUTHORIZATION_ERROR'
        end
        sendReply(result)
      end

      def update_balancing_group
        result = {
            success: false,
            messages: ['not implemented']
        }

        dg_id = params[:dispatch_group_id]
        bg_id = params[:balancing_group][:id]

        Rails.logger.info "Update balancing group #{bg_id} for dg #{dg_id} #{params}"

        dispatch_group = DispatchGroup.find(dg_id)
        balancing_group = dispatch_group.balancing_groups.find(bg_id)

        if balancing_group.update(balancing_group_params)
          result[:success] = true
          result[:messages] = []
        else
          result[:success] = false
          result[:messages] = balancing_group.errors.full_messages
        end

        sendReply(result)
      end

      def create_balancing_group
        result = {
            success: false,
            messages: ['not implemented']
        }

        dg_id = params[:dispatch_group_id]

        Rails.logger.info "Create balancing group for dg #{dg_id} #{params}"

        dispatch_group = DispatchGroup.find(dg_id)
        balancing_group = dispatch_group
                              .balancing_groups
                              .new(balancing_group_params)

        if balancing_group.save
          result[:success] = true
          result[:messages] = []
        else
          result[:success] = false
          result[:messages] = balancing_group.errors.full_messages
        end

        sendReply(result)
      end

      def delete_balancing_group
        dg_id = params[:dispatch_group_id]
        bg_id = params[:balancing_group_id]

        DispatchGroup
            .find(dg_id)
            .balancing_groups.find(bg_id)
            .destroy

        result = {
            success: true
        }

        sendReply(result)
      end

      def dispatch_group_params
        params
            .require(:dispatch_group)
            .permit!
      end

      def balancing_group_params
        params
            .require(:balancing_group)
            .permit(:start_date, :end_date, :name, :version)
      end

      def delete_dispatch_group
        result = {
            success: true
        }

        if user_owned_dg_ids.include?(params[:id].try(:to_i))
          dg = DispatchGroup.find(params[:id])
          if dg.has_subpools?
            result[:success] = false
            result[:error] = I18n.t('errors.messages.unable_to_delete_dg',
                                        dg_name: dg.name,
                                        subpools: dg.subpools.collect {|s| s.name}.join(", "))
          elsif dg.has_allocations? || dg.has_nominations?
            result[:success] = false
            result[:error] = I18n.t('errors.messages.unable_to_delete_dg_in_use',
                                        dg_name: dg.name)
          else
            dg.mark_as_deleted
          end
        else
          result[:success] = true
        end

        sendReply(result)
      end

      def dispatch_groups_list
        per_page = (params[:per_page] || 25).to_i
        page = (params[:page] || 1).to_i
        dg_id = params[:dg_id]

        query = DispatchGroup
                    .not_deleted
                    .includes(:tso, :market)
                    .order(name: :asc)

        if (dg_id)
          index = query.pluck(:id).index(dg_id.to_i)
          if (!index || (index < per_page))
            page = 1
          else
            page = (((index + 1) / per_page) + 1).to_i
          end
        end

        collection = DispatchGroup
                         .not_deleted
                         .includes(:tso, :market)
                         .order(name: :asc)
                         .page(page).per(per_page)
        collection = collection.where(id: user_owned_dg_ids)

        balancing_groups =
            BalancingGroupDg
                .overlap_date_interval(Date.today..Date.today)
                .where(dispatch_group_id: collection.collect(&:id))
                .group_by(&:dispatch_group_id)

        ret = {
            dispatch_groups: collection.map do |dg|
              x = {
                  id: dg.id,
                  name: dg.name,
                  tso: {
                      id: dg.tso.id,
                      name: dg.tso.name
                  },
                  market: {
                      id: dg.market.try(:id),
                      name: dg.market.try(:name)
                  },
                  dsos: dg.dsos.map { |x| {id: x.id, name: x.name} },
                  nomination_tool_enabled: dg.nomination_tool_enabled?,
                  automatic_allocation_enabled: dg.automatic_allocation_enabled?,
                  signals_list: dg.signals_list,
                  has_generic_config: dg.has_generic_config,
                  generic_config_enabled: dg.generic_config_enabled
              }
              if (bg = balancing_groups[dg.id.to_i].try(:first)).present?
                x[:bg_label] = "#{bg.name} / #{l bg.start_date} - #{l bg.end_date}"
              end
              x
            end,
            #balancing_groups: balancing_groups,
            total_entries: collection.total_count,
            page: page
        }

        # Rails.logger.info "Loaded dispatch groups #{collection.count}"
        # Rails.logger.info "Returning #{ret}"

        sendReply(ret)
      end

      def audit_actions
        {
          create_dispatch_group: 'Create Dispatch Group',
          update_dispatch_group: 'Update Dispatch Group',
          delete_dispatch_group: 'Delete Dispatch Group',
          create_balancing_group: 'Create Balancing Group',
          update_balancing_group: 'Update Balancing Group',
          delete_balancing_group: 'Update Balancing Group',
        }
      end
    end
  end
end
