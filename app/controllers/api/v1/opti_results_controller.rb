module Api
  module V1
    class OptiResultsController < Api::V1::ApiController

      authorize_actions_for DistributedUnit
      authority_actions upload: :create, download: :read

      def upload
        result = {
          success: false,
          result: nil,
          error: nil,
          messages: []
        }
        asset_id = params[:asset_id]
        if params[:file].present?
          file_name = params[:file].original_filename
          file_contents = params[:file].read
          begin
            xl = Services::OptiResultImporter.new.parse_buffer(file_contents)
            err = xl[:error]
            if err.present?
              result[:error] = 'ISSUE_UPLOADING_FILE'
              result[:messages] = [err]
            else
              opti_result_lines = xl[:lines].map do |line|
                OptiResultLine.new({asset_id: asset_id}.merge(line))
              end
              errors = OptiResult.validate_upload(opti_result_lines, asset_id)
              if errors.empty?
                errors = OptiResult.save_upload(trading_service, file_name, asset_id, opti_result_lines, current_user.id)
                if errors.empty?
                  result[:success] = true
                  result[:result] = { validation_result: { validationSuccess: true } }
                else
                  result[:error] = 'ISSUE_UPLOADING_FILE'
                  result[:messages] = [errors.to_sentence]
                end
              else
                result[:error] = 'ISSUE_UPLOADING_FILE'
                result[:messages] = errors
              end
            end
          rescue Exception => e
            result[:error] = 'ISSUE_UPLOADING_FILE'
            result[:messages] = [e.message]
          end
        else
          result[:error] = 'NO_FILE_UPLOADED'
        end

        sendReply(result)
      end

      def download
        asset = Asset.find(params[:asset_id])
        selected_date = Time.find_zone(asset.tso.time_zone).parse(params[:selected_date])
        lines = OptiResultLine.
          select('DISTINCT ON (datetime) *').
          where(asset_id: asset.id).
          where('datetime >= ?', selected_date.beginning_of_day).
          where('datetime <= ?', selected_date.end_of_day).
          where(autofill: false).
          order(:datetime, created: :desc)

        exporter = Services::OptiResultExporter.new(
          opti_result_lines: lines,
          asset: asset,
          selected_date: selected_date
        )

        send_data(
          exporter.content,
          type: exporter.content_type,
          disposition: 'attachment',
          filename: exporter.filename)
      end

      def audit_actions
        {
            upload: 'Upload Market Auction Results',
        }
      end

    end
  end
end
