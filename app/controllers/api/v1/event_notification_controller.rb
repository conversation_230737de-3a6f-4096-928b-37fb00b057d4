module Api
  module V1

    class EventNotificationController < Api::V1::ApiController

      before_action :enforce_read_authority, only: [:event_notification_recipients]
      before_action :enforce_create_authority, only: [:update_event_notification_recipients]

      def event_notification_recipients
        users = (User.where(deleted: false) - User.customers).collect {|u| {id: u.id, name: u.name}}
        event_types = (EventType.all_notifiable - EventType.notifiable_for_auction_config - EventType.notifiable_for_dispatch_group)

        selection = EventNotification.where(event_type: event_types).collect do |e|
          {
              eventType: e.event_type.id,
              channel: e.notification_type,
              uid: e.user_id
          }
        end

        ret = {
            users: users,
            eventTypes: event_types,
            selection: selection
        }

        sendReply(ret)
      end

      def update_event_notification_recipients
        # create entities from params
        recipients_param = params['event_notification_recipients']
        entities = build_notifications(recipients_param)

        # save
        EventNotification.update_notification_recipiens(entities)

        # return the updated event_notification_recipients
        event_notification_recipients
      end

      def build_notifications(param)
        notifications = []
        event_types = (EventType.all_notifiable - EventType.notifiable_for_auction_config - EventType.notifiable_for_dispatch_group)
        if param
          event_types.each do |event_type|
            event_type = event_type.to_s
            EventNotification::TYPES.each do |notification_type|
              if param[event_type] && param[event_type][notification_type]
                param[event_type][notification_type].each do |user|
                  user_id = user['id']
                  if user_id.present?
                    notifications << EventNotification.new(
                        event_type: EventType.find(event_type),
                        notification_type: notification_type,
                        user_id: user_id
                    )
                  end
                end
              end
            end
          end
        end
        notifications
      end

      def event_notification_auction_config_recipients
        users = (User.where(deleted: false) - User.customers).collect {|u| {id: u.id, name: u.name}}
        event_types = EventType.notifiable_for_auction_config

        selection = OwnerEventNotification.where(owner_type: AuctionConfig.polymorphic_name, owner_id: user_owned_auction_config_ids).collect do |e|
          {
              eventType: e.event_type.id,
              owner_id: e.owner_id,
              channel: e.notification_type,
              uid: e.contact_id
          }
        end

        ret = {
            users: users,
            eventTypes: event_types,
            auctionConfigs: AuctionConfig.not_deleted.
              where(id: user_owned_auction_config_ids).
              order(name: :asc).
              collect { |acfg| {id: acfg.id, name: acfg.name} },
            selection: selection
        }

        sendReply(ret)
      end

      def update_event_notification_auction_config_recipients
        # create entities from params
        recipients_param = params['event_notification_auction_config_recipients']
        entities = build_auction_config_notifications(recipients_param)

        # save
        OwnerEventNotification.update_notification_recipiens(entities, AuctionConfig.polymorphic_name)

        # return the updated event_notification_auction_config_recipients
        event_notification_auction_config_recipients
      end

      def build_auction_config_notifications(param)
        notifications = []
        if param
          auction_configs = AuctionConfig.not_deleted.
            where(id: user_owned_auction_config_ids).
            order(name: :asc)
          EventType.notifiable_for_auction_config.each do |event_type|
            event_type = event_type.to_s
            auction_configs.each do |auction_config|
              EventNotification::TYPES.each do |notification_type|
                if param[event_type] && param[event_type][auction_config.id.to_s] && param[event_type][auction_config.id.to_s][notification_type]
                  param[event_type][auction_config.id.to_s][notification_type].each do |user|
                    user_id = user['id']
                    if user_id.present?
                      notifications << OwnerEventNotification.new(
                          event_type: EventType.find(event_type),
                          owner: auction_config,
                          notification_type: notification_type,
                          contact_type: User.name,
                          contact_id: user_id
                      )
                    end
                  end
                end
              end
            end
          end
        end
        notifications
      end
      
      def event_notification_dispatch_group_recipients
        users = (User.where(deleted: false) - User.customers).collect {|u| {id: u.id, name: u.name}}
        event_types = EventType.notifiable_for_dispatch_group

        selection = OwnerEventNotification.where(owner_type: DispatchGroup.polymorphic_name, owner_id: user_owned_dg_ids).collect do |e|
          {
              eventType: e.event_type.id,
              owner_id: e.owner_id,
              channel: e.notification_type,
              uid: e.contact_id
          }
        end

        ret = {
            users: users,
            eventTypes: event_types,
            dispatchGroups: DispatchGroup.not_deleted.
              where(id: user_owned_dg_ids).
              order(name: :asc).
              collect { |dg| {id: dg.id, name: dg.name} },
            selection: selection
        }

        sendReply(ret)
      end

      def update_event_notification_dispatch_group_recipients
        # create entities from params
        recipients_param = params['event_notification_dispatch_group_recipients']
        entities = build_dispatch_group_notifications(recipients_param)

        # save
        OwnerEventNotification.update_notification_recipiens(entities, DispatchGroup.polymorphic_name)

        # return the updated event_notification_dispatch_group_recipients
        event_notification_dispatch_group_recipients
      end

      def build_dispatch_group_notifications(param)
        notifications = []
        if param
          dgs = DispatchGroup.not_deleted.
            where(id: user_owned_dg_ids).
            order(name: :asc)
          EventType.notifiable_for_dispatch_group.each do |event_type|
            event_type = event_type.to_s
            dgs.each do |dg|
              EventNotification::TYPES.each do |notification_type|
                if param[event_type] && param[event_type][dg.id.to_s] && param[event_type][dg.id.to_s][notification_type]
                  param[event_type][dg.id.to_s][notification_type].each do |user|
                    user_id = user['id']
                    if user_id.present?
                      notifications << OwnerEventNotification.new(
                          event_type: EventType.find(event_type),
                          owner: dg,
                          notification_type: notification_type,
                          contact_type: User.name,
                          contact_id: user_id
                      )
                    end
                  end
                end
              end
            end
          end
        end
        notifications
      end

      def audit_actions
        {
            update_event_notification_recipients: 'Update Event Notification Recipients',
            update_event_notification_auction_config_recipients: 'Update Event Notification Tender Recipients',
            update_event_notification_dispatch_group_recipients: 'Update Event Notification Dispatch Group Recipients',
        }
      end

      def enforce_read_authority
        if !current_user.can?(:reporting_and_notification_read)
          raise Authority::SecurityViolation.new(current_user, action_name, controller_name)
        end
      end

      def enforce_create_authority
        if !current_user.can?(:reporting_and_notification_create)
          raise Authority::SecurityViolation.new(current_user, action_name, controller_name)
        end
      end

    end

  end
end
