module Api
  module V1
    class ApiController < ApplicationController

      prepend_before_action :login_url_for_spa_requests, except: [:health]

      around_action :set_time_zone, if: :current_user

      protected

      def sendReply(data)
        render json: data.to_json, status: 200
      end

      def sendError(message)
        render json: {error: message}, status: :not_found
      end

      private

      def login_url_for_spa_requests
        if self.class.to_s != 'VppAuthentication::SessionsController' &&
            current_user.blank? &&
            request.headers['X-CSRF-Token'].present?

          render :plain => vpp_authentication.login_url, :status => :unauthorized
          false
        end
      end

    end
  end
end
