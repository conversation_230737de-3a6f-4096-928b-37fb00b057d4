module Api
  module V1
    class BidController < Api::V1::ApiController

      before_action :enforce_read_authority, only: [:prepare_auctions_download, :download_all_bids, :eon_nomination_bids]
      before_action :enforce_upload_authority, only: [:upload, :upload_bids, :delete_bids]

      def upload
        result = {
          success: false,
          result: nil,
          error: nil
        }

        if params[:file].present?
          file_name = params[:file].original_filename
          file_contents = params[:file].read
          skip_validations = Array(params['skip_validations'])
          contents_type = 'auctions'
          begin
            created_source_id =
              trading_service.dus_file_upload(
                file_name: file_name,
                file_contents: file_contents,
                user_id: current_user.id,
                auction_config_id: nil,
                skip_validations: skip_validations,
                contents_type: contents_type)

            result[:success] = true
            result[:result] = created_source_id
          rescue trading_service::UnableToFileUpload => e
            result[:error] = 'ISSUE_UPLOADING_FILE'
          end
        else
          result[:error] = 'NO_FILE_UPLOADED'
        end

        sendReply(result)
      end

      def upload_bids
        result = {
          success: false,
          result: nil,
          error: nil,
          messages: []
        }
        error_source = 0

        auction_config_id = params[:auction_config_id]
        if !user_owned_auction_config_ids.include?(auction_config_id.try(:to_i))
          result[:error] = 'AUTHORIZATION_ERROR'
        # parse the upload and create the bid
        elsif params[:file].present?
          file = params[:file]
          file_name = file.original_filename
          file_contents = file.read
          auction_config = AuctionConfig.find(auction_config_id)
          auction_config.from_value_hash
          if auction_config.bidding_method_nl_afrr?
            xl = Services::N2exEpexDsAfrrAuctionResultImporter.new.parse_buffer(file_contents, false)
            err = xl[:error]
            if err.present?
              result[:error] = 'ISSUE_UPLOADING_FILE'
              result[:messages] = [err]
            else
              Rails.logger.info "Imported aFRR Bids from #{file_name} as #{xl.to_json}"
              begin
                payload = {
                  user_id: current_user.id,
                  lines: xl[:lines]
                }
                created_source_id =
                  trading_service.post_afrr_bids(auction_config_id, payload)
                result[:success] = true
                nbs = NominationBidSource.where(id: created_source_id).first
                result[:result] = nbs
                result[:result_type] = 'NominationBidSource'
                if nbs.is_success?
                  result[:messages] = ['BIDDING.UPLOAD_BIDS.SUCCESS_UPLOADED']
                  if xl[:lines].first
                    result[:delivery_date] = xl[:lines].first[:time_from]
                  else
                    Date.today.strftime("%Y-%m-%dT%H:%M:%SZ")
                  end
                  result[:auction_config_id] = auction_config_id
                else
                  result[:messages] = get_error_messages(nomination_bid_source: nbs, is_file_upload: true)
                end
              rescue trading_service::UnableToFileUpload => e
                result[:error] = 'ISSUE_UPLOADING_FILE'
                result[:messages] = [e.to_s]
              end
            end
          else
            # Regeleistung
            xl = Services::NominationBidImporter.new.parse_buffer(file_contents)
            err = xl[:error]
            if err.present?
              result[:error] = 'ISSUE_UPLOADING_FILE'
              result[:messages] = [err]
            else
              Rails.logger.info "Imported bids from #{file_name} as #{xl.to_json}"
              begin
                pms_req = prepare_bid_upload_request(xl, @current_user.id, auction_config_id)
                nbs_ids = trading_service.post_bids(bid_request: pms_req)

                nbs = nbs_ids.collect {|nbs_id| NominationBidSource.find(nbs_id)}
                result[:success] = true
                result[:result] = nbs.first

                result[:delivery_date] = xl[:start_date]
                result[:market_name] = xl[:market_name]
                result[:auction_config_id] = auction_config_id

                error_sources = nbs.select {|ns| !ns.is_success?}
                if error_sources.empty?
                  result[:messages] = ['BIDDING.UPLOAD_BIDS.SUCCESS_UPLOADED']
                  if nbs_ids.size == 2
                    result[:messages] = ['BIDDING.UPLOAD_BIDS.SUCCESS_UPLOADED', 'BIDDING.UPLOAD_BIDS.SUCCESS_UPLOADED_AUTOMATIC']
                  end
                end
              rescue trading_service::UnableToFileUpload => e
                result[:error] = 'ISSUE_UPLOADING_FILE'
                result[:messages] = [e.to_s]
              end
            end
          end
        else
          result[:error] = 'NO_FILE_UPLOADED'
        end
        sendReply(result)

      rescue Exception => e
        Rails.logger.error "Failed to upload_bids #{e} #{e.backtrace}"
        result[:error] = 'ISSUE_UPLOADING_FILE'
        sendReply(result)
      end

      def prepare_auctions_download
        date = Date.parse(params[:date])

        product_intervals = (params[:product_intervals].present? ? params[:product_intervals].split(',') : self.product_intervals)
          .collect do |i|
            s, e = i.split('-')
            start_time = date.to_time.change(hour: s)
            end_time = date.to_time.change(hour: e)
            start_time..end_time
          end

        market = Market.find(params[:market_id])

        nominateable_volumes =
          Services::PortfolioManagementService
            .nominateable_volumes(intervals: product_intervals, market_id: market.id,
                                  delivery_date: date,
                                  user_id: current_user.id)

        exporter = Services::NominateableVolumesExporter.new(
          nominateable_volumes: nominateable_volumes,
          start_date: date,
          end_date: date,
          market: market,
          tsos: Tso.all,
          supplier_bgs: supplier_bgs(nominateable_volumes),
          use_shorthand_payment_direction: true)

        send_data(
          exporter.export_to_string,
          type: 'application/excel',
          disposition: 'attachment',
          filename: exporter.filename)
      end

      def delete_bids
        auction_config_id = params[:auction_config_id]
        selected_auction = AuctionConfig.where(id: auction_config_id).last
        start_date = params[:start_delivery_date].present? ?  Date.parse(params[:start_delivery_date]) : Date.tomorrow
        end_date = params[:end_delivery_date].present? ?  Date.parse(params[:end_delivery_date]) : Date.tomorrow
        asset_ids = params[:asset_ids]
        if params[:only_bids_without_asset] == "true"
          asset_ids = nil
        elsif !asset_ids.present?
          asset_ids = []
        end
        auction_status = auction_closed(params: params)
        result = {
          success: false,
          is_auction_closed: auction_status[:is_auction_closed],
          auction_closed_error: auction_status[:error],
          nomination_bids: [],
          message: "",
          auction_config: selected_auction,
          latest_nomination_bid: nil,
          last_upload_timestamp: nil,
          last_upload_user: nil
        }
        begin
          if !user_owned_auction_config_ids.include?(auction_config_id.try(:to_i))
            raise trading_service::UnableToDeleteBids, 'AUTHORIZATION_ERROR'
          end
          nbs_id = trading_service.delete_bids(
            user_id: current_user.id,
            auction_config_id: selected_auction.id,
            asset_ids: asset_ids,
            start_date: start_date,
            end_date: end_date)
          nbs = NominationBidSource.find(nbs_id)
          if nbs.is_success?
            result[:success] = true
            result[:message] = "BIDDING.LIST.DELETED_SUCCESS"
          else
            result[:success] = false
            result[:message] = get_error_messages(nomination_bid_source: nbs, is_file_upload: false)
          end
        rescue trading_service::UnableToDeleteBids => e
          result[:success] = false
          result[:message] = "BIDDING.LIST.DELETED_ERROR"
        end

        sendReply(result)
      end

      def time_slices
        sendReply(product_intervals)
      end

      def products
        sendReply([Market.srl, Market.mrl].compact)
      end

      def download_all_bids
        auction_config_id = params[:auction_config_id].try(:to_i)
        auction_config = AuctionConfig.where(id: auction_config_id).first
        asset_ids = params[:asset_ids]
        only_bids_without_asset = params[:only_bids_without_asset]

        start_date = params[:start_delivery_date].present? ?  Date.parse(params[:start_delivery_date]) : Date.tomorrow
        end_date = params[:end_delivery_date].present? ?  Date.parse(params[:end_delivery_date]) : Date.tomorrow
        delivery_intervals = trading_service.delivery_intervals([auction_config_id], start_date, end_date)
        start_time = Time.parse(delivery_intervals[auction_config_id.to_s]["start"])
        end_time = Time.parse(delivery_intervals[auction_config_id.to_s]["end"])

        if !user_owned_auction_config_ids.include?(auction_config_id)
          nomination_bids = []
        else
          nomination_bids = NominationBid.bids_by_auction_config_and_date(
            auction_config_id: auction_config_id,
            asset_ids: asset_ids,
            start_date: start_time, end_date: end_time,
            only_bids_without_asset: only_bids_without_asset)
        end

        exporter = Services::NominationBidExporter.new(
          nomination_bids: nomination_bids,
          auction_config: auction_config,
          start_date: start_date,
          end_date: end_date)

        send_data(
          exporter.content,
          type: exporter.content_type,
          disposition: 'attachment',
          filename: exporter.filename)
      end

      def eon_nomination_bids
        is_auction_closed = nil
        auction_closed_error = nil
        auction_config_id = params[:auction_config_id].try(:to_i)
        auction_config = AuctionConfig.select(:id, :name).where(id: auction_config_id).first
        auction_config_id = auction_config.try(:id)
        asset_ids = params[:asset_ids]
        only_bids_without_asset = params[:only_bids_without_asset]

        start_date = params[:start_delivery_date].present? ?  Date.parse(params[:start_delivery_date]) : Date.tomorrow
        end_date = params[:end_delivery_date].present? ?  Date.parse(params[:end_delivery_date]) : Date.tomorrow
        if auction_config.present?
          delivery_intervals = trading_service.delivery_intervals([auction_config_id], start_date, end_date)
          if delivery_intervals.empty?
            start_time = start_date.beginning_of_day
            end_time = end_date.end_of_day
            delivery_interval = { "start": start_time.getutc.strftime("%Y-%m-%dT%H:%M:%SZ"), "end": end_time.getutc.strftime("%Y-%m-%dT%H:%M:%SZ") }
          else
            start_time = Time.parse(delivery_intervals[auction_config_id.to_s]["start"])
            end_time = Time.parse(delivery_intervals[auction_config_id.to_s]["end"])
            delivery_interval = delivery_intervals[auction_config_id.to_s]
          end
        else
          start_time = start_date.beginning_of_day
          end_time = end_date.end_of_day
          delivery_interval = { "start": start_time.getutc.strftime("%Y-%m-%dT%H:%M:%SZ"), "end": end_time.getutc.strftime("%Y-%m-%dT%H:%M:%SZ") }
        end

        auction_status = auction_closed(params: params)
        nomination_bids = NominationBid.eon_bids_by_auction_config_and_date(auction_config_id: auction_config_id, asset_ids: asset_ids,
            start_date: start_time, end_date: end_time, only_bids_without_asset: only_bids_without_asset)

        latest_nomination_bid = nomination_bids.max_by(&:created)
        last_upload_timestamp = latest_nomination_bid.try(:created)
        last_upload_user = latest_nomination_bid.try(:nomination_bid_source).try(:user)
        bids = []

        nomination_bids.each do |nb|
          if !nb.auction_config.present? || user_owned_auction_config_ids.include?(nb.auction_config.id)
            b = JSON.parse(nb.to_json)
            b[:product] = nb.product_interval
            b[:product_with_minutes] = nb.product_interval(true)
            b[:start_time_in_tz] = nb.start_time_in_tz.strftime("%a, %d %b %H:%M ")
            b[:tso_name] = nb.tso.name
            b[:asset_name] = nb.try(:asset).try(:name)
            b[:asset_external_id] = nb.try(:asset).try(:short_name).try(:value)
            b[:auction_config_name] = nb.auction_config.present? ? nb.auction_config.name : ''
            bids << b
          end
        end

        sendReply({
          is_auction_closed: auction_status[:is_auction_closed],
          auction_closed_error: auction_status[:error],
          nomination_bids: bids,
          auction_config: auction_config,
          latest_nomination_bid: latest_nomination_bid,
          last_upload_timestamp: last_upload_timestamp,
          last_upload_user: last_upload_user,
          delivery_interval: delivery_interval
        })
      end

      def upload_bids_sample
        if params[:market] == 'afrr'
          send_file Rails.root.join('public', 'samples', 'upload_bids_sample_afrr.xlsx')
        else
          # Regeleistung
          send_file Rails.root.join('public', 'samples', 'upload_bids_sample.xlsx')
        end
      end

      def product_intervals
        Market
        .nomination_tool_product_intervals
        .collect { |i| "#{"%02d" % i.first}-#{"%02d" % i.last}" }
      end

      def get_error_messages(nomination_bid_source:, is_file_upload:)
        errmsg = I18n.t('bid.err_validation') + ': <br/>'
        nomination_bid_source.validation_errors.collect{|e| [e['name'], e['details']] }.uniq.each do |error_code|
          if error_code.last
            errmsg << I18n.t("bid.err_#{error_code.first}", JSON.parse(error_code.last).symbolize_keys)
          else
            errmsg << I18n.t("bid.err_#{error_code.first}")
          end
          errmsg << '<br/>'
        end
        if is_file_upload
          errmsg << ' '
          errmsg << I18n.t('bid.please_check_and_try_again')
        end
        errmsg.html_safe
      end

      def prepare_bid_upload_request (xl, user_id, auction_config_id)
        date_str = xl[:start_date].strftime('%0Y-%0m-%0d')
        market_name = xl[:market_name]

        {
          userId: user_id,
          customerId: nil,
          auctionRuleId: auction_config_id.try(:to_i),
          bids: xl[:bid_items].map do |bi|
            bid_data = {
              date: date_str,
              tsoIdentifier: bi[:tso_id],
              marketName: market_name,
              productName: bi[:product_interval],
              flexVolumeMegawatt: bi[:volume_mw],
              capacityPrice: bi[:capacity_price],
              energyPrice: bi[:energy_price],
              paymentDirection: get_pms_payment_direction(bi[:payment_direction])
            }
            bid_data[:assetId] = bi[:asset_id] unless bi[:asset_id].nil?
            bid_data
          end
        }
      end

      def get_pms_payment_direction(xl_payment_direction)
        case xl_payment_direction
        when 'AN'
          'ANBIETER_AN_NETZ'
        when 'NA'
          'NETZ_AN_ANBIETER'
        else
          xl_payment_direction
        end
      end

      def supplier_bgs(nominateable_volumes)
        nominateable_volumes
          .collect { |nv| [nv['tso_id'], nv['market_id']] }
          .uniq
          .inject({}) do |acc, ids|
            tso_id, market_id = ids
            dg = DispatchGroup.not_deleted.where(tso_id: tso_id, market_id: market_id, nomination_tool_enabled: true).first
            if dg.present?
              bgs = dg.balancing_groups.overlap_date_interval(Date.today..Date.today)
              acc[[tso_id, market_id]] = bgs.first.try(:name)
            end
            acc
          end
      end

      def auction_closed(params: {})

        if trading_service.respond_to?(:always_open_bidding)
          return {
            is_auction_closed: false,
            error: nil
          }
        end

        is_auction_closed = nil
        auction_closed_error = nil
        market_id = params[:market_id].present? ? params[:market_id].to_i : Market.srl.id
        market = Market.where(id: market_id).first
        date = params[:delivery_date].present? ?  Date.strptime(params[:delivery_date], '%Y-%m-%d') : Date.tomorrow

        begin
          is_auction_closed = begin
            if date == Date.tomorrow
              Time.now >= trading_service.get_eon_bid_closing_time()[market.name.to_sym]
            else
              date < Date.tomorrow
            end
          end
          {
            is_auction_closed: is_auction_closed,
            error: nil
          }
        rescue trading_service::UnableToGetBiddingWindows => e
          auction_closed_error = e.to_s
          {
            is_auction_closed: nil,
            error: auction_closed_error
          }
        end
      end


      def audit_actions
        {
            upload: "Upload Auction Results",
            upload_bids: "Upload Bids",
            delete_bids: "Delete Bids",
        }
      end

      def enforce_read_authority
        if !current_user.can_read?(NominationBid)
          raise Authority::SecurityViolation.new(current_user, action_name, controller_name)
        end
      end

      def enforce_upload_authority
        if !current_user.can_create?(NominationBid)
          raise Authority::SecurityViolation.new(current_user, action_name, controller_name)
        end
      end

    end
  end
end
