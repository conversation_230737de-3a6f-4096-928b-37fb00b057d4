module Api
  module V1
    class AuctionController < Api::V1::ApiController

      before_action :enforce_read_authority, only: [:results, :meta_data, :download]

      def results
        bids_list = []

        auction_configs = {}
        tsos = []
        products = []
        energy_directions = []
        asset_ids = params[:asset_ids]
        start_date = params[:start_delivery_date].present? ?  Date.parse(params[:start_delivery_date]) : Date.tomorrow
        end_date = params[:end_delivery_date].present? ?  Date.parse(params[:end_delivery_date]) : Date.tomorrow
        start_time = start_date.beginning_of_day.to_datetime
        if start_date == end_date
          end_time = end_date.end_of_day.to_datetime
        else
          end_time = end_date.beginning_of_day.to_datetime
        end
        bids = NominationBid.bids_between_date_times_including_meta(
            start_time: start_time,
            end_time: end_time,
            asset_ids: asset_ids)

        bids.each do |bid|
          if !bid.auction_config.present? || user_owned_auction_config_ids.include?(bid.auction_config.id)
            temp = JSON.parse(bid.to_json)
            energy_direction = bid.product ? bid.product.energy_direction : nil
            temp['product_interval'] =  bid.product_interval
            temp['product_interval_with_minutes'] =  bid.product_interval(true)
            temp['flex_volume'] = temp['flex_volume'] != nil ? (temp['flex_volume'] / 1000).round(2) : nil
            temp['accepted_flex_volume'] = temp['accepted_flex_volume'] != nil ? (temp['accepted_flex_volume'] / 1000).round(2) : nil
            temp['energy_direction'] = energy_direction
            if bid.asset_id
              temp[:asset_id_and_name] = "##{bid.asset_id} #{bid.try(:asset).try(:name)}"
              temp[:asset_external_id] = "#{bid.try(:asset).try(:short_name).try(:value)}"
            end
            bids_list << temp

            if energy_direction.present? && !energy_directions.index(energy_direction).present?
              energy_directions << energy_direction
            end

            if !tsos.index(bid['tso_name']).present?
              tsos << bid['tso_name']
            end

            if !auction_configs[bid['auction_config_id']].present?
              auction_configs[bid['auction_config_id']] = bid['auction_config_name']
            end

            if !products.index(temp['product_interval']).present?
              products << temp['product_interval']
            end
          end
        end

        sendReply({
                      bids_list: bids_list,
                      tsos: tsos,
                      auction_configs: auction_configs.to_a.collect{|x| {id: x.first, name: x.last} },
                      products: products,
                      energy_directions: energy_directions
                  })
      end

      def meta_data
        nominateable_volume = NominateableVolume.select(:id, :created, :user_id, :delivery_date, :meta_data).
        where(id: params[:id]).first

        exporter = Services::NominateableVolumesMetaDataExporter.new(nominateable_volume)
        send_data(
              exporter.export_meta_data_to_string,
              type: 'application/excel',
              disposition: 'attachment',
              filename: exporter.meta_data_filename)
      end

      def download_results
        asset_ids = params[:asset_ids]
        start_date = params[:start_delivery_date].present? ?  Date.parse(params[:start_delivery_date]) : Date.tomorrow
        end_date = params[:end_delivery_date].present? ?  Date.parse(params[:end_delivery_date]) : Date.tomorrow
        start_time = start_date.beginning_of_day.to_datetime
        if start_date == end_date
          end_time = end_date.end_of_day.to_datetime
        else
          end_time = end_date.beginning_of_day.to_datetime
        end
        bids = NominationBid.bids_between_date_times_including_meta(
          start_time: start_time,
          end_time: end_time,
          asset_ids: asset_ids
        )

        # apply filter
        if params[:auction_config_id].present?
          bids = bids.where('nomination_bid.auction_rules_id = ?', params[:auction_config_id])
        end
        if params[:tso_id].present? && params[:tso_id] != 'Select TSO'
          bids = bids.where('tso.name = ?', params[:tso_id])
        end
        if params[:direction].present? && params[:direction] != 'all'
          bids = bids.select{|b| params[:direction] == b.try(:product).try(:energy_direction) }
        end
        if params[:accepted_only].present? && params[:accepted_only] == 'true'
          bids = bids.select{|b| b.try(:bid_status) == 'accepted' }
        end

        bids = bids.select{|b| !b.auction_config.present? || user_owned_auction_config_ids.include?(b.auction_config.id)}

        exporter = Services::AuctionResultsExporter.new(bids, start_date, end_date, I18n.locale)

        send_data(
          exporter.export_to_string,
          type: 'application/excel',
          disposition: 'attachment',
          filename: exporter.filename
        )
      end

      def download
        nominateable_volume = NominateableVolume.select(:id, :created, :user_id, :delivery_date, :nominateable_volumes).
            where(id: params[:id]).first

        nv = HashCaseConverter.to_underscore(nominateable_volume.nominateable_volumes)
        market_id = nv.first['market_id']

        exporter = Services::NominateableVolumesExporter.new(
          nominateable_volumes: nv,
          start_date: nominateable_volume.delivery_date.to_date,
          end_date: nominateable_volume.delivery_date.to_date,
          market: Market.find(market_id),
          tsos: Tso.all,
          supplier_bgs: supplier_bgs(nv),
          use_shorthand_payment_direction: true)

        send_data(
          exporter.export_to_string,
          type: 'application/excel',
          disposition: 'attachment',
          filename: exporter.filename)
      end

      private

      def supplier_bgs(nominateable_volumes)
        nominateable_volumes
          .collect { |nv| [nv['tso_id'], nv['market_id']] }
          .uniq
          .inject({}) do |acc, ids|
            tso_id, market_id = ids
            dg = DispatchGroup.not_deleted.where(tso_id: tso_id, market_id: market_id, nomination_tool_enabled: true).first
            if dg.present?
              bgs = dg.balancing_groups.overlap_date_interval(Date.today..Date.today)
              acc[[tso_id, market_id]] = bgs.first.try(:name)
            end
            acc
          end
      end

      def enforce_read_authority
        if !current_user.can?(:auction_results_read)
          raise Authority::SecurityViolation.new(current_user, action_name, controller_name)
        end
      end

    end
  end
end