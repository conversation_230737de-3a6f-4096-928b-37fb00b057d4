module Api
  module V1
    class AuctionConfigController < Api::V1::ApiController

      authorize_actions_for NominationBid
      authority_actions list: :read,
        list_with_names: :read, list_with_names_for_asset_optimization: :read,
        available_signals: :read

      def list
        collection = AuctionConfig.where(id: user_owned_auction_config_ids).
          not_deleted.order(name: :asc).
          page(params[:page]).
          per((params[:per_page] || 25).to_i)

        configs = collection.map do |a_cfg|
          a_cfg.from_value_hash
          if a_cfg.dispatch_group_ids
            a_cfg.dispatch_group_ids_and_names = DispatchGroup.where(id: a_cfg.dispatch_group_ids).collect{|dg| {id: dg.id, name: dg.name}}
          end
          a_cfg.as_json(methods: [:dispatch_group_ids, :dispatch_group_ids_and_names].concat(AuctionConfig::VALUE_ATTRIBUTES))
        end

        sendReply({
          auctions: configs.as_json,
          total_entries: collection.total_count,
          page: params[:page] || 1
        })
      end

      def list_with_names
        collection = AuctionConfig.where(id: user_owned_auction_config_ids).not_deleted.order(name: :asc)
        if params[:active]
          collection = collection.select{ |a_cfg| a_cfg.active? }
        end
        if params[:with_bid_input]
          collection = collection.select{ |a_cfg| a_cfg.has_bid_input?(params[:with_bid_input]) }
        end
        if params[:has_internal_bidding_input_format]
          collection = collection.select{ |a_cfg| a_cfg.has_internal_bidding_input_format?(nil) }
        end
        configs = collection.collect { |a_cfg| { id: a_cfg.id, name: a_cfg.name, id_and_name: "##{a_cfg.id} #{a_cfg.name}" } }
        sendReply({
          auctions: configs.as_json,
        })
      end

      def list_with_names_for_asset_optimization
        configs = trading_service.get_open_tenders().
          select {|a_cfg| user_owned_auction_config_ids.include?(a_cfg[:id].to_i)}.
          collect { |a_cfg|
            bidding_method_market = a_cfg.dig(:biddingMethod, :market)
            markets = nil
            if bidding_method_market.present?
              if bidding_method_market.kind_of?(String)
                markets = [bidding_method_market]
              elsif bidding_method_market.kind_of?(Array)
                markets = bidding_method_market
              end
            end
            time_zone = a_cfg[:timeZone]
            {
              id: a_cfg[:id],
              name: a_cfg[:name],
              id_and_name: "##{a_cfg[:id]} #{a_cfg[:name]}",
              biddingMethodMarket: markets,
              tz: time_zone,
              lastPriceForecast: last_price_forecast(markets, time_zone)
            }
          }
        sendReply({
          auctions: configs.as_json(:methods => [:id_and_name]),
        })
      end

      def create
        result = {
          success: false,
          messages: []
        }

        a_config = AuctionConfig.create(a_config_params)

        if a_config.errors.any?
          result[:messages] = a_config.errors.full_messages
        else
          if !current_user.has_privilege?(:super_admin) && current_user.organization.present?
            org = current_user.organization
            org.auction_configs << a_config
            org.save
          end
          result[:success] = true
          result[:id] = a_config.id
        end

        sendReply(result)
      end

      def update
        result = {
          success: false,
          messages: []
        }

        p = a_config_params
        if user_owned_auction_config_ids.include?(p[:id].try(:to_i))
          a_config = AuctionConfig.update(p[:id], p)

          if a_config.errors.any?
            result[:messages] = a_config.errors.full_messages
          else
            result[:success] = true
            result[:id] = a_config.id
          end
        else
          result[:messages] = 'AUTHORIZATION_ERROR'
        end

        sendReply(result)
      end

      def destroy
        result = {
          success: true
        }
        # TODO: destroy if is not used
        # AuctionConfig.destroy(params[:id])

        if user_owned_auction_config_ids.include?(params[:id].try(:to_i))
          config = AuctionConfig.find(params[:id])
          config.mark_as_deleted if config
        else
          result[:success] = true
        end

        sendReply(result)
      end

      def a_config_params
        params.require(:auction_config).permit!
      end


      def audit_actions
        {
          create: 'Create Auction Config (Tender)',
          update: 'Update Auction Config (Tender)',
          destroy: 'Delete Auction Config (Tender)',
        }
      end

      def last_price_forecast(markets, time_zone)
        tz = ActiveSupport::TimeZone[time_zone]
        today_23h = tz.now.change(hour: 23, min: 0, sec: 0)
        tomorrow_23h = (tz.now + 1.day).change(hour: 23, min: 0, sec: 0)
        Services::MarketdataApiService.last_price_forecast(markets, today_23h, tomorrow_23h)
      end
    end
  end
end
