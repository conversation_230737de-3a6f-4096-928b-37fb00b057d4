module Api
  module V1
    class ReportsController < Api::V1::ApiController
      inherit_resources

      before_action :enforce_read_authority, only: [:tso_report, :tso_download]
      before_action :enforce_create_authority, only: [:import_tso_report]

      def tso_report
        result = {
          success: false,
          result: nil,
          error: nil,
          messages: []
        }

        search_params = { date: Date.parse(params[:date]).to_s } if params[:date].present?
        collection = ImportedReport
        .find(:all, params: search_params)
        .group_by { |r| [r.report_date, r.tso, r.market] }

        imported_reports_array = []

        collection.each do |(report_date, report_tso, report_market), reports|
          imported_reports_array << {
            report_date: report_date,
            report_tso: report_tso.try(:name),
            report_tso_id: report_tso.try(:id),
            report_market: report_market.try(:name),
            report_market_id: report_market.try(:id),
            reports: reports
          }
        end

        sendReply(
          {
            data: {
              imported_reports: imported_reports_array
            }
          }
        )
      end

      def import_tso_report
        imported_report = ImportedReport.new(build_resource_params)

        imported_report.market_id = Market.srl.id
        imported_report.report_type = ImportedReport::REPORT_TYPE_TSO_SCHEDULE

        imported_report.save
        if imported_report.errors.empty?
          imported_report.reload
        end

        sendReply({
          success: imported_report.errors.empty? ? true : false,
          imported_report: imported_report.id ? imported_report.id : nil,
          error: imported_report.errors.any? ? imported_report.errors : nil
        })

      rescue Exception => e
        Rails.logger.error "Failed to import tso report #{e} #{e.backtrace}"
        sendReply({
                      success: false,
                      messages: [e.to_s]
                  })
      end

      def tso_download
        report = ImportedReport.find(params[:id])

        send_data(report.contents,
                  type: 'application/excel',
                  disposition: 'attachment',
                  filename: report.filename.presence)
      end

      def tso_control
        date = Date.parse params[:date]
        tso = Tso.find(params[:tso])
        market = Market.find(params[:market]) if params[:market].present?

        report = SchedulingControlReport.get(tso, market, date)
        exporter_klass = Services::SchedulingBalancingGroupsReportExporter

        if report.present?
          if report.report.try(:attributes).present?
            exporter = exporter_klass.new(report.report)
            send_exported_file(filename: nil, extension: "xlsx", report: report, exporter: exporter)
          else
            send_exported_error_file(file_name: "Error", extension: "txt", report: report)
          end
        end
      end

      def audit_actions
        {
          import_tso_report: 'Import TSO Report',
        }
      end

      protected

      def send_exported_file(filename: nil, extension: "xlsx", report: nil, exporter: nil)
        t = Tempfile.new()
        file_name = exporter.export(t)
        file_extension = exporter.respond_to?(:file_extension) ? exporter.file_extension : extension

        send_file(t,
                  type: 'application/excel',
                  disposition: 'attachment',
                  filename: (filename || report.report.file_name) + "." + file_extension)
      end

      def send_exported_error_file(file_name: "Error", extension: "txt", report: nil)
        send_data(report.errors.inspect,
                  type: 'text/plain',
                  disposition: 'attachment',
                  filename: file_name + "." + extension)
      end

      def build_resource_params
        permitted =
          params
          .permit(:tso_id, :market_id, :report_type, :file)
          .to_h


        if params[:file].present?
          permitted[:filename] = params[:file].original_filename
          permitted[:contents] = Base64.encode64(params[:file].read)
          permitted.delete(:file)
        end

        permitted[:user_account_id] = current_user.id

        permitted
      end

      def enforce_read_authority
        if !current_user.can?(:tso_reports_read)
          raise Authority::SecurityViolation.new(current_user, action_name, controller_name)
        end
      end

      def enforce_create_authority
        if !current_user.can?(:tso_reports_create)
          raise Authority::SecurityViolation.new(current_user, action_name, controller_name)
        end
      end

    end
  end
end