module Api
  module V1
    class EmailConfigurationController < Api::V1::ApiController
      inherit_resources

      before_action :enforce_read_authority, only: [:scheduling_report, :rollup, :allocation]
      before_action :enforce_create_authority, only: [:save_rollup, :save_allocation]
      before_action :enforce_scheduling_create_authority, only: [:save_scheduling_report]

      def scheduling_report
        config = ReportEmailSendingsConfiguration.find || ReportEmailSendingsConfiguration.new
        sendReply({scheduling_report_settings: config})
      end

      def save_scheduling_report
        config = ReportEmailSendingsConfiguration.find
        begin
          config.update(build_report_email_sendings_params)

          sendReply({
            success: true,
            scheduling_report_settings: config
          })
        rescue StandardError => e
          sendReply({
            success: false,
            scheduling_report_settings: config,
            messages: [],
            error: 'ISSUE_SAVING_SETTINGS'
          })
        end
      end

      def rollup
        config = RollupEmailSendingsConfiguration.all.first || RollupEmailSendingsConfiguration.new
        sendReply({rollup: config})
      end

      def save_rollup
        config = RollupEmailSendingsConfiguration.all.first || RollupEmailSendingsConfiguration.new
        begin
          config.update(build_rollup_email_sendings_params)
          sendReply({
            success: true,
            rollup: config
          })
        rescue StandardError => e
          sendReply({
            success: false,
            rollup: config,
            messages: [],
            error: 'ISSUE_SAVING_SETTINGS'
          })
        end
      end

      def allocation
        config =  AutomaticAllocationEmailSendingsConfiguration.all.first || AutomaticAllocationEmailSendingsConfiguration.new
        sendReply({allocation: config})
      end

      def save_allocation
        config =  AutomaticAllocationEmailSendingsConfiguration.all.first || AutomaticAllocationEmailSendingsConfiguration.new
        begin
          config.update(build_automatic_allocation_email_sending_params)
          sendReply({
            success: true,
            allocation: config
          })
        rescue StandardError => e
          sendReply({
            success: false,
            allocation: config,
            messages: [],
            error: 'ISSUE_SAVING_SETTINGS'
          })
        end
      end

      def build_report_email_sendings_params
        params.require(:report_email_sendings_configuration).permit!.to_h
      end

      def build_rollup_email_sendings_params
        params.require(:rollup_email_sendings_configuration).permit!.to_h
      end

      def build_automatic_allocation_email_sending_params
        params.require(:automatic_allocation_email_sendings_configuration).permit!.to_h
      end

      def audit_actions
        {
          save_scheduling_report: 'Save Scheduling Report Email Sending Config',
          save_rollup: 'Save Rollups Email Sending Config',
          save_allocation: 'Save Automatic Allocation Email Sending Config',
        }
      end

      def enforce_read_authority
        if !current_user.can?(:reporting_and_notification_read)
          raise Authority::SecurityViolation.new(current_user, action_name, controller_name)
        end
      end

      def enforce_create_authority
        if !current_user.can?(:reporting_and_notification_create)
          raise Authority::SecurityViolation.new(current_user, action_name, controller_name)
        end
      end

      def enforce_scheduling_create_authority
        if !current_user.has_privilege?(:scheduling_reports_create)
          raise Authority::SecurityViolation.new(current_user, action_name, controller_name)
        end
      end

    end
  end
end