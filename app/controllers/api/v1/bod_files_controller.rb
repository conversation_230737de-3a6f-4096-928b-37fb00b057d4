module Api
  module V1
    class BodFilesController < Api::V1::ApiController

      authorize_actions_for DistributedUnit
      authority_actions upload: :create

      def upload
        result = {
          success: false,
          result: nil,
          error: nil,
          messages: []
        }

        if params[:file].present?
          file_name = params[:file].original_filename
          file_contents = params[:file].read
          begin
            xl = Services::BodFileImporter.new.parse_buffer(file_contents)
            err = xl[:error]
            if err.present?
              result[:error] = 'ISSUE_UPLOADING_FILE'
              result[:messages] = [err]
            else
              resp = Services::MarketdataApiService.post_bod(xl)
              if resp[:status] == "success"
                result[:success] = true
                result[:result] = { validation_result: { validationSuccess: true } }
                result[:messages] = resp[:messages]
              else
                result[:error] = 'ISSUE_UPLOADING_FILE'
                result[:messages] = [I18n.t('activerecord.errors.messages.marketdata_api_error', error: resp[:messages])]
              end
            end
          rescue Exception => e
            puts "#### ERROR: #{e.backtrace}"
            result[:error] = 'ISSUE_UPLOADING_FILE'
            result[:messages] = [e.message]
          end
        else
          result[:error] = 'NO_FILE_UPLOADED'
        end

        sendReply(result)
      end
    end
  end
end