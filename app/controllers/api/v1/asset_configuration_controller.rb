module Api
  module V1

    class AssetConfigurationController < Api::V1::ApiController
      include ApplicationHelper
      include ActionView::Helpers::NumberHelper

      authorize_actions_for AssetConfiguration
      authority_actions box_type_configurations: :read,
                        create_box_type_configuration: :create,
                        update_box_type_configuration: :create,
                        delete_box_type_configuration: :create,
                        signal_lists: :read,
                        create_signal_list: :create,
                        update_signal_list: :create,
                        delete_signal_list: :create


      def box_type_configurations
        collection = AssetConfiguration
                         .where(:asset_configuration_type_id => 1)
                         .page(params[:page])
                         .per((params[:per_page] || 25).to_i)

        sendReply(
            {
                boxes: collection.map do |x|
                  {
                      id: x.id,
                      box_type: x.box_type,
                      box_protocol: x.box_protocol,
                      box_port: x.box_port,
                      box_vpn_type: x.box_vpn_type,
                      box_contact_person_email: x.box_contact_person_email,
                      box_contact_person_first_name: x.box_contact_person_first_name,
                      box_contact_person_last_name: x.box_contact_person_last_name,
                      box_contact_person_phone_number: x.box_contact_person_phone_number,
                      box_contact_person_pretty_phone_number: pretty_phone_number(x.box_contact_person_phone_number),
                      asset_type: x.asset_type
                  }
                end,
                total_entries: collection.total_count,
                page: params[:page] || 1
            }
        )
      end

      def create_box_type_configuration
        result = {
            success: false,
            messages: []
        }

        box = AssetConfiguration.new(box_params)
        box.asset_configuration_type_id = 1

        box.save

        if box.errors.any?
          result[:messages] = box.errors.full_messages
        else
          result[:success] = true
          result[:id] = box.id
        end

        sendReply(result)
      end

      def update_box_type_configuration
        result = {
            success: false,
            messages: []
        }

        p = box_params
        box = AssetConfiguration.update(p[:id], p)

        if box.errors.any?
          result[:messages] = box.errors.full_messages
        else
          result[:success] = true
          result[:id] = box.id
        end

        sendReply(result)

      end

      def delete_box_type_configuration
        result = {
            success: true
        }

        AssetConfiguration.destroy(params[:id])

        sendReply(result)
      end

      def box_params
        params
            .require(:box_type)
            .permit(:box_type, :box_protocol, :box_port, :box_vpn_type, :asset_type,
                    :box_contact_person_email, :box_contact_person_first_name,
                    :box_contact_person_last_name, :box_contact_person_phone_number,
                    :id)
      end

      def signal_lists
        signal_lists = SignalList.all
                         .order(name: :asc)
                         .page(params[:page])
                         .per((params[:per_page] || 25).to_i)

        sendReply({
                      signal_lists: JSON.parse(signal_lists.to_json(:include => {
                          :read_signals => {:only => [:id, :vpp_name]},
                          :write_signals => {:only => [:id, :vpp_name]},
                          :vpp_signals => {:only => [:id, :vpp_name, :direction]}
                      })),
                      total_entries: signal_lists.total_count,
                      page: params[:page] || 1
                  })
      end

      def create_signal_list
        result = {
            success: false,
            messages: []
        }

        p = signal_list_create_params

        sl = SignalList.create(p)

        if sl.errors.any?
          result[:messages] = sl.errors.full_messages
        else
          result[:success] = true
          result[:id] = sl.id
        end

        sendReply(result)
      end

      def signal_list_create_params
        params.require(:signal_list).permit(:name, vpp_signals_attributes: [:id, :direction, :vpp_name])
      end

      def update_signal_list
        result = {
            success: false,
            messages: []
        }

        p = signal_list_update_params

        sl = SignalList.update(p[:id], p)

        if sl.errors.any?
          result[:messages] = sl.errors.full_messages
        else
          result[:success] = true
          result[:id] = sl.id
        end

        sendReply(result)
      end

      def signal_list_update_params
        params.require(:signal_list).permit(:id, :name, vpp_signals_attributes: [:id, :direction, :vpp_name])
      end

      def delete_signal_list
        result = {
            success: true
        }

        SignalList.destroy(params[:id])

        sendReply(result)
      end

      def audit_actions
        {
          create_box_type_configuration: 'Create Box Type Configuration',
          update_box_type_configuration: 'Create Box Type Configuration',
          delete_box_type_configuration: 'Create Box Type Configuration',
          create_signal_list: 'Create Signal List',
          update_signal_list: 'Update Signal List',
          delete_signal_list: 'Delete Signal List',
        }
      end

    end
  end
end