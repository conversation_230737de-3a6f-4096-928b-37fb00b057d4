module Api
  module V1
    class DlmChargingSessionsController < Api::V1::ApiController

      authorize_actions_for DlmChargingSession
      authority_actions list: :read, upload: :create

      def list
        dlm_charging_sessions = DlmChargingSession.order(created: :desc)

        if params[:page].present?
          dlm_charging_sessions = dlm_charging_sessions.page(params[:page]).per(params[:per_page])
        end

        sendReply(
          {
            data: JSON.parse(dlm_charging_sessions.to_json),
            total_entries: dlm_charging_sessions.total_count,
            page: params[:page]
          }
        )
      end

      def new
        if params[:file].present?
          upload
        end
      end

      def upload
        result = {
          success: false,
          result: nil,
          error: nil,
          messages: []
        }

        if params[:file].present?
          file_contents = params[:file].read
          csv = CSV.parse(file_contents, headers: true, col_sep: ';')
          sessions_data = csv.map do |row|
            {
              customer_id: row.fields[0],
              average_charging_duration_minutes: row.fields[1],
              average_charged_energy_watt_hour: row.fields[2],
            }
          end

          dlm_charging_sessions = DlmChargingSession
            .batch_insert(sessions_batch: sessions_data, now: Time.now.utc)

          validations = dlm_charging_sessions.collect {|s| s.valid? }.uniq
          valid = validations.size == 1 && validations.first

          result[:result] = { validation_result: { validationSuccess: valid } }
          errors = dlm_charging_sessions.collect {|s| s.errors.full_messages}.flatten.uniq
          unless errors.empty?
            result[:error] = errors.join(", ")
          end
          result[:success] = valid
        else
          result[:error] = 'NO_FILE_UPLOADED'
        end

        sendReply(result)
      end

      def audit_actions
        {
          new: 'Upload DLM Charging Sessions',
          upload: 'Upload DLM Charging Sessions',
        }
      end
    end
  end
end


