require 'base64'

module Api
  module V1

    class SchedulingReportController < Api::V1::ApiController

      include ApplicationHelper

      before_action :enforce_create_authority, only: [:regenerate_scheduling_balancing_groups_third_party_reports,
                                                      :regenerate_bg_asset_activations_reports,
                                                      :generate_report]

      def download_reports_form_data
        ret = {
            tsos: Tso.all.sort_by(&:name).map { |x| {id: x.try(:id), name: x.name} },
            available_markets: Market.where(name: [Market::MRL_MARKET_NAME, Market::SRL_MARKET_NAME]).map { |x| {id: x.id, name: x.name} }
        }

        sendReply(ret)
      end

      def regenerate_scheduling_balancing_groups_third_party_reports
        date = Date.strptime(params[:date], '%d/%m/%y')
        regeneration = ReportEmailRegeneration.new(date: date)

        begin
          regeneration.save!
          flash[:notice] =
          ret = {
              success: true,
              notice: t('regenerate_scheduling_balancing_groups_third_party_reports.success', scope: 'flash.scheduling_reports', date: regeneration.date)
          }
        rescue
          ret = {
              success: false,
              error: t('regenerate_scheduling_balancing_groups_third_party_reports.failure', scope: 'flash.scheduling_reports', date: regeneration.date)
          }
          if regeneration.errors.present?
            ret[:error] += regeneration.errors.full_messages.to_sentence
          end
        end

        sendReply(ret)
      end

      def regenerate_bg_asset_activations_reports
        date = Date.strptime(params[:date], '%d/%m/%y')
        regeneration = BgAssetActivationsEmailRegeneration.new(date: date)

        begin
          regeneration.save!
          flash[:notice] =
          ret = {
              success: true,
              notice: t('regenerate_bg_asset_activations_reports.success', scope: 'flash.scheduling_reports', date: regeneration.date)
          }
        rescue
          ret = {
              success: false,
              error: t('regenerate_bg_asset_activations_reports.failure', scope: 'flash.scheduling_reports', date: regeneration.date)
          }
          if regeneration.errors.present?
            ret[:error] += regeneration.errors.full_messages.to_sentence
          end
        end

        sendReply(ret)
      end

      def generate_report
        tso_id = params[:tso_id]
        date = Date.strptime(params[:date], '%d/%m/%y')
        market_id = params[:market_id]
        tso = Tso.find(tso_id)
        market = Market.find(market_id) if params[:market_id].present?
        third_party_balancing_group = params['third_party_balancing_group']
        energy_direction = params['energy_direction']
        report_type = params[:report_type]

        Rails.logger.info "Generating report of type #{report_type}"

        dbg_msg = "Download #{report_type} report for tso ##{tso_id} and market ##{market_id} from #{date}"
        Rails.logger.info dbg_msg

        if (report_type == 'edg')
          report = SchedulingBalancingGroupsReport.get(tso, market, date)
          exporter_klass = Services::SchedulingBalancingGroupsReportExporter
        elsif (report_type == 'ugc')
          report = SchedulingDayAfterReport.get(tso, market, energy_direction, date)
          exporter_klass = Services::SchedulingDayAfterReportExporter
        else
          report = SchedulingBalancingGroupsThirdPartyReport.get(tso, third_party_balancing_group, date)
          exporter_klass = Services::SchedulingBalancingGroupsThirdPartyReportExporter
        end

        if report.present?
          if report.report.try(:attributes).present?
            exporter = exporter_klass.new(report.report)
            send_exported_json(exporter, report)
          else
            # report feedback
            res = {success: false}
            if report.attributes[:errors].present?
              res[:error] = localize_report_feedback([report.attributes[:errors].first]).first
              Rails.logger.info "Returning error #{res[:error]}"
            elsif report.attributes[:messages].present?
              res[:notice] =  localize_report_feedback([report.attributes[:messages].first]).first
              Rails.logger.info "Returning notice #{res[:notice]}"
            end
            sendReply(res)
          end
        else
          sendReply({
                        success: false,
                        error: 'missing report'
                    })
        end
      end

      def audit_actions
        {
            regenerate_scheduling_balancing_groups_third_party_reports: 'Regenerate 3rd Party BG Reports',
            regenerate_bg_asset_activations_reports: 'Regenerate BG Asset Activations Report',
            generate_report: 'Generate Report',
        }
      end

      protected

      def send_exported_json(exporter, report, filename = nil, extension = "xlsx")
        t = Tempfile.new("#{self.class.to_s.underscore}-")
        file_name = exporter.export(t)
        file_extension = exporter.respond_to?(:file_extension) ? exporter.file_extension : extension

        t.rewind
        res = {
            success: true,
            content: Base64.encode64(t.read),
            type: 'application/excel',
            disposition: 'attachment',
            filename: (filename || report.report.file_name) + "." + file_extension
        }
        sendReply(res)
      ensure
        t.close rescue nil
      end

      def enforce_create_authority
        if !current_user.can?(:scheduling_reports_create)
          raise Authority::SecurityViolation.new(current_user, action_name, controller_name)
        end
      end

    end
  end
end