module Api
  module V1
    class DeclarationOfUnavailabilityController < Api::V1::ApiController

      authorize_actions_for DistributedUnit
      authority_actions upload: :create

      def upload
        result = {
          success: false,
          result: nil,
          error: nil,
          messages: []
        }

        asset_id = params[:asset_id]
        if params[:file].present?
          file_name = params[:file].original_filename
          file_path = params[:file].tempfile.path
          begin
            file_upload_resp = Services::DeclarationOfUnavailabilityUploader.new.upload(asset_id, file_name, file_path)
            err = file_upload_resp[:errors]
            if err.present?
              result[:error] = 'ISSUE_UPLOADING_FILE'
              result[:messages] = err
            else
              result[:success] = true
              result[:result] = { validation_result: { validationSuccess: true } }
            end
          rescue Exception => e
            Rails.logger.error "Failed to upload declaration of availability/unavailability file #{e.message} #{e.backtrace}"
            result[:error] = 'ISSUE_UPLOADING_FILE'
            result[:messages] = [e.message, e.backtrace]
          end
        else
          result[:error] = 'NO_FILE_UPLOADED'
        end

        sendReply(result)
      end

      def audit_actions
        {
            # upload: 'Upload Declaration of Availability/Unavailability File',
        }
      end

    end
  end
end
