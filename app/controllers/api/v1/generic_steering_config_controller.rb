module Api
  module V1

    class GenericSteeringConfigController < Api::V1::ApiController

      authorize_actions_for GenericSteeringConfig
      authority_actions generic_steering_configs: :read,
                        create_generic_steering_config: :create,
                        update_generic_steering_config: :create,
                        delete_generic_steering_config: :create


      def generic_steering_configs
        collection = GenericSteeringConfig.all.sort_by{|x| x.config.name.downcase}

        sendReply({
                      configs: collection
                  })
      end

      def create_generic_steering_config
        result = {
            success: false,
            messages: []
        }

        p = steering_type_params
        x = GenericSteeringConfig.new
        data = GenericSteeringConfigData.new
        data.attributes.merge!(p.to_h.deep_transform_keys! { |key| key.underscore })
        x.attributes[:config] = data
        x.save

        if x.errors.any?
          result[:messages] = x.errors.full_messages
        else
          result[:success] = true
          result[:id] = x.id
        end

      rescue ActiveResource::BadRequest => err
        result[:messages] = get_errors_from_response(err.response)
      rescue => err
        result[:messages] = ["Error #{err.to_s}"]
      ensure
        sendReply(result)
      end

      def get_error_message(error_key)
        GenericSteeringConfig.t("e_#{error_key}")
      end

      def get_errors_from_response(response)
        errs = []
        if response && response.body
          #Rails.logger.debug "Handling error response #{response.body}"
          body = JSON.parse(response.body)
          errors = body['errors']
          if errors
            errors.each do |_, v|
              v.each {|error_key| errs << get_error_message(error_key)}
            end
          end
        end
        errs << get_error_message('unknown_error') unless errs.any?
        errs
      end

      def update_generic_steering_config
        Rails.logger.info "Update steering params #{params}"

        result = {
            success: false,
            messages: []
        }

        p = steering_type_params
        id = p[:id]

        x = GenericSteeringConfig.find(id)
        data = GenericSteeringConfigData.new
        data.attributes.merge!(p.to_h.deep_transform_keys! { |key| key.underscore })
        x.attributes[:config] = data
        x.save

        if x.errors.any?
          result[:messages] = x.errors.full_messages
        else
          result[:success] = true
          result[:id] = x.id
        end

      rescue ActiveResource::BadRequest => err
        result[:messages] = get_errors_from_response(err.response)
      rescue => err
        result[:messages] = ["Error #{err.to_s}"]
      ensure
        sendReply(result)
      end

      def delete_generic_steering_config
        result = {
            success: true
        }

        x = GenericSteeringConfig.find(params[:id])
        x.destroy

      rescue ActiveResource::BadRequest => err
        result[:messages] = get_errors_from_response(err.response)
      rescue => err
        result[:messages] = ["Error #{err.to_s}"]
      ensure
        sendReply(result)
      end


      def steering_type_params
        params
            .require(:steering_type)
            .permit(
                :id,
                :name, :hasBasepoint, :scheduleType, :flexType, :setpointValueType,
                :hasSetpointFeedback, :setpointType, :setpointIntervalType, :setpointFrequencySeconds,
                :hasLock, :hasDispatchedDeviated, :hasHeartbeatVPP, :heartbeatVPPFrequencySeconds,
                :heartbeatVPPType, :hasHeartbeatAsset, :heartbeatAssetFrequencySeconds,
                :heartbeatAssetType, :defaultAbsoluteSetpoint)
      end


      def audit_actions
        {
            create_generic_steering_config: 'Create Generic Steering Config',
            update_generic_steering_config: 'Update Generic Steering Config',
            delete_generic_steering_config: 'Delete Generic Steering Config',
        }
      end
    end
  end
end