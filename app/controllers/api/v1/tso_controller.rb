module Api
  module V1
    class TsoController < Api::V1::ApiController

      def list
        tsos ||= Tso.all.order(name: :asc)

        if params[:page].present?
          tsos = tsos.page(params[:page]).per(params[:per_page])
        end

        sendReply({tsos: tsos})
      end

      def tso_names
        tsos = Tso.all.order(name: :asc)

        sendReply({
                      tsos: tsos.map do |x|
                        {
                            id: x.id,
                            name: x.name
                        }
                      end
                  })
      end
    end
  end
end