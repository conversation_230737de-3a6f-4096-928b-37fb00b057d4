module Api
  module V1
    class HistoryController < Api::V1::ApiController

      before_action :enforce_allocations_read_authority, only: [:asset_manual_allocations, :asset_automatic_allocations]
      before_action :enforce_nominations_read_authority, only: [:nominations]
      before_action :enforce_bids_read_authority, only: [:auctions, :bids]
      before_action :enforce_reporting_and_nofications_read_authority, only: [:report_emails, :report_email, :report_email_recipients, :report_email_download]

      def asset_manual_allocations
        asset_dg_allocation_sources =
            AssetDgAllocationSource
                .where.not(user_id: 0)
                .includes(:user_account)
                .order('created desc')
                .page(params[:page])
                .per((params[:per_page] || 25).to_i)


        sendReply(
            {
                data: JSON.parse(asset_dg_allocation_sources.to_json(:include => {
                    :user_account => {
                        :only => [:email, :name]
                    }
                })),
                total_entries: asset_dg_allocation_sources.total_count,
                page: params[:page]
            }
        )
      end

      def asset_automatic_allocations
        dispatch_groups_for_list = DispatchGroup
                                       .not_deleted
                                       .includes(:tso)
                                       .order(name: :asc)
        start_date = params[:start_date]
        end_date = params[:end_date]
        type = params[:type]
        status = params[:status]
        dispatch_group = params[:dispatch_group_id]
        page = (params[:page] || 1).to_i
        per_page = (params[:per_page] || 25).to_i
        automatic_allocations = AutomaticAllocation.
          filter_paginate(dispatch_group, start_date, end_date, status, type, page, per_page)
        total_allocations_count = automatic_allocations.total_count
        automatic_allocations = automatic_allocations.collect { |a| a.value.deep_symbolize_keys }

        asset_dg_allocation_source_ids = automatic_allocations.collect{ |a| a[:allocationSourceId].try(:[], :id) }.compact
        asset_dg_allocation_sources = AssetDgAllocationSource.find(asset_dg_allocation_source_ids).map { |x| [x.id, x] }.to_h

        tso_ids = automatic_allocations.collect { |a| a[:tsoId].try(:[], :value) }.uniq.compact
        tsos = Tso.where(id: tso_ids)
        tsos = JSON.parse(tsos.to_json(only: [:id, :name]))

        sendReply(
            {
                data: {
                    automatic_allocations: automatic_allocations,
                    allocation_sources: asset_dg_allocation_sources,
                    dispatch_groups: dispatch_groups_for_list,
                    tsos: tsos
                },
                total_entries: total_allocations_count,
                page: params[:page]
            }
        )
      end

      def nominations
        sources = DistributedUnitSource
                      .includes(:user_account)
                      .order('created desc')
                      .page(params[:page])
                      .per(params[:per_page])

        sendReply(
            {
                data: {
                    nomination_sources: JSON.parse(sources.to_json(:include => {
                        :user_account => {
                            :only => [:email, :name]
                        }
                    }))
                },
                total_entries: sources.total_count,
                page: params[:page]
            }
        )
      end

      def auctions
        auctions = NominateableVolume.select(:id, :created, :user_id, :delivery_date, :nominateable_volumes)
                       .includes(:user_account)
                       .order('created desc')
                       .page(params[:page])
                       .per(params[:per_page])
        total_count = auctions.total_count

        sendReply(
            {
                data: {
                    auctions: auctions.map { |a|
                      tmp_auction = JSON.parse(a.to_json(:include => {
                          :user_account => {
                              :only => [:email, :name]
                          }
                      }))
                      tmp_auction['meta_data_link'] = nominateable_volumes_meta_data_nominateable_volume_path(id: a['id'])
                      tmp_auction['source_link'] = a['nominateable_volumes'].any? ? nominateable_volumes_volumes_nominateable_volume_path(id: a['id']) : nil

                      tmp_auction
                    }
                },
                total_entries: total_count,
                page: params[:page]
            }
        )
      end

      def report_email_recipients
        recipients = ReportEmailSending.
            select(:email_to, :email_cc).
            collect(&:email_recipients).
            flatten.uniq

        sendReply(recipients)
      end

      def report_emails
        puts "### REPORT EMAILS #{params.inspect}}"
        r = ReportEmailSending.where("1=1")
        r = r.where("created >= ? ", params[:start_date]) if params[:start_date].present?
        r = r.where("created <= ? ", params[:end_date]) if params[:end_date].present?
        r = r.where(report_type: params[:report_type]) if params[:report_type].present?
        # r = r.where("report_file_content IS NOT NULL") #TODO: REMOVE!
        if params[:status].present?
          if params[:status] == "Skipped"
            r = r.where(report_delivery_skipped: true)
          elsif params[:status] == "Success"
            r = r.where(report_delivery_skipped: false, report_generation_success: true, report_delivery_success: true)
          elsif params[:status] == "Error"
            r = r.where("report_delivery_skipped = false AND (report_generation_success = false OR report_delivery_success = false)")
          end
        end
        if params[:recipients].present?
          like_rcp = "%#{params[:recipients]}%"
          r = r.where('email_to LIKE ? OR email_cc LIKE ?', like_rcp, like_rcp)
        end
        r = r.where(report_delivery_skipped: params[:active] == "false") if params[:active].present?
        r = r.order('report_date desc').
            page(params[:page]).
            per(params[:per_page])

        r_json = r.as_json(
            except: [:report_file_content],
            methods: [:has_attachment, :status, :email_recipients, :localized_report_parameters,
                      :localized_report_generation_errors, :localized_report_feedback]
        )
        sendReply(
            {
                data: {
                    report_emails: r_json
                },
                total_entries: r.total_count,
                page: params[:page]
            }
        )
      end

      def report_email
        result = nil
        if params[:id].present?
          result = ReportEmailSending.where(id: params[:id]).first.as_json(
              except: [:report_file_content],
              methods: [:has_attachment, :status, :email_recipients, :localized_report_parameters,
                        :localized_report_generation_errors, :localized_report_feedback]
          )
        end
        sendReply(result)
      end

      def report_email_download
        resource = ReportEmailSending.find(params[:id])
        if resource
          if ["xls", "xlsx"].include?(resource.report_file_extension)
            mime_type = 'application/excel'
          elsif "zip" == resource.report_file_extension
            mime_type = 'application/zip'
          elsif "csv" == resource.report_file_extension
            mime_type = "text/csv"
          else
            mime_type = "application/octet-stream"
          end
          send_data(resource.report_file_content,
                    type: mime_type,
                    disposition: 'attachment',
                    filename: (resource.report_file_name) + "." + resource.report_file_extension)
        end
      end

      def bids
        sources = NominationBidSource
                      .includes(:user)
                      .order('created desc')
                      .page(params[:page])
                      .per(params[:per_page])

        sendReply(
            {
                data: {
                    bid_sources: JSON.parse(sources.to_json(:include => {
                        :user => {
                            :only => [:email, :name]
                        }
                    }))
                },
                total_entries: sources.total_count,
                page: params[:page]
            }
        )
      end

      def bid_source
        id = params[:id]

        bid_source = NominationBidSource.find(id)
        ret = JSON.parse(bid_source.to_json(:include => {
            :user => {
                :only => [:email, :name]
            },
            #:nomination_bids => {}
        }))

        bids = []
        if bid_source.nomination_bids
          bid_source.nomination_bids.each do |nb|
            b = JSON.parse(nb.to_json)
            b[:market_name] = nb.product.try(:market).try(:name)
            b[:product] = nb.product_interval
            b[:product_with_minutes] = nb.product_interval(true)
            b[:tso_name] = nb.tso.name
            b[:asset_name] = nb.try(:asset).try(:name)
            b[:asset_external_id] = nb.try(:asset).try(:short_name).try(:value)
            bids << b
          end
        end
        ret['nomination_bids'] = bids

        sendReply({
                      bid_source: ret
                  })
      end

      def v2g_optimization_job_create
        begin
          job = Services::PortfolioManagementService.create_v2g_optimization_job(
              user_id: current_user.id
          )
          Rails.logger.info "Created v2g optimization job #{job}"
          sendReply({success: true})
        rescue Services::PortfolioManagementService::UnableToCreateV2GOptimizationJob => e
          sendReply({
                        success: false,
                        error: e.to_s
                    })
        end
      end

      def v2g_optimization_jobs
        optimization_type = params[:job_type].presence || 'trading'
        page = params[:page]

        collection =
            V2gOptimizationJob
                .where(optimization_type: optimization_type)
                .order(start_time: :desc, created: :desc)
                .page(page)

        optimizations =
            if optimization_type == 'trading'
              V2gOptimizationJob.bidding_optimizations_for_jobs(job_ids: collection.map(&:id))
            else
              V2gOptimizationJob.steering_optimizations_for_jobs(job_ids: collection.map(&:id))
            end

        sendReply({
                      jobs: collection,
                      optimizations: optimizations,
                      total_entries: collection.total_count,
                      page: page
                  })

      end


      def audit_actions
        {
            v2g_optimization_job_create: 'Create V2G Optinization Job',
        }
      end

      def enforce_allocations_read_authority
        if !current_user.can_read?(AssetDgAllocation)
          raise Authority::SecurityViolation.new(current_user, action_name, controller_name)
        end
      end

      def enforce_nominations_read_authority
        if !current_user.can_read?(DistributedUnit)
          raise Authority::SecurityViolation.new(current_user, action_name, controller_name)
        end
      end

      def enforce_bids_read_authority
        if !current_user.can_read?(NominationBid)
          raise Authority::SecurityViolation.new(current_user, action_name, controller_name)
        end
      end

      def enforce_reporting_and_nofications_read_authority
        if !current_user.can?(:reporting_and_notification_read)
          raise Authority::SecurityViolation.new(current_user, action_name, controller_name)
        end
      end

    end
  end
end