module Api
  module V1
    class AssetOptimizationsController < Api::V1::ApiController

      authorize_actions_for DistributedUnit
      authority_actions upload: :create,
        portfolio_optimizations: :create,
        validate_bids: :create,
        reject_bids: :create,
        download_market_positions: :create

      def upload
        result = {
          success: false,
          result: nil,
          error: nil,
          messages: []
        }

        bidding_method = params[:bidding_method]
        perspective = params[:perspective]
        asset_id = params[:asset_id]
        swing_limit_value = params[:swing_limit_value].try(:to_i)
        auction_configs = params[:auction_config_id]
        markets = []
        prices = {}
        price_errors = []
        if auction_configs
          auction_configs = auction_configs.collect{|id| id.try(:to_i)}
          auction_configs.each do |a_cfg_id|
            a_cfg = AuctionConfig.find(a_cfg_id)
            a_cfg.from_value_hash
            markets << a_cfg.bidding_method_market
            price_forcast_type = params["price_forecast_type_#{a_cfg_id}"]
            if price_forcast_type == "manual"
              price_forecast_file = params["price_forecast_file_#{a_cfg_id}"]
              prices[a_cfg] = Services::PriceForecastImporter.new.parse_file(
                price_forecast_file.read, 
                price_forecast_file.original_filename, 
                a_cfg)
              if !prices[a_cfg][:errors].empty?
                price_errors << I18n.t('errors.messages.invalid_prices_for_tender', 
                  tender_name: a_cfg.name, 
                  file_name: prices[a_cfg][:file_name],
                  errors: prices[a_cfg][:errors].join("; "))
              end
            end
          end
        end
        markets = markets.compact.flatten
        is_n2ex1h = markets.include?('N2EX1H')
        is_epex30min = markets.include?('EPEX30MIN')
        is_dc = markets.include?('DynamicContainment')
        is_dm = markets.include?('DynamicModeration')
        is_dr = markets.include?('DynamicRegulation')
        exclude_dc_high_efa_blocks = params[:exclude_dc_high_efa_block]
        exclude_dc_low_efa_blocks = params[:exclude_dc_low_efa_block]
        exclude_dm_high_efa_blocks = params[:exclude_dm_high_efa_block]
        exclude_dm_low_efa_blocks = params[:exclude_dm_low_efa_block]
        exclude_dr_high_efa_blocks = params[:exclude_dr_high_efa_block]
        exclude_dr_low_efa_blocks = params[:exclude_dr_low_efa_block]

        if !price_errors.empty?  
          result[:error] = 'ISSUE_UPLOADING_FILE'
          result[:messages] = price_errors
        else
          begin
            payload = vpp_trading_payload(asset_id, perspective, auction_configs, prices,
              swing_limit_value,
              exclude_dc_high_efa_blocks, exclude_dc_low_efa_blocks,
              exclude_dm_high_efa_blocks, exclude_dm_low_efa_blocks,
              exclude_dr_high_efa_blocks, exclude_dr_low_efa_blocks)
            result[:result] = trading_service.post_asset_optimizations(payload)
            if result[:result]["status"] == "success"
              result[:success] = true
            else
              result[:error] = 'ISSUE_SUBMITTING_ASSET_OPTIMIZATION'
              result[:messages] = result[:result]["errors"]
            end
          rescue trading_service::UnableToRunAssetOptimization => e
            result[:error] = 'ISSUE_SUBMITTING_ASSET_OPTIMIZATION'
            result[:messages] = [e.to_s]
          end
        end
        sendReply(result)
      end

      def portfolio_optimizations
        day = Time.now.beginning_of_day
        bidding_method = params[:bidding_method]
        optimizations = PortfolioOptimization.optimizations(bidding_method, day, day + 24.hours).
          select{ |o| ((o.auction_configs + o.nbs_auction_configs).collect(&:id).uniq - user_owned_auction_config_ids).empty? }

        sendReply({
          optimizations: optimizations.as_json(include: {
            user_account: {only: [:id, :name, :email]},
            asset: {only: [:id, :name]},
            nomination_bid_sources: {include: {auction_config: {only: [:id, :name]}}},
          }, methods: [:auction_configs, :nbs_auction_configs]),
        })
      end

      def validate_bids
        result = {
          success: false,
          result: nil,
          error: nil,
          messages: []
        }
        begin
          json_result = trading_service.validate_bids(
            user_id: current_user.id,
            portfolio_optimization_id: params[:id])
          if json_result["status"] == "success"
            result[:success] = true
            result[:messages] = ["ASSET_OPTIMIZATIONS.BIDS_VALIDATED_SUCCESS"]
          else
            result[:success] = false
            result[:messages] = json_result["errors"]
          end
        rescue trading_service::UnableToValidateBids => e
          result[:success] = false
          result[:messages] = ["ASSET_OPTIMIZATIONS.BIDS_VALIDATED_ERROR", e.to_s]
        end
        sendReply(result)
      end

      def reject_bids
        result = {
          success: false,
          result: nil,
          error: nil,
          messages: []
        }
        begin
          json_result = trading_service.reject_bids(
            user_id: current_user.id,
            portfolio_optimization_id: params[:id])
          if json_result["status"] == "success"
            result[:success] = true
            result[:messages] = ["ASSET_OPTIMIZATIONS.BIDS_REJECTED_SUCCESS"]
          else
            result[:success] = false
            result[:messages] = json_result["errors"]
          end
        rescue trading_service::UnableToRejectBids => e
          result[:success] = false
          result[:messages] = ["ASSET_OPTIMIZATIONS.BIDS_REJECTED_ERROR", e.to_s]
        end
        sendReply(result)
      end

      def download_market_positions
        optimization_id = params[:id]
        po = PortfolioOptimization.find(optimization_id)
        data = trading_service.get_market_positions(optimization_id)
        filename = "market_positions_#{optimization_id}_#{po.created.strftime("%Y-%m-%dT%H:%M:%S%z")}"
        send_data(data, filename: "#{filename}.xlsx")
      end

      def audit_actions
        {
            upload: 'Asset Optimizations',
            validate_bids: 'Asset Optimizations - Validate Bids',
            reject_bids: 'Asset Optimizations - Reject Bids',
        }
      end

      private

      def vpp_trading_payload(asset_id, perspective, auction_configs, auction_config_prices,
        swing_limit_value, 
        exclude_dc_high_efa_blocks, exclude_dc_low_efa_blocks,
        exclude_dm_high_efa_blocks, exclude_dm_low_efa_blocks,
        exclude_dr_high_efa_blocks, exclude_dr_low_efa_blocks)
        # {
        #     "userId": current_user.id,
        #     "assetId": asset_id,
        #     "dcHighExclusions": [
        #         "2023-01-01T00:00:00Z/2023-01-01T01:00:00Z",
        #         "2023-01-01T00:01:00Z/2023-01-02T01:00:00Z"
        #         ],
        #     "dcLowExclusions": [],
        #     "prices": {
        #         "dch":[["2022-04-24T22:00:00Z/2029-12-31T22:00:00Z", 111]],
        #         "dcl": [["2022-04-24T22:00:00Z/2029-12-31T22:00:00Z", 112]],
        #         "dmh":[["2022-04-24T22:00:00Z/2029-12-31T22:00:00Z", 113]],
        #         "dml": [["2022-04-24T22:00:00Z/2029-12-31T22:00:00Z", 114]],
        #         "drh":[["2022-04-24T22:00:00Z/2029-12-31T22:00:00Z", 115]],
        #         "drl": [["2022-04-24T22:00:00Z/2029-12-31T22:00:00Z", 116]],
        #         "n2ex1h": [["2022-04-24T22:00:00Z/2029-12-31T22:00:00Z", 117]],
        #         "epex30min": [["2022-04-24T22:00:00Z/2029-12-31T22:00:00Z", 118]]
        #     },
        #     "optimizeForAuctions": [16],
        #     "perspective": "Customer"
        # }
        dc_high_exclusions = efa_blocks_to_payload(exclude_dc_high_efa_blocks || [])
        dc_low_exclusions = efa_blocks_to_payload(exclude_dc_low_efa_blocks || [])
        dm_high_exclusions = efa_blocks_to_payload(exclude_dm_high_efa_blocks || [])
        dm_low_exclusions = efa_blocks_to_payload(exclude_dm_low_efa_blocks || [])
        dr_high_exclusions = efa_blocks_to_payload(exclude_dr_high_efa_blocks || [])
        dr_low_exclusions = efa_blocks_to_payload(exclude_dr_low_efa_blocks || [])
        payload = {
          user_id: current_user.id,
          asset_id: asset_id.try(:to_i),
          dc_high_exclusions: dc_high_exclusions,
          dc_low_exclusions: dc_low_exclusions,
          dm_high_exclusions: dm_high_exclusions,
          dm_low_exclusions: dm_low_exclusions,
          dr_high_exclusions: dr_high_exclusions,
          dr_low_exclusions: dr_low_exclusions,
          optimize_for_auctions: auction_configs,
          perspective: perspective,
          power_swing_limit: {
            applies: swing_limit_value.present?,
            kilowatts: swing_limit_value || 0
          }
        }
        prices = {
          dch: {},
          dcl: {},
          dmh: {},
          dml: {},
          drh: {},
          drl: {},
          n2ex1h: {},
          epex30min: {}
        }
        auction_config_prices.each do |a_cfg, price|
          price[:price_lines].each do |price_line|
            interval = "#{format_time(price_line[:time_from])}/#{format_time(price_line[:time_to])}"
            prices[:dch][interval] = price_line[:price_forecast_dch] if price_line[:price_forecast_dch]
            prices[:dcl][interval] = price_line[:price_forecast_dcl] if price_line[:price_forecast_dcl]
            prices[:dmh][interval] = price_line[:price_forecast_dmh] if price_line[:price_forecast_dmh]
            prices[:dml][interval] = price_line[:price_forecast_dml] if price_line[:price_forecast_dml]
            prices[:drh][interval] = price_line[:price_forecast_drh] if price_line[:price_forecast_drh]
            prices[:drl][interval] = price_line[:price_forecast_drl] if price_line[:price_forecast_drl]
            prices[:n2ex1h][interval] = price_line[:price_forecast_n2ex1h] if price_line[:price_forecast_n2ex1h]
            prices[:epex30min][interval] = price_line[:price_forecast_epex30min] if price_line[:price_forecast_epex30min]
          end          
        end
        prices = prices.transform_values(&:to_a).delete_if { |_, value| value.empty? }
        payload[:prices] = prices
        HashCaseConverter.to_camel_case(payload)
      end

      def format_time(time)
        time.getutc.strftime("%Y-%m-%dT%H:%M:%SZ")
      end

      def efa_blocks_to_payload(efa_blocks)
        # EFA Blocks are excluded for all the optimisation period (3 days).
        (0..2).collect{ |offset|
          efa_blocks.collect{|b| efa_block_to_timestamps(b, offset)}
        }.flatten
      end

      def efa_block_to_timestamps(efa_block, day_offset)
        efa_block.slice!("EFA")
        if efa_block.to_i == 1
          efa_block_start = apply_day_offset(Date.today, day_offset).in_time_zone("Europe/London").change({hour: 23})
          efa_block_end = apply_day_offset(Date.tomorrow, day_offset).in_time_zone("Europe/London").change({hour: 3})
        else
          start_hour = (efa_block.to_i - 1) * 4 - 1
          end_hour = start_hour + 4
          efa_block_start = apply_day_offset(Date.tomorrow, day_offset).in_time_zone("Europe/London").change({hour: start_hour})
          efa_block_end = apply_day_offset(Date.tomorrow, day_offset).in_time_zone("Europe/London").change({hour: end_hour})
        end
        "#{format_time(efa_block_start)}/#{format_time(efa_block_end)}"
      end

      def apply_day_offset(date, offset)
        date_with_day_offset = date
        current = 0
        while current < offset do
          date_with_day_offset = date_with_day_offset.tomorrow
          current += 1
        end
        date_with_day_offset
      end

    end
  end
end
