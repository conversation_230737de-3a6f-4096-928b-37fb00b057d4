module Api
  module V1

    class BalancingGroupController < Api::V1::ApiController

      authorize_actions_for DistributedUnit
      authority_actions bgs: :read,
                        update_bgs: :create

      DATE_FORMAT = '%Y%m%d'


      def bgs
        bgs = Db::DbBalancingGroupName.order('start_date desc, name asc').group_by(&:type)

        sendReply({
                      bgs: {
                          supplier_bgs: balancing_group_name_data(bgs[Db::DbBgNameSupplier.to_s]),
                          collecting_bgs: balancing_group_name_data(bgs[Db::DbBgNameCollecting.to_s], true),
                          internal_bgs: balancing_group_name_data(bgs[Db::DbBgNameInternal.to_s]),
                          third_party_bgs: balancing_group_name_data(bgs[Db::DbBgNameThirdParty.to_s])
                      }
                  }
        )
      end

      def balancing_group_name_data(bg_names, has_date_interval = false)
        ret = []
        bg_names.each do |bg|
          bg_data = {
              id: bg.id,
              name: bg.name
          }
          if (has_date_interval)
            bg_data[:start_date] = bg.start_date.strftime(DATE_FORMAT)
            bg_data[:end_date] = bg.end_date.strftime(DATE_FORMAT)
          end
          ret << bg_data
        end
        ret
      end

      def update_bgs
        result = {
            success: false,
            messages: ['not implemented']
        }

        Rails.logger.info "Updating bgs #{params}"

        type_param = params['type']
        bgs_param = params['balancing_groups']

        db_type = json_to_db_type_map[type_param]
        if db_type
          has_date_range = db_type == 'Db::DbBgNameCollecting'

          Rails.logger.info "Checking bg class #{db_type} #{has_date_range ? 'with' : 'without'} date range"

          existing_ids = Db::DbBalancingGroupName.where(type: db_type).collect(&:id)
          param_ids = bgs_param.collect { |x| x['id'].try(&:to_i) }.reject(&:blank?)
          ids_to_delete = existing_ids - param_ids

          Rails.logger.info "Existing bg ids #{existing_ids}"
          Rails.logger.info "Requested db ids #{param_ids}"
          Rails.logger.info "Bg ids to delete #{ids_to_delete}"

          bgs_param.each do |p|
            if p['id'].blank?
              attrs = {
                  type: db_type,
                  name: p['name']
              }
              if has_date_range
                attrs[:start_date] = Date.strptime(p['start_date'], DATE_FORMAT)
                attrs[:end_date] = Date.strptime(p['end_date'], DATE_FORMAT)
              end
              bg = Db::DbBalancingGroupName.new(attrs)
              Rails.logger.info "Creating bg #{bg}"
              bg.save!
            else
              bg = Db::DbBalancingGroupName.find(p['id'])
              new_name = p['name']
              if (bg[:name] != new_name) ||
                  (has_date_range &&
                      (bg[:start_date].strftime(DATE_FORMAT) != p['start_date'] ||
                      bg[:end_date].strftime(DATE_FORMAT) != p['end_date']))

                Rails.logger.info "Updating bg #{bg.attributes.inspect}"

                attrs = {
                    name: p['name']
                }
                if has_date_range
                  attrs[:start_date] = Date.strptime(p['start_date'], DATE_FORMAT)
                  attrs[:end_date] = Date.strptime(p['end_date'], DATE_FORMAT)
                end
                bg.update!(attrs)
              end
            end
          end
        else
          Rails.logger.error "Unknown balancing group name type #{type_param}"
          result = {
              success: false,
              messages: ['Unexpected balancing group type']
          }
        end

        if (ids_to_delete.any?)
          Db::DbBalancingGroupName.where(type: db_type).where(id: ids_to_delete).delete_all
        end

        result[:success] = true

      rescue ActiveRecord::RecordInvalid => err_invalid
        Rails.logger.info "Error #{err_invalid}"
        result[:messages] = ["#{err_invalid}"]

      ensure
        sendReply(result)

      end


      def json_to_db_type_map
        {
            'supplier_bgs': 'Db::DbBgNameSupplier',
            'internal_bgs': 'Db::DbBgNameInternal',
            'collecting_bgs': 'Db::DbBgNameCollecting',
            'third_party_bgs': 'Db::DbBgNameThirdParty'
        }.stringify_keys
      end

      def audit_actions
        {
          update_bgs: "Update Balancing Groups"
        }
      end

    end
  end
end
