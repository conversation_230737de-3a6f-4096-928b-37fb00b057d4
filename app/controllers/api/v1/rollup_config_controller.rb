module Api
  module V1

    class RollupConfigController < Api::V1::ApiController

      authorize_actions_for Rollup
      authority_actions dg_rollups_data: :read,
                        asset_rollups_data: :read,
                        update_asset_rollup_families: :create,
                        save_asset_rollup_products: :create,
                        save_asset_rollup_interval: :create,
                        save_dg_rollup_interval: :create,
                        generate_rollup: :create


      # localhost:3003/api/v1/configuration/rollups/dg_rollups_data
      def dg_rollups_data
        dispatch_groups = DispatchGroup.not_deleted.where(rollups_enabled: true)
        rollup_types = RollupType.
            where(owner_type: 'DispatchGroup').
            includes(:rollups)

        sendReply({
                      dispatch_groups: dispatch_groups.map do |dg|
                        {
                            id: dg.id,
                            name: dg.name,
                            label: "##{dg.id} #{dg.name} (#{dg.tso.name})"
                        }
                      end,
                      rollup_row_data: rollup_types.map do |t|
                        row_data = {
                            rollup_type_name: t.name,
                            rollup_type_code: t.code,
                            rollup_type_id: t.id,
                        }
                        dispatch_groups.each do |dg|
                          key = dg.id.to_s.to_sym
                          row_data[key] = to_minutes(rollup_for_dg(t, dg).aggregation_interval)
                        end
                        row_data
                      end
                  })
      end

      # localhost:3003/api/v1/configuration/rollups/asset_rollups_data
      def asset_rollups_data
        rollup_families = RollupFamily.where(active: true).order('id asc').select(:id, :name)
        rollup_types = RollupType.
            where(owner_type: 'RollupFamily').
            includes(:products).
            includes(:rollups)
        products = Product.all.decorate


        sendReply(
            {
                rollup_families: rollup_families,
                rollup_row_data: rollup_types.map do |t|
                  row_data = {
                      name: t.name,
                      code: t.code,
                      rollup_type_id: t.id,
                      #products: t.products.select(:id, :name)
                      products: t.products.pluck(:id)
                  }
                  rollup_families.each do |rollup_family|
                    key = rollup_family.id.to_s.to_sym
                    row_data[key] = to_minutes(rollup_for(t, rollup_family).aggregation_interval)
                  end
                  row_data
                end,
                products: products
            }
        )
      end

      def update_asset_rollup_families
        resp = {
            success: true,
            messages: []
        }

        changed_families = params[:changed_families]
        deleted_families = params[:deleted_families]
        new_families = params[:new_families]

        if not changed_families.blank?
          changed_families.each do |p|
            f = RollupFamily.find(p[:id])
            if (f.name != p[:name])
              Rails.logger.info "Update rollup family ##{p[:id]} #{p[:name]}"
              f.name = p[:name]
              f.save
              if f.errors.any?
                resp[:success] = false
                resp[:messages] = resp[:messages] + f.errors.full_messages
              end
            end
          end
        end

        if not deleted_families.blank?
          deleted_families.each do |p|
            Rails.logger.info "Disable rollup family ##{p[:id]} #{p[:name]}"
            f = RollupFamily.find(p[:id])
            active_contracts = f.active_contracts
            if active_contracts.empty?
              f.active = false
              f.save
            else
              customer_names = active_contracts.collect(&:customer).uniq.collect(&:name).join(", ")
              resp[:success] = false
              resp[:messages] << I18n.t('asset_rollups.form_rollup_family.rollup_family_disable_error', rollup_family: f.name, customers: customer_names)
            end
          end
        end

        if not new_families.blank?
          new_families.each do |p|
            Rails.logger.info "Create rollup family #{p[:name]}"
            f = RollupFamily.new({name: p[:name], active: true})
            f.save
            if f.errors.any?
              resp[:success] = false
              resp[:messages] = resp[:messages] + f.errors.full_messages
            end
          end
        end

        resp[:rollup_families] = RollupFamily.where(active: true).order('id asc').select(:id, :name)

        sendReply(resp)
      end

      def save_asset_rollup_products
        resp = {
            success: false
        }

        Rails.logger.info "Saving asset rollup products #{params}"

        rollup_type = RollupType.where(code: params[:rollup_type_code]).first
        if rollup_type.blank?
          resp[:error] = "Invalid rollup type #{params[:rollup_type_code]}"
        else
          rollup_type.products = Product.where(id: params[:val])
          rollup_type.save

          if rollup_type.errors.any?
            resp[:error] = rollup_type.errors.full_messages
          else
            resp[:success] = true
            resp[:val] = rollup_type.products.pluck(:id)
          end
        end

        sendReply(resp)
      end

      def save_asset_rollup_interval
        resp = {
            success: false
        }

        Rails.logger.info "Saving asset rollup interval #{params}"

        rollup_type = RollupType.where(code: params[:rollup_type_code]).first
        if rollup_type.blank?
          resp[:error] = "Invalid rollup type #{params[:rollup_type_code]}"
        else
          rollup_family = RollupFamily.find(params[:rollup_family_id])
          if rollup_family.blank?
            resp[:error] = "Invalid rollup family #{params[:rollup_family_name]}"
          else
            rollup = rollup_type.rollups.find { |r| r.owner == rollup_family }
            val = params[:val]
            if val.blank?
              rollup.destroy unless rollup.blank?
              resp[:success] = true
            else
              if rollup.blank?
                rollup = Rollup.new({rollup_type: rollup_type, owner: rollup_family})
              end
              rollup.aggregation_interval = val.to_i * 60
              rollup.save

              if rollup.errors.any?
                resp[:error] = rollup.errors.full_messages
              else
                resp[:success] = true
                resp[:val] = to_minutes(rollup.aggregation_interval)
              end
            end
          end
        end

        sendReply(resp)
      end

      def save_dg_rollup_interval
        resp = {
            success: false
        }

        Rails.logger.info "Saving dg rollup interval #{params}"

        rollup_type = RollupType.where(code: params[:rollup_type_code]).first
        if rollup_type.blank?
          resp[:error] = "Invalid rollup type #{params[:rollup_type_code]}"
        else
          dg = DispatchGroup.find(params[:dg_id])
          if dg.blank?
            resp[:error] = "Invalid dispatch group #{params[:dg_id]}"
          else
            rollup = rollup_type.rollups.find { |r| r.owner == dg }
            val = params[:val]
            if val.blank?
              rollup.destroy unless rollup.blank?
              resp[:success] = true
            else
              if rollup.blank?
                rollup = Rollup.new({rollup_type: rollup_type, owner: dg})
              end
              rollup.aggregation_interval = val.to_i * 60
              rollup.save

              if rollup.errors.any?
                resp[:error] = rollup.errors.full_messages
              else
                resp[:success] = true
                resp[:val] = to_minutes(rollup.aggregation_interval)
              end
            end
          end
        end

        sendReply(resp)
      end

      def generate_rollup
        resp = {
            success: true
        }

        Rails.logger.info "Generating rollup for #{params}"

        g_params = {
            user_id: current_user.id,
            from_time: Time.parse(params[:from_time]),
            to_time: Time.parse(params[:to_time]),
            item_type: params[:item_type],
            status: "scheduled",
            rollup_type_ids: params[:rollup_type_ids]
        }

        if (g_params[:item_type] == Asset.model_name.to_s)
          g_params[:asset_ids] = params[:asset_ids]
        elsif (g_params[:item_type] == RollupFamily.model_name.to_s)
          g_params[:rollup_family_ids] = params[:rollup_family_ids]
        elsif (g_params[:item_type] == DispatchGroup.model_name.to_s)
          g_params[:dispatch_group_ids] = params[:dispatch_group_ids]
        else
          resp[:success] = false
          resp[:error] = "Unexpected rollup type #{g_params[:item_type]}"
        end

        if (resp[:success])
          rollup_regeneration = RollupRegeneration.new(g_params)
          rollup_regeneration.save

          if rollup_regeneration.errors.any?
            resp[:success] = false
            resp[:error] = rollup_regeneration.errors.full_messages.join('\n')
          else
            begin
              Services::RollupsGeneration.new(rollup_regeneration).generate_rollups
              resp[:success] = true
            rescue Services::RollupsGeneration::UnableToGenerateRollups
              resp[:success] = false
              resp[:error] = I18n.t('asset_rollups.form_generate_rollups.rollup_regeneration_error')
            end
          end
        end

        sendReply(resp)
      end

      def audit_actions
        {
          update_asset_rollup_families: 'Update Asset Rollup Types',
          save_asset_rollup_products: 'Save Asset Rollup Products',
          save_asset_rollup_interval: 'Save Asset Rollup Interval',
          save_dg_rollup_interval: 'Save Dispatch Group Rollup Interval',
          generate_rollup: 'Re-generate Rollups',
        }
      end

      private

      def rollup_for(rollup_type, rollup_family)
        rollup = rollup_type.rollups.find { |r| r.owner_id == rollup_family.id && r.owner_type == RollupFamily.model_name.to_s }
        unless rollup
          rollup = Rollup.new(rollup_type_id: rollup_type.id,
                              owner_type: RollupFamily.model_name.to_s,
                              owner_id: rollup_family.id)
        end
        rollup
      end

      def rollup_for_dg(rollup_type, dispatch_group)
        rollup = rollup_type.rollups.find { |r| r.owner_id == dispatch_group.id && r.owner_type == DispatchGroup.model_name.to_s }
        unless rollup
          rollup = Rollup.new(rollup_type_id: rollup_type.id,
                              owner_type: DispatchGroup.model_name.to_s,
                              owner_id: dispatch_group.id)
        end
        rollup
      end

      def to_minutes(seconds)
        seconds.blank? ? seconds : seconds / 60
      end

      def from_minutes(minutes)
        minutes.blank? ? minutes : minutes * 60
      end


    end
  end
end
