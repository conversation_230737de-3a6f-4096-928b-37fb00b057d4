module Api
  module V1

    class SubpoolController < Api::V1::ApiController

      authorize_actions_for Subpool
      authority_actions subpools: :read,
                        subpool_signals: :read,
                        create_subpool: :create,
                        update_subpool: :create,
                        delete_subpool: :create

      include SubpoolHelper

      def subpools
        collection = Subpool
                         .all
                         .order(name: :asc)
                         .page(params[:page]).per((params[:per_page] || 25)
                                                      .to_i)

        sendReply({
                      # subpools: JSON.parse(collection.to_json(:include => {
                      #     :dispatch_groups => {
                      #         :only => [:id, :name]
                      #     }
                      # })),
                      subpools: collection.map do |s|
                        {
                          id: s.id,
                          name: s.name,
                          dispatch_groups: s.dispatch_groups.map do |dg|
                            {
                              id: dg.id,
                              name: dg.name,
                              label: dg_with_subpool_values(dg),
                              ps_name: "##{dg.id} #{dg.name} (#{dg.tso.name})"
                            }
                          end,
                          signals_list: s.signals_list,
                          implicit: s.implicit?,
                          asset_count: s.implicit? ? 0 : s.assets.try(:size),
                          assets: s.assets.map do |a|
                            {
                                id: a.id,
                                name: a.name,
                                customer_name: a.customer.name,
                                ps_name: "##{a.id} #{a.name} (#{a.customer.name})"
                            }
                          end
                        }
                      end,
                      total_entries: collection.total_count,
                      page: params[:page] || 1
                  })
      end

      def subpool_signals
        collection = Subpool.available_signals
        sendReply({
                      subpool_signals: collection
                  })
      end

      # Processing by SubpoolsController#create as HTML
      #   Parameters: {"utf8"=>"✓", "authenticity_token"=>"bUFGeoJO+Ba7hFpZ+obO3f+Gbr53cue6OO5Q7ckWfYBVaXwJAvshDxvGwFwjRgi0hlhFMYViLAXgMb4BR6GiVg==",
      # "subpool"=>{
      #   "name"=>"r old 1",
      #   "implicit"=>"0",
      #   "signals_list"=>["", "BasePointAverage", "HeadroomResponse", "MRMinusBand"],
      #   "dispatch_group_ids"=>["", "337", "1"],
      #   "asset_ids"=>["", "1571", "1572", "1573"]},
      # "commit"=>"Create Subpool"}
      def create_subpool
        result = {
            success: false,
            messages: []
        }

        p = subpool_params

        x = Subpool.create(p)

        if x.errors.any?
          result[:messages] = x.errors.full_messages
        else
          result[:success] = true
          result[:id] = x.id
        end

        sendReply(result)
      end

      def update_subpool
        result = {
            success: false,
            messages: []
        }

        p = subpool_params

        x = Subpool.update(p[:id], p)

        if x.errors.any?
          result[:messages] = x.errors.full_messages
        else
          result[:success] = true
          result[:id] = x.id
        end

        sendReply(result)
      end

      def delete_subpool
        result = {
            success: true
        }

        Subpool.destroy(params[:id])

        sendReply(result)
      end

      def subpool_params
        params
            .require(:subpool)
            .permit(:id, :name, :implicit, signals_list: [], asset_ids: [], dispatch_group_ids: [])
      end

      def audit_actions
        {
            create_subpool: 'Create Subpool',
            update_subpool: 'Update Subpool',
            delete_subpool: 'Delete Subpool',
        }
      end

    end
  end
end

