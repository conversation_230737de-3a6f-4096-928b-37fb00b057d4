class HealthController < ApplicationController
  skip_before_action :authenticate_user!

  def index
    checks = Services::HealthCheck.checks_with_placeholders.inject([]) do |acc, health_check|
      acc << health_check_to_hash(health_check)
    end
    render json: checks
  end

  def health_check_to_hash(health_check)
    {
      name: health_check.name,
      status: health_check.status == :ok ? 'PASS' : 'FAIL',
      since: health_check.created_at,
      info: [health_check.error_message, health_check.error_backtrace].flatten.select(&:present?)
    }
  end
end
