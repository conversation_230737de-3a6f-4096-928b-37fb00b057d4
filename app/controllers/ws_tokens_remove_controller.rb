class WsTokensRemoveController < ActionController::Base

  before_action :authenticate

  def remove
    # delete without callbacks
    keep_months = ENV['WS_TOKEN_KEEP_MONTHS'] || 3
    Rails.logger.info("WS TOKENS - remove errors logs older than #{keep_months} months")
    WsToken.unscoped.where('expire < ?', keep_months.months.ago).delete_all
    render json: {success: true}
  end

  private

  def authenticate
    is_authenticated = params['token'] == ENV['WS_TOKENS_SECRET_KEY']
    if !is_authenticated
      Rails.logger.info "WS TOKENS REMOVE request not authenticated. Token was #{params['token']}"
      render json: { error: 'Not Authorized' }, status: 401
    end
  end

end