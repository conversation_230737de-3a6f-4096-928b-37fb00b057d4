class V2gOptimizationJobsController < ApplicationController

  include V2gOptimizationJobsHelper

  def download
    job = V2gOptimizationJob.find(params[:id])

    filename = [
      "v2g_optimization_data",
      short_date(job.start_time),
      short_date(job.end_time),
    ].join('_') + '.json'

    data = JSON.pretty_unparse(JSON.parse(job.payload))

    send_data(
      data,
      type: 'application/json',
      disposition: 'attachment',
      filename: filename)

  end

end