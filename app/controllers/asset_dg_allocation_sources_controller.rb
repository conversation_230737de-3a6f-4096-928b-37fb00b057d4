#
class AssetDgAllocationSourcesController < ApplicationController
  authorize_actions_for AssetDgAllocation
  authority_actions download: :read,
                    download_auto_allocation: :read

  #
  # Extract file from the given source
  # and send its contents for download
  #
  def download
    @source =
      AssetDgAllocationSource
      .filter_file_uploads
      .find(params[:id])

    send_data(
      @source.content_file_contents,
      type: 'text/csv',
      disposition: 'attachment',
      filename: @source.content_file_name.presence)
  end

  def download_auto_allocation
    @source =
      AssetDgAllocationSource
      .filter_aas_updates
      .find(params[:id])

    csv = "Time From, Time To, Asset ID, DG ID, Allocate"
    # example of contents for the auto allocation
    # AllocationAPICreation(UserId(0),List(RawAllocationEntry(2019-01-14T09:37:42.257Z,2019-01-14T10:37:42.257Z,1073,35,true), RawAllocationEntry(2019-01-14T09:37:42.257Z,2019-01-14T10:37:42.257Z,288,35,true), RawAllocationEntry(2019-01-14T09:37:42.257Z,2019-01-14T10:37:42.257Z,287,35,true)),None)
    if @source.content.index("RawAllocationEntry") != -1
      contents = @source.content
      raw_allocation_entries =
        contents[contents.index("RawAllocationEntry")..contents.index(")),")].
        split(", ")
        raw_allocation_entries.each_with_index do |rae, index|
          csv += "\n"
          csv += rae["RawAllocationEntry(".length..rae.length-2].
                   sub('true', '1').
                   sub('false', '0')
        end
    end
    send_data(
      csv,
      type: 'text/csv',
      disposition: 'attachment',
      filename: "auto-allocation-#{@source.id}.csv")
  end
end
