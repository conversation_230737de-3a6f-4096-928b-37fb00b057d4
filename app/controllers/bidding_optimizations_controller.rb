class BiddingOptimizationsController < ApplicationController

  include V2gOptimizationJobsHelper

  def download
    o = BiddingOptimization.find(params[:id])
    s = o.value

    filename = [
      "bidding_optimization_data",
      short_date(o.optimization_interval_start),
      short_date(o.optimization_interval_end),
    ].join('_') + '.json'

    data = JSON.pretty_unparse(JSON.parse(o.value))

    send_data(
      data,
      type: 'application/json',
      disposition: 'attachment',
      filename: filename)

  end
end
