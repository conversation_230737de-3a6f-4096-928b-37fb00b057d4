module ApplicationCable
  class Connection < ActionCable::Connection::Base
    identified_by :current_user

    def connect
      self.current_user = find_verified_user
      logger.add_tags 'ActionCable', current_user.name
    end

    def session
      cookies.encrypted[Rails.application.config.session_options[:key]]
    end

    protected
      def find_verified_user
        user_id = session['current_user_id']
        if verified_user = User.find_by(id: user_id, deleted: false)
          #Rails.logger.info('Verified user with id ' + user_id.to_s)
          verified_user
        else
          Rails.logger.error('Failed to verify user with id ' + (user_id.present? ? user_id.to_s : '<nil>' + ' in session ' + session.to_s))
          reject_unauthorized_connection
        end
      end
  end
end