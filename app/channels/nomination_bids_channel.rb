class NominationBidsChannel < ApplicationCable::Channel

  CHANNEL_NAME = 'nomination_bids_channel'.freeze

  def subscribed
    topic = "#{params[:topic]}"

    # broadcast future messages
    stream_from topic
  end

  def unsubscribed
  end

  def receive(message)
    Rails.logger.debug "NominationBidsChannel received message #{message}"
  end

  def self.topic_name(market_name:, delivery_date:)
    "#{market_name}_#{delivery_date.strftime('%d_%m_%Y')}"
  end
end