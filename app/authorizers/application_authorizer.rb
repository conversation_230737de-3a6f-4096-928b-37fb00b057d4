class ApplicationAuthorizer < Authority::Authorizer

  def self.default(adjective, user)
    # 'Whitelist' strategy: defaults to everything is forbidden
    false
  end

  # generate methods for each default abilities defined in the initializer
  # (see initializers/authority.rb)
  Authority.abilities.each do |verb, adjective|
    method_name = "#{adjective}_by?"

    define_method(method_name) do |user|
      has_ability?(user, [:manage, verb])
    end

    define_singleton_method(method_name) do |user|
      has_class_ability?(user, [:manage, verb], self.to_s.gsub('Authorizer', '').constantize)
    end
  end

  def self.authorizes_to_manage_plant?(user, options = {})
    user.has_privilege?(:super_admin) || user.has_privilege?(:asset_manage_plant)
  end

  #TODO: remove market_access
  def self.authorizes_to_market_access?(user, options = {})
    user.has_privilege?(:super_admin) || user.has_privilege?(:market_access)
  end

  def self.authorizes_to_auction_results_read?(user, options = {})
    user.has_privilege?(:super_admin) || user.has_privilege?(:auction_results_read)
  end

  def self.authorizes_to_auction_results_create?(user, options = {})
    user.has_privilege?(:super_admin) || user.has_privilege?(:auction_results_create)
  end

  def self.authorizes_to_tso_reports_read?(user, options = {})
    user.has_privilege?(:super_admin) || user.has_privilege?(:tso_reports_read)
  end

  def self.authorizes_to_tso_reports_create?(user, options = {})
    user.has_privilege?(:super_admin) || user.has_privilege?(:tso_reports_create)
  end

  def self.authorizes_to_reporting_and_notification_read?(user, options = {})
    user.has_privilege?(:super_admin) || user.has_privilege?(:reporting_and_notification_read)
  end

  def self.authorizes_to_reporting_and_notification_create?(user, options = {})
    user.has_privilege?(:super_admin) || user.has_privilege?(:reporting_and_notification_create)
  end

  def self.authorizes_to_scheduling_reports_create?(user, options = {})
    user.has_privilege?(:super_admin) || user.has_privilege?(:scheduling_reports_create)
  end

  protected

    def self.has_class_ability?(user, abilities, resource_class)
      user.has_privilege?(:super_admin) ||
        has_privilege?(user, abilities, resource_class)
    end

    def has_ability?(user, abilities)
      user.has_privilege?(:super_admin) ||
        self.class.has_privilege?(user, abilities, resource.class) ||
        (self.class.has_privilege?(user, abilities, resource.class, true) && user.owner_of?(resource))
    end

    def self.has_privilege?(user, abilities, resource_class, own = false)
      abilities.each do |ability|
        ability = ability.to_s + "_own" if own
        if user.has_privilege?(privilege_for(ability, resource_class))
          return true
        end
      end
      false
    end

    def self.privilege_for(ability, resource_class)
      resource_class.to_s.underscore + "_" + ability.to_s
    end

end
