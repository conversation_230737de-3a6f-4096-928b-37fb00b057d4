#
class DistributedUnitDecorator < Draper::Decorator
  delegate_all

  def start_time
    format_time(object.start_time)
  end

  def end_time
    format_time(object.end_time)
  end

  private

  def format_time(original_timestamp)
    tso = object.dispatch_group.tso
    t = original_timestamp.in_time_zone(Time.zone.name)
    show_year = t.year != Time.now.year
    year = show_year ? " #{t.to_date.year}" : ''

    "#{l(t.to_date, format: :short)} #{t.to_formatted_s(:time)}#{year}"
  end
end
