#
class LocalizedDecorator < Draper::Decorator
  cattr_accessor :raise_missing_translation

  def self.localize_attribute_as(attribute, as)
    define_method as do
      begin
        if object.send(attribute).present?
          I18n.t(object.send(attribute),
                scope: "activerecord.attributes.#{object.class.model_name.i18n_key}.#{as}",
                raise: true)
        end
      rescue I18n::MissingTranslationData => e
        raise e if self.class.raise_missing_translation
        object.send(attribute)
      end
    end
  end
end
