class GenericSteeringConfigData < ActiveResource::Base
end

class GenericSteeringConfig < ActiveResource::Base

  include Authority::Abilities

  self.site = ENV['ASSET_SCHEDULE_SERVICE_URL']

  self.collection_name = 'genericsteeringconfig'

  BASEPOINT_OPTS = [:NoBasepoint, :HasBasepoint, :HasBasepointAndPbp]
  SETPOINT_TYPE_OPTS = [:Boolean, :<PERSON><PERSON><PERSON>s]
  SETPOINT_VALUE_TYPE_OPTS = [:Relative, :Absolute]
  SETPOINT_INTERVAL_TYPE_OPTS = [:Continuous, :OnOff, :Steps]
  SCHEDULE_TYPE_OPTS = [:Production, :Flexibility]
  HEARTBEAT_TYPE_OPTS = [:ReadWrite, :Read, :Write]
  FLEX_TYPE_OPTS = [:Positive, :Negative, :Both]
  DEFAULT_ABSOLUTE_SETPOINT_OPTS = [:ImposedPMax, :ImposedPMin, :PNorm]

  def initialize(attributes = {}, persisted = false)
    if attributes && attributes['config']
      if !attributes['config']['default_absolute_setpoint'] then
        attributes['config']['default_absolute_setpoint'] = 'Default'
      end
      if !attributes['config']['allow_steering_by_schedule'] then
        attributes['config']['allow_steering_by_schedule'] = false
      end
    end

    super(attributes, persisted)
  end


  def as_hash
    c = attributes[:config]

    h = {
      id: id,
      name: c.name,
      hasBasepoint: c.has_basepoint,
      scheduleType: c.schedule_type,
      flexType: c.flex_type,
      setpointValueType: c.setpoint_value_type,
      hasSetpointFeedback: c.has_setpoint_feedback == true || c.has_setpoint_feedback == 'true',
      setpointType: c.setpoint_type,
      setpointIntervalType: c.setpoint_interval_type,
      setpointFrequencySeconds: c.setpoint_frequency_seconds.to_i,
      hasLock: c.has_lock == true || c.has_lock == 'true',
      hasDispatchedDeviated: c.has_dispatched_deviated == true || c.has_dispatched_deviated == 'true',
      hasHeartbeatVPP: c.has_heartbeat_vpp == true || c.has_heartbeat_vpp == 'true',
      heartbeatVPPFrequencySeconds: c.heartbeat_vpp_frequency_seconds.to_i,
      heartbeatVPPType: c.heartbeat_vpp_type,
      hasHeartbeatAsset: c.has_heartbeat_asset == true || c.has_heartbeat_asset == 'true',
      heartbeatAssetFrequencySeconds: c.heartbeat_asset_frequency_seconds.to_i,
      heartbeatAssetType: c.heartbeat_asset_type,
      defaultAbsoluteSetpoint: 'Default'
    }

    if c.respond_to?(:default_absolute_setpoint) then
      h[:defaultAbsoluteSetpoint] = c.default_absolute_setpoint
    end

    if c.respond_to?(:allow_steering_by_schedule) then
      h[:allowSteeringBySchedule] = c.allow_steering_by_schedule == true || c.allow_steering_by_schedule == 'true'
    end

    h
  end

  def as_json(options = {})
    as_hash.as_json(options)
  end

  def self.basepoints_for_select
    array_for_select(:basepoint, BASEPOINT_OPTS)
  end

  def self.schedules_for_select
    array_for_select(:schedule, SCHEDULE_TYPE_OPTS)
  end

  def self.flexes_for_select
    array_for_select(:flex, FLEX_TYPE_OPTS)
  end

  def self.setpoint_relativities_for_select
    array_for_select(:setpoint_relativity, SETPOINT_VALUE_TYPE_OPTS)
  end

  def self.setpoint_units_for_select
    array_for_select(:setpoint_unit, SETPOINT_TYPE_OPTS)
  end

  def self.default_absolute_setpoints_for_select
    array_for_select(:default_absolute_setpoint, DEFAULT_ABSOLUTE_SETPOINT_OPTS)
  end

  def self.setpoint_locks_for_select
    boolean_for_select(:has_setpoint_lock)
  end

  def self.setpoint_intervals_for_select
    array_for_select(:setpoint_interval, SETPOINT_INTERVAL_TYPE_OPTS)
  end

  def self.yes_nos_for_select
    boolean_for_select(:yes_no)
  end

  def self.heartbeat_vpp_authorizations_for_select
    array_for_select(:heartbeat_authorization, HEARTBEAT_TYPE_OPTS)
  end

  def self.heartbeat_asset_authorizations_for_select
    array_for_select(:heartbeat_authorization, HEARTBEAT_TYPE_OPTS)
  end

  def self.t(key)
    I18n.t("activerecord.attributes.#{model_name.i18n_key}.#{key}")
  end


  def self.array_for_select(attr_name, array)
    array.map do |v|
      [t("#{attr_name.to_s.pluralize}.#{v}"), v]
    end
  end

  def self.boolean_for_select(attribute_name)
    [true, false].map do |v|
      [t("#{attribute_name.to_s.pluralize}.#{v}"), v]
    end
  end
end