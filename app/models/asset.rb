#
# Represents an Asset entity
#
class Asset < ApplicationRecord

  TNO_ZONES = ["Northern Scotland", "Southern Scotland", "Northern", "North West", "Yorkshire",
               "N Wales & Mersey", "East Midlands", "Midlands", "Eastern", "South Wales",
               "South East", "London", "Southern", "South Western"].each_with_index.map {|x, i| ["#{i + 1} #{x}", (i + 1).to_s]}

  belongs_to :customer
  belongs_to :asset_type, foreign_key: :asset_type
  belongs_to :tso
  has_and_belongs_to_many :contracts, join_table: 'contract_asset'
  has_many :asset_key_value_stores
  has_many :prequalifications
  has_many :nomination_bids

  default_scope -> { where('asset.deleted IS NULL OR asset.deleted > CURRENT_TIMESTAMP') }
  scope :not_deleted, -> { where('asset.deleted IS NULL OR asset.deleted > CURRENT_TIMESTAMP') }
  scope :with_prequalifications, -> {includes(prequalifications: [prequalified_products: [:product]])}
  scope :with_key_value_stores, -> {includes(:asset_key_value_stores)}

  def minimum_charge_limit
    key_value_store(:minimum_charge_limit)
  end

  def maximum_charge_limit
    key_value_store(:maximum_charge_limit)
  end

  def short_name
    key_value_store(:short_name)
  end

  def key_value_store(attribute)
    asset_key_value_stores.find {|kvs| kvs.key.underscore == attribute.to_s}
  end

  def to_label
    "##{id} #{name} (#{customer.name})"
  end

  def id_and_name
    "##{id} #{name}"
  end

  def product_prequalified_flex(product)
    preq_prod = prequalified_for.find do |pp|
      pp = pp.first if pp.kind_of?(Array)
      pp.product_id == product.id
    end
    if preq_prod
      preq_prod.prequalified_flex
    else
      nil
    end
  end

  # arrange prequalifications in chains based on parent_id
  def prequalification_chains
    @pq_chains ||= begin
      root_preqs = self.prequalifications.select{|p| p.parent_id.nil?}
      child_preqs = self.prequalifications - root_preqs
      preq_chains = root_preqs.map{|p| [p]}
      while !child_preqs.empty? do
        # place children preqs in chains
        found_children = []
        preq_chains.each do |c|
          if child = child_preqs.find{|p| p.parent_id == c.last.id}
            c.push(child)
            found_children.push(child)
          end
        end
        child_preqs = child_preqs - found_children
      end
      preq_chains
    end
  end

  def prequalified_for
    today = Time.now.to_date
    pq_products = []
    prequalification_chains.each do |c|
      if c.size == 1
        pq_products.concat(c.last.prequalified_products)
      else
        c.last.prequalified_products.each do |pp|
          if pp.prequalification_date.in_time_zone(Time.zone.name).to_date > today
            # pp pq is in the future
            now_pp = c[c.size - 2].prequalified_products.find{|npp| npp.product.id == pp.product.id}
            now_pp.next_in_chain = pp
            pq_products.push([now_pp, pp])
          else
            pq_products.push(pp)
          end
        end
      end
    end
    pq_products
  end

end
