class NominationBid < ApplicationRecord

  include Authority::Abilities

  belongs_to :product
  belongs_to :tso
  belongs_to :nomination_bid_source , foreign_key: 'source_id', class_name: 'NominationBidSource'
  belongs_to :asset, optional: true
  belongs_to :auction_config, foreign_key: 'auction_rules_id', class_name: 'AuctionConfig', optional: true

  default_scope {where("nomination_bid.deleted is null")}

  # make sure we never try to update the data in the DB
  def create; end
  def save; end
  def update; end

  def self.bids_by_date(start_date:, end_date:)
    where('(start_time, end_time) overlaps (?, ?)', start_date, end_date)
  end

  def self.bids_between_date_times(start_time: nil, end_time: nil)
    where('(start_time, end_time) overlaps (?, ?)', start_time, end_time)
  end

  def self.bids_by_date_including_meta(start_date:, end_date:)
    bids_by_date(start_date: start_date, end_date: end_date)
    .select('nomination_bid.*')
    .select('market.id as market_id, market.name as market_name')
    .select('product.name as product_name')
    .select('tso.id as tso_id, tso.name as tso_name, tso.tso_id as tso_tso_id')
    .select('CASE WHEN nomination_bid.accepted_flex_volume = 0 THEN \'rejected\' WHEN nomination_bid.accepted_flex_volume < nomination_bid.flex_volume THEN \'partial\' WHEN nomination_bid.accepted_flex_volume >= nomination_bid.flex_volume THEN \'accepted\' ELSE \'pending\' END as bid_status')
    .joins('left join product on product.id = nomination_bid.product_id')
    .joins('left join market on market.id = product.market_id')
    .joins('left join tso on nomination_bid.tso_id = tso.id')
    .where('nomination_bid.deleted IS NULL')
  end

  def self.bids_between_date_times_including_meta(start_time: nil, end_time: nil, asset_ids:)
    selected_bids = bids_between_date_times(start_time: start_time, end_time: end_time)
    .select('nomination_bid.*')
    .select('vpp_trading_auction_rules.id as auction_config_id, vpp_trading_auction_rules.name as auction_config_name')
    .select('product.name as product_name')
    .select('tso.id as tso_id, tso.name as tso_name, tso.tso_id as tso_tso_id')
    .select('CASE WHEN nomination_bid.accepted_flex_volume = 0 THEN \'rejected\' WHEN nomination_bid.accepted_flex_volume < nomination_bid.flex_volume THEN \'partial\' WHEN nomination_bid.accepted_flex_volume >= nomination_bid.flex_volume THEN \'accepted\' ELSE \'pending\' END as bid_status')
    .joins('left join product on product.id = nomination_bid.product_id')
    .joins('left join vpp_trading_auction_rules on vpp_trading_auction_rules.id = nomination_bid.auction_rules_id')
    .joins('left join tso on nomination_bid.tso_id = tso.id')
    .includes(asset: [:asset_key_value_stores])
    .where('nomination_bid.deleted IS NULL')
    if asset_ids.present?
      selected_bids = selected_bids.where(asset_id: asset_ids)
    end
    selected_bids
  end

  def self.bids_by_auction_config_and_date(auction_config_id:, asset_ids:, start_date:, end_date:, only_bids_without_asset:)
    selected_bids = bids_by_date(start_date: start_date, end_date: end_date)
    .joins('left join product on product.id = nomination_bid.product_id')
    .joins('left join market on market.id = product.market_id')
    .includes(asset: [:asset_key_value_stores])
    .includes(:auction_config)
    if auction_config_id.present?
      selected_bids = selected_bids.where(auction_rules_id: auction_config_id)
    end
    if only_bids_without_asset == "true"
      selected_bids = selected_bids.where(asset_id: nil)
    elsif asset_ids.present?
      selected_bids = selected_bids.where(asset_id: asset_ids)
    else
      selected_bids = selected_bids.where('asset_id IS NOT NULL')
    end
    selected_bids
  end

  def self.eon_bids_by_auction_config_and_date(auction_config_id:, asset_ids:, start_date:, end_date:, only_bids_without_asset:)
    bids_by_auction_config_and_date(auction_config_id: auction_config_id, asset_ids: asset_ids,
        start_date: start_date, end_date: end_date,
        only_bids_without_asset: only_bids_without_asset)
      .where('customer_id is null')
  end

  def product_interval(with_minutes = false)
    self.product.product_interval(tz, start_time, end_time, with_minutes)
  end

  def start_time_in_tz
    self.start_time.in_time_zone(tz)
  end

  def tz
    tz = Time.zone.name
    if auction_config.present?
      @h ||= HashCaseConverter.to_underscore(auction_config.value).deep_symbolize_keys
      tz = @h[:time_zone]
    else
      tz = tso.time_zone
    end
    tz
  end

end