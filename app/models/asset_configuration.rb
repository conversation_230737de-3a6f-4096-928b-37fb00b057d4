class AssetConfiguration < ApplicationRecord

  include Authority::Abilities

  paginates_per 20
  PROTOCOLS = ["modbus", "iec104", "eclipse scada", "web services", "opc xml da", "thrift"]
  PORTS = {
    :modbus => "502",
    :iec104 => "2404",
    :opc_xml_da => "8070",
    :thrift => "9090",
    :web_services => ""
  }
  VPN = {
    :modbus => "openvpn",
    :iec104 => "openvpn",
    :eclipse_scada => "openvpn",
    :opc_xml_da => "openvpn",
    :thrift => "openvpn",
    :web_services => "ip sec"
  }
  belongs_to :customer

  validates :box_contact_person_phone_number, format: { with: /\A\+[0-9]+\z/, message: :phone_number_invalid },
            if: Proc.new { |asset_config| !asset_config.box_contact_person_phone_number.blank? }

  validates :box_contact_person_email, format: { with: /\A([^@\s]+)@((?:[-a-z0-9]+\.)+[a-z]{2,})\Z/i, allow_blank: true }

  def signal_asset_type
    if self.signal_asset_type_id
      AssetConfigurationSignalAssetType.find(self.signal_asset_type_id)
    end
  end

  def self.vpns
    VPN
  end

  def self.ports
    PORTS
  end

  def self.protocols
    PROTOCOLS
  end
end
