module GenericDgConfig
  extend ActiveSupport::Concern

  included do
    before_save :build_generic_config_jspn, if: Proc.new { |dg| dg.generic_config_enabled && !dg.generic_config_json_override }

    attr_accessor :is_generic_config

    attr_accessor :export_entrader, #Boolean
      :entrader_treshold_update, #Double
      :entrader_lead_time_update, #Double
      :entrader_upload_folder#String

    validates :entrader_treshold_update, presence: true,
      numericality: true,
      if: Proc.new { |dg| dg.is_generic_config && dg.allows_export_entrader? && dg.export_entrader.present? }
    validates :entrader_lead_time_update, presence: true,
      numericality: { only_integer: true },
      if: Proc.new { |dg| dg.is_generic_config && dg.allows_export_entrader? && dg.export_entrader.present? }
    validates :entrader_upload_folder, presence: true,
      if: Proc.new { |dg| dg.is_generic_config && dg.allows_export_entrader? && dg.export_entrader.present? }

    def build_entrader_upload
      h = {}
      if self.export_entrader.present?
        h = {
          threshold_kw: (self.entrader_treshold_update.try(:to_f) || 0) * 1000,
          lead_time_seconds: (self.entrader_lead_time_update.try(:to_i) || 0) * 60,
          upload_folder: self.entrader_upload_folder
        }
        HashCaseConverter.to_camel_case({
          entrader_upload: h
        })
      else
        h
      end
    end
    def set_entrader_upload(generic_conf)
      entrader_upload = generic_conf[:entrader_upload]
      if entrader_upload
        self.export_entrader = true
        self.entrader_treshold_update = (entrader_upload[:threshold_kw].try(:to_f) || 0) / 1000.0
        self.entrader_lead_time_update = (entrader_upload[:lead_time_seconds].try(:to_i) || 0) / 60
        self.entrader_upload_folder = entrader_upload[:upload_folder]
      end
    end

    attr_accessor :has_exclusive_behaviour,
      :has_nomination, #Boolean
      :has_preceding_basepoint #Boolean

    def build_basic_behaviour
      HashCaseConverter.to_camel_case({
        basic_behaviour: {
          has_exclusive_behaviour: self.has_exclusive_behaviour || false,
          has_nomination: self.has_nomination || false,
          has_preceding_basepoint: self.has_preceding_basepoint || false
        }
      })
    end
    def set_basic_behaviour(generic_conf)
      basic_behaviour = generic_conf[:basic_behaviour]
      self.has_exclusive_behaviour = basic_behaviour[:has_exclusive_behaviour]
      self.has_nomination = basic_behaviour[:has_nomination]
      self.has_preceding_basepoint = basic_behaviour[:has_preceding_basepoint]
    end

    #assetActivationType: AssetActivationType
    attr_accessor :asset_activation_type, #ActivationRange(frequencyLocalSteeringParameters), Basepoint (with NotSupported), Setpoint
      :aat_ar_flsp_dead_band_pos, #Double
      :aat_ar_flsp_dead_band_neg, #Double
      :aat_ar_flsp_max_frequency_deviation_pos, #Double
      :aat_ar_flsp_max_frequency_deviation_neg, #Double
      :aat_ar_flsp_high_knee_joint, #Double
      :aat_ar_flsp_low_knee_joint, #Double
      :state_of_charge_management,
      :scm_dc_target_state_of_charge_low, #Double
      :scm_dc_target_state_of_charge_high, #Double
      :scm_dc_target_state_of_charge_both, #Double
      :scm_dc_foot_room, #Percentage
      :scm_dc_head_room, #Percentage
      :scm_dc_ramp_rate_limit, #Percentage
      :scm_dc_energy_reserve, #Double
      :scm_dc_min_energy_recovery, #Double
      :scm_dc_dead_band_factor, #Percentage
      :scm_vb_dc_delivery_duration, #Double
      :scm_vb_dc_delivery_duration_buffer, #Double
      :scm_vb_dc_min_energy_recovery, #Percentage
      :scm_vb_dm_delivery_duration, #Double
      :scm_vb_dm_delivery_duration_buffer, #Double
      :scm_vb_dm_min_energy_recovery, #Percentage
      :scm_vb_dr_delivery_duration, #Double
      :scm_vb_dr_delivery_duration_buffer, #Double
      :scm_vb_dr_min_energy_recovery, #Percentage
      :scm_fcr_lower_soc_limit,  #Percentage
      :scm_fcr_upper_soc_limit, #Percentage
      :scm_opt_sp_duration_minutes #Integer

    validates :asset_activation_type, presence: true,
      if: Proc.new { |dg| dg.is_generic_config }
    validates :aat_ar_flsp_dead_band_pos, :aat_ar_flsp_dead_band_neg,
      :aat_ar_flsp_max_frequency_deviation_pos, :aat_ar_flsp_max_frequency_deviation_neg,
      numericality: true,
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.asset_activation_type.present? &&
        dg.asset_activation_type[0][:id] == "activationRange" && 
        !(dg.market_id == Market.dynamic_containment.id || dg.market_id == Market.dynamic_moderation.id || dg.market_id == Market.dynamic_regulation.id)
      }
    validates :aat_ar_flsp_high_knee_joint, :aat_ar_flsp_low_knee_joint, allow_blank: true,
      numericality: true,
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.asset_activation_type.present? &&
        dg.asset_activation_type[0][:id] == "activationRange" &&
        !(dg.market_id == Market.dynamic_containment.id || dg.market_id == Market.dynamic_moderation.id || dg.market_id == Market.dynamic_regulation.id)
      }
    validates :scm_dc_energy_reserve, :scm_dc_min_energy_recovery,
      numericality: true,
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.state_of_charge_management.present? &&
        (dg.state_of_charge_management.collect{|s| s[:id]} & ["dynamicContainment", "dynamicContainmentLow", "optimizedDc"]).present?
      }
    validates :scm_dc_target_state_of_charge_low,
      numericality: { greater_than_or_equal_to: 0 },
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.state_of_charge_management.present? &&
        (dg.state_of_charge_management.collect{|s| s[:id]} & ["dynamicContainment", "dynamicContainmentLow"]).present?
      }
    validates :scm_dc_target_state_of_charge_high, :scm_dc_target_state_of_charge_both,
      numericality: { greater_than_or_equal_to: 0 },
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.state_of_charge_management.present? &&
        dg.state_of_charge_management.collect{|s| s[:id]}.include?("dynamicContainment")
      }
    validates :scm_dc_foot_room,
      numericality: { greater_than_or_equal_to: 0 },
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.state_of_charge_management.present? &&
        (dg.state_of_charge_management.collect{|s| s[:id]} & ["dynamicContainment", "dynamicContainmentLow", "optimizedDc"]).present?
      }
    validates :scm_dc_head_room,
      numericality: { greater_than_or_equal_to: 0 },
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.state_of_charge_management.present? &&
        (dg.state_of_charge_management.collect{|s| s[:id]} & ["dynamicContainment", "optimizedDc"]).present?
      }

    validates :scm_dc_target_state_of_charge_low, :scm_dc_target_state_of_charge_high, :scm_dc_target_state_of_charge_both,
              :scm_dc_foot_room, :scm_dc_head_room,
      numericality: { greater_than_or_equal_to: 0 },
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.state_of_charge_management.present? &&
        dg.state_of_charge_management.collect{|s| s[:id]}.include?("dynamicContainment")
      }
    validates :scm_dc_target_state_of_charge_low, :scm_dc_foot_room,
      numericality: { greater_than_or_equal_to: 0 },
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.state_of_charge_management.present? &&
        dg.state_of_charge_management.collect{|s| s[:id]}.include?("dynamicContainmentLow")
      }
    validates :scm_dc_ramp_rate_limit,
      numericality: { greater_than_or_equal_to: 0 },
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.state_of_charge_management.present? &&
        (dg.state_of_charge_management.collect{|s| s[:id]} & ["dynamicContainment", "dynamicContainmentLow", "optimizedDc"]).present?
      }
    validates :scm_dc_dead_band_factor,
      numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 100 },
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.state_of_charge_management.present? &&
        (dg.state_of_charge_management.collect{|s| s[:id]} & ["dynamicContainment", "dynamicContainmentLow"]).present?
      }

    validates :scm_vb_dc_delivery_duration, :scm_vb_dm_delivery_duration, :scm_vb_dr_delivery_duration,
        numericality: { only_integer: true, greater_than_or_equal_to: 0 },
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.state_of_charge_management.present? &&
        dg.state_of_charge_management.collect{|s| s[:id]}.include?("volumeBaseline")
      }
    validates :scm_vb_dc_delivery_duration_buffer, :scm_vb_dm_delivery_duration_buffer, :scm_vb_dr_delivery_duration_buffer,
        numericality: { greater_than_or_equal_to: 0 },
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.state_of_charge_management.present? &&
        dg.state_of_charge_management.collect{|s| s[:id]}.include?("volumeBaseline")
      }
    validates :scm_vb_dc_min_energy_recovery, :scm_vb_dm_min_energy_recovery, :scm_vb_dr_min_energy_recovery,
        numericality: { only_integer: true, greater_than_or_equal_to: 0, less_than_or_equal_to: 100 },
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.state_of_charge_management.present? &&
        dg.state_of_charge_management.collect{|s| s[:id]}.include?("volumeBaseline")
      }

    validates :scm_fcr_lower_soc_limit,
      numericality: { greater_than_or_equal_to: 0 },
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.state_of_charge_management.present? &&
        dg.state_of_charge_management.collect{|s| s[:id]}.include?("fcrNl")
      }
    validates :scm_fcr_upper_soc_limit,
      numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 100 },
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.state_of_charge_management.present? &&
        dg.state_of_charge_management.collect{|s| s[:id]}.include?("fcrNl")
      }
    validates :scm_opt_sp_duration_minutes,
      numericality: { greater_than_or_equal_to: 0, only_integer: true },
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.state_of_charge_management.present? &&
        dg.state_of_charge_management.collect{|s| s[:id]}.include?("optimizationPassThrough")
      }

    def build_asset_activation_type
      h = {}
      asset_activation_type_id = self.asset_activation_type[0][:id]
      if asset_activation_type_id == 'activationRange' && 
        !(self.market_id == Market.dynamic_containment.id || self.market_id == Market.dynamic_moderation.id || self.market_id == Market.dynamic_regulation.id)
        h[:frequency_local_steering_parameters] = {
          dead_band_neg_hz: self.aat_ar_flsp_dead_band_neg,
          dead_band_pos_hz: self.aat_ar_flsp_dead_band_pos,
          max_frequency_deviation_neg_hz: self.aat_ar_flsp_max_frequency_deviation_neg,
          max_frequency_deviation_pos_hz: self.aat_ar_flsp_max_frequency_deviation_pos,
          high_knee_joint_hz: self.aat_ar_flsp_high_knee_joint,
          low_knee_joint_hz: self.aat_ar_flsp_low_knee_joint,
        }
      end
      HashCaseConverter.to_camel_case({
        asset_activation_type: {
          "#{asset_activation_type_id}": h
        }
      })
    end
    def set_asset_activation_type(generic_conf)
      aat = generic_conf[:asset_activation_type]
      aat_id = aat.keys.first
      self.asset_activation_type = [aat_id.to_s.camelize(:lower)]
      if aat_id.to_s.camelize(:lower) == 'activationRange' && 
        !(self.market_id == Market.dynamic_containment.id || self.market_id == Market.dynamic_moderation.id || self.market_id == Market.dynamic_regulation.id)
        flsp = aat[aat_id][:frequency_local_steering_parameters]
        self.aat_ar_flsp_dead_band_neg = flsp[:dead_band_neg_hz]
        self.aat_ar_flsp_dead_band_pos = flsp[:dead_band_pos_hz]
        self.aat_ar_flsp_max_frequency_deviation_neg = flsp[:max_frequency_deviation_neg_hz]
        self.aat_ar_flsp_max_frequency_deviation_pos = flsp[:max_frequency_deviation_pos_hz]
        self.aat_ar_flsp_high_knee_joint = flsp[:high_knee_joint_hz]
        self.aat_ar_flsp_low_knee_joint = flsp[:low_knee_joint_hz]
      end
    end

    def build_state_of_charge_management
      h = {}
      h[:state_of_charge_management] = nil
      if self.state_of_charge_management
        if self.state_of_charge_management.collect{|s| s[:id]}.include?("dynamicContainment")
          h[:state_of_charge_management] = {
            dynamic_containment: {
              target_state_of_charge_low: self.scm_dc_target_state_of_charge_low,
              target_state_of_charge_high: self.scm_dc_target_state_of_charge_high,
              target_state_of_charge_both: self.scm_dc_target_state_of_charge_both,
              foot_room: self.scm_dc_foot_room,
              head_room: self.scm_dc_head_room,
              ramp_rate_limit: self.scm_dc_ramp_rate_limit,
              energy_reserve_in_seconds: self.scm_dc_energy_reserve,
              min_energy_recovery_in_seconds: self.scm_dc_min_energy_recovery,
              dead_band_factor: self.scm_dc_dead_band_factor,
            }
          }
        elsif self.state_of_charge_management.collect{|s| s[:id]}.include?("dynamicContainmentLow")
          h[:state_of_charge_management] = {
            dynamic_containment_low: {
              target_state_of_charge: self.scm_dc_target_state_of_charge_low,
              foot_room: self.scm_dc_foot_room,
              ramp_rate_limit: self.scm_dc_ramp_rate_limit,
              energy_reserve_in_seconds: self.scm_dc_energy_reserve,
              min_energy_recovery_in_seconds: self.scm_dc_min_energy_recovery,
              dead_band_factor: self.scm_dc_dead_band_factor,
            }
          }
        elsif self.state_of_charge_management.collect{|s| s[:id]}.include?("optimizedDc")
          h[:state_of_charge_management] = {
            optimized_dc: {
              foot_room: self.scm_dc_foot_room,
              head_room: self.scm_dc_head_room,
              ramp_rate_charge_limit: self.scm_dc_ramp_rate_limit,
              ramp_rate_discharge_limit: self.scm_dc_ramp_rate_limit,
              energy_reserve_in_seconds: self.scm_dc_energy_reserve,
              min_energy_recovery_in_seconds: self.scm_dc_min_energy_recovery,
            }
          }
        elsif self.state_of_charge_management.collect{|s| s[:id]}.include?("volumeBaseline")
          h[:state_of_charge_management] = {
            volume_baseline: {
              dc: {
                delivery_duration_in_seconds: self.scm_vb_dc_delivery_duration,
                delivery_duration_buffer: self.scm_vb_dc_delivery_duration_buffer,
                min_energy_recovery: self.scm_vb_dc_min_energy_recovery
              },
              dm: {
                delivery_duration_in_seconds: self.scm_vb_dm_delivery_duration,
                delivery_duration_buffer: self.scm_vb_dm_delivery_duration_buffer,
                min_energy_recovery: self.scm_vb_dm_min_energy_recovery
              },
              dr: {
                delivery_duration_in_seconds: self.scm_vb_dr_delivery_duration,
                delivery_duration_buffer: self.scm_vb_dr_delivery_duration_buffer,
                min_energy_recovery: self.scm_vb_dr_min_energy_recovery
              }
            }
          }
        elsif self.state_of_charge_management.collect{|s| s[:id]}.include?("fcrNl")
          h[:state_of_charge_management] = {
            fcr_nl: {
              lower_soc_limit: self.scm_fcr_lower_soc_limit,
              upper_soc_limit: self.scm_fcr_upper_soc_limit,
            }
          }
        elsif self.state_of_charge_management.collect{|s| s[:id]}.include?("optimizationPassThrough")
          h[:state_of_charge_management] = {
            optimization_pass_through: {
              sp_duration_minutes: self.scm_opt_sp_duration_minutes.try(:to_i)
            }
          }
        end
      end
      HashCaseConverter.to_camel_case(h)
    end
    def set_state_of_charge_management(generic_conf)
      self.state_of_charge_management = []
      scm = generic_conf[:state_of_charge_management]
      puts "#### SCM #{scm.inspect}"
      if scm && scm[:dynamic_containment]
        self.state_of_charge_management << {id: "dynamicContainment", name: "Dynamic Containment"}
        dc = scm[:dynamic_containment] || {}
        self.scm_dc_target_state_of_charge_low = dc[:target_state_of_charge_low]
        self.scm_dc_target_state_of_charge_high = dc[:target_state_of_charge_high]
        self.scm_dc_target_state_of_charge_both = dc[:target_state_of_charge_both]
        self.scm_dc_foot_room = dc[:foot_room]
        self.scm_dc_head_room = dc[:head_room]
        self.scm_dc_ramp_rate_limit = dc[:ramp_rate_limit]
        self.scm_dc_energy_reserve = dc[:energy_reserve_in_seconds]
        self.scm_dc_min_energy_recovery = dc[:min_energy_recovery_in_seconds]
        self.scm_dc_dead_band_factor = dc[:dead_band_factor]
      end
      if scm && scm[:dynamic_containment_low]
        self.state_of_charge_management << {id: "dynamicContainmentLow", name: "Dynamic Containment Low"}
        dc = scm[:dynamic_containment_low] || {}
        self.scm_dc_target_state_of_charge_low = dc[:target_state_of_charge]
        self.scm_dc_foot_room = dc[:foot_room]
        self.scm_dc_ramp_rate_limit = dc[:ramp_rate_limit]
        self.scm_dc_energy_reserve = dc[:energy_reserve_in_seconds]
        self.scm_dc_min_energy_recovery = dc[:min_energy_recovery_in_seconds]
        self.scm_dc_dead_band_factor = dc[:dead_band_factor]
      end
      if scm && scm[:optimized_dc]
        self.state_of_charge_management << {id: "optimizedDc", name: "Variable SoE"}
        odc = scm[:optimized_dc] || {}
        self.scm_dc_foot_room = odc[:foot_room]
        self.scm_dc_head_room = odc[:head_room]
        self.scm_dc_ramp_rate_limit = odc[:ramp_rate_charge_limit]
        self.scm_dc_energy_reserve = odc[:energy_reserve_in_seconds]
        self.scm_dc_min_energy_recovery = odc[:min_energy_recovery_in_seconds]
      end
      if scm && scm[:volume_baseline]
        self.state_of_charge_management << {id: "volumeBaseline", name: "Volume Baselining"}
        vb = scm[:volume_baseline] || {}
        self.scm_vb_dc_delivery_duration = vb[:dc][:delivery_duration_in_seconds]
        self.scm_vb_dc_delivery_duration_buffer = vb[:dc][:delivery_duration_buffer]
        self.scm_vb_dc_min_energy_recovery = vb[:dc][:min_energy_recovery]
        self.scm_vb_dm_delivery_duration = vb[:dm][:delivery_duration_in_seconds]
        self.scm_vb_dm_delivery_duration_buffer = vb[:dm][:delivery_duration_buffer]
        self.scm_vb_dm_min_energy_recovery = vb[:dm][:min_energy_recovery]
        self.scm_vb_dr_delivery_duration = vb[:dr][:delivery_duration_in_seconds]
        self.scm_vb_dr_delivery_duration_buffer = vb[:dr][:delivery_duration_buffer]
        self.scm_vb_dr_min_energy_recovery = vb[:dr][:min_energy_recovery]
      end
      if scm && scm[:fcr_nl]
        self.state_of_charge_management << {id: "fcrNl", name: "FCR NL"}
        fcr = scm[:fcr_nl] || {}
        self.scm_fcr_lower_soc_limit = fcr[:lower_soc_limit]
        self.scm_fcr_upper_soc_limit = fcr[:upper_soc_limit]
      end
      if scm && scm[:optimization_pass_through]
        self.state_of_charge_management << {id: "optimizationPassThrough", name: "Optimization Pass Through"}
        opt = scm[:optimization_pass_through] || {}
        self.scm_opt_sp_duration_minutes = opt[:sp_duration_minutes]
      end
    end

    #assetDispatchStrategy: AssetDispatchStrategyConfig
    attr_accessor :ads_start_using_assets_only_when_at_basepoint, #boolean
      :ads_strategy, #ByPrice(preserveCurrentDispatches: Boolean), ByAvailableFlex, ProRata(symmetric: Boolean), OnOff, V2GExternalOptimizer, NominationPassThrough, ByPriceAndSoC
      :ads_strategy_by_price_preserve_current_dispatches,
      :ads_strategy_pro_rata_symmetric,
      :ads_strategy_on_off_signal,
      :ads_strategy_by_price_and_soc_limit_battery_power_window

    validates :ads_strategy, presence: true,
      if: Proc.new { |dg| dg.is_generic_config }

    validates :ads_strategy_on_off_signal, presence: true,
        if: Proc.new { |dg| dg.is_generic_config &&
          dg.ads_strategy.present? &&
          dg.ads_strategy.collect{|ds| ds[:id]}.include?("onOff")
        }

    validates :ads_strategy_by_price_and_soc_limit_battery_power_window, allow_blank: true,
        numericality: { only_integer: true, greater_than_or_equal_to: 0 },
        if: Proc.new { |dg| dg.is_generic_config &&
          dg.ads_strategy.present? &&
          dg.ads_strategy.collect{|ds| ds[:id]}.include?("byPriceAndSoC")
        }

    def build_asset_dispatch_strategy
      h = {}
      ads_strategy_id = self.ads_strategy[0][:id]
      if ads_strategy_id == 'byPrice'
        h[ads_strategy_id] = {
          preserve_current_dispatches: self.ads_strategy_by_price_preserve_current_dispatches || false
        }
      elsif ads_strategy_id == 'byPriceAndSoC'
        h[ads_strategy_id] = {
          limit_battery_power_window: self.ads_strategy_by_price_and_soc_limit_battery_power_window
        }
      elsif ads_strategy_id == 'proRata'
        h[ads_strategy_id] = {
          symmetric: self.ads_strategy_pro_rata_symmetric || false
        }
      elsif ads_strategy_id == 'onOff'
        h[ads_strategy_id] = {
          signal: self.ads_strategy_on_off_signal[0][:id]
        }
      else
        h[ads_strategy_id] = {}
      end
      HashCaseConverter.to_camel_case({
        asset_dispatch_strategy: {
          start_using_assets_only_when_at_basepoint: self.ads_start_using_assets_only_when_at_basepoint || false,
          strategy: h
        }
      })
    end
    def set_asset_dispatch_strategy(generic_conf)
      ads = generic_conf[:asset_dispatch_strategy]
      self.ads_start_using_assets_only_when_at_basepoint = ads[:start_using_assets_only_when_at_basepoint]
      strategy = ads[:strategy]
      ads_strategy_id = strategy.keys.first
      self.ads_strategy = [ads_strategy_id.to_s.camelize(:lower)]
      if ads_strategy_id.to_s.camelize(:lower) == 'byPrice'
        self.ads_strategy_by_price_preserve_current_dispatches = strategy[ads_strategy_id][:preserve_current_dispatches]
      elsif ads_strategy_id.to_s.camelize(:lower) == 'byPriceAndSoC'
        self.ads_strategy_by_price_and_soc_limit_battery_power_window = strategy[ads_strategy_id][:limit_battery_power_window]
      elsif ads_strategy_id.to_s.camelize(:lower) == 'proRata'
        self.ads_strategy_pro_rata_symmetric = strategy[ads_strategy_id][:symmetric]
      elsif ads_strategy_id.to_s.camelize(:lower) == 'onOff'
        self.ads_strategy_on_off_signal = [strategy[ads_strategy_id][:signal]].compact
      end
    end

    #crossDgLinks: Set[CrossDgLink]
    attr_accessor :cross_dg_links,  #CrossPlanPropagation(frequency: FiniteDuration)
      :cdl_cross_plan_propagation_frequency  #CrossPlanPropagation(frequency: FiniteDuration)

    validates :cdl_cross_plan_propagation_frequency, numericality: true,
      if: Proc.new { |dg| dg.is_generic_config && dg.cross_dg_links.present? && dg.cross_dg_links.collect{|x| x[:id]}.include?('crossPlanPropagation') }

    def cross_dg_link_params(cdgl)
      p = {}
      if cdgl == 'crossPlanPropagation'
        p[:frequency_seconds] = self.cdl_cross_plan_propagation_frequency
      end
      p
    end

    def build_cross_dg_links
      cdg_links = (self.cross_dg_links || []).collect{|x| x[:id]}
      HashCaseConverter.to_camel_case({
        cross_dg_links: cdg_links.collect{|cdgl| {"#{cdgl}": cross_dg_link_params(cdgl)} }
      })
    end
    def set_cross_dg_links(generic_conf)
      cdg_links = generic_conf[:cross_dg_links]
      self.cross_dg_links = cdg_links.collect {|x| x.keys.first.to_s.camelize(:lower)}
      puts "### CDG LINKS 1: #{cdg_links.inspect}"
      cdg_cpp = cdg_links.find{|x| x['crossPlanPropagation'.underscore.to_sym] }
      puts "### CDG LINKS 2: #{cdg_cpp.inspect}"
      if cdg_cpp
        p = cdg_cpp['crossPlanPropagation'.underscore.to_sym]
        self.cdl_cross_plan_propagation_frequency = p[:frequency_seconds]
      end
    end

    # deviationsCompensation: Option[DeviationsCompensationConfig],
    attr_accessor :dc_check_interval_seconds, #FiniteDuration
      :dc_over_delivery_excess_compensation_factor, #Double
      :dc_over_delivery_compensation_limit_factor, #Double
      :dc_under_delivery_excess_compensation_factor, #Double
      :dc_under_delivery_compensation_limit_factor, #Double
      :dc_compensation_resolution_kw, #KW
      #isAtSetpoint: IsAtSetpointDetectionConfig
      :dc_is_at_setpoint_upper_tolerance_factor, #BigDecimal
      :dc_is_at_setpoint_upper_tolerance_minimum_kw, #KW
      :dc_is_at_setpoint_lower_tolerance_factor, #BigDecimal
      :dc_is_at_setpoint_lower_tolerance_minimum_kw #KW

    validates :dc_check_interval_seconds, :dc_over_delivery_excess_compensation_factor,
      :dc_over_delivery_compensation_limit_factor, :dc_under_delivery_excess_compensation_factor,
      :dc_under_delivery_compensation_limit_factor, :dc_compensation_resolution_kw,
      :dc_is_at_setpoint_upper_tolerance_factor, :dc_is_at_setpoint_upper_tolerance_minimum_kw,
      :dc_is_at_setpoint_lower_tolerance_factor, :dc_is_at_setpoint_lower_tolerance_minimum_kw,
      numericality: true,
      if: Proc.new { |dg| dg.is_generic_config && (
        dg.dc_check_interval_seconds.present? || dg.dc_over_delivery_excess_compensation_factor.present? ||
        dg.dc_over_delivery_compensation_limit_factor.present? || dg.dc_under_delivery_excess_compensation_factor.present? ||
        dg.dc_under_delivery_compensation_limit_factor.present? || dg.dc_compensation_resolution_kw.present? ||
        dg.dc_is_at_setpoint_upper_tolerance_factor.present? || dg.dc_is_at_setpoint_upper_tolerance_minimum_kw.present? ||
        dg.dc_is_at_setpoint_lower_tolerance_factor.present? || dg.dc_is_at_setpoint_lower_tolerance_minimum_kw.present?
      )}

    def build_deviations_compensation_opt
      if !self.dc_check_interval_seconds.present?
        return { deviations_compensation: nil }
      end

      HashCaseConverter.to_camel_case({
        deviations_compensation: {
          check_interval_seconds: self.dc_check_interval_seconds,
          over_delivery_excess_compensation_factor: self.dc_over_delivery_excess_compensation_factor,
          over_delivery_compensation_limit_factor: self.dc_over_delivery_compensation_limit_factor,
          under_delivery_excess_compensation_factor: self.dc_under_delivery_excess_compensation_factor,
          under_delivery_compensation_limit_factor: self.dc_under_delivery_compensation_limit_factor,
          compensation_resolution_kw: self.dc_compensation_resolution_kw,
          is_at_setpoint: {
            upper_tolerance_factor: self.dc_is_at_setpoint_upper_tolerance_factor,
            lower_tolerance_factor: self.dc_is_at_setpoint_upper_tolerance_minimum_kw,
            upper_tolerance_minimum_kw: self.dc_is_at_setpoint_lower_tolerance_factor,
            lower_tolerance_minimum_kw: self.dc_is_at_setpoint_lower_tolerance_minimum_kw
          }
        }
      })
    end
    def set_deviations_compensation_opt(generic_conf)
      dc = generic_conf[:deviations_compensation]
      if dc
        self.dc_check_interval_seconds = dc[:check_interval_seconds]
        self.dc_over_delivery_excess_compensation_factor = dc[:over_delivery_excess_compensation_factor]
        self.dc_over_delivery_compensation_limit_factor = dc[:over_delivery_compensation_limit_factor]
        self.dc_under_delivery_excess_compensation_factor = dc[:under_delivery_excess_compensation_factor]
        self.dc_under_delivery_compensation_limit_factor = dc[:under_delivery_compensation_limit_factor]
        self.dc_compensation_resolution_kw = dc[:compensation_resolution_kw]

        self.dc_is_at_setpoint_upper_tolerance_factor = dc[:is_at_setpoint][:upper_tolerance_factor]
        self.dc_is_at_setpoint_upper_tolerance_minimum_kw = dc[:is_at_setpoint][:upper_tolerance_minimum_kw]
        self.dc_is_at_setpoint_lower_tolerance_factor = dc[:is_at_setpoint][:lower_tolerance_factor]
        self.dc_is_at_setpoint_lower_tolerance_minimum_kw = dc[:is_at_setpoint][:lower_tolerance_minimum_kw]
      end
    end

    #dgActivationType: DGActivationType
    attr_accessor :dg_activation_type #ActivationRange, OnOff, Relative

    validates :dg_activation_type, presence: true,
      if: Proc.new { |dg| dg.is_generic_config }

    def build_dg_activation_type
      HashCaseConverter.to_camel_case({
        dg_activation_type: {
          "#{self.dg_activation_type[0][:id]}": {}
        }
      })
    end
    def set_dg_activation_type(generic_conf)
      self.dg_activation_type = generic_conf[:dg_activation_type].keys.collect{|x| x.to_s.camelize(:lower)}
    end

    #dispatchCommands: Set[DispatchCommands],
    attr_accessor :dispatch_commands #DispatchNow, PlannedDispatch, CancelPlannedDispatch RTNAtEndOfNomination, DispatchConfirmation

    def build_dispatch_commands
      HashCaseConverter.to_camel_case({
        dispatch_commands: (self.dispatch_commands || []).collect{|x| {x[:id] => {}}}
      })
    end
    def set_dispatch_commands(generic_conf)
      self.dispatch_commands = generic_conf[:dispatch_commands].collect {|x| x.keys.first.to_s.camelize(:lower)}
    end

    #dispatchSource: Set[DispatchSource],
    attr_accessor :dispatch_source, #FollowSchedule, Merlin, NominatedVolume(activationFactor: Option[Percentage], symmetricActivation: Boolean), Sally, UI(edgSchedule: Boolean), ResidualShape
      #NominatedVolume
      :ds_nominated_volume_activation_factor, #Option[Percentage]
      :ds_nominated_volume_symmetric_activation, #Boolean
      # UI
      :ds_ui_edg_schedule, #Boolean
      # ResidualShape
      :ds_residual_shape_positive_threshold, #PosKW
      :ds_residual_shape_negative_threshold, #NegKW
      :ds_residual_shape_window, # FiniteDuration
      # Price Trigger
      :ds_price_trigger_window, # FiniteDuration
      :ds_price_trigger_price_type, # String
      :ds_price_trigger_settlement_period, # FiniteDuration
      :ds_price_trigger_price_threshold_neg, # Price (BigDecimal)
      :ds_price_trigger_price_threshold_pos, # Price ()BigDecimal)
      :ds_price_trigger_price_expiration_seconds, # FiniteDuration
      # nlAfrr
      :ds_nlafrr_bleeding_time_seconds# FiniteDuration

    validates :ds_residual_shape_positive_threshold, :ds_residual_shape_window,
      numericality: { only_integer: true, greater_than_or_equal_to: 0 },
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.dispatch_source.present? &&
        dg.dispatch_source.collect{|ds| ds[:id]}.include?("residualShape")
      }

    validates :ds_residual_shape_negative_threshold,
      numericality: { only_integer: true },
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.dispatch_source.present? &&
        dg.dispatch_source.collect{|ds| ds[:id]}.include?("residualShape")
      }

    validates :ds_price_trigger_price_type, presence: true,
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.dispatch_source.present? &&
        dg.dispatch_source.collect{|ds| ds[:id]}.include?("priceTrigger")
      }

    validates :ds_price_trigger_window, :ds_price_trigger_settlement_period,
      :ds_price_trigger_price_expiration_seconds,
      presence: true, numericality: { only_integer: true },
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.dispatch_source.present? &&
        dg.dispatch_source.collect{|ds| ds[:id]}.include?("priceTrigger")
      }

    validates :ds_price_trigger_price_threshold_neg, :ds_price_trigger_price_threshold_pos,
      presence: true, numericality: true,
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.dispatch_source.present? &&
        dg.dispatch_source.collect{|ds| ds[:id]}.include?("priceTrigger")
      }

    validates :ds_nlafrr_bleeding_time_seconds,
      presence: true, numericality: { only_integer: true },
      if: Proc.new { |dg| dg.is_generic_config &&
        dg.dispatch_source.present? &&
        dg.dispatch_source.collect{|ds| ds[:id]}.include?("nlAfrr")
      }

    def dispatch_source_params(ds)
      p = {}
      if ds == 'nominatedVolume'
        p[:activation_factor] = self.ds_nominated_volume_activation_factor
        p[:symmetric_activation] = self.ds_nominated_volume_symmetric_activation || false
      elsif ds == 'ui'
        p[:edg_schedule] = self.ds_ui_edg_schedule || false
      elsif ds == 'residualShape'
        p[:positive_threshold] = self.ds_residual_shape_positive_threshold
        p[:negative_threshold] = self.ds_residual_shape_negative_threshold
        p[:window] = self.ds_residual_shape_window
      elsif ds == 'priceTrigger'
        p[:window] = self.ds_price_trigger_window
        p[:price_type] = self.ds_price_trigger_price_type
        p[:settlement_period] = self.ds_price_trigger_settlement_period
        p[:price_threshold_neg] = self.ds_price_trigger_price_threshold_neg
        p[:price_threshold_pos] = self.ds_price_trigger_price_threshold_pos
        p[:price_expiration_seconds] = self.ds_price_trigger_price_expiration_seconds
      elsif ds == 'nlAfrr'
        p[:bleeding_time_seconds] = self.ds_nlafrr_bleeding_time_seconds
      end
      p
    end

    def build_dispatch_source
      d_source = (self.dispatch_source || []).collect{|x| x[:id]}
      HashCaseConverter.to_camel_case({
        dispatch_source: d_source.collect { |ds| {"#{ds}": dispatch_source_params(ds)} }
      })
    end
    def set_dispatch_source(generic_conf)
      ds = generic_conf[:dispatch_source]
      self.dispatch_source = ds.collect {|x| x.keys.first.to_s.camelize(:lower)}
      ds_nv = ds.find{|x| x['nominatedVolume'.underscore.to_sym] }
      if ds_nv
        p = ds_nv['nominatedVolume'.underscore.to_sym]
        self.ds_nominated_volume_activation_factor = p[:activation_factor]
        self.ds_nominated_volume_symmetric_activation = p[:symmetric_activation]
      end
      ds_ui = ds.find{|x| x['ui'.underscore.to_sym] }
      if ds_ui
        p = ds_ui['ui'.underscore.to_sym]
        self.ds_ui_edg_schedule = p[:edg_schedule]
      end
      ds_residual_shape = ds.find{|x| x['residualShape'.underscore.to_sym] }
      if ds_residual_shape
        p = ds_residual_shape['residualShape'.underscore.to_sym]
        self.ds_residual_shape_positive_threshold = p[:positive_threshold]
        self.ds_residual_shape_negative_threshold = p[:negative_threshold]
        self.ds_residual_shape_window = p[:window]
      end
      ds_price_trigger = ds.find{|x| x['priceTrigger'.underscore.to_sym] }
      if ds_price_trigger
        p = ds_price_trigger['priceTrigger'.underscore.to_sym]
        self.ds_price_trigger_window = p[:window]
        self.ds_price_trigger_price_type = p[:price_type]
        self.ds_price_trigger_settlement_period = p[:settlement_period]
        self.ds_price_trigger_price_threshold_neg = p[:price_threshold_neg]
        self.ds_price_trigger_price_threshold_pos = p[:price_threshold_pos]
        self.ds_price_trigger_price_expiration_seconds = p[:price_expiration_seconds]
      end
      nl_afrr = ds.find{|x| x['nlAfrr'.underscore.to_sym] }
      if nl_afrr
        p = nl_afrr['nlAfrr'.underscore.to_sym]
        self.ds_nlafrr_bleeding_time_seconds = p[:bleeding_time_seconds]
      end
    end

    #dispatchExecutionConfig: Option[executionPlanWindow],
    attr_accessor :execution_plan_window_seconds
    validates :execution_plan_window_seconds,
      allow_blank: true,
      numericality: { greater_than_or_equal_to: 0 },
      if: Proc.new { |dg| dg.is_generic_config }

    attr_accessor :execution_plan_frequency_seconds
    validates :execution_plan_frequency_seconds,
      allow_blank: true,
      numericality: { greater_than_or_equal_to: 0 },
      if: Proc.new { |dg| dg.is_generic_config }

    def build_dispatch_execution_config
      h = {}
      if self.execution_plan_window_seconds.present?
        h[:execution_plan_window_seconds] = self.execution_plan_window_seconds
      end
      if self.execution_plan_frequency_seconds.present?
        h[:execution_plan_frequency_seconds] = self.execution_plan_frequency_seconds
      end
      if !h.empty?
        HashCaseConverter.to_camel_case({
          dispatch_execution_config: h
        })
      else
        h
      end
    end

    def set_dispatch_execution_config(generic_conf)
      d_exec_cfg = generic_conf[:dispatch_execution_config]
      if d_exec_cfg
        self.execution_plan_window_seconds = d_exec_cfg[:execution_plan_window_seconds]
        self.execution_plan_frequency_seconds = d_exec_cfg[:execution_plan_frequency_seconds]
      end
    end

    #nominationExtensionTimes: Option[NominationExtensionTimes],
    attr_accessor :net_extend_before_nomination_seconds, #FiniteDuration
      :net_overlap_nominations_seconds, #FiniteDuration
      :net_stop_before_end_of_nomination_seconds #FiniteDuration

    validates :net_extend_before_nomination_seconds, :net_overlap_nominations_seconds,
      :net_stop_before_end_of_nomination_seconds,
      numericality: true,
      if: Proc.new { |dg| dg.is_generic_config && (
         dg.net_extend_before_nomination_seconds.present? || dg.net_overlap_nominations_seconds.present? ||
         dg.net_stop_before_end_of_nomination_seconds.present?
       )}

    def build_nomination_extension_times_opt
      if !self.net_extend_before_nomination_seconds.present?
        return { nomination_extension_times: nil }
      end
      HashCaseConverter.to_camel_case({
        nomination_extension_times: {
          extend_before_nomination_seconds: self.net_extend_before_nomination_seconds,
          overlap_nominations_seconds: self.net_overlap_nominations_seconds,
          stop_before_end_of_nomination_seconds: self.net_stop_before_end_of_nomination_seconds
        }
      })
    end
    def set_nomination_extension_times_opt(generic_conf)
      net = generic_conf[:nomination_extension_times]
      if net
        self.net_extend_before_nomination_seconds = net[:extend_before_nomination_seconds]
        self.net_overlap_nominations_seconds = net[:overlap_nominations_seconds]
        self.net_stop_before_end_of_nomination_seconds = net[:stop_before_end_of_nomination_seconds]
      end
    end

    #notifications: Set[NotificationConfig],
    attr_accessor :notifications, #FlexTooLow, MerlinState, OverDeliveryNegative, OverDeliveryPositive, SetpointNotReached, SetpointReachable
      #FlexTooLow
      :ntf_flex_too_low_threshold_factor, #Double
      :ntf_flex_too_low_threshold_buffer_seconds, #FiniteDuration
      #OverDeliveryNegative
      :ntf_over_delivery_negative_threshold_kw, #KW
      :ntf_over_delivery_negative_delay_seconds, #FiniteDuration
      #OverDeliveryPositive
      :ntf_over_delivery_positive_threshold_kw, #KW,
      :ntf_over_delivery_positive_delay_seconds, #FiniteDuration,
      #SetpointNotReached
      :ntf_setpoint_not_reached_reach_setpoint_in_seconds, #FiniteDuration
      :ntf_setpoint_not_reached_is_at_setpoint_detection_upper_tolerance_factor, #BigDecimal
      :ntf_setpoint_not_reached_is_at_setpoint_detection_upper_tolerance_minimum_kw, #KW
      :ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_factor, #BigDecimal
      :ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_minimum_kw, #KW
      #SetpointReachable
      :ntf_setpoint_reachable_is_at_setpoint_detection_upper_tolerance_factor, #BigDecimal
      :ntf_setpoint_reachable_is_at_setpoint_detection_upper_tolerance_minimum_kw, #KW
      :ntf_setpoint_reachable_is_at_setpoint_detection_lower_tolerance_factor, #BigDecimal
      :ntf_setpoint_reachable_is_at_setpoint_detection_lower_tolerance_minimum_kw #KW

    validates :ntf_flex_too_low_threshold_factor, :ntf_flex_too_low_threshold_buffer_seconds, numericality: true,
      if: Proc.new { |dg| dg.is_generic_config && dg.notifications.present? && dg.notifications.collect{|x| x[:id]}.include?('flexTooLow')}
    validates :ntf_over_delivery_negative_threshold_kw, :ntf_over_delivery_negative_delay_seconds, numericality: true,
      if: Proc.new { |dg| dg.is_generic_config && dg.notifications.present? && dg.notifications.collect{|x| x[:id]}.include?('overDeliveryNegative')}
    validates :ntf_over_delivery_positive_threshold_kw, :ntf_over_delivery_positive_delay_seconds, numericality: true,
      if: Proc.new { |dg| dg.is_generic_config && dg.notifications.present? && dg.notifications.collect{|x| x[:id]}.include?('overDeliveryPositive')}
    validates :ntf_setpoint_not_reached_reach_setpoint_in_seconds,
      :ntf_setpoint_not_reached_is_at_setpoint_detection_upper_tolerance_factor,
      :ntf_setpoint_not_reached_is_at_setpoint_detection_upper_tolerance_minimum_kw,
      :ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_factor,
      :ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_minimum_kw,
      numericality: true,
      if: Proc.new { |dg| dg.is_generic_config && dg.notifications.present? && dg.notifications.collect{|x| x[:id]}.include?('setpointNotReached')}
    validates :ntf_setpoint_reachable_is_at_setpoint_detection_upper_tolerance_factor,
      :ntf_setpoint_reachable_is_at_setpoint_detection_upper_tolerance_minimum_kw,
      :ntf_setpoint_reachable_is_at_setpoint_detection_lower_tolerance_factor,
      :ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_minimum_kw,
      numericality: true,
      if: Proc.new { |dg| dg.is_generic_config && dg.notifications.present? && dg.notifications.collect{|x| x[:id]}.include?('setpointReachable')}

    def notification_params(n)
      p = {}
      if n == 'flexTooLow'
        p[:threshold_factor] = self.ntf_flex_too_low_threshold_factor
        p[:threshold_buffer_seconds] = self.ntf_flex_too_low_threshold_buffer_seconds
      elsif n == 'overDeliveryNegative'
        p[:threshold_kw] = self.ntf_over_delivery_negative_threshold_kw
        p[:delay_seconds] = self.ntf_over_delivery_negative_delay_seconds
      elsif n == 'overDeliveryPositive'
        p[:threshold_kw] = self.ntf_over_delivery_positive_threshold_kw
        p[:delay_seconds] = self.ntf_over_delivery_positive_delay_seconds
      elsif n == 'setpointNotReached'
        p[:reach_setpoint_in_seconds] = self.ntf_setpoint_not_reached_reach_setpoint_in_seconds
        p[:is_at_setpoint_detection_config] = {
          upper_tolerance_factor: self.ntf_setpoint_not_reached_is_at_setpoint_detection_upper_tolerance_factor,
          upper_tolerance_minimum_kw: self.ntf_setpoint_not_reached_is_at_setpoint_detection_upper_tolerance_minimum_kw,
          lower_tolerance_factor: self.ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_factor,
          lower_tolerance_minimum_kw: self.ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_minimum_kw
        }
      elsif n == 'setpointReachable'
        p[:is_at_setpoint_detection_config] = {
          upper_tolerance_factor: self.ntf_setpoint_reachable_is_at_setpoint_detection_upper_tolerance_factor,
          upper_tolerance_minimum_kw: self.ntf_setpoint_reachable_is_at_setpoint_detection_upper_tolerance_minimum_kw,
          lower_tolerance_factor: self.ntf_setpoint_reachable_is_at_setpoint_detection_lower_tolerance_factor,
          lower_tolerance_minimum_kw: self.ntf_setpoint_reachable_is_at_setpoint_detection_lower_tolerance_minimum_kw
        }
      end
      p
    end

    def build_notifications
      notifs = (self.notifications || []).collect{|x| x[:id]}
      HashCaseConverter.to_camel_case({
        notifications: notifs.collect { |n| {"#{n}": notification_params(n)} }
      })
    end
    def set_notifications(generic_conf)
      notifs = generic_conf[:notifications]
      self.notifications = notifs.collect {|x| x.keys.first.to_s.camelize(:lower)}
      ntf_ftl = notifs.find{|x| x['flexTooLow'.underscore.to_sym] }
      if ntf_ftl
        p = ntf_ftl['flexTooLow'.underscore.to_sym]
        self.ntf_flex_too_low_threshold_factor = p[:threshold_factor]
        self.ntf_flex_too_low_threshold_buffer_seconds = p[:threshold_buffer_seconds]
      end
      ntf_odn = notifs.find{|x| x['overDeliveryNegative'.underscore.to_sym] }
      if ntf_odn
        p = ntf_odn['overDeliveryNegative'.underscore.to_sym]
        self.ntf_over_delivery_negative_threshold_kw = p[:threshold_kw]
        self.ntf_over_delivery_negative_delay_seconds = p[:delay_seconds]
      end
      ntf_odp = notifs.find{|x| x['overDeliveryPositive'.underscore.to_sym] }
      if ntf_odp
        p = ntf_odp['overDeliveryPositive'.underscore.to_sym]
        self.ntf_over_delivery_positive_threshold_kw = p[:threshold_kw]
        self.ntf_over_delivery_positive_delay_seconds = p[:delay_seconds]
      end
      ntf_snt = notifs.find{|x| x['setpointNotReached'.underscore.to_sym] }
      if ntf_snt
        p = ntf_snt['setpointNotReached'.underscore.to_sym]
        self.ntf_setpoint_not_reached_reach_setpoint_in_seconds = p[:reach_setpoint_in_seconds]
        is_at_setpoint = p[:is_at_setpoint_detection_config]
        self.ntf_setpoint_not_reached_is_at_setpoint_detection_upper_tolerance_factor = is_at_setpoint[:upper_tolerance_factor]
        self.ntf_setpoint_not_reached_is_at_setpoint_detection_upper_tolerance_minimum_kw = is_at_setpoint[:upper_tolerance_minimum_kw]
        self.ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_factor = is_at_setpoint[:lower_tolerance_factor]
        self.ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_minimum_kw = is_at_setpoint[:lower_tolerance_minimum_kw]
      end
      ntf_sr = notifs.find{|x| x['setpointReachable'.underscore.to_sym] }
      if ntf_sr
        p = ntf_sr['setpointReachable'.underscore.to_sym]
        is_at_setpoint = p[:is_at_setpoint_detection_config]
        self.ntf_setpoint_reachable_is_at_setpoint_detection_upper_tolerance_factor = is_at_setpoint[:upper_tolerance_factor]
        self.ntf_setpoint_reachable_is_at_setpoint_detection_upper_tolerance_minimum_kw = is_at_setpoint[:upper_tolerance_minimum_kw]
        self.ntf_setpoint_reachable_is_at_setpoint_detection_lower_tolerance_factor = is_at_setpoint[:lower_tolerance_factor]
        self.ntf_setpoint_reachable_is_at_setpoint_detection_lower_tolerance_minimum_kw = is_at_setpoint[:lower_tolerance_minimum_kw]
      end
    end

    #periodicity: PeriodicityConfig
    attr_accessor :periodicity_dg_aggregations_seconds, #FiniteDuration
      :periodicity_output_signal_write_seconds #FiniteDuration

    validates :periodicity_dg_aggregations_seconds, :periodicity_output_signal_write_seconds, numericality: true,
      if: Proc.new { |dg| dg.is_generic_config }

    def build_periodicity
      HashCaseConverter.to_camel_case({
        periodicity: {
          dg_aggregations_seconds: self.periodicity_dg_aggregations_seconds,
          output_signal_write_seconds: self.periodicity_output_signal_write_seconds,
        }
      })
    end
    def set_periodicity(generic_conf)
      periodicty = generic_conf[:periodicity]
      self.periodicity_dg_aggregations_seconds = periodicty[:dg_aggregations_seconds]
      self.periodicity_output_signal_write_seconds = periodicty[:output_signal_write_seconds]
    end

    #rampAdjustmentStrategy: AllAtOnceOnRampUp, AllAtOnceOnRampUpWithIndirectSteeringAdjustments, IndividuallyOnRampUp, NoRampAdjustment
    attr_accessor :ramp_adjustment_strategy

    validates :ramp_adjustment_strategy, presence: true,
      if: Proc.new { |dg| dg.is_generic_config }

    def build_ramp_adjustment_strategy
      HashCaseConverter.to_camel_case({
        ramp_adjustment_strategy: {
          "#{self.ramp_adjustment_strategy[0][:id]}": {},
        }
      })
    end
    def set_ramp_adjustment_strategy(generic_conf)
      self.ramp_adjustment_strategy = generic_conf[:ramp_adjustment_strategy].keys.collect{|x| x.to_s.camelize(:lower)}
    end

    #redispatchTriggers: Set[RedispatchTrigger],
    attr_accessor :redispatch_triggers, #AllocationChange, AssetAvailableFlexChange(threshold: KW), AssetAvailableFlexChangeSinceActivation(factor: Percentage), AssetFaultChange, BackupAssetActivationChange, DGTotalDeviation(buffer: KW, suppressRedispatch: FiniteDuration)
      #AssetAvailableFlexChange
      :rt_asset_available_flex_change_threshold_kw, #KW
      #AssetAvailableFlexChangeSinceActivation
      :rt_asset_available_flex_change_since_activation_factor, #Percentage
      #DGTotalDeviation
      :rt_dg_total_deviation_buffer_kw, #KW,
      :rt_dg_total_deviation_suppress_redispatch_seconds #FiniteDuration)

    validates :rt_asset_available_flex_change_threshold_kw, numericality: true,
      if: Proc.new { |dg| dg.is_generic_config && dg.redispatch_triggers.present? && dg.redispatch_triggers.collect{|x| x[:id]}.include?('assetAvailableFlexChange')}
    validates :rt_asset_available_flex_change_since_activation_factor, numericality: true,
      if: Proc.new { |dg| dg.is_generic_config && dg.redispatch_triggers.present? && dg.redispatch_triggers.collect{|x| x[:id]}.include?('assetAvailableFlexChangeSinceActivation')}
    validates :rt_dg_total_deviation_buffer_kw, :rt_dg_total_deviation_suppress_redispatch_seconds, numericality: true,
      if: Proc.new { |dg| dg.is_generic_config && dg.redispatch_triggers.present? && dg.redispatch_triggers.collect{|x| x[:id]}.include?('dgTotalDeviation')}

    def trigger_params(t)
      p = {}
      if t == 'assetAvailableFlexChange'
        p[:threshold_kw] = self.rt_asset_available_flex_change_threshold_kw
      elsif t == 'assetAvailableFlexChangeSinceActivation'
        p[:factor] = self.rt_asset_available_flex_change_since_activation_factor
      elsif t == 'dgTotalDeviation'
        p[:buffer_kw] = self.rt_dg_total_deviation_buffer_kw
        p[:suppress_redispatch_seconds] = self.rt_dg_total_deviation_suppress_redispatch_seconds
      end
      p
    end

    def build_redispatch_triggers
      triggers = (self.redispatch_triggers || []).collect{|x| x[:id]}
      HashCaseConverter.to_camel_case({
        redispatch_triggers: triggers.collect { |t| {"#{t}": trigger_params(t)} }
      })
    end
    def set_redispatch_triggers(generic_conf)
      triggers = generic_conf[:redispatch_triggers]
      self.redispatch_triggers = triggers.collect {|x| x.keys.first.to_s.camelize(:lower)}
      tr_aafc = triggers.find{|x| x['assetAvailableFlexChange'.underscore.to_sym] }
      if tr_aafc
        p = tr_aafc['assetAvailableFlexChange'.underscore.to_sym]
        self.rt_asset_available_flex_change_threshold_kw = p[:threshold_kw]
      end
      tr_aafcsa = triggers.find{|x| x['assetAvailableFlexChangeSinceActivation'.underscore.to_sym] }
      if tr_aafcsa
        p = tr_aafcsa['assetAvailableFlexChangeSinceActivation'.underscore.to_sym]
        self.rt_asset_available_flex_change_since_activation_factor = p[:factor]
      end
      tr_dgtd = triggers.find{|x| x['dgTotalDeviation'.underscore.to_sym] }
      if tr_dgtd
        p = tr_dgtd['dgTotalDeviation'.underscore.to_sym]
        self.rt_dg_total_deviation_buffer_kw = p[:buffer_kw]
        self.rt_dg_total_deviation_suppress_redispatch_seconds = p[:suppress_redispatch_seconds]
      end
    end

    #setpointValidation: SetpointValidation,
    attr_accessor :setpoint_validation_reaction_time_seconds, #FiniteDuration
      :setpoint_validation_cap_by_nomination, #Boolean
      :setpoint_validation_minimum_duration_of_dispatch_seconds, #Option[FiniteDuration],
      :setpoint_validation_nomination_interval_validation, #Option[SetpointValidation.SetpointValidationAgainstNomination]: MRLSetpointValidation, NominationExtendedIntervalValidation,, NominationIntervalOnlyValidation
      :setpoint_validation_quantization_filter_resolution_kw, #KW
      :setpoint_validation_quantization_filter_quantization_duration_seconds #FiniteDuration

    validates :setpoint_validation_reaction_time_seconds, numericality: true,
      if: Proc.new { |dg| dg.is_generic_config }
    validates :setpoint_validation_quantization_filter_resolution_kw,
      :setpoint_validation_quantization_filter_quantization_duration_seconds,
      numericality: true,
      if: Proc.new { |dg| dg.is_generic_config && (
        dg.setpoint_validation_quantization_filter_resolution_kw.present? ||
        dg.setpoint_validation_quantization_filter_quantization_duration_seconds.present?
      ) }

    def build_setpoint_validation
      nomination_interval_validation_h = nil
      if self.setpoint_validation_nomination_interval_validation
        nomination_interval_validation_h = {
          "#{self.setpoint_validation_nomination_interval_validation[0][:id]}": {},
        }
      end
      quantization_filter_h = nil
      if self.setpoint_validation_quantization_filter_resolution_kw
        quantization_filter_h = {
          resolution_kw: self.setpoint_validation_quantization_filter_resolution_kw,
          quantization_duration_seconds: self.setpoint_validation_quantization_filter_quantization_duration_seconds
        }
      end
      HashCaseConverter.to_camel_case({
        setpoint_validation: {
          reaction_time_seconds: self.setpoint_validation_reaction_time_seconds,
          cap_by_nomination: self.setpoint_validation_cap_by_nomination || false,
          minimum_duration_of_dispatch_seconds: self.setpoint_validation_minimum_duration_of_dispatch_seconds,
          nomination_interval_validation: nomination_interval_validation_h,
          quantization_filter: quantization_filter_h
        }
      })
    end
    def set_setpoint_validation(generic_conf)
      stp_val = generic_conf[:setpoint_validation]
      self.setpoint_validation_reaction_time_seconds = stp_val[:reaction_time_seconds]
      self.setpoint_validation_cap_by_nomination = stp_val[:cap_by_nomination]
      self.setpoint_validation_minimum_duration_of_dispatch_seconds = stp_val[:minimum_duration_of_dispatch_seconds]
      nomination_interval_validation_h = stp_val[:nomination_interval_validation]
      if nomination_interval_validation_h.present?
        self.setpoint_validation_nomination_interval_validation = nomination_interval_validation_h.keys.collect{|x| x.to_s.camelize(:lower)}
      end
      quantization_filter_h = stp_val[:quantization_filter]
      if quantization_filter_h.present?
        self.setpoint_validation_quantization_filter_resolution_kw = quantization_filter_h[:resolution_kw]
        self.setpoint_validation_quantization_filter_quantization_duration_seconds = quantization_filter_h[:quantization_duration_seconds]
      end
    end

    #signals: Signals,
    #outputSignals: Set[Signal],
    attr_accessor :signals_output_signals, #AssetTotalSetpoint, SCState, etc
      #subPoolsValues: Option[SubpoolsValues],
      :signals_sub_pools_values_on, #Int
      :signals_sub_pools_values_off, #Int
      #dsoForSignals: Set[DSOName],
      :signals_dso_for_signals, #[String]
      #heartBeatMirror: Boolean
      :signals_heart_beat_mirror #Boolean

    validates :signals_sub_pools_values_on, :signals_sub_pools_values_off,
        numericality: true,
        if: Proc.new { |dg| dg.is_generic_config && (
          dg.signals_sub_pools_values_on.present? || dg.signals_sub_pools_values_off.present?
        ) }

    def build_signals
      sp_vals = nil
      if self.signals_sub_pools_values_on.present?
        sp_vals = {
          on: self.signals_sub_pools_values_on,
          off: self.signals_sub_pools_values_off,
        }
      end
      HashCaseConverter.to_camel_case({
        signals: {
          output_signals: (self.signals_output_signals || []).collect {|x| x[:id]},
          sub_pools_values: sp_vals,
          dso_for_signals: (self.signals_dso_for_signals || []).collect {|x| x[:id]},
          heart_beat_mirror: self.signals_heart_beat_mirror || false
        }
      })
    end
    def set_signals(generic_conf)
      signals = generic_conf[:signals]
      self.signals_output_signals = signals[:output_signals]
      sp_vals = signals[:sub_pools_values]
      if sp_vals
        self.signals_sub_pools_values_on = sp_vals[:on]
        self.signals_sub_pools_values_off = sp_vals[:off]
      end
      self.signals_dso_for_signals = signals[:dso_for_signals]
      self.signals_heart_beat_mirror = signals[:heart_beat_mirror]
    end

    # batteryDispatchConfig: Option[BatteryDispatchPercentageBounds]
    attr_accessor :dpb_min_power_for_dispatch_discharge, #Percentage
    :dpb_min_power_for_dispatch_charge  #Percentage

    validates :dpb_min_power_for_dispatch_discharge, :dpb_min_power_for_dispatch_charge,
      allow_blank: true,
      numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 100 },
      if: Proc.new { |dg| dg.is_generic_config }

    def build_battery_dispatch_config
      h = {}
      if self.dpb_min_power_for_dispatch_discharge.present?
        h[:min_power_for_dispatch_discharge] = self.dpb_min_power_for_dispatch_discharge
      end
      if self.dpb_min_power_for_dispatch_charge..present?
        h[:min_power_for_dispatch_charge] = self.dpb_min_power_for_dispatch_charge
      end
      if !h.empty?
        HashCaseConverter.to_camel_case({
          battery_dispatch_config: h
        })
      else
        h
      end
    end

    def set_battery_dispatch_config(generic_conf)
      dpb = generic_conf[:battery_dispatch_config]
      if dpb
        self.dpb_min_power_for_dispatch_discharge = dpb[:min_power_for_dispatch_discharge] if dpb[:min_power_for_dispatch_discharge]
        self.dpb_min_power_for_dispatch_charge = dpb[:min_power_for_dispatch_charge] if dpb[:min_power_for_dispatch_charge]
      end
    end

    def build_generic_config_jspn
      if self.is_generic_config
        self.generic_config = build_generic_config_hash
      else
        self.generic_config = nil
      end
    end

    def build_generic_config_hash
      h = {}
        .merge(build_entrader_upload)
        .merge(build_basic_behaviour)
        .merge(build_asset_activation_type)
        .merge(build_state_of_charge_management)
        .merge(build_asset_dispatch_strategy)
        .merge(build_cross_dg_links)
        .merge(build_deviations_compensation_opt)
        .merge(build_dg_activation_type)
        .merge(build_dispatch_commands)
        .merge(build_dispatch_source)
        .merge(build_dispatch_execution_config)
        .merge(build_nomination_extension_times_opt)
        .merge(build_notifications)
        .merge(build_periodicity)
        .merge(build_ramp_adjustment_strategy)
        .merge(build_redispatch_triggers)
        .merge(build_setpoint_validation)
        .merge(build_signals)
        .merge(build_battery_dispatch_config)
      puts ("### GENERIC CONFIG BUILD, #{h.as_json}")
      h
    end

    def set_generic_config_attrs_from_json
      generic_conf = HashCaseConverter.to_underscore(self.generic_config).deep_symbolize_keys
      puts "### GENERIC CONF FROM JSON: #{generic_conf.inspect}"
      set_entrader_upload(generic_conf)
      set_basic_behaviour(generic_conf)
      set_asset_activation_type(generic_conf)
      set_state_of_charge_management(generic_conf)
      set_asset_dispatch_strategy(generic_conf)
      set_cross_dg_links(generic_conf)
      set_deviations_compensation_opt(generic_conf)
      set_dg_activation_type(generic_conf)
      set_dispatch_commands(generic_conf)
      set_dispatch_source(generic_conf)
      set_dispatch_execution_config(generic_conf)
      set_nomination_extension_times_opt(generic_conf)
      set_notifications(generic_conf)
      set_periodicity(generic_conf)
      set_ramp_adjustment_strategy(generic_conf)
      set_redispatch_triggers(generic_conf)
      set_setpoint_validation(generic_conf)
      set_signals(generic_conf)
      set_battery_dispatch_config(generic_conf)
    end
  end
end
