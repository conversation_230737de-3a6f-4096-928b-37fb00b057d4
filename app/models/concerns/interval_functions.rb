module IntervalFunctions
  extend ActiveSupport::Concern

  included do

    def self.interval_start_attribute
      :start_time
    end

    def self.interval_end_attribute
      :end_time
    end

    def self.overlap_date_interval(interval)
       where("(#{self.table_name}.#{self.interval_start_attribute} <= ? AND #{self.table_name}.#{self.interval_end_attribute} >= ?)
           OR (#{self.table_name}.#{self.interval_start_attribute} >= ? AND #{self.table_name}.#{self.interval_start_attribute} <= ?)",
             interval.first, interval.first,
             interval.first, interval.last
             )
    end
  end
end
