# behaviour for active record models
# for which date range pickers are used in the UI
#
module IntervalInput
  INTERVAL_TIME_FORMAT = '%d/%m/%Y %H:%M'.freeze
  INTERVAL_SEPPARATOR = ' - '.freeze

  def interval
    if self[:start_time] && self[:end_time]
      [
        start_time.strftime(INTERVAL_TIME_FORMAT),
        end_time.strftime(INTERVAL_TIME_FORMAT)
      ].join(INTERVAL_SEPPARATOR)
    end
  end

  def interval=(i)
    s, e = i.split(INTERVAL_SEPPARATOR)
    self.start_time = Time.parse(s) if s.present?
    self.end_time = Time.parse(e) if e.present?
  end
end
