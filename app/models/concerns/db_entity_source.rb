require 'active_support/concern'

# Common functionality for entities that represent the date source / API source
# for the creation of other entities
#
# generated by PMS when creating/changing DUs and allocations
#
module DbEntitySource
  extend ActiveSupport::Concern

  CONTENT_TYPES = [
    CONTENT_TYPE_FILE = 'file_import'.freeze,
    CONTENT_TYPE_UI_UPDATE = 'ui_update'.freeze,
    CONTENT_TYPE_AAS_UPDATE = 'aas_update'.freeze
  ].freeze

  included do
    serialize :content, JSON
    serialize :validation_result, JSON
  end

  #
  module ClassMethods
    def filter_file_uploads
      where(content_type: CONTENT_TYPE_FILE)
    end

    def filter_ui_updates
      where(content_type: CONTENT_TYPE_UI_UPDATE)
    end

    def filter_aas_updates
      where(user_id: CONTENT_TYPE_AAS_UPDATE)
    end
  end

  def contains_file?
    content_type == CONTENT_TYPE_FILE
  end

  def content_file_contents
    content['contents'] if contains_file?
  end

  def content_file_name
    content['fileName'] if contains_file?
  end

  def success?
    !!validation_result.try(:[], 'validationSuccess')
  end

  def validation_result_errors
    validation_result.try(:[], 'errors') || []
  end

  def validation_result_warnings
    validation_result.try(:[], 'warnings') || []
  end
end