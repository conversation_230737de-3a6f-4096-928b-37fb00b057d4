module DgDlmParams
  extend ActiveSupport::Concern

  included do

    before_save :build_dlm_parameters_json

    attr_accessor :grouping_rule
    validates :grouping_rule, presence: true,
      if: Proc.new { |dg| dg.dlm_parameters_enabled? && dg.has_dlm_market?}

    attr_accessor :nominal_site_current,
      :reduction_factor
    validates :nominal_site_current, :reduction_factor, numericality: true,
      if: Proc.new { |dg| dg.dlm_parameters_enabled? && dg.has_dlm_market?}

    attr_accessor :dlm_group_reduced_id,
      :dlm_group_blocked_id
    validates :dlm_group_reduced_id, :dlm_group_blocked_id, numericality: true,
      if: Proc.new { |dg| dg.dlm_parameters_enabled? && dg.has_dlm_market?}
    validate do
      if dlm_parameters_enabled? && has_dlm_market?
        # dlm_group_reduced_id is unique
        if self.dlm_group_reduced_id.present?
          if DispatchGroup.where('id != ?', self.id || -1).where('dlm_parameters @> ?', {:dlm_group_reduced_id.to_s.camelize(:lower) => self.dlm_group_reduced_id}.to_json).size > 0
            errors.add(:dlm_group_reduced_id, I18n.t('errors.messages.taken'))
          end
        end
        # dlm_group_blocked_id is unique
        if self.dlm_group_blocked_id.present?
          if DispatchGroup.where('id != ?', self.id || -1).where('dlm_parameters @> ?', {:dlm_group_blocked_id.to_s.camelize(:lower) => self.dlm_group_blocked_id}.to_json).size > 0
            errors.add(:dlm_group_blocked_id, I18n.t('errors.messages.taken'))
          end
        end
      end
    end

    attr_accessor :control_windows
    validates :control_windows, presence: true,
      if: Proc.new { |dg| dg.dlm_parameters_enabled? && dg.has_dlm_market?}
    validate do
      hh_mm_regex = /^(0[0-9]|1[0-9]|2[0-3]):[0-5][0-9]$/
      if dlm_parameters_enabled? && has_dlm_market? && self.control_windows && !self.control_windows.empty?
        self.control_windows.each do |cw|
          start_time = cw[:start_time.to_s] || cw[:start_time]
          end_time = cw[:end_time.to_s] || cw[:end_time]
          if start_time.blank? || !start_time.match(hh_mm_regex) ||
              end_time.blank? || !end_time.match(hh_mm_regex)
            errors.add(:control_windows, I18n.t('activerecord.errors.messages.control_windows'))
          end
        end
      end
    end

    attr_accessor  :session_duration_threshold_minutes,
      :session_energy_threshold_watt_hour
    validates :session_duration_threshold_minutes, numericality: {
        greater_than_or_equal_to: 0, less_than_or_equal_to: 60 * 24,
        message: Proc.new { I18n.translate('activerecord.errors.messages.session_duration_threshold_minutes') }
      },
      if: Proc.new { |dg| dg.dlm_parameters_enabled? && dg.has_dlm_market? && dg.grouping_rule == 'session_based'}
    validates :session_duration_threshold_minutes, allow_blank: true, numericality: {
        greater_than_or_equal_to: 0, less_than_or_equal_to: 60 * 24,
        message: Proc.new { I18n.translate('activerecord.errors.messages.session_duration_threshold_minutes') }
      },
      if: Proc.new { |dg| dg.dlm_parameters_enabled? && dg.has_dlm_market? && dg.grouping_rule != 'session_based'}

    validates :session_energy_threshold_watt_hour, numericality: true,
      if: Proc.new { |dg| dg.dlm_parameters_enabled? && dg.has_dlm_market? && dg.grouping_rule == 'session_based'}
    validates :session_energy_threshold_watt_hour, numericality: true, allow_blank: true,
      if: Proc.new { |dg| dg.dlm_parameters_enabled? && dg.has_dlm_market? && dg.grouping_rule != 'session_based'}

    attr_accessor :grouping_rule_both_thresholds
    validates :grouping_rule_both_thresholds, presence: true,
      if: Proc.new { |dg| dg.dlm_parameters_enabled? && dg.has_dlm_market? && ['session_based', 'customer_based'].include?(dg.grouping_rule)}
      
    attr_accessor :min_average_charged_energy_factor,
      :min_average_charging_duration_factor
    validates :min_average_charged_energy_factor, :min_average_charging_duration_factor, numericality: true,
      if: Proc.new { |dg| dg.dlm_parameters_enabled? && dg.has_dlm_market? && dg.grouping_rule == 'customer_based'}
    validates :min_average_charged_energy_factor, :min_average_charging_duration_factor, numericality: true, allow_blank: true,
      if: Proc.new { |dg| dg.dlm_parameters_enabled? && dg.has_dlm_market? && dg.grouping_rule != 'customer_based'}

    def build_dlm_parameters_json
      if dlm_parameters_enabled?
        if has_dlm_market?
          self.dlm_parameters = build_dlm_params_hash
        else
          self.dlm_parameters = nil
        end
      end
    end

    def build_dlm_params_hash
      h = {
        grouping_rule: self.grouping_rule,
        nominal_site_current: self.nominal_site_current,
        reduction_factor: self.reduction_factor,
        dlm_group_reduced_id: self.dlm_group_reduced_id,
        dlm_group_blocked_id: self.dlm_group_blocked_id,
        control_windows: self.control_windows,
        session_duration_threshold_minutes: self.session_duration_threshold_minutes,
        session_energy_threshold_watt_hour: self.session_energy_threshold_watt_hour,
        grouping_rule_both_thresholds: self.grouping_rule_both_thresholds,
        min_average_charged_energy_factor: self.min_average_charged_energy_factor,
        min_average_charging_duration_factor: self.min_average_charging_duration_factor
      }
      HashCaseConverter.to_camel_case(h)
    end

    def set_dlm_params_attrs_from_json
      if has_dlm_market?
        dlm_params = HashCaseConverter.to_underscore(self.dlm_parameters).deep_symbolize_keys
        self.grouping_rule = dlm_params[:grouping_rule]
        self.nominal_site_current = dlm_params[:nominal_site_current]
        self.reduction_factor = dlm_params[:reduction_factor]
        self.dlm_group_reduced_id = dlm_params[:dlm_group_reduced_id]
        self.dlm_group_blocked_id =  dlm_params[:dlm_group_blocked_id]
        self.control_windows = dlm_params[:control_windows]
        self.session_duration_threshold_minutes = dlm_params[:session_duration_threshold_minutes]
        self.session_energy_threshold_watt_hour = dlm_params[:session_energy_threshold_watt_hour]
        self.grouping_rule_both_thresholds = dlm_params[:grouping_rule_both_thresholds]
        self.min_average_charged_energy_factor = dlm_params[:min_average_charged_energy_factor]
        self.min_average_charging_duration_factor = dlm_params[:min_average_charging_duration_factor]
      end
    end

    def has_dlm_market?
      Market.dlm.present? && self.market_id == Market.dlm.id
    end

    def dlm_parameters_enabled?
      DispatchGroup.column_names.include?(:dlm_parameters.to_s)
    end

  end
end