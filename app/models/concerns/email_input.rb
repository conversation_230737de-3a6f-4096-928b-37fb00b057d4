module EmailInput
  VALID_EMAIL_REGEX = /\A([^@\s]+)@((?:[-a-z0-9]+\.)+[a-z]{2,})\z/i

  # one or multiple email addresses per line, divided by semicolon
  # remove whitespaces and filter out invalid email addresses
  def extract_email_addresses(str) # todo extract method to utility or base class
    (str || '')
      .split(/$|;/)
      .collect{ |x| x.strip.presence }.compact
      .select { |x| x.match VALID_EMAIL_REGEX }
  end
end