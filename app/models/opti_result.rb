class OptiResult

  def self.validate_upload(or_lines, asset_id)
    errors = []
    # all lines must be valid
    invalid_lines = or_lines.
      each_with_index.map{ |l, index| [l.valid?, index]}.
      select{|v| v[0] == false }.collect { |v| v[1] + 1 }
    if invalid_lines.empty?
      # at least two lines in the uploaded dike
      if or_lines.size < 2
        errors << I18n.t('errors.messages.opti_result_file_invalid_not_enough_intervals')
      # all datetime values must be in the same timezone
      elsif or_lines.collect{|l| l.datetime.utc_offset}.uniq.size > 1
        errors << I18n.t('errors.messages.opti_result_file_invalid_timezone')
      # all records must have the same settlement interval (duration)
      elsif or_lines.each_cons(2).map{|a, b| b.datetime.to_i - a.datetime.to_i}.uniq.size > 1
        errors << I18n.t('errors.messages.opti_result_file_invalid_settlement_interval')
      elsif (or_lines[1].datetime.to_i - or_lines[0].datetime.to_i) != 30.minutes.seconds.to_i
        errors << I18n.t('errors.messages.opti_result_file_invalid_settlement_interval')
      elsif !allocations?(or_lines.first.datetime, or_lines.last.datetime, asset_id)
        str_from = or_lines.first.datetime.strftime("%Y-%m-%d %H:%M:%S%z")
        str_to = or_lines.last.datetime.strftime("%Y-%m-%d %H:%M:%S%z")
        errors << I18n.t('errors.messages.opti_result_file_missing_allocations', from_time: str_from, to_time: str_to)
      elsif (asset_attr_errors = validate_asset_attrs(asset_id, or_lines)).present?
        errors.concat(asset_attr_errors)
      else
        errors
      end
    else
      errors << I18n.t('errors.messages.opti_result_file_invalid', lines: invalid_lines.join(", "))
    end
    errors
  end

  def self.save_upload(vpp_trading_service, file_name, asset_id, or_lines, user_id)
    errors = []
    payload = {
      user_id: user_id,
      asset_id: asset_id,
      file_name: file_name,
      lines: or_lines.collect { |line| line.to_payload_for_upload }
    }
    err = vpp_trading_service.post_opti_result(payload)
    errors = err if err.present?
    errors
  end

  private

  def self.allocations?(start_time, end_time, asset_id)
    dg_ids = DispatchGroup.with_opti_lines.collect(&:id)
    if dg_ids.present?
      dg_allocations = Services::PortfolioManagementService.allocations(
        start_time: start_time.getutc,
        end_time: end_time.getutc,
        dg_ids: dg_ids)
      asset_allocations = dg_allocations.select{ |a|
        a['assetId'].try(:to_i) == asset_id.try(:to_i) &&
        a['allocation']
      }
      asset_allocations.present?
    else
      false
    end
  end

  def self.validate_asset_attrs(asset_id, or_lines)
    errors = []
    asset = Asset.find(asset_id)
    imposed_power_min = asset.imposed_power_min.abs
    imposed_power_max = asset.imposed_power_max
    minimum_charge_limit = asset.minimum_charge_limit.try(:value).try(:to_f)
    maximum_charge_limit = asset.maximum_charge_limit.try(:value).try(:to_f)
    dc_low_pq_flex = asset.product_prequalified_flex(Product.dynamic_containment_plus)
    dc_high_pq_flex = asset.product_prequalified_flex(Product.dynamic_containment_minus)
    dm_low_pq_flex = asset.product_prequalified_flex(Product.dynamic_moderation_plus)
    dm_high_pq_flex = asset.product_prequalified_flex(Product.dynamic_moderation_minus)
    dr_low_pq_flex = asset.product_prequalified_flex(Product.dynamic_regulation_plus)
    dr_high_pq_flex = asset.product_prequalified_flex(Product.dynamic_regulation_minus)

    invalid_soc = []
    invalid_activation_dc_pos = []
    invalid_activation_dc_neg = []
    invalid_activation_dm_pos = []
    invalid_activation_dm_neg = []
    invalid_activation_dr_pos = []
    invalid_activation_dr_neg = []
    invalid_available_char_power = []
    invalid_available_dischar_power = []
    or_lines.each_with_index do |line, index|
      if minimum_charge_limit && maximum_charge_limit && !(line.soc * 100 >= minimum_charge_limit && line.soc * 100 <= maximum_charge_limit)
        invalid_soc << (index + 1)
      end
      if dc_low_pq_flex && !(line.activation_dc_pos >= 0 && line.activation_dc_pos <= dc_low_pq_flex)
        invalid_activation_dc_pos << (index + 1)
      end
      if dc_high_pq_flex && !(line.activation_dc_neg >= 0 && line.activation_dc_neg <= dc_high_pq_flex)
        invalid_activation_dc_neg << (index + 1)
      end
      if dm_low_pq_flex && !(line.activation_dm_pos >= 0 && line.activation_dm_pos <= dm_low_pq_flex)
        invalid_activation_dm_pos << (index + 1)
      end
      if dm_high_pq_flex && !(line.activation_dm_neg >= 0 && line.activation_dm_neg <= dm_high_pq_flex)
        invalid_activation_dm_neg << (index + 1)
      end
      if dr_low_pq_flex && !(line.activation_dr_pos >= 0 && line.activation_dr_pos <= dr_low_pq_flex)
        invalid_activation_dr_pos << (index + 1)
      end
      if dr_high_pq_flex && !(line.activation_dr_neg >= 0 && line.activation_dr_neg <= dr_high_pq_flex)
        invalid_activation_dr_neg << (index + 1)
      end
      if !(line.available_char_power >= 0 && line.available_char_power <= imposed_power_min)
        invalid_available_char_power << (index + 1)
      end
      if !(line.available_dischar_power >= 0 && line.available_dischar_power <= imposed_power_max)
        invalid_available_dischar_power << (index + 1)
      end
    end
    errors << I18n.t('errors.messages.opti_result_file_invalid_soc',
      lines: invalid_soc.join(", "),
      low_val: minimum_charge_limit, high_val: maximum_charge_limit) if invalid_soc.present?
    errors << I18n.t('errors.messages.opti_result_file_invalid_activation_dc_pos',
      lines: invalid_activation_dc_pos.join(", "),
      low_val: 0, high_val: dc_low_pq_flex) if invalid_activation_dc_pos.present?
    errors << I18n.t('errors.messages.opti_result_file_invalid_activation_dc_neg',
      lines: invalid_activation_dc_neg.join(", "),
      low_val: 0, high_val: dc_high_pq_flex) if invalid_activation_dc_neg.present?
      errors << I18n.t('errors.messages.opti_result_file_invalid_activation_dm_pos',
      lines: invalid_activation_dm_pos.join(", "),
      low_val: 0, high_val: dm_low_pq_flex) if invalid_activation_dm_pos.present?
    errors << I18n.t('errors.messages.opti_result_file_invalid_activation_dm_neg',
      lines: invalid_activation_dm_neg.join(", "),
      low_val: 0, high_val: dm_high_pq_flex) if invalid_activation_dm_neg.present?
      errors << I18n.t('errors.messages.opti_result_file_invalid_activation_dr_pos',
      lines: invalid_activation_dr_pos.join(", "),
      low_val: 0, high_val: dr_low_pq_flex) if invalid_activation_dr_pos.present?
    errors << I18n.t('errors.messages.opti_result_file_invalid_activation_dr_neg',
      lines: invalid_activation_dr_neg.join(", "),
      low_val: 0, high_val: dr_high_pq_flex) if invalid_activation_dr_neg.present?
      errors << I18n.t('errors.messages.opti_result_file_invalid_available_char_power',
      lines: invalid_available_char_power.join(", "),
      low_val: 0, high_val: imposed_power_min) if invalid_available_char_power.present?
    errors << I18n.t('errors.messages.opti_result_file_invalid_available_dischar_power',
      lines: invalid_available_dischar_power.join(", "),
      low_val: 0, high_val: imposed_power_max) if invalid_available_dischar_power.present?

    errors
  rescue ActiveRecord::RecordNotFound
    [I18n.t('errors.messages.opti_result_file_invalid_asset', asset_id: asset_id)]
  end

end