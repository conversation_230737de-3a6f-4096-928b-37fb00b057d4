class AuctionConfig < ApplicationRecord

  include Authority::Abilities

  self.table_name = 'vpp_trading_auction_rules'
  self.locking_column = :version

  has_and_belongs_to_many :dispatch_groups,
    join_table: 'dispatch_group_vpp_trading_auction_rules',
    foreign_key: 'vpp_trading_auction_rules_id',
    validate: false

  has_many :nomination_bids, foreign_key: 'auction_rules_id'

  scope :not_deleted, -> { where("vpp_trading_auction_rules.deleted IS NULL OR vpp_trading_auction_rules.deleted > CURRENT_TIMESTAMP")}

  def self.active_with_bidding_method(bidding_method_name, interval_start)
    acfg_by_market = {}
    AuctionConfig.not_deleted.
      where("value->'biddingMethod'->>'key'=?", bidding_method_name).
      where("to_timestamp(value->'auctionDefinitionInterval'->>'start', 'YYYY-MM-DD\"T\"HH24:MI:SS\"Z\"') <= ?", interval_start).
      where("value->'auctionDefinitionInterval'->>'end' is null OR to_timestamp(value->'auctionDefinitionInterval'->>'end', 'YYYY-MM-DD\"T\"HH24:MI:SS\"Z\"') > ?", interval_start).
      each do |cfg|
        markets = cfg.value.dig("biddingMethod", "market")
        markets = [markets] if markets.kind_of?(String)
        markets.each do |market|
          if acfg_by_market[market].present?
            acfg_by_market[market] << cfg
          else
            acfg_by_market[market]  = [cfg]
          end
        end
      end
      acfg_by_market
  end

  def self.with_bidding_method(bidding_method_name)
    AuctionConfig.not_deleted.
      where("value->'biddingMethod'->>'key'=?", bidding_method_name)
  end

  def self.polymorphic_name
    'auctionRule'
  end

  # rule is active if its end time is in the future
  def active?
    @h ||= HashCaseConverter.to_underscore(self.value).deep_symbolize_keys
    end_time = @h[:auction_definition_interval][:end]
    end_time.nil? || Time.parse(end_time) >= Time.now
  end

  def bidding_method_nl_afrr?
    selected_id(self.bidding_method) == "nlAfrr"
  end

  def bidding_method_battery_uk?
    selected_id(self.bidding_method) == "BatteryUk"
  end

  def bidding_method_battery_de?
    selected_id(self.bidding_method) == "BatteryDe"
  end

  def has_bid_input?(bid_input)
    @h ||= HashCaseConverter.to_underscore(self.value).deep_symbolize_keys
    if @h[:bid_inputs]
      @h[:bid_inputs].find{|bi| bi[:key] == bid_input}.present?
    else
      false
    end
  end

  def has_internal_bidding_input_format?(input_array)
    @h ||= HashCaseConverter.to_underscore(self.value).deep_symbolize_keys
    internal_bi = @h[:bid_inputs].find{|bi| bi[:key] == 'InternalBiddingKind'} if @h[:bid_inputs]
    if internal_bi
      if input_array.present?
        input_array.include?(internal_bi[:bidding_input_format])
      else
        internal_bi[:bidding_input_format].present?
      end
    else
      false
    end
  end

  def bidding_method_market_name
    @h ||= HashCaseConverter.to_underscore(self.value).deep_symbolize_keys
    bidding_method = @h[:bidding_method]
    if bidding_method.is_a?(Hash)
      bidding_method[:market]
    elsif bidding_method == 'nlAfrr'
      Market::AFRR__MARKET_NAME
    else
      nil
    end
  end

  VALUE_ATTRIBUTES = [
    :auction_definition_interval,
    :time_zone,
    :bidding_method,
    :bidding_method_market,
    :swing_limit,
    :swing_limit_value,
    :assets,
    :frequency_time_minutes,
    :delivery_interval,
    :delivery_interval_days_shifted_hours,
    :offers_time_block,
    :bidding_direction,
    :rounding_digits,
    :minimum_mw_volume_for1_bid,
    :minimum_mw_volume,
    :price_type,
    :energy_price_rounding_digits,
    :capacity_price_rounding_digits,
    :optimization_horizon_change,
    :optimization_horizon_change_days_before_delivery,
    :optimization_horizon_change_time_hours,
    :optimization_horizon_change_time_minutes,
    :market_auction_start,
    :market_auction_start_days_before_delivery,
    :market_auction_start_time_hours,
    :market_auction_start_time_minutes,
    :market_auction_start_minutes,
    :market_auction_end,
    :market_auction_end_days_before_delivery,
    :market_auction_end_time_hours,
    :market_auction_end_time_minutes,
    :market_auction_end_minutes,
    :bid_inputs,
    :internal_bidding_input_format,
    :internal_auction_start,
    :internal_auction_start_days_before_delivery,
    :internal_auction_start_time_hours,
    :internal_auction_start_time_minutes,
    :internal_auction_start_minutes,
    :internal_auction_end,
    :internal_auction_end_days_before_delivery,
    :internal_auction_end_time_hours,
    :internal_auction_end_time_minutes,
    :internal_auction_end_minutes,
    :customer_bidding_input_format,
    :customer_auction_start,
    :customer_auction_start_days_before_delivery,
    :customer_auction_start_local_time_hours,
    :customer_auction_start_local_time_minutes,
    :customer_auction_start_minutes,
    :customer_auction_minimum_mw_volume_for1_bid,
    :customer_auction_end,
    :customer_auction_end_days_before_delivery,
    :customer_auction_end_local_time_hours,
    :customer_auction_end_local_time_minutes,
    :customer_auction_end_minutes,
    :optimizer_bod_frequency_minutes,
    :optimizer_bod_computed_sp,
    :optimizer_bod_delta_for_submission,
    :optimizer_bod_delta_for_undo,
    :api_auction_bidding,
    :auction_system,
    :auction_system_upload_behaviour,
    :auction_system_upload_market_time,
    :auction_system_upload_number_of_retries,
    :auction_system_upload_seconds_between_retries,
    :auction_system_download_behaviour,
    :auction_system_download_market_time,
    :auction_system_download_number_of_retries,
    :auction_system_download_seconds_between_retries,
    :auction_system_ui_trader_look_ahead_hours,
    :auction_system_ui_price_grouping_step,
    :auction_system_ui_bid_expiry_warning_minutes,
    :auction_system_ui_currency,
    :auction_system_ui_site_id,
    :auction_system_ui_for_battery_uk_intraday_asset_id,
    :auction_system_ui_for_battery_uk_intraday_portfolio_id,
    :auction_system_ui_for_battery_uk_intraday_export_entrader,
    :auction_system_ui_for_battery_uk_intraday_upload_folder,
    :auction_system_uk_vatp_smart_volume_strategy_template_id,
    :auction_system_uk_vatp_hidden_iceberg_strategy_template_id,
    :auction_system_uk_vatp_smart_iceberg_strategy_template_id,
    :auction_system_de_vatp_smart_volume_strategy_template_id,
    :auction_system_de_vatp_hidden_iceberg_strategy_template_id,
    :auction_system_de_vatp_smart_iceberg_strategy_template_id,
    :create_nominations,
    :create_allocations,
    :allocation_start_extension_seconds,
    :allocation_end_extension_seconds,
    :allocation_end_extension_from_ramp_rate
  ]

  attr_accessor :dispatch_group_ids_and_names
  attr_accessor *VALUE_ATTRIBUTES

  validates :name, presence: true
  validates :name, uniqueness: { scope: :deleted }
  validates :dispatch_group_ids, presence: true,
    if: Proc.new { |cfg| selected_id(cfg.bidding_method) != "SpotOptimization" && !ids_from_selection(cfg.auction_system).include?("ui_for_battery_uk_intraday") && (selected_id(cfg.create_allocations) || selected_id(cfg.create_nominations))}
  validates :auction_definition_interval,
            :time_zone,
            :bidding_method,
            :delivery_interval,
            :offers_time_block,
            :bidding_direction,
            presence: true

  validates :delivery_interval_days_shifted_hours,presence: true,
    numericality: {only_integer: true},
    if: Proc.new { |cfg| ["Days", "Blocks"].include?(selected_id(cfg.delivery_interval))  }

  validates :name, :bidding_method_market, presence: true,
    if: Proc.new { |cfg| ["BatteryUk", "BatteryDe", "BatteryUkWithinDayOptimization", "BatteryDeWithinDayOptimization"].include?(selected_id(cfg.bidding_method)) }

  validates :swing_limit_value,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| ["BatteryUk", "BatteryDe"].include?(selected_id(cfg.bidding_method)) && cfg.swing_limit }

  validates :rounding_digits,
            numericality: {greater_than_or_equal_to: 0, only_integer: true}

  validates :energy_price_rounding_digits,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| ['EnergyOnly', 'CapacityOnly', 'EnergyAndCapacity'].include?(selected_id(cfg.price_type)) }
  validates :capacity_price_rounding_digits,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| ['EnergyAndCapacity'].include?(selected_id(cfg.price_type)) }

  validates :optimization_horizon_change_days_before_delivery,
            :optimization_horizon_change_time_hours,
            :optimization_horizon_change_time_minutes,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| selected_id(cfg.optimization_horizon_change) == 'OptimizationHorizonChangeAbsolute' }

  validates :market_auction_start,
            :market_auction_end,
            presence: true
  validates :market_auction_start_days_before_delivery,
            :market_auction_start_time_hours,
            :market_auction_start_time_minutes,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| selected_id(cfg.market_auction_start) == 'AuctionTimeAbsolute' }
  validates :market_auction_start_minutes,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| selected_id(cfg.market_auction_start) == 'AuctionTimeRelative' }

  validates :market_auction_end_days_before_delivery,
            :market_auction_end_time_hours,
            :market_auction_end_time_minutes,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| selected_id(cfg.market_auction_end) == 'AuctionTimeAbsolute' }
  validates :market_auction_end_minutes,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| selected_id(cfg.market_auction_end) == 'AuctionTimeRelative' }

  validates :internal_bidding_input_format,
            :internal_auction_start,
            :internal_auction_end,
            presence: true,
            if: Proc.new { |cfg| ids_from_selection(cfg.bid_inputs).include?("InternalBiddingKind") }
  validates :internal_auction_start_days_before_delivery,
            :internal_auction_start_time_hours,
            :internal_auction_start_time_minutes,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| ids_from_selection(cfg.bid_inputs).include?("InternalBiddingKind") &&
                                  selected_id(cfg.internal_auction_start) == 'AuctionTimeAbsolute' }
  validates :internal_auction_start_minutes,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| ids_from_selection(cfg.bid_inputs).include?("InternalBiddingKind") &&
                                  selected_id(cfg.internal_auction_start) == 'AuctionTimeRelative' }
  validates :internal_auction_end_days_before_delivery,
            :internal_auction_end_time_hours,
            :internal_auction_end_time_minutes,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| ids_from_selection(cfg.bid_inputs).include?("InternalBiddingKind") &&
                                  selected_id(cfg.internal_auction_end) == 'AuctionTimeAbsolute' }
  validates :internal_auction_end_minutes,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| ids_from_selection(cfg.bid_inputs).include?("InternalBiddingKind") &&
                                  selected_id(cfg.internal_auction_end) == 'AuctionTimeRelative' }

  validates :customer_bidding_input_format,
            :customer_auction_start,
            :customer_auction_end,
            :customer_auction_minimum_mw_volume_for1_bid,
            presence: true,
            if: Proc.new { |cfg| ids_from_selection(cfg.bid_inputs).include?("CustomerBiddingKind") }
  validates :customer_auction_start_days_before_delivery,
            :customer_auction_start_local_time_hours,
            :customer_auction_start_local_time_minutes,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| ids_from_selection(cfg.bid_inputs).include?("CustomerBiddingKind") &&
                                  selected_id(cfg.customer_auction_start) == 'AuctionTimeAbsolute' }
  validates :customer_auction_start_minutes,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| ids_from_selection(cfg.bid_inputs).include?("CustomerBiddingKind") &&
                                  selected_id(cfg.customer_auction_start) == 'AuctionTimeRelative' }
  validates :customer_auction_end_days_before_delivery,
            :customer_auction_end_local_time_hours,
            :customer_auction_end_local_time_minutes,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| ids_from_selection(cfg.bid_inputs).include?("CustomerBiddingKind") &&
                                  selected_id(cfg.customer_auction_end) == 'AuctionTimeAbsolute' }
  validates :customer_auction_end_minutes,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| ids_from_selection(cfg.bid_inputs).include?("CustomerBiddingKind") &&
                                  selected_id(cfg.customer_auction_end) == 'AuctionTimeRelative' }

  validates :optimizer_bod_frequency_minutes,
            :optimizer_bod_computed_sp,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| ids_from_selection(cfg.bid_inputs).include?("OptimizerBodKind") }
  validates :optimizer_bod_delta_for_submission,
            :optimizer_bod_delta_for_undo,
            numericality: true,
            if: Proc.new { |cfg| ids_from_selection(cfg.bid_inputs).include?("OptimizerBodKind") }

  validates :auction_system, presence: true

  validates :auction_system_upload_behaviour,
            presence: true,
            if: Proc.new { |cfg| ids_from_selection(cfg.auction_system).include?("upload") }
  validates :auction_system_upload_market_time,
            :auction_system_upload_number_of_retries,
            :auction_system_upload_seconds_between_retries,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| ids_from_selection(cfg.auction_system).include?("upload") }

  validates :auction_system_download_behaviour,
            presence: true,
            if: Proc.new { |cfg| ids_from_selection(cfg.auction_system).include?("download") }
  validates :auction_system_download_market_time,
            :auction_system_download_number_of_retries,
            :auction_system_download_seconds_between_retries,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| ids_from_selection(cfg.auction_system).include?("download") }
  validates :auction_system_ui_bid_expiry_warning_minutes,
            :auction_system_ui_trader_look_ahead_hours,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| ids_from_selection(cfg.auction_system).include?("ui") }
  validates :auction_system_ui_price_grouping_step,
            numericality: {greater_than_or_equal_to: 0, only_integer: false},
            if: Proc.new { |cfg| ids_from_selection(cfg.auction_system).include?("ui") }

  validates :auction_system_ui_for_battery_uk_intraday_asset_id, :auction_system_ui_for_battery_uk_intraday_portfolio_id,
            :auction_system_ui_for_battery_uk_intraday_export_entrader,
            presence: true,
            if: Proc.new { |cfg| ids_from_selection(cfg.auction_system).include?("ui_for_battery_uk_intraday") }

  validates :auction_system_ui_for_battery_uk_intraday_upload_folder,
            presence: true,
            if: Proc.new { |cfg| ids_from_selection(cfg.auction_system).include?("ui_for_battery_uk_intraday") && selected_id(cfg.auction_system_ui_for_battery_uk_intraday_export_entrader)}

  UUID_FORMAT = /\A[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\z/i
  validates :auction_system_uk_vatp_smart_volume_strategy_template_id,
            :auction_system_uk_vatp_hidden_iceberg_strategy_template_id,
            :auction_system_uk_vatp_smart_iceberg_strategy_template_id,
            presence: true,
            format: { with: UUID_FORMAT, message: :invalid_uuid },
            if: Proc.new { |cfg| ids_from_selection(cfg.auction_system).include?("uk_vatp") }

  validates :auction_system_de_vatp_smart_volume_strategy_template_id,
            :auction_system_de_vatp_hidden_iceberg_strategy_template_id,
            :auction_system_de_vatp_smart_iceberg_strategy_template_id,
            presence: true,
            format: { with: UUID_FORMAT, message: :invalid_uuid },
            if: Proc.new { |cfg| ids_from_selection(cfg.auction_system).include?("de_vatp") }

  validates :create_nominations, presence: true
  validates :create_allocations, presence: true
  validates :allocation_start_extension_seconds, :allocation_end_extension_seconds, allow_blank: true,
            numericality: {greater_than_or_equal_to: 0, only_integer: true},
            if: Proc.new { |cfg| selected_id(cfg.create_allocations) }

  validate do
    if errors.empty?
      # enforce that all the DG's have the same market
      market_ids = DispatchGroup.select(:market_id).where(id: self.dispatch_group_ids).
        collect{|dg| dg.market_id}.uniq
      if market_ids.size > 1
        errors.add(:dispatch_group_ids, I18n.t('errors.messages.dispatch_groups_same_market'))
      end
    end
    if errors.empty?
      if selected_id(self.bidding_method) == "SpotOptimization" && selected_id(self.create_nominations) == true
        errors.add(:create_nominations, I18n.t('errors.messages.cannot_create_nomination_for_spot_optimization'))
      end
    end
    if errors.empty?
      if ["BatteryUk", "BatteryDe"].include?(selected_id(self.bidding_method))
        err_msg = I18n.t('errors.messages.duplicate_tender_with_same_bidding_method_and_market')
        interval_start = Time.parse(self.auction_definition_interval.first)
        interval_end = self.auction_definition_interval.size > 1 ? Time.parse(self.auction_definition_interval.last) : nil
        duplicates = AuctionConfig.not_deleted
          .where.not(id: self.id)
          .where(
            "(value->'auctionDefinitionInterval'->>'end' IS NULL) OR
             (to_timestamp(value->'auctionDefinitionInterval'->>'end', 'YYYY-MM-DD\"T\"HH24:MI:SS\"Z\"') > ?)",
            interval_start
          )
          .where("value->'biddingMethod'->>'key' = ?", selected_id(self.bidding_method))
          .where(
            "((value->'biddingMethod'->'market'->>0 IS NOT NULL AND (value->'biddingMethod'->>'market') IN (:market_string)) OR
             ((value->'biddingMethod'->'market')::jsonb @> '[]'::jsonb AND (value->'biddingMethod'->'market')::jsonb ?| array[:market_array]))",
            market_string: selected_id(self.bidding_method_market), market_array: ids_from_selection(self.bidding_method_market)
          )
        if interval_end
          duplicates = duplicates.where("to_timestamp(value->'auctionDefinitionInterval'->>'start', 'YYYY-MM-DD\"T\"HH24:MI:SS\"Z\"') < ?", interval_end)
        end
        asset_conditions = []
        asset_params = {}
        uk_selected = selected_id(self.bidding_method) == "BatteryUk"
        if uk_selected
          asset_conditions << "value::jsonb->'auctionSystem'->'uiForBatteryUkIntraday'->>'assetId' = :uk_asset_id"
          asset_params[:uk_asset_id] = selected_id(self.auction_system_ui_for_battery_uk_intraday_asset_id).try(:to_s)
        end
        if asset_conditions.any?
          err_msg = I18n.t('errors.messages.duplicate_tender_with_same_bidding_method_and_market_site')
          duplicates = duplicates.where(asset_conditions.join(' OR '), asset_params)
        end
        if duplicates.size > 0
          errors.add(:base, err_msg)
        end
      end
    end
    if errors.empty?
      if ["BatteryUkWithinDayOptimization", "BatteryDeWithinDayOptimization"].include?(selected_id(self.bidding_method)) &&
        self.bidding_method_market.size > 1
        errors.add(:bidding_method_market, :invalid)
      end
    end
    if errors.empty?
      config = HashCaseConverter.to_camel_case(to_value_hash).to_json
      puts "### VALIDATING CONFIG: \n#{config}"
      if !Services::VppTradingService.validate_tender_config(config)
        errors.add(:base, I18n.t('errors.messages.invalid_tender_config'))
      end
    end
  end

  before_save :set_json_value
  def set_json_value
    value_hash = to_value_hash
    self.value = HashCaseConverter.to_camel_case(value_hash)
  end

  # used for circumventing the UI for configuring an Auction Config - the json containing the configuration
  # will be set directly
  def value_json=(json_string)
    self[:value] = JSON.parse(json_string)
    from_value_hash
  end

  def mark_as_deleted
    update_columns(deleted: Time.now.getutc)
  end

  def from_value_hash
    h = HashCaseConverter.to_underscore(self.value).deep_symbolize_keys
    self.auction_definition_interval = [h[:auction_definition_interval][:start]]
    if h[:auction_definition_interval][:end]
      self.auction_definition_interval << h[:auction_definition_interval][:end]
    end
    self.time_zone = arr_for_selection(h[:time_zone])
    bidding_method_from_hash(h)
    market_auction_from_hash(h[:auction_time_settings])
    self.delivery_interval = arr_for_selection(h[:auction_time_settings][:delivery_interval][:kind])
    if ["Days", "Blocks"].include?(h[:auction_time_settings][:delivery_interval][:kind])
      self.delivery_interval_days_shifted_hours = h[:auction_time_settings][:delivery_interval][:shifted_hours]
    end
    self.offers_time_block = arr_for_selection(h[:auction_time_settings][:offers_time_block][:kind])
    self.bidding_direction = arr_for_selection(h[:bid_direction_and_volume_settings][:bidding_direction])
    self.rounding_digits = h[:bid_direction_and_volume_settings][:rounding_digits]
    self.minimum_mw_volume_for1_bid = h[:bid_direction_and_volume_settings][:minimum_mw_volume_for1_bid]
    self.minimum_mw_volume = h[:bid_direction_and_volume_settings][:minimum_mw_volume]
    self.price_type = h[:price_type] ? arr_for_selection(h[:price_type][:key]) : []
    self.energy_price_rounding_digits = h[:price_type][:digits_for_rounding] if h[:price_type]
    self.capacity_price_rounding_digits =  h[:price_type][:digits_for_rounding_capacity] if h[:price_type]

    self.bid_inputs = []
    internal_input_from_hash(h)
    customer_input_from_hash(h)
    api_input_from_hash(h)
    optimizer_bod_from_hash(h)

    self.auction_system = []
    upload = h[:auction_system][:upload]
    download = h[:auction_system][:download]
    ui = h[:auction_system][:ui]
    ui_for_battery_uk_intraday = h[:auction_system][:ui_for_battery_uk_intraday]
    uk_vatp = h[:auction_system][:uk_vatp]
    de_vatp = h[:auction_system][:de_vatp]
    if upload
      self.auction_system << "upload"
      self.auction_system_upload_behaviour = arr_for_selection(upload[:behaviour])
      self.auction_system_upload_market_time = upload[:market_time][:minutes]
      self.auction_system_upload_number_of_retries = upload[:number_of_retries]
      self.auction_system_upload_seconds_between_retries = upload[:seconds_between_retries]
    end
    if download
      self.auction_system << "download"
      self.auction_system_download_behaviour = arr_for_selection(download[:behaviour])
      self.auction_system_download_market_time = download[:market_time][:minutes]
      self.auction_system_download_number_of_retries = download[:number_of_retries]
      self.auction_system_download_seconds_between_retries = download[:seconds_between_retries]
    end
    if ui
      self.auction_system << "ui"
      self.auction_system_ui_trader_look_ahead_hours = ui[:trader_look_ahead_hours]
      self.auction_system_ui_price_grouping_step = ui[:price_grouping_step]
      self.auction_system_ui_bid_expiry_warning_minutes = ui[:bid_expiry_warning_minutes]
      self.auction_system_ui_currency = ui[:currency][:currency]
      self.auction_system_ui_site_id = ui[:site_id]
    end
    if ui_for_battery_uk_intraday
      self.auction_system << "ui_for_battery_uk_intraday"
      self.auction_system_ui_for_battery_uk_intraday_asset_id = arr_for_selection(ui_for_battery_uk_intraday[:asset_id])
      self.auction_system_ui_for_battery_uk_intraday_portfolio_id = ui_for_battery_uk_intraday[:portfolio_id]
      self.auction_system_ui_for_battery_uk_intraday_export_entrader = arr_for_selection(ui_for_battery_uk_intraday[:export_entrader])
      self.auction_system_ui_for_battery_uk_intraday_upload_folder = ui_for_battery_uk_intraday[:upload_folder]
    end
    if uk_vatp
      self.auction_system << "uk_vatp"
      self.auction_system_uk_vatp_smart_volume_strategy_template_id = uk_vatp[:smart_volume_strategy_template_id]
      self.auction_system_uk_vatp_hidden_iceberg_strategy_template_id = uk_vatp[:hidden_iceberg_strategy_template_id]
      self.auction_system_uk_vatp_smart_iceberg_strategy_template_id = uk_vatp[:smart_iceberg_strategy_template_id]
    end
    if de_vatp
      self.auction_system << "de_vatp"
      self.auction_system_de_vatp_smart_volume_strategy_template_id = de_vatp[:smart_volume_strategy_template_id]
      self.auction_system_de_vatp_hidden_iceberg_strategy_template_id = de_vatp[:hidden_iceberg_strategy_template_id]
      self.auction_system_de_vatp_smart_iceberg_strategy_template_id = de_vatp[:smart_iceberg_strategy_template_id]
    end
    arpp = h[:auction_results_post_processing]
    self.create_nominations = arr_for_selection(arpp[:create_nominations])
    self.create_allocations = arr_for_selection(arpp[:create_allocations])
    self.allocation_start_extension_seconds = arpp[:allocation_start_extension_seconds]
    self.allocation_end_extension_seconds = arpp[:allocation_end_extension_seconds]
    self.allocation_end_extension_from_ramp_rate = arr_for_selection(arpp[:allocation_end_extension_from_ramp_rate])
  end


  private

  def to_value_hash
    interval_start = self.auction_definition_interval.first
    interval_end = self.auction_definition_interval.size > 1 ? self.auction_definition_interval.last : nil
    h = {
      auction_definition_interval: {
        'start': interval_start,
        'end': nil_if_blank(interval_end)
      },
      name: self.name,
      bidding_method: build_bidding_method,
      auction_time_settings: {
        auction_start: build_market_auction_start,
        auction_end: build_market_auction_end,
        delivery_interval: build_delivery_interval,
        offers_time_block: {
          kind: selected_id(offers_time_block),
        }
      },
      bid_direction_and_volume_settings: {
        bidding_direction: selected_id(self.bidding_direction),
        rounding_digits: self.rounding_digits,
        minimum_mw_volume_for1_bid: self.minimum_mw_volume_for1_bid,
        minimum_mw_volume: self.minimum_mw_volume
      },
      price_type: build_price_type, #optional
      bid_inputs: build_bid_inputs,
      auction_system: build_auction_system,
      auction_results_post_processing: build_auction_results_post_processing,
      time_zone: self.time_zone.try(:first)
    }
    h
  end

  def build_auction_results_post_processing
    {
      create_nominations: selected_id(self.create_nominations),
      create_allocations: selected_id(self.create_allocations),
      allocation_start_extension_seconds: nil_if_blank(self.allocation_start_extension_seconds),
      allocation_end_extension_seconds: nil_if_blank(self.allocation_end_extension_seconds),
      allocation_end_extension_from_ramp_rate: selected_id(self.allocation_end_extension_from_ramp_rate),
    }
  end

  def build_delivery_interval
    h = {
      number: 1,
      kind: selected_id(self.delivery_interval)
    }
    if ["Days", "Blocks"].include?(selected_id(self.delivery_interval))
      h[:shifted_hours] =  self.delivery_interval_days_shifted_hours
    end
    h
  end

  def market_auction_from_hash(h)
    self.market_auction_start = arr_for_selection(h[:auction_start][:key])
    self.market_auction_start_days_before_delivery = h[:auction_start][:days_before_delivery]
    self.market_auction_start_time_hours = h[:auction_start][:hours]
    self.market_auction_start_time_minutes = h[:auction_start][:minutes]
    self.market_auction_start_minutes = h[:auction_start][:minutes]
    self.market_auction_end = arr_for_selection(h[:auction_end][:key])
    self.market_auction_end_days_before_delivery = h[:auction_end][:days_before_delivery]
    self.market_auction_end_time_hours = h[:auction_end][:hours]
    self.market_auction_end_time_minutes = h[:auction_end][:minutes]
    self.market_auction_end_minutes = h[:auction_end][:minutes]
  end

  def build_market_auction_start
    auction_start = {}
    if selected_id(self.market_auction_start) == 'AuctionTimeAbsolute'
      auction_start = {
        key: 'AuctionTimeAbsolute',
        days_before_delivery: self.market_auction_start_days_before_delivery,
        hours: self.market_auction_start_time_hours,
        minutes: self.market_auction_start_time_minutes
      }
    elsif selected_id(self.market_auction_start) == 'AuctionTimeRelative'
      auction_start = {
        key: 'AuctionTimeRelative',
        minutes: self.market_auction_start_minutes,
      }
    elsif selected_id(self.market_auction_start) == 'AuctionTimeAnyTime'
      auction_start = {
        key: 'AuctionTimeAnyTime',
      }
    end
    auction_start
  end

  def build_market_auction_end
    auction_end = {}
    if selected_id(self.market_auction_end) == 'AuctionTimeAbsolute'
      auction_end = {
        key: 'AuctionTimeAbsolute',
        days_before_delivery: self.market_auction_end_days_before_delivery,
        hours: self.market_auction_end_time_hours,
        minutes: self.market_auction_end_time_minutes
      }
    elsif selected_id(self.market_auction_end) == 'AuctionTimeRelative'
      auction_end = {
        key: 'AuctionTimeRelative',
        minutes: self.market_auction_end_minutes,
      }
    elsif selected_id(self.market_auction_end) == 'AuctionTimeAnyTime'
      auction_end = {
        key: 'AuctionTimeAnyTime'
      }
    elsif selected_id(self.market_auction_end) == 'AuctionTimeRelativeToBid'
      auction_end = {
        key: 'AuctionTimeRelativeToBid'
      }
    end
    auction_end
  end

  def api_input_from_hash(h)
    v2g_bi = h[:bid_inputs].find{|bi| bi[:key] == 'V2gBiddingKind'} if h[:bid_inputs]
    if v2g_bi
      self.api_auction_bidding = arr_for_selection(v2g_bi[:key])
      self.bid_inputs << v2g_bi[:key]
    end
    spot_optimization_bi = h[:bid_inputs].find{|bi| bi[:key] == 'SpotOptimisationBiddingKind'} if h[:bid_inputs]
    if spot_optimization_bi
      self.api_auction_bidding = arr_for_selection(spot_optimization_bi[:key])
      self.bid_inputs << spot_optimization_bi[:key]
    end
  end

  def nil_if_blank(value)
    value.blank? ? nil : value
  end

  def build_bidding_method
    bidding_method_id = selected_id(self.bidding_method)
    if bidding_method_id == "BatteryUk" || bidding_method_id == "BatteryDe"
      {
        key: bidding_method_id,
        market: self.bidding_method_market.size == 1 ? selected_id(self.bidding_method_market) : ids_from_selection(self.bidding_method_market),
        swing_limit: self.swing_limit ? self.swing_limit_value : nil
      }
    elsif bidding_method_id == "BatteryUkWithinDayOptimization" || bidding_method_id == "BatteryDeWithinDayOptimization"
      optimization_horizon_change = {}
      if selected_id(self.optimization_horizon_change) == 'OptimizationHorizonChangeAbsolute'
        optimization_horizon_change = {
          days_before_delivery: self.optimization_horizon_change_days_before_delivery,
          hours: self.optimization_horizon_change_time_hours,
          minutes: self.optimization_horizon_change_time_minutes
        }
      end
      {
        key: bidding_method_id,
        market: selected_id(self.bidding_method_market),
        asset_ids: ids_from_selection(self.assets),
        frequency_time_minutes: self.frequency_time_minutes,
        optimization_horizon_change: optimization_horizon_change
      }
    else
      bidding_method_id
    end
  end
  
  def build_price_type
    if self.price_type.empty?
      nil
    else
      {
        key: selected_id(self.price_type),
        digits_for_rounding: nil_if_blank(self.energy_price_rounding_digits),
        digits_for_rounding_capacity: nil_if_blank(self.capacity_price_rounding_digits)
      }
    end
  end

  def build_bid_inputs
    bid_inputs_arr = []
    if ids_from_selection(self.bid_inputs).include?("InternalBiddingKind")
      bid_inputs_arr << build_internal_bidding_input
    end
    if ids_from_selection(self.bid_inputs).include?("CustomerBiddingKind")
      bid_inputs_arr << build_customer_bidding_input
    end
    if ids_from_selection(self.bid_inputs).include?("V2gBiddingKind")
      bid_inputs_arr << {
        key: selected_id(self.api_auction_bidding),
        auction_start: {
          key: 'AuctionTimeAnyTime'
        },
        auction_closing: {
          key: 'AuctionTimeAnyTime'
        }
      }
    end
    if ids_from_selection(self.bid_inputs).include?("SpotOptimisationBiddingKind")
      bid_inputs_arr << {
        key: "SpotOptimisationBiddingKind",
      }
    end
    if ids_from_selection(self.bid_inputs).include?("OptimizerBodKind")
      bid_inputs_arr << build_optimizer_bod_bidding_input
    end
    bid_inputs_arr
  end

  def bidding_method_from_hash(h)
    if h[:bidding_method].kind_of?(String)
      self.bidding_method = arr_for_selection(h[:bidding_method])
    else
      self.bidding_method = arr_for_selection(h[:bidding_method][:key])
      if h[:bidding_method][:key] == "BatteryUk" || h[:bidding_method][:key] == "BatteryDe"
        if h[:bidding_method][:market].kind_of?(String)
          self.bidding_method_market = arr_for_selection(h[:bidding_method][:market])
        else
          self.bidding_method_market = h[:bidding_method][:market]
        end
        self.swing_limit = h[:bidding_method][:swing_limit].present?
        self.swing_limit_value = h[:bidding_method][:swing_limit] if h[:bidding_method][:swing_limit]
      elsif h[:bidding_method][:key] == "BatteryUkWithinDayOptimization" || h[:bidding_method][:key] == "BatteryDeWithinDayOptimization"
        self.assets = h[:bidding_method][:asset_ids]
        self.bidding_method_market = arr_for_selection(h[:bidding_method][:market])
        self.frequency_time_minutes = h[:bidding_method][:frequency_time_minutes]
        self.optimization_horizon_change = arr_for_selection('OptimizationHorizonChangeAbsolute')
        self.optimization_horizon_change_days_before_delivery = h[:bidding_method][:optimization_horizon_change][:days_before_delivery]
        self.optimization_horizon_change_time_hours = h[:bidding_method][:optimization_horizon_change][:hours]
        self.optimization_horizon_change_time_minutes = h[:bidding_method][:optimization_horizon_change][:minutes]
      end
    end
  end

  def internal_input_from_hash(h)
    internal_bi = h[:bid_inputs].find{|bi| bi[:key] == 'InternalBiddingKind'} if h[:bid_inputs]
    if internal_bi
      self.bid_inputs << 'InternalBiddingKind'
      self.internal_bidding_input_format = arr_for_selection(internal_bi[:bidding_input_format])
      self.internal_auction_start = arr_for_selection(internal_bi[:auction_start][:key])
      self.internal_auction_start_days_before_delivery = internal_bi[:auction_start][:days_before_delivery]
      self.internal_auction_start_time_hours = internal_bi[:auction_start][:hours]
      self.internal_auction_start_time_minutes = internal_bi[:auction_start][:minutes]
      self.internal_auction_start_minutes = internal_bi[:auction_start][:minutes]
      self.internal_auction_end = arr_for_selection(internal_bi[:auction_closing][:key])
      self.internal_auction_end_days_before_delivery = internal_bi[:auction_closing][:days_before_delivery]
      self.internal_auction_end_time_hours = internal_bi[:auction_closing][:hours]
      self.internal_auction_end_time_minutes = internal_bi[:auction_closing][:minutes]
      self.internal_auction_end_minutes = internal_bi[:auction_closing][:minutes]
    end
  end

  def build_internal_bidding_input
    auction_start = {}
    if selected_id(self.internal_auction_start) == 'AuctionTimeAbsolute'
      auction_start = {
        key: 'AuctionTimeAbsolute',
        days_before_delivery: self.internal_auction_start_days_before_delivery,
        hours: self.internal_auction_start_time_hours,
        minutes: self.internal_auction_start_time_minutes
      }
    elsif selected_id(self.internal_auction_start) == 'AuctionTimeRelative'
      auction_start = {
        key: 'AuctionTimeRelative',
        minutes: self.internal_auction_start_minutes,
      }
    elsif selected_id(self.internal_auction_start) == 'AuctionTimeAnyTime'
      auction_start = {
        key: 'AuctionTimeAnyTime',
      }
    end

    auction_end = {}
    if selected_id(self.internal_auction_end) == 'AuctionTimeAbsolute'
      auction_end = {
        key: 'AuctionTimeAbsolute',
        days_before_delivery: self.internal_auction_end_days_before_delivery,
        hours: self.internal_auction_end_time_hours,
        minutes: self.internal_auction_end_time_minutes
      }
    elsif selected_id(self.internal_auction_end) == 'AuctionTimeRelative'
      auction_end = {
        key: 'AuctionTimeRelative',
        minutes: self.internal_auction_end_minutes,
      }
    elsif selected_id(self.internal_auction_end) == 'AuctionTimeAnyTime'
      auction_end = {
        key: 'AuctionTimeAnyTime'
      }
    end

    {
      key: 'InternalBiddingKind',
      bidding_input_format: selected_id(self.internal_bidding_input_format),
      auction_start: auction_start,
      auction_closing: auction_end
    }
  end

  def customer_input_from_hash(h)
    customer_bi = h[:bid_inputs].find{|bi| bi[:key] == 'CustomerBiddingKind'} if h[:bid_inputs]
    if customer_bi
      self.bid_inputs << 'CustomerBiddingKind'
      self.customer_bidding_input_format = arr_for_selection(customer_bi[:bidding_input_format])
      self.customer_auction_start = arr_for_selection(customer_bi[:auction_start][:key])
      self.customer_auction_start_days_before_delivery = customer_bi[:auction_start][:days_before_delivery]
      self.customer_auction_start_local_time_hours = customer_bi[:auction_start][:hours]
      self.customer_auction_start_local_time_minutes = customer_bi[:auction_start][:minutes]
      self.customer_auction_start_minutes = customer_bi[:auction_start][:minutes]
      self.customer_auction_minimum_mw_volume_for1_bid = customer_bi[:minimum_mw_volume_for1_bid]
      self.customer_auction_end = arr_for_selection(customer_bi[:auction_closing][:key])
      self.customer_auction_end_days_before_delivery = customer_bi[:auction_closing][:days_before_delivery]
      self.customer_auction_end_local_time_hours = customer_bi[:auction_closing][:hours]
      self.customer_auction_end_local_time_minutes = customer_bi[:auction_closing][:minutes]
      self.customer_auction_end_minutes = customer_bi[:auction_closing][:minutes]
    end
  end

  def build_customer_bidding_input
    auction_start = {}
    if selected_id(self.customer_auction_start) == 'AuctionTimeAbsolute'
      auction_start = {
        key: 'AuctionTimeAbsolute',
        days_before_delivery: self.customer_auction_start_days_before_delivery,
        hours: self.customer_auction_start_local_time_hours,
        minutes: self.customer_auction_start_local_time_minutes
      }
    elsif selected_id(self.customer_auction_start) == 'AuctionTimeRelative'
      auction_start = {
        key: 'AuctionTimeRelative',
        minutes: self.customer_auction_start_minutes,
      }
    elsif selected_id(self.customer_auction_start) == 'AuctionTimeAnyTime'
      auction_start = {
        key: 'AuctionTimeAnyTime'
      }
    end

    auction_end = {}
    if selected_id(self.customer_auction_end) == 'AuctionTimeAbsolute'
      auction_end = {
        key: 'AuctionTimeAbsolute',
        days_before_delivery: self.customer_auction_end_days_before_delivery,
        hours: self.customer_auction_end_local_time_hours,
        minutes: self.customer_auction_end_local_time_minutes
      }
    elsif selected_id(self.customer_auction_end) == 'AuctionTimeRelative'
      auction_end = {
        key: 'AuctionTimeRelative',
        minutes: self.customer_auction_end_minutes,
      }
    elsif selected_id(self.customer_auction_end) == 'AuctionTimeAnyTime'
      auction_end = {
        key: 'AuctionTimeAnyTime'
      }
    end

    {
      key: 'CustomerBiddingKind',
      bidding_input_format: selected_id(self.customer_bidding_input_format),
      auction_start: auction_start,
      auction_closing: auction_end,
      minimum_mw_volume_for1_bid: self.customer_auction_minimum_mw_volume_for1_bid
    }
  end

  def optimizer_bod_from_hash(h)
    optimizer_bod_bi = h[:bid_inputs].find{|bi| bi[:key] == 'OptimizerBodKind'} if h[:bid_inputs]
    if optimizer_bod_bi
      self.bid_inputs << 'OptimizerBodKind'
      self.optimizer_bod_frequency_minutes = optimizer_bod_bi[:frequency_minutes]
      self.optimizer_bod_computed_sp = optimizer_bod_bi[:computed_sp]
      self.optimizer_bod_delta_for_submission = optimizer_bod_bi[:delta_for_submission]
      self.optimizer_bod_delta_for_undo = optimizer_bod_bi[:delta_for_undo]
    end
  end

  def build_optimizer_bod_bidding_input
    {
      key: 'OptimizerBodKind',
      frequency_minutes: self.optimizer_bod_frequency_minutes,
      computed_sp: self.optimizer_bod_computed_sp,
      delta_for_submission: self.optimizer_bod_delta_for_submission,
      delta_for_undo: self.optimizer_bod_delta_for_undo
    }
  end

  def build_auction_system
    auction_system_h = {}
    if ids_from_selection(self.auction_system).include?("upload")
      auction_system_h[:upload] = build_auction_system_upload
    end
    if ids_from_selection(self.auction_system).include?("download")
      auction_system_h[:download] = build_auction_system_download
    end
    if ids_from_selection(self.auction_system).include?("ui")
      auction_system_h[:ui] = build_auction_system_ui
    end
    if ids_from_selection(self.auction_system).include?("ui_for_battery_uk_intraday")
      auction_system_h[:ui_for_battery_uk_intraday] = build_auction_system_ui_for_battery_uk_intraday
    end
    if ids_from_selection(self.auction_system).include?("uk_vatp")
      auction_system_h[:uk_vatp] = build_auction_system_uk_vatp
    end
    if ids_from_selection(self.auction_system).include?("de_vatp")
      auction_system_h[:de_vatp] = build_auction_system_de_vatp
    end
    auction_system_h
  end

  def build_auction_system_upload
    {
      behaviour: selected_id(self.auction_system_upload_behaviour),
      market_time: {
        key: 'AuctionTimeRelative',
        minutes: self.auction_system_upload_market_time,
      },
      number_of_retries: self.auction_system_upload_number_of_retries,
      seconds_between_retries: self.auction_system_upload_seconds_between_retries
    }
  end

  def build_auction_system_download
    {
      behaviour: selected_id(self.auction_system_download_behaviour),
      market_time: {
        key: 'AuctionTimeRelative',
        minutes: self.auction_system_download_market_time,
      },
      number_of_retries: self.auction_system_download_number_of_retries,
      seconds_between_retries: self.auction_system_download_seconds_between_retries
    }
  end

  def build_auction_system_ui
    {
      trader_look_ahead_hours: self.auction_system_ui_trader_look_ahead_hours,
      bid_expiry_warning_minutes: self.auction_system_ui_bid_expiry_warning_minutes,
      price_grouping_step: self.auction_system_ui_price_grouping_step,
      currency: {
        currency: "GBP"
      },
      site_id: self.auction_system_ui_site_id
    }
  end

  def build_auction_system_ui_for_battery_uk_intraday
    {
      asset_id: selected_id(self.auction_system_ui_for_battery_uk_intraday_asset_id),
      portfolio_id: self.auction_system_ui_for_battery_uk_intraday_portfolio_id,
      export_entrader: selected_id(self.auction_system_ui_for_battery_uk_intraday_export_entrader),
      upload_folder: self.auction_system_ui_for_battery_uk_intraday_upload_folder,
    }
  end

  def build_auction_system_uk_vatp
    {
      smart_volume_strategy_template_id: self.auction_system_uk_vatp_smart_volume_strategy_template_id,
      hidden_iceberg_strategy_template_id: self.auction_system_uk_vatp_hidden_iceberg_strategy_template_id,
      smart_iceberg_strategy_template_id: self.auction_system_uk_vatp_smart_iceberg_strategy_template_id,
    }
  end

    def build_auction_system_de_vatp
    {
      smart_volume_strategy_template_id: self.auction_system_de_vatp_smart_volume_strategy_template_id,
      hidden_iceberg_strategy_template_id: self.auction_system_de_vatp_hidden_iceberg_strategy_template_id,
      smart_iceberg_strategy_template_id: self.auction_system_de_vatp_smart_iceberg_strategy_template_id,
    }
  end

  def arr_for_selection(value)
    value.nil? ? [] : [value]
  end

  def selected_id(selection)
    ids_from_selection(selection).first
  end

  def ids_from_selection(selection)
    (selection || []).collect{|x| x.is_a?(Hash) ? x[:id] : x }
  end
end
