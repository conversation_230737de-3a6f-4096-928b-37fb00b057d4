class OwnerEventNotification < ApplicationRecord
  self.table_name = 'event_owner_notification_recipient'
  #include Authority::Abilities

  self.primary_keys = :event_type, :contact_type, :notification_type, :contact_id, :owner_type, :owner_id

  belongs_to :owner, polymorphic: true
  belongs_to :event_type, foreign_key: 'event_type'
  belongs_to :user_account

  TYPE_EMAIL     = 'email'
  TYPE_PHONECALL = 'phonecall'
  TYPE_SMS       = 'sms'
  TYPES = [TYPE_EMAIL, TYPE_PHONECALL, TYPE_SMS]

  def self.polymorphic_class_for(name)
    if name == AuctionConfig.polymorphic_name
      super(AuctionConfig.name)
    elsif name == DispatchGroup.polymorphic_name
      super(DispatchGroup.name)
    else
      super(name)
    end
  end


  def to_string_id
    "#{self.owner_id}:#{self.event_type.id}:#{self.notification_type}:#{self.contact_type}:#{self.contact_id}"
  end

  def self.update_notification_recipiens(notifications, owner_type)
    notifications_str_ids = notifications.collect { |n| n.to_string_id }
    to_remove = OwnerEventNotification.where(owner_type: owner_type).select { |n| !notifications_str_ids.include?(n.to_string_id) }
    to_remove.each do |remove_notification|
      remove_notification.destroy
    end
    asset_notifications_str_ids = OwnerEventNotification.where(owner_type: owner_type).collect { |n| n.to_string_id }
    to_add = notifications.select { |n| !asset_notifications_str_ids.include?(n.to_string_id) }
    to_add.each do |new_notification|
      new_notification.save
    end
  end

end