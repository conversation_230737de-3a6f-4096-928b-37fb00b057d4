class V2gOptimizationJob < ApplicationRecord

  #has_many :bidding_optimizations, -> (job) { where("v2g_optimization_job.id::varchar = bidding_optimization.value::json->'request'->>'id'") }
  #has_many :steering_optimizations, -> (job) { where("v2g_optimization_job.id::varchar = steering_optimization.value::json->'request'->>'id'") }

  has_many :steering_optimizations, foreign_key: "value::json->'request'->>'id'"

  def save; end
  def create; end
  def update; end

  def self.bidding_optimizations_for_jobs(job_ids:)
    optimizations = 
      if job_ids.present?
        BiddingOptimization
          .where("(value::json->'request'->>'id')::integer IN (?)", job_ids)
          .order(created: :desc)
      else
        []
      end
    optimizations.group_by(&:job_id)
  end

  def self.steering_optimizations_for_jobs(job_ids:)
    optimizations = 
      if job_ids.present?
        SteeringOptimization
          .where("(value::json->'request'->>'id')::integer IN (?)", job_ids)
          .order(created: :desc)
      else
        []
      end
    optimizations.group_by(&:job_id)
  end

end