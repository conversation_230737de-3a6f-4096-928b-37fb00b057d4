class RollupRegeneration < ApplicationRecord

  self.locking_column = :version

  def self.item_types
    [[Asset.model_name.human, Asset.model_name], [RollupFamily.model_name.human, RollupFamily.model_name]]
  end

  attr_accessor :interval

  belongs_to :user_account,
             foreign_key: 'user_id'

  has_and_belongs_to_many :rollup_types, join_table: 'rollup_regeneration_rollup_type', validate: false
  has_and_belongs_to_many :rollup_families, join_table: 'rollup_regeneration_rollup_family', validate: false
  has_and_belongs_to_many :assets, join_table: 'rollup_regeneration_asset', validate: false
  has_and_belongs_to_many :dispatch_groups, join_table: 'rollup_regeneration_dispatch_group', validate: false

  validates :from_time, :to_time, :item_type,
            presence: true

  validate do
    errors.add(:base, I18n.t('asset_rollups.form_generate_rollups.please_select_rollup')) if rollup_types.empty?
    if errors.empty?
      if item_type == RollupFamily.model_name.to_s
        errors.add(:base, I18n.t('asset_rollups.form_generate_rollups.please_select_rollup_family')) if rollup_families.empty?
      elsif item_type == Asset.model_name.to_s
        errors.add(:base, I18n.t('asset_rollups.form_generate_rollups.please_select_asset')) if assets.empty?
      elsif item_type == DispatchGroup.model_name.to_s
        errors.add(:base, I18n.t('dispatch_group_rollups.form_generate_rollups.please_select_dispatch_group')) if dispatch_groups.empty?
      end
    end
  end

end
