#
# Represents a Subpool
#
class Subpool < ApplicationRecord
  SUBPOOL_SIGNALS = [
    'AssetTotalSetpoint'.freeze,
    'BasePoint'.freeze,
    'BasePointAverage'.freeze,
    'Frequency'.freeze,
    'HeadroomResponse'.freeze,
    'MRMinusBand'.freeze,
    'MRPlusBand'.freeze,
    'PrecedingBasePoint'.freeze,
    'PrecedingBasePoint4S'.freeze,
    'PrequalificationPositive'.freeze,
    'PrequalificationNegative'.freeze,
    'PRLUsableEnergyNegative'.freeze,
    'PRLUsableEnergyPositive'.freeze,
    'RampDownRate'.freeze,
    'RampUpRate'.freeze,
    'SCState'.freeze,
    'SetpointMirror'.freeze,
    'SubPoolIndex'.freeze,
    'TradedFlexNegative'.freeze,
    'TradedFlexPositive'.freeze,
    'VPPDeliveredFlex'.freeze,
    'VPPTotalGeneration'.freeze,
    'VPPTotalGeneration4S'.freeze
  ].freeze

  SIGNALS_STRING_SEPARATOR = ','.freeze

  include Authority::Abilities

  has_and_belongs_to_many :assets

  has_and_belongs_to_many :dispatch_groups

  validates :name, presence: true

  before_save :clear_assets_if_implicit

  validate :unique_implicit_subpool_per_dg

  def self.available_signals
    SUBPOOL_SIGNALS
  end

  def signals_list
    return [] if signals.blank?
    signals.split(SIGNALS_STRING_SEPARATOR)
  end

  def signals_list=(v)
    self[:signals] =
      Array(v)
      .select { |x| SUBPOOL_SIGNALS.include?(x) }
      .join(SIGNALS_STRING_SEPARATOR)
  end

  private

  # remove assets if this is marked as `implicit`
  def clear_assets_if_implicit
    assets.clear if implicit?
  end

  # each DG can only have one implicit Subpool
  def unique_implicit_subpool_per_dg
    if implicit?
      dgs_with_other_implicit_subpools =
        DispatchGroup
        .where(id: dispatch_groups.collect(&:id))
        .where(subpool: { implicit: true })
        .includes(:subpools)

      if persisted?
        dgs_with_other_implicit_subpools =
          dgs_with_other_implicit_subpools
          .where.not(subpool: { id: id })
      end

      if dgs_with_other_implicit_subpools.present?
        dg_names =
          dgs_with_other_implicit_subpools
          .collect(&:name)
          .to_sentence
        errors.add(
          :base,
          :unique_implicit_subpool_per_dg,
          dg_names: dg_names)
      end
    end
  end
end
