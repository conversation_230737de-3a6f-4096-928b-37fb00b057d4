class BiddingOptimization < ApplicationRecord

  def save; end
  def create; end
  def update; end

  def job_id
    JSON.parse(value)["request"]["id"].to_i
  rescue 
    nil
  end

  def optimization_interval_start
    Time.parse(JSON.parse(value)["request"]["start_time"])
  rescue 
    nil
  end

  def optimization_interval_end
    Time.parse(JSON.parse(value)["request"]["end_time"])
  rescue 
    nil
  end

end