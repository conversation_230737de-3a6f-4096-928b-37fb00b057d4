#
# Represents a Dispatch Group entity
#
class DispatchGroup < ApplicationRecord

  include GenericDgConfig
  include DgDlmParams

  DISPATCH_GROUP_SIGNALS = [
    'AssetTotalSetpoint'.freeze,
    'BasePoint'.freeze,
    'BasePointAverage'.freeze,
    'Frequency'.freeze,
    'MinuteAverageActivePower'.freeze,
    'HeadroomResponse'.freeze,
    'LowerPowerLimit'.freeze,
    'MRMinusBand'.freeze,
    'MRPlusBand'.freeze,
    'PrecedingBasePoint'.freeze,
    'PrecedingBasePoint4S'.freeze,
    'PrequalificationPositive'.freeze,
    'PrequalificationNegative'.freeze,
    'PRLUsableEnergyNegative'.freeze,
    'PRLUsableEnergyPositive'.freeze,
    'RampDownRate'.freeze,
    'RampUpRate'.freeze,
    'SCState'.freeze,
    'SetpointMirror'.freeze,
    'TradedFlexNegative'.freeze,
    'TradedFlexPositive'.freeze,
    'UpperPowerLimit'.freeze,
    'VPPDeliveredFlex'.freeze,
    'VPPDeliveredFlexPositive'.freeze,
    'VPPDeliveredFlexNegative'.freeze,
    'VPPTotalGeneration'.freeze,
    'VPPTotalGeneration4S'.freeze
  ].freeze

  SIGNALS_STRING_SEPARATOR = ' '.freeze
  STRING_ATTRIBUTE_SEPARATOR = ','.freeze

  include Authority::Abilities

  scope :not_deleted, -> {where("dispatch_group.deleted IS NULL OR dispatch_group.deleted > CURRENT_TIMESTAMP")}

  scope :with_opti_lines, -> {    
    where(market: Market.dynamic_containment).
    where("generic_config->'stateOfChargeManagement'->'volumeBaseline' is not null OR generic_config->'stateOfChargeManagement'->'optimizedDc' is not null")
  }

  belongs_to :tso
  belongs_to :market

  has_many :balancing_groups,
           class_name: 'BalancingGroupDg'

  has_and_belongs_to_many :dsos, foreign_key: 'dg_id'
  has_and_belongs_to_many :subpools
  has_and_belongs_to_many :auction_configs,
    class_name: "AuctionConfig",
    association_foreign_key: 'vpp_trading_auction_rules_id',
    join_table: 'dispatch_group_vpp_trading_auction_rules',
    validate: false

  has_many :rollups, as: :owner

  validates :name,
            :tso, :market,
            presence: true

  validate :nomination_tool_enabled_uniqueness

  # ensure that we apply the validation only on not-deleted entries
  private def nomination_tool_enabled_uniqueness
    if nomination_tool_enabled && !deleted && tso_id.present? && market_id.present?
      other_dgs = self.class.where(nomination_tool_enabled: true, tso_id: tso_id, market_id: market_id).not_deleted
      other_dgs = other_dgs.where.not(id: id) unless new_record?
      existing_dg = other_dgs.first
      if existing_dg.present?
        errors.add(
          :nomination_tool_enabled, 
          I18n.t("taken", 
            scope: "activerecord.errors.models.dispatch_group.attributes.nomination_tool_enabled", 
            dg_id: existing_dg.id,
            dg_name: existing_dg.name))
      end
    end
  end

  def self.polymorphic_name
    'dispatchGroup'
  end

  validates :dead_band_pos_mhz, :dead_band_neg_mhz, :max_frequency_deviation_pos_mhz, :max_frequency_deviation_neg_mhz,
    numericality: {greater_than_or_equal_to: 0, only_integer: true},
    if: Proc.new { |dg| !has_generic_config && Market.ffrd.present? && dg.market_id == Market.ffrd.id }

  validates :nomination_tool_max_invalid_rollups_percentage, :nomination_tool_max_fault_seconds_percentage,
    numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 1 },
    allow_nil: true

  validates :nomination_tool_target_bid_volume_mw,
    numericality: { greater_than_or_equal_to: 1, only_integer: true },
    allow_nil: true

  validates :nomination_tool_past_availability_hours,
    numericality: { greater_than_or_equal_to: 0 },
    allow_nil: true

  validates :nomination_tool_additional_buffer_pos_mw, :nomination_tool_additional_buffer_neg_mw,
    numericality: { greater_than_or_equal_to: 0 },
    allow_nil: true

  validates :automatic_allocation_weight,
    numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 1 },
    allow_nil: true

  validates :automatic_allocation_nomination_buffer_percentage,
            numericality: true,
            allow_nil: true

  validates :max_asset_reallocation,
            numericality: { only_integer: true },
            allow_nil: true

  validates :ext_backup_buffer,
            numericality: {greater_than_or_equal_to: 0},
            if: Proc.new { |dg| dg.ext_backup_active }
  validates :ext_backup_threshold_pos, :ext_backup_threshold_neg,
            numericality: true,
            if: Proc.new { |dg| dg.ext_backup_active }
  validates :ext_backup_nomination_threshold_pos, :ext_backup_nomination_threshold_neg,
            numericality: {greater_than_or_equal_to: 0},
            if: Proc.new { |dg| dg.ext_backup_active }
  validates :ext_backup_increment_pos, :ext_backup_increment_neg,
            numericality: {greater_than_or_equal_to: 0},
            if: Proc.new { |dg| dg.ext_backup_active }


  validates :alarm_flexibility_threshold_buffer,
            numericality: { greater_than_or_equal_to: 0 },
            if: Proc.new { |dg| !has_generic_config && dg.individual_alarms_enabled }

  validates :individual_alarm_overdelivery_neg_threshold,
            numericality: { greater_than_or_equal_to: 0 },
            if: Proc.new { |dg| !has_generic_config && dg.individual_alarm_overdelivery_neg_enabled }

  validates :individual_alarm_overdelivery_neg_delay,
            numericality: { greater_than_or_equal_to: 0, only_integer: true },
            if: Proc.new { |dg| !has_generic_config && dg.individual_alarm_overdelivery_neg_enabled }

  validates :individual_alarm_overdelivery_pos_threshold,
            numericality: { greater_than_or_equal_to: 0 },
            if: Proc.new { |dg| !has_generic_config && dg.individual_alarm_overdelivery_pos_enabled }

  validates :individual_alarm_overdelivery_pos_delay,
            numericality: { greater_than_or_equal_to: 0, only_integer: true },
            if: Proc.new { |dg| !has_generic_config && dg.individual_alarm_overdelivery_pos_enabled }

  validates :portfolio_id, presence: true,
            if: Proc.new { |dg|
    (dg.has_spot_market? && !dg.is_generic_config) ||
    (dg.has_intraday_market? && dg.is_generic_config &&
      ( dg.export_entrader.present? ||
        (dg.dispatch_source.present? && dg.dispatch_source.collect{|ds| ds[:id]}.include?("residualShape"))
      )
    ) ||
    (dg.is_generic_config && dg.dispatch_source.present? &&
      dg.dispatch_source.collect{|ds| ds[:id]}.include?("nlAfrr")
    )}

  before_save :enforce_nomination_tool_enabled
  before_save :enforce_nomination_tool_energy_price_target_margin

  # true when a generic_config_json attribute was received; this json will be used directly as the config json; no validations are applied
  # used for circumventing the UI for configuring a DG with a generic config json directly
  attr_accessor :generic_config_json_override
  def generic_config_json=(json_string)
    self.generic_config_json_override = true
    self[:generic_config] = JSON.parse(json_string)
  end

  attr_accessor :generic_config_enabled

  def generic_config_enabled
    DispatchGroup.column_names.include?(:generic_config.to_s)
  end

  def has_generic_config
    generic_config_enabled && self.generic_config.present?
  end

  def has_generic_config=(has_generic_config)
    self.is_generic_config = has_generic_config
  end

  def has_spot_market?
    Market.spot.present? && self.market_id == Market.spot.id
  end

  def has_intraday_market?
    Market.intraday.present? && self.market_id == Market.intraday.id
  end

  def allows_export_entrader?
    Market.intraday.present? && self.market_id == Market.intraday.id
  end

  def nomination_tool_target_bid_volume_mw=(v)
    self.nomination_tool_target_bid_volume = v.present? ? v.to_i * 1000 : nil
  end

  def nomination_tool_target_bid_volume_mw
    self.nomination_tool_target_bid_volume / 1_000 if nomination_tool_target_bid_volume.present?
  end

  def nomination_tool_additional_buffer_pos_mw=(v)
    self.nomination_tool_additional_buffer_pos = v.present? ? v.to_f * 1000 : nil
  end

  def nomination_tool_additional_buffer_neg_mw=(v)
    self.nomination_tool_additional_buffer_neg = v.present? ? v.to_f * 1000 : nil
  end

  def nomination_tool_additional_buffer_pos_mw
    self.nomination_tool_additional_buffer_pos / 1_000.0 if nomination_tool_additional_buffer_pos.present?
  end

  def nomination_tool_additional_buffer_neg_mw
    self.nomination_tool_additional_buffer_neg / 1_000.0 if nomination_tool_additional_buffer_neg.present?
  end

  def ext_backup_threshold_pos_mw
    ext_backup_threshold_pos / 1_000.0 if ext_backup_threshold_pos.present?
  end

  def ext_backup_threshold_pos_mw=(v)
    self.ext_backup_threshold_pos = v.present? ? v.to_f * 1_000 : nil
  end

  def ext_backup_threshold_neg_mw
    ext_backup_threshold_neg / 1_000.0 if ext_backup_threshold_neg.present?
  end

  def ext_backup_threshold_neg_mw=(v)
    self.ext_backup_threshold_neg = v.present? ? v.to_f * 1_000 : nil
  end

  def ext_backup_increment_pos_mw
    ext_backup_increment_pos / 1_000.0 if ext_backup_increment_pos.present?
  end

  def ext_backup_increment_pos_mw=(v)
    self.ext_backup_increment_pos = v.present? ? v.to_f * 1_000 : nil
  end

  def ext_backup_increment_neg_mw
    ext_backup_increment_neg / 1_000.0 if ext_backup_increment_neg.present?
  end

  def ext_backup_increment_neg_mw=(v)
    self.ext_backup_increment_neg = v.present? ? v.to_f * 1_000 : nil
  end

  def individual_alarm_overdelivery_neg_threshold_mw
    individual_alarm_overdelivery_neg_threshold / 1_000.0 if individual_alarm_overdelivery_neg_threshold.present?
  end

  def individual_alarm_overdelivery_neg_threshold_mw=(v)
    self.individual_alarm_overdelivery_neg_threshold = v.present? ? v.to_f * 1_000 : nil
  end

  def individual_alarm_overdelivery_pos_threshold_mw
    individual_alarm_overdelivery_pos_threshold / 1_000.0 if individual_alarm_overdelivery_pos_threshold.present?
  end

  def individual_alarm_overdelivery_pos_threshold_mw=(v)
    self.individual_alarm_overdelivery_pos_threshold = v.present? ? v.to_f * 1_000 : nil
  end

  # always force value of nomination_tool_energy_price_target_margin to 0 for now
  def enforce_nomination_tool_energy_price_target_margin
    self.nomination_tool_energy_price_target_margin = 0
  end

  def enforce_nomination_tool_enabled
    self.nomination_tool_enabled = self.nomination_tool_enabled && allows_nomination_tool_enablement?
  end

  def self.available_signals
    DISPATCH_GROUP_SIGNALS
  end

  def signals_list
    return [] if signals.blank?
    signals.split(SIGNALS_STRING_SEPARATOR)
  end

  def signals_list=(v)
    self[:signals] =
      Array(v)
      .select { |x| DISPATCH_GROUP_SIGNALS.include?(x) }
      .join(SIGNALS_STRING_SEPARATOR)
  end

  def capacity_price_grouping_steps_list
    return [] if capacity_price_grouping_steps.blank?
    capacity_price_grouping_steps.split(STRING_ATTRIBUTE_SEPARATOR)
  end

  def capacity_price_grouping_steps_list=(v)
    self[:capacity_price_grouping_steps] =
      Array(v)
      .reject(&:blank?)
      .collect { |n| Integer(n) rescue nil }
      .compact
      .sort
      .join(STRING_ATTRIBUTE_SEPARATOR)
  end

  def event_types_acknowledge_alarm_list
    return [] if event_types_acknowledge_alarm.blank?
    event_types_acknowledge_alarm.split(STRING_ATTRIBUTE_SEPARATOR)
  end

  def event_types_acknowledge_alarm_list=(v)
    self[:event_types_acknowledge_alarm] =
      Array(v)
      .reject(&:blank?)
      .join(STRING_ATTRIBUTE_SEPARATOR)
  end

  def event_types_acknowledge_audio_alarm_list
    return [] if event_types_acknowledge_audio_alarm.blank?
    event_types_acknowledge_audio_alarm.split(STRING_ATTRIBUTE_SEPARATOR)
  end

  def event_types_acknowledge_audio_alarm_list=(v)
    self[:event_types_acknowledge_audio_alarm] =
      Array(v)
      .reject(&:blank?)
      .join(STRING_ATTRIBUTE_SEPARATOR)
  end

  def allows_nomination_tool_enablement?
    market.try(:allows_nomination_tool_enablement?)
  end

  def to_label
    "##{id} #{name} (#{tso.name})"
  end

  def mark_as_deleted
    set_generic_config_attrs_from_json if has_generic_config
    set_dlm_params_attrs_from_json if dlm_parameters_enabled? && has_dlm_market?
    deleted_at = Time.now.getutc
    self.deleted = deleted_at
    self.save!
  end

  def has_subpools?
    self.subpools.any?
  end

  def has_allocations?
    begin
      start_time = DateTime.now
      end_time = start_time + 2.months
      allocations =
        Services::PortfolioManagementService.allocations(
          start_time: start_time,
          end_time: end_time,
          dg_ids: [self.id])
      !allocations.empty?
    rescue Services::PortfolioManagementService::UnableToGetAllocations => e
      Rails.logger.error(e)
      true
    end
  end

  def has_nominations?
    DistributedUnit
      .where(:dispatch_group_id => self.id)
      .where('deleted IS NULL')
      .where('end_time >= ?', DateTime.now)
      .any?
  end

end
