#
# Represents a time bound allocation of a DG to a Balancing Group
#
class BalancingGroupDg < ApplicationRecord
  include IntervalFunctions

  self.locking_column = :version

  validates :start_date, :end_date, :dispatch_group, :name,
            presence: true

  validates :name, length: { is: 16 }

  validates_with BgWithTimeIntervalValidator

  belongs_to :dispatch_group

  # used by the interval functions module
  def self.interval_start_attribute
    :start_date
  end

  # used by the interval functions module
  def self.interval_end_attribute
    :end_date
  end

  def can_delete
    self.start_date > Date.today
  end

  #
  # Returns entries that overlap this entry
  # and belong to the same dispatch group
  #
  def overlapping_bg
    res = self
          .class
          .overlap_date_interval(start_date..end_date)
          .where(dispatch_group: dispatch_group)
    res = res.where('id <> ?', id) if persisted?
    res
  end
end
