class ReportEmailSending < ReportingDatabaseModel

  include ApplicationHelper
  
  serialize :report_delivery_errors
  serialize :report_generation_errors
  serialize :report_parameters
  serialize :report_raw_report
  serialize :email_to
  serialize :email_cc

  def has_attachment
    report_file_content.present?
  end

  def status
    if report_delivery_skipped
      "Skipped"
    elsif report_generation_success && report_delivery_success
      "Success"
    else
      "Error"
    end
  end

  def email_recipients
    recipients = []
    unless email_to.blank?
      recipients << email_to if email_to.kind_of?(String)
      recipients = recipients + email_to if email_to.kind_of?(Array)
    end
    unless email_cc.blank?
      recipients << email_cc if email_cc.kind_of?(String)
      recipients = recipients + email_cc if email_cc.kind_of?(Array)
    end
    recipients.uniq
  end

  def localized_report_parameters
    if report_parameters
      localize_report_parameters(report_parameters.stringify_keys)
    end
  end

  def localized_report_generation_errors
    if report_generation_errors.present?
      localize_report_feedback(report_generation_errors)
    end
  end

  def localized_report_feedback
    if report_raw_report.present? && report_raw_report[:messages].present?
      localize_report_feedback(report_raw_report[:messages])
    end
  end

end


# #
# class ReportEmailSending < ActiveResource::Base
#   self.site = ENV['VPP_REPORTING_URL']
#
#   def report_date
#     Date.parse(super) if super.present?
#   end
#
#   def created
#     Time.parse(super).in_time_zone(Time.zone.name) if super.present?
#   end
# end
