#
# Represents an Product entity
#
class Product < ActiveRecord::Base
  self.table_name = 'product'

  belongs_to :market

  ENERGY_DIRECTIONS = [
    ENERGY_DIRECTION_POSITIVE = 'positive'.freeze,
    ENERGY_DIRECTION_NEGATIVE = 'negative'.freeze
  ].freeze

  MARKET_TYPES = [
    MARKET_TYPE_POSITIVE = 'POSITIVE_RESERVE'.freeze,
    MARKET_TYPE_NEGATIVE = 'NEGATIVE_RESERVE'.freeze
  ].freeze

  DYNAMIC_CONTAIMENT_PLUS_NAME = 'DynamicContainment_PLUS'.freeze
  DYNAMIC_CONTAIMENT_MINUS_NAME = 'DynamicContainment_MINUS'.freeze,
  DYNAMIC_MODERATION_PLUS_NAME = 'DynamicModeration_PLUS'.freeze
  DYNAMIC_MODERATION_MINUS_NAME = 'DynamicModeration_MINUS'.freeze,
  DYNAMIC_REGULATION_PLUS_NAME = 'DynamicRegulation_PLUS'.freeze
  DYNAMIC_REGULATION_MINUS_NAME = 'DynamicRegulation_MINUS'.freeze,

  def self.market_type(energy_direction)
    case energy_direction
    when ENERGY_DIRECTION_NEGATIVE
      MARKET_TYPE_NEGATIVE
    when ENERGY_DIRECTION_POSITIVE
      MARKET_TYPE_POSITIVE
    end
  end

  def energy_direction
    case market_type
    when MARKET_TYPE_NEGATIVE
      ENERGY_DIRECTION_NEGATIVE
    when MARKET_TYPE_POSITIVE
      ENERGY_DIRECTION_POSITIVE
    end
  end

  def product_interval(time_zone, start_time, end_time, with_minutes = false)
    energy_direction = self.energy_direction.upcase
    start_time_in_tz = start_time.in_time_zone(time_zone)
    end_time_in_tz = end_time.in_time_zone(time_zone)
    start_hour = start_time_in_tz.hour
    end_hour = end_time_in_tz.hour
    if with_minutes
      start_minute = start_time_in_tz.min
      end_minute = end_time_in_tz.min
      [
        energy_direction[0..2],
        ('%02d' % start_hour) + ('%02d' % start_minute),
        ('%02d' % (end_hour == 0 ? 24 : end_hour)) + ('%02d' % end_minute),
      ].join('_')
    else
      [
        energy_direction[0..2],
        '%02d' % start_hour,
        '%02d' % (end_hour == 0 ? 24 : end_hour),
      ].join('_')
    end
  end

  def self.dynamic_containment_plus
    where(code: DYNAMIC_CONTAIMENT_PLUS_NAME).first
  end

  def self.dynamic_containment_minus
    where(code: DYNAMIC_CONTAIMENT_MINUS_NAME).first
  end

  def self.dynamic_moderation_plus
    where(code: DYNAMIC_MODERATION_PLUS_NAME).first
  end

  def self.dynamic_moderation_minus
    where(code: DYNAMIC_MODERATION_MINUS_NAME).first
  end

  def self.dynamic_regulation_plus
    where(code: DYNAMIC_REGULATION_PLUS_NAME).first
  end

  def self.dynamic_regulation_minus
    where(code: DYNAMIC_REGULATION_MINUS_NAME).first
  end

end
