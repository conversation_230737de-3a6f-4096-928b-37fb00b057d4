#
class Market < ActiveRecord::Base
  # this is not an exhaustive list of markets
  # only those explicitly used in this app are listed
  MARKET_NAMES = [
    TEST_MARKET_NAME = 'TEST'.freeze,

    # german markets
    MRL_MARKET_NAME = 'MRL'.freeze,
    PRL_MARKET_NAME = 'PRL'.freeze,
    SRL_MARKET_NAME = 'SRL'.freeze,

    # uk markets
    LM_MARKET_NAME = 'LM'.freeze,
    STOR_MARKET_NAME = 'STOR'.freeze,
    UKPN_MARKET_NAME = 'UKPN'.freeze,
    FFR_MARKET_NAME = 'FFR'.freeze,
    FFRD_MARKET_NAME = 'FFRDynamic'.freeze,
    DAYAHEAD_MARKET_NAME = 'DayAhead'.freeze,
    DYNAMIC_CONTAIMENT_MARKET_NAME =  'DynamicContainment'.freeze,
    DYNAMIC_MODERATION_MARKET_NAME =  'DynamicModeration'.freeze,
    DYNAMIC_REGULATION_MARKET_NAME =  'DynamicRegulation'.freeze,
    N2EX1H = "N2EX1H".freeze,
    EPEX30MIN = "EPEX30MIN".freeze,
    IMBALANCE = "Imbalance".freeze,

    DLM_MARKET_NAME = 'DLM'.freeze,
    SPOT_MARKET_NAME = 'SPOT'.freeze,
    INTRADAY_MARKET_NAME = 'Intraday'.freeze,

    AFRR__MARKET_NAME = 'AFRR'.freeze,
  ].freeze

  # exhaustive list of exclussive markets
  EXCLUSSIVE_MARKET_NAME = [
    TEST_MARKET_NAME,

    # german markets
    MRL_MARKET_NAME,
    PRL_MARKET_NAME,
    SRL_MARKET_NAME,

    # uk markets
    LM_MARKET_NAME,
    STOR_MARKET_NAME,
    FFR_MARKET_NAME,
    DYNAMIC_CONTAIMENT_MARKET_NAME,
  ].freeze

  ALLOW_NOMINATION_TOOL_ENABLEMENT = [
    # german markets
    MRL_MARKET_NAME,
    PRL_MARKET_NAME,
    SRL_MARKET_NAME,

    # uk markets
    DAYAHEAD_MARKET_NAME,
  ]

  def self.dynamic_services_mapping_abbr
    {
      DYNAMIC_CONTAIMENT_MARKET_NAME => 'DC',
      DYNAMIC_MODERATION_MARKET_NAME => 'DM',
      DYNAMIC_REGULATION_MARKET_NAME => 'DR'
    }
  end

  def self.abbr_dynamic_services_mapping
    {
      'DC' => DYNAMIC_CONTAIMENT_MARKET_NAME,
      'DM' => DYNAMIC_MODERATION_MARKET_NAME,
      'DR' => DYNAMIC_REGULATION_MARKET_NAME
    }
  end

  def self.mrl
    where(name: MRL_MARKET_NAME).first
  end

  def self.srl
    where(name: SRL_MARKET_NAME).first
  end

  def self.ffrd
    where(name: FFRD_MARKET_NAME).first
  end

  def self.dlm
    where(name: DLM_MARKET_NAME).first
  end

  def self.spot
    where(name: SPOT_MARKET_NAME).first
  end

  def self.intraday
    where(name: INTRADAY_MARKET_NAME).first
  end

  def self.exclussive_markets
    where(name: EXCLUSSIVE_MARKET_NAME)
  end

  def self.nomination_tool_product_intervals
    [0..4, 4..8, 8..12, 12..16, 16..20, 20..24]   
  end

  def exclussive_market?
    name.in?(EXCLUSSIVE_MARKET_NAME)
  end

  def allows_nomination_tool_enablement?
    name.in?(ALLOW_NOMINATION_TOOL_ENABLEMENT)
  end

  def self.nominateable_volume_markets?
    !Market.mrl.nil? && !Market.srl.nil?
  end

  def self.dynamic_containment
    where(name: DYNAMIC_CONTAIMENT_MARKET_NAME).first
  end

  def self.dynamic_moderation
    where(name: DYNAMIC_MODERATION_MARKET_NAME).first
  end

  def self.dynamic_regulation
    where(name: DYNAMIC_REGULATION_MARKET_NAME).first
  end

end
