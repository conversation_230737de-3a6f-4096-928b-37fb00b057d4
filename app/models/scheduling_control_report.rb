#
class SchedulingControlReport < ActiveResource::Base
  self.site = ENV['VPP_REPORTING_URL']
  self.timeout = 5.minutes

  schema do
    attribute 'date', :string
    attribute 'report', :string
  end

  def self.get(tso, market, date)
    Rails.logger.info "Fetch report /reports/scheduling_control/#{tso.id}/#{market.name}/#{date}"
    self.find(:one, from: "/reports/scheduling_control/#{tso.id}/#{market.name}/#{date}")
  end
end
