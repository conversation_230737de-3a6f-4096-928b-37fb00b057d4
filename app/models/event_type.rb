class EventType < ActiveRecord::Base

  DG_HEARTBEAT_MIRROR_FAILURE = "DG_HEARTBEAT_MIRROR_FAILURE"
  BIDDING_API_FAILURE = "BIDDING_API_FAILURE"
  BIDDING_API_SUCCESS = "BIDDING_API_SUCCESS"
  AUCTION_DOWNLOAD_FAILURE = "AUCTION_DOWNLOAD_FAILURE"
  AUCTION_DOWNLOAD_SUCCESS = "AUCTION_DOWNLOAD_SUCCESS"
  AUCTION_DOWNLOAD_RESULTS_EXCEED_BIDS = "AUCTION_DOWNLOAD_RESULTS_EXCEED_BIDS"
  DG_OVER_DELIVERY = "DG_OVER_DELIVERY"
  DG_AVAILABLE_FLEX_TOO_LOW = "DG_AVAILABLE_FLEX_TOO_LOW"
  DG_MERLIN_ISSUE = "DG_MERLIN_ISSUE"
  V2G_OPTIMIZATION_SUBMISSION_FAILURE = "V2G_OPTIMIZATION_SUBMISSION_FAILURE"
  AUCTION_MISSING_BIDS = "AUCTION_MISSING_BIDS"
  DLM_ASSET_FAULT = "DLM_ASSET_FAULT"
  DLM_EXECUTION_FAILURE = "DLM_EXECUTION_FAILURE"
  VIRTA_DLM_API_FAILURE = "VIRTA_DLM_API_FAILURE"
  VIRTA_ADMIN_API_FAILURE = "VIRTA_ADMIN_API_FAILURE"
  ERROR_UPLOADING_TRADES_SPOT_OPTIMISATION = "ERROR_UPLOADING_TRADES_SPOT_OPTIMISATION"
  AFRR_DA_NO_SETPOINT = "AFRR_DA_NO_SETPOINT"
  AFRR_DA_UPLOAD_FAILED = "AFRR_DA_UPLOAD_FAILED"
  AFRR_DA_UPLOAD_SUCCESS = "AFRR_DA_UPLOAD_SUCCESS"
  AFRR_DA_MISSING_DATA = "AFRR_DA_MISSING_DATA"
  AFRR_DA_ACK_WITH_INFO = "AFRR_DA_ACK_WITH_INFO"
  AFRR_DA_NON_ACK = "AFRR_DA_NON_ACK"
  AFRR_DA_NO_ACK = "AFRR_DA_NO_ACK"
  NLAFRR_BID_MESSAGE_NON_ACK = "NLAFRR_BID_MESSAGE_NON_ACK"
  NLAFRR_BID_MESSAGE_NO_ACK = "NLAFRR_BID_MESSAGE_NO_ACK"
  NLAFRR_BID_MESSAGE_ACK_WITH_INFO = "NLAFRR_BID_MESSAGE_ACK_WITH_INFO"
  NLAFRR_BID_MESSAGE_UPLOAD_FAILURE = "NLAFRR_BID_MESSAGE_UPLOAD_FAILURE"
  AFRR_SIGNAL_UPLOAD_FAILURE = "AFRR_SIGNAL_UPLOAD_FAILURE"
  AFRR_SIGNAL_UPLOAD_FAILURE_OVER = "AFRR_SIGNAL_UPLOAD_FAILURE_OVER"
  AFRR_NEGATIVE_POOL_CONFIGURATION = "AFRR_NEGATIVE_POOL_CONFIGURATION"
  AFRR_CONFIRMATION_MARKET_DOCUMENT_RECEIVED = "AFRR_CONFIRMATION_MARKET_DOCUMENT_RECEIVED"
  AFRR_CONFIRMATION_MARKET_DOCUMENT_MISSING = "AFRR_CONFIRMATION_MARKET_DOCUMENT_MISSING"

  def self.all_notifiable
    EventType.constants(inherited = false).sort_by{|e| I18n.t("activerecord.attributes.event_type.name.#{e}")}
  end

  def self.notifiable_for_auction_config
    [:AUCTION_DOWNLOAD_FAILURE, :AUCTION_DOWNLOAD_SUCCESS, :AUCTION_DOWNLOAD_RESULTS_EXCEED_BIDS].sort_by{|e| I18n.t("activerecord.attributes.event_type.name.#{e}")}
  end

  def self.notifiable_for_dispatch_group
    [:DG_AVAILABLE_FLEX_TOO_LOW, :DG_HEARTBEAT_MIRROR_FAILURE, :DG_MERLIN_ISSUE, :DG_OVER_DELIVERY].sort_by{|e| I18n.t("activerecord.attributes.event_type.name.#{e}")}
  end
end
