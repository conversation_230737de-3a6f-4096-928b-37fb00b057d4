module Db
  #
  # Represents available Balancing Groups for:
  # * assignment to DG / Asset
  # * collecting levels (these are also time bound)
  #
  class DbBalancingGroupName < ApplicationRecord
    include IntervalFunctions

    self.table_name = :balancing_group_name
    self.locking_column = :version

    validates :name, length: { is: 16 }

    # used by the interval functions module
    def self.interval_start_attribute
      :start_date
    end

    # used by the interval functions module
    def self.interval_end_attribute
      :end_date
    end

    #
    # Returns entries that overlap this entry
    # and belong to the same dispatch group
    #
    def overlapping_bg
      res = self
            .class
            .overlap_date_interval(start_date..end_date)
      res = res.where('id <> ?', id) if persisted?
      res
    end
  end
end
