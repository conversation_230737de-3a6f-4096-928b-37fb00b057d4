# a ... Customer !
class Customer < ApplicationRecord
	has_many :asset_configuratios
  has_many :contracts
  has_many :nomination_bids
  has_many :nomination_bid_sources

  def get_bids_for_date_and_market(date: Date.current.tomorrow.strftime('%Y-%m-%d'), market: nil)
    bids = nomination_bids
      .joins('left join product on nomination_bid.product_id = product.id')
      .joins('left join market on product.market_id = market.id')
      .joins('left join tso on nomination_bid.tso_id = tso.id')
      .select('nomination_bid.*')
      .select('market.id as market_id, market.name as market_name')
      .select('product.name as product_name')
      .select('tso.id as tso_id, tso.name as tso_name, tso.tso_id as tso_tso_id')
      .select('CASE WHEN nomination_bid.accepted_flex_volume = 0 THEN \'rejected\' WHEN nomination_bid.accepted_flex_volume < nomination_bid.flex_volume THEN \'partial\' WHEN nomination_bid.accepted_flex_volume >= nomination_bid.flex_volume THEN \'accepted\' ELSE \'pending\' END as bid_status')
      .where('nomination_bid.deleted IS NULL')
      .where("((nomination_bid.start_time at time zone 'UTC') at time zone tso.time_zone)::date = ?", date)

    if market.present?
      bids = bids.where('LOWER(market.name) = ?', market.downcase )
    end

    bids
  end

  def last_nomination_bid_source_by_market_and_date(market: nil, date: Date.current.tomorrow.strftime('%Y-%m-%d'))
    return nil unless market.present?

    nomination_bid = nomination_bids
      .joins('left join product on nomination_bid.product_id = product.id')
      .joins('left join market on product.market_id = market.id')
      .where('LOWER(market.name) = ?', market.downcase).last

    if nomination_bid.present?
      nomination_bid.nomination_bid_source
    else
      nil
    end
  end
end
