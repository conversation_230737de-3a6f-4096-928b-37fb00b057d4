#
class SchedulingBalancingGroupsThirdPartyReport < ActiveResource::Base
  self.site = ENV['VPP_REPORTING_URL']
  self.timeout = 5.minutes

  schema do
    attribute 'date', :string
    attribute 'report', :string
  end

  def self.get(tso, third_party_balancing_group, date)
    self.find(:one, from: "/reports/scheduling_balancing_groups_third_party/#{tso.id}/#{date}?third_party_balancing_group_id=#{URI.encode(third_party_balancing_group)}")
  end
end
