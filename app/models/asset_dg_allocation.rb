#
# Represents an Asset-DG Allocation Source entity
#
class AssetDgAllocation < ApplicationRecord
  include IntervalInput
  include Authority::Abilities

  validates :start_time, presence: true
  validates :end_time, presence: true
  validates :asset_id, presence: true
  validates :dispatch_group_id, presence: true

  # make sure we never try to update the data in the DB
  def create; end

  def save; end

  def update; end
end
