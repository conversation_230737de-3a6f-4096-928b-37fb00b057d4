class AutomaticAllocation < OpenStruct

  KEY = "automatic-allocation"

  def self.value_hash_by_id(id)
    VppEngineExecutionPlanKeyValue.
      where(key: KEY).
      where("value->>'id' = ?", id).
      first.try(:value).try(:deep_symbolize_keys)
  end

  def self.from_value_hash(value_hash)
    if value_hash
      AutomaticAllocation.new(value_hash)
    else
      nil
    end
  end

  def self.filter_paginate(dispatch_group, start_date, end_date, status, type, page, per_page)
    allocations =
      VppEngineExecutionPlanKeyValue.
      where(key: KEY)

    if dispatch_group.present?
      allocations = allocations.where("value->'dgIds' @> '?'", dispatch_group.to_i)
    end
    if start_date.present? && !start_date.blank?
      allocations = allocations.where("to_timestamp(value->'endTime') >= ?", start_date)
    end
    if end_date.present? && !end_date.blank?
      allocations = allocations.where("to_timestamp(value->'startedAt') <= ?", end_date)
    end
    if status.present? && !status.blank?
      allocations = allocations.where("value->'status'->>'type' = ?", status)
    end
    if type.present? == AutomaticAllocation::TYPE_FALLBACK
      allocations = allocations.where("value->'fallbackStart' is not null")
    elsif type == AutomaticAllocation::TYPE_STANDARD
      allocations = allocations.where("value->'fallbackStart' is null")
    end
    allocations.
      order(Arel.sql("to_timestamp(value->>'startedAt', 'YYYY-MM-DD\"T\"HH24:MI:SS\"Z\"')").desc).
      page(page).
      per(per_page)
  end

  TYPES = [
    TYPE_FALLBACK = 'fallback'.freeze,
    TYPE_STANDARD = 'standard'.freeze
  ].freeze

  def dispatch_groups
    @dispatch_groups ||= DispatchGroup.not_deleted.where(id: dgIds)
  end

  def tso
    @tso ||= Tso.find(tsoId.try(:[], :value))
  end

  def type
    if fallbackStart.present?
      TYPE_FALLBACK
    else
      TYPE_STANDARD
    end
  end

  # store_in collection: "allocationRun", database: "automatic-allocation"
  # field :startedAt, type: DateTime
  # field :endTime, type: DateTime
  # field :fallbackStart, type: DateTime
  # embeds_one :status
  # embeds_many :triggerEvents
  # field :dgIds, type: Array
  # field :allocationSourceId, type: Hash
  # field :tsoId, type: Hash
  # class Status
  #   field :type, type: String
  # end
  # class TriggerEvent
  #   field :triggerType, type: String
  #   field :startTime, type: String
  # end

end

