#
class ImportedReport < ActiveResource::Base
  REPORT_TYPES = [
    REPORT_TYPE_TSO_SCHEDULE = 'tso_schedule'.freeze
  ].freeze

  self.site = ENV['VPP_REPORTING_URL']

  # validates :tso_id, :market_id, :contents, presence: true
  validates :filename, :contents, presence: true

  schema do
    attribute 'tso_id',     :integer
    attribute 'market_id',  :integer
    attribute 'filename',   :string
    attribute 'contents',   :string

    attribute 'user_account', :string
    attribute 'tso', :string
    attribute 'market', :string
  end

  def created
    Time.parse(super) if super.present?
  end

  def report_date
    Date.parse(super) if super.present?
  end

  #
  class UserAccount < ActiveResource::Base
    self.site = ImportedReport.site
  end
  #
  class Tso < ActiveResource::Base
    self.site = ImportedReport.site
  end
  #
  class Market < ActiveResource::Base
    self.site = ImportedReport.site
  end
end
