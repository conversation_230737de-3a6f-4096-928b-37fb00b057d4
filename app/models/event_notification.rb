class EventNotification < ApplicationRecord
  self.table_name = 'event_notification_recipient'
  #include Authority::Abilities

  self.primary_keys = :event_type, :notification_type, :user_id

  belongs_to :event_type, foreign_key: 'event_type'
  belongs_to :user_account

  TYPE_EMAIL     = 'email'
  TYPE_PHONECALL = 'phonecall'
  TYPE_SMS       = 'sms'
  TYPES = [TYPE_EMAIL, TYPE_PHONECALL, TYPE_SMS]

  def to_string_id
    "#{self.event_type.id}:#{self.notification_type}:#{self.user_id}"
  end

  def self.update_notification_recipiens(notifications)
    notifications_str_ids = notifications.collect { |n| n.to_string_id }
    to_remove = EventNotification.all.select { |n| !notifications_str_ids.include?(n.to_string_id) }
    to_remove.each do |remove_notification|
      remove_notification.destroy
    end
    event_notifications_str_ids = EventNotification.all.collect { |n| n.to_string_id }
    to_add = notifications.select { |n| !event_notifications_str_ids.include?(n.to_string_id) }
    to_add.each do |new_notification|
      new_notification.save
    end
  end

end