class SignalList < ApplicationRecord

  has_many :vpp_signals

  accepts_nested_attributes_for :vpp_signals, allow_destroy: true

  validates :name, presence: true, length: {minimum: 1, maximum: 255}, allow_blank: false


  def read_signals
    vpp_signals.select{|s| s.direction == VppSignal.vpp_signal_directions[:read]}
  end

  def write_signals
    vpp_signals.select{|s| s.direction == VppSignal.vpp_signal_directions[:write]}
  end

end
