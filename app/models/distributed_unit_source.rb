#
# Represents an Asset-DG Allocation Source entity
#
class DistributedUnitSource < ApplicationRecord
  include DbEntitySource

  self.table_name = 'distributed_unit_source'

  belongs_to :user_account,
             foreign_key: 'user_id'

  has_many :distributed_units,
           foreign_key: 'source_id'

  def nothing_imported?
    validation_result_errors.empty? &&
      validation_result_warnings.empty? &&
      distributed_units.count == 0
  end
end
