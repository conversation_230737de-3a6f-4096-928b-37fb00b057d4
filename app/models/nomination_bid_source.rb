class NominationBidSource < ApplicationRecord
  include DbEntitySource

  belongs_to :customer, optional: true
  belongs_to :user, foreign_key: 'user_id', class_name: 'User'
  belongs_to :auction_config, foreign_key: 'auction_rules_id', class_name: 'AuctionConfig', optional: true
  has_many :nomination_bids, class_name: 'NominationBid', foreign_key: 'source_id'

  # make sure we never try to update the data in the DB
  def create; end
  def save; end
  def update; end

  def is_success?
    validation_result_obj['validationSuccess'] || false
  end

  def validation_errors
    validation_result_obj['errors']
  end

  def validation_warnings
    validation_result_obj['warnings']
  end

  def validation_result_obj
    @validation_result_obj ||= validation_result
  end

end