class Services::PriceForecastImporter

  def parse_file(file_contents, file_name, auction_config)
    markets = [] 
    markets << auction_config.bidding_method_market
    markets = markets.flatten
    is_dc = markets.include?('DynamicContainment')
    is_dm = markets.include?('DynamicModeration')
    is_dr = markets.include?('DynamicRegulation')
    is_epex30min = markets.include?('EPEX30MIN')
    is_n2ex1h = markets.include?('N2EX1H')
    tz = auction_config.time_zone.try(:first)

    if is_n2ex1h || is_epex30min
      parse_n2ex1h_epex30min_prices(file_contents, file_name, is_n2ex1h, is_epex30min, tz)
    else
      parse_ds_prices(file_contents, file_name, is_dc, is_dm, is_dr)
    end
  end

  private 

  def parse_ds_prices(file_contents, file_name, is_dc, is_dm, is_dr)
    price_lines = []
    errors = []
    begin
      csv = CSV.parse(file_contents, headers: true, col_sep: ';')
      expected_headers = ["datetime_from", "datetime_to"]
      expected_headers += ["Priceforecast_DCH", "Priceforecast_DCL"] if is_dc
      expected_headers += ["Priceforecast_DMH", "Priceforecast_DML"] if is_dm
      expected_headers += ["Priceforecast_DRH", "Priceforecast_DRL"] if is_dr
      missing_headers = expected_headers - csv.headers
      if missing_headers.empty?
        price_lines = csv.map do |row|
          r = {
            time_from: parse_time(row['datetime_from']),
            time_to: parse_time(row['datetime_to']),
          }
          if is_dc
            r = r.merge({
              price_forecast_dch: parse_float(row['Priceforecast_DCH']),
              price_forecast_dcl: parse_float(row['Priceforecast_DCL'])
            })
          end
          if is_dm
            r = r.merge({
              price_forecast_dmh: parse_float(row['Priceforecast_DMH']),
              price_forecast_dml: parse_float(row['Priceforecast_DML'])
            })
          end
          if is_dr
            r = r.merge({
              price_forecast_drh: parse_float(row['Priceforecast_DRH']),
              price_forecast_drl: parse_float(row['Priceforecast_DRL'])
            })
          end
          r
        end
        if price_lines.empty?
          errors << I18n.t('asset_optimization.err_missing_price_forecast', file_name: file_name)
        else
          errors = validate_prices(price_lines, is_dc, is_dm, is_dr, false, false)
        end
      else
        errors = [I18n.t('errors.messages.price_forecast_file_invalid_header', header: missing_headers.join(", "), file_name: file_name)]
      end
    rescue Exception => e
      errors =[e.message]
    end
    {
      price_lines: price_lines,
      errors: errors,
      file_name: file_name
    }
  end

  def parse_n2ex1h_epex30min_prices(file_contents, file_name, is_n2ex1h, is_epex30min, tz)
    price_lines = []
    errors = []
    begin
      csv = CSV.parse(file_contents, headers: true, col_sep: ',')
      expected_headers = []
      expected_headers << "EPEX 30 Min FC File" if is_epex30min
      expected_headers << nil if is_n2ex1h
      expected_headers += (1..50).to_a.collect(&:to_s)
      missing_headers = expected_headers - csv.headers
      if missing_headers.empty?
        price_lines = csv.map do |row|
          day_date = parse_date(row[0], tz)
          (1..50).to_a.collect do |sp_number| 
            if row[sp_number].present?
              sp_price = {
                time_from: day_date + (sp_number - 1) * 30.minutes,
                time_to: day_date + (sp_number) * 30.minutes,
              }
              if is_n2ex1h
                sp_price[:price_forecast_n2ex1h] = parse_float(row[sp_number])
              end
              if is_epex30min
                sp_price[:price_forecast_epex30min] = parse_float(row[sp_number])
              end
              sp_price
            else
              nil
            end
          end
        end
        price_lines = price_lines.flatten.compact
        if price_lines.empty?
          errors << I18n.t('asset_optimization.err_missing_price_forecast', file_name: file_name)
        else
          errors = validate_prices(price_lines, false, false, false, is_n2ex1h, is_epex30min)
        end        
      else
        errors = [I18n.t('errors.messages.price_forecast_file_invalid_header', header: missing_headers.join(", "), file_name: file_name)]
      end

    rescue Exception => e
      errors =[e.message]
    end
    {
      price_lines: price_lines,
      errors: errors,
      file_name: file_name
    }
  end

  def validate_prices(price_lines, is_dc, is_dm, is_dr, is_n2ex1h, is_epex30min)
    errors = []
    invalid_lines = price_lines.
          each_with_index.map{ |p, index| [validate_price(p, is_dc, is_dm, is_dr, is_n2ex1h, is_epex30min).empty?, index]}.
          select{|v| v[0] == false }.collect { |v| v[1] + 1 }
    if !invalid_lines.empty?
      errors << I18n.t('asset_optimization.err_invalid_price', lines: invalid_lines.join(", "))
    end
    errors
  end

  def validate_price(price, is_dc, is_dm, is_dr, is_n2ex1h, is_epex30min)
    errors = {}
    errors[:time_from] = true unless price[:time_from]
    errors[:time_to] = true unless price[:time_to]
    if is_dc
      errors[:price_forecast_dch] = true unless price[:price_forecast_dch]
      errors[:price_forecast_dcl] = true unless price[:price_forecast_dcl]
    end
    if is_dm
      errors[:price_forecast_dmh] = true unless price[:price_forecast_dmh]
      errors[:price_forecast_dml] = true unless price[:price_forecast_dml]
    end
    if is_dr
      errors[:price_forecast_drh] = true unless price[:price_forecast_drh]
      errors[:price_forecast_drl] = true unless price[:price_forecast_drl]
    end
    if is_n2ex1h
      errors[:price_forecast_n2ex1h] = true unless price[:price_forecast_n2ex1h]
    end
    if is_epex30min
      errors[:price_forecast_epex30min] = true unless price[:price_forecast_epex30min]
    end
    errors
  end

  def parse_date(str_date, tz)
    begin
      ActiveSupport::TimeZone[tz].parse(str_date).getutc
    rescue
      nil
    end
  end

  def parse_time(str_time)
    begin
      Time.strptime(str_time, "%Y-%m-%dT%H:%M:%S%z")
    rescue
      nil
    end
  end

  def parse_float(str_float)
    if str_float.present?
      str_float.gsub!(/\,/,".")
      if (Float(str_float) != nil rescue false)
        str_float.to_f
      else
        nil
      end
    else
      nil
    end
  end


end