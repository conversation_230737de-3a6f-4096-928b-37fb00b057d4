class Services::PerfDataFilesUploader

  require 'net/sftp'
  
  ALLOWED_ASSET_EXTERNAL_IDS = ['EONDC-01', 'EONDC-02', 'EONDC-03']

  def upload(asset_id, file_name, file_path)
    result = {
      success: true,
      errors: []
    }
    if !valid_asset?(asset_id)
      result[:success] = false
      result[:errors] << I18n.t('errors.messages.perf_data_asset_not_supported')
    end
    if !result[:errors].present?
      result[:errors] = validate_file_name(file_name)
    end
    if !result[:errors].present?
      result[:errors] = upload_file_contents(@asset.short_name.value, file_name, file_path)
    end
    result
  end

  def valid_asset?(asset_id)
    @asset = Asset.where(id: asset_id).first
    puts "#### SHORT NAME for ##{asset_id} = #{@asset.short_name.value}"
    @asset && ALLOWED_ASSET_EXTERNAL_IDS.include?(@asset.short_name.value)
  end

  def validate_file_name(file_name)
    errors = []
    if @asset.short_name.value == 'EONDC-01'
      errors = validate_eondc_1_file(file_name)
    elsif @asset.short_name.value == 'EONDC-02'
      errors = validate_eondc_2_file(file_name)
    elsif @asset.short_name.value == 'EONDC-03'
      errors = validate_eondc_3_file(file_name)
    end
    errors
  end

  def validate_eondc_1_file(file_name)
    # perfdata_identifier_yyyymmddThh0000Z_yyyymmddThh0000Z_yyyymmddThhmmss.csv.gz
    # perfdata_identifier_yyyymmddThh0000Z_yyyymmddThh0000Z_yyyymmddThhmmss.csv
    errors = []
    str_start_ts = nil
    str_end_ts = nil
    if file_name.end_with?('.csv.gz') || file_name.end_with?('.csv')
      file_name_no_ext = file_name.sub(/\.csv\.gz\z/, "") if file_name.end_with?('.csv.gz')
      file_name_no_ext = file_name.sub(/\.csv\z/, "") if file_name.end_with?('.csv')
      pattern_2_3 = /([0-9]{4})(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])T(2[0-3]|[01][0-9])0000Z/
      pattern_4   = /([0-9]{4})(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])T(2[0-3]|[01][0-9])([0-5][0-9])([0-5][0-9])/
      groups = file_name_no_ext.split("_")
      if !(groups.size == 5 && groups[0] == 'perfdata' &&
          groups[2].match?(pattern_2_3) && groups[3].match?(pattern_2_3) &&
          groups[4].match?(pattern_4))
        errors << I18n.t('errors.messages.perf_data_invalid_file_name', name_format: 'perfdata_identifier_yyyymmddThh0000Z_yyyymmddThh0000Z_yyyymmddThhmmss')
      else
        str_start_ts = groups[2]
        str_end_ts = groups[3]
      end
    else
      errors << I18n.t('errors.messages.perf_data_invalid_file_extension', supported_extension: '.csv.gz, .csv')
    end
    if errors.empty?
      start_time = Time.parse(str_start_ts)
      end_time = Time.parse(str_end_ts)
      # The difference between the first hh timestamp (from time) and the second one (to time) needs to be a multiple of 1 h
      if start_time >= end_time || (end_time.to_i - start_time.to_i) % 3600 != 0
        errors << I18n.t('errors.messages.perf_data_invalid_times')
      end
    end
    errors
  end

  def validate_eondc_2_file(file_name)
    # EONDC-02_yyyymmddhh0000_perfmonv1.csv
    errors = []
    pattern = /EONDC-02_([0-9]{4})(0[1-9]|1[0-2])(0[1-9]|[1-2][0-9]|3[0-1])(2[0-3]|[01][0-9])0000_perfmonv1.csv/
    if !file_name.match?(pattern)
      errors << I18n.t('errors.messages.perf_data_invalid_file_name', name_format: 'EONDC-02_yyyymmddhh0000_perfmonv1.csv')
    end
    errors
  end

  def validate_eondc_3_file(file_name)
    # EONDC-03_yyyy-mm-dd_hh.00.00.txt
    errors = []
    pattern = /EONDC-03_([0-9]{4})-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])_(2[0-3]|[01][0-9]).00.00.txt/
    if !file_name.match?(pattern)
      errors << I18n.t('errors.messages.perf_data_invalid_file_name', name_format: 'EONDC-03_yyyy-mm-dd_hh.00.00.txt')
    end
    errors
  end

  def upload_file_contents(asset_external_id, file_name, file_path)
    errors = []
    uri = URI.parse('sftp://' + ENV['PERF_DATA_SFTP_HOST'])
    username = ENV['PERF_DATA_SFTP_USERNAME']
    password = ENV['PERF_DATA_SFTP_PASSWORD']
    port = ENV['PERF_DATA_SFTP_PORT']
    begin
      Net::SFTP.start(uri.host, username, password: password, port: port, verbose: Logger::DEBUG) do |sftp|
        remote_file_path = "#{asset_external_id}/manual/#{file_name}"
        sftp.upload!(file_path, remote_file_path)
      end
    rescue => e
      Rails.logger.error "SFTP error for perf data file #{file_path} #{e.message} #{e.backtrace}"
      errors << "An unexpected error occurred: #{e.message} #{e.backtrace}"
    end
    errors
  end
end