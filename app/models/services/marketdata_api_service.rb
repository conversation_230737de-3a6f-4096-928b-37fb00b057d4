module Services
  #
  # Utility for interacting with vpp-trading
  #
  class MarketdataApiService
    include HTTParty
    base_uri ENV['MARKET_DATA_API_URL']
    debug_output $stdout

    UnableToFileUpload = Class.new(StandardError)

    class << self

      def ping
        result = get('/', timeout: 5)
        raise "Expected 200 but got #{result.code} #{result.body}" unless result.ok?
      end

      def post_bod(payload)
        Rails.logger.info "POST BOD payload: #{payload}"
        result = post(
          '/v1/bid_offer_data',
          headers: { 'Content-Type' => 'application/json' },
          body: payload.to_json)

        ok = result.success?
        Rails.logger.info "Success: #{ok} #{result.code} body: #{result.body}"

        resp = {}
        if ok
          resp[:status] = "success"
          resp[:messages] = ["Success", result.body]
          resp
        else
          Rails.logger.info "Posting market data bid offer data failed with status #{result.code} and body #{result.body}"
          begin
            resp[:status] = "failure"
            resp[:messages] = [result.body]
            resp
          rescue
            raise UnableToFileUpload, I18n.t('errors.messages.bod_file_upload')
          end
        end
      end

      def post_margin_data(payload)
        Rails.logger.info "POST margin data payload: #{payload}"
        # payload sample
        # [
        #   {
        #     "settlement_period": 0,
        #     "total_offer_margin": 0,
        #     "total_bid_margin": 0
        #   }
        # ]
        result = post(
          '/v1/bm_margin_data',
          headers: { 'Content-Type' => 'application/json' },
          body: payload.to_json)

        ok = result.success?
        Rails.logger.info "Success: #{ok} #{result.code} body: #{result.body}"

        resp = {}
        if ok
          resp[:status] = "success"
          resp[:messages] = ["Success", result.body]
          resp
        else
          Rails.logger.info "Posting margin data failed with status #{result.code} and body #{result.body}"
          begin
            resp[:status] = "failure"
            resp[:messages] = [result.body]
            resp
          rescue
            raise UnableToFileUpload, I18n.t('errors.messages.margin_data_file_upload')
          end
        end
      end

      def last_price_forecast(markets, start_time, end_time)
        normalizer = proc do |query|
          query.flat_map do |key, value|
            if value.is_a?(Array)
              value.map { |v| "#{key}=#{URI.encode_www_form_component(v)}" }
            else
              ["#{key}=#{URI.encode_www_form_component(value)}"]
            end
          end.join('&')
        end

        if markets.include?('DYNAMICCONTAINMENT') || markets.include?('DYNAMICMODERATION') || markets.include?('DYNAMICREGULATION')
          last_price_forecast_for_dc(markets, start_time, end_time, normalizer)
        elsif markets.include?('EPEX30MIN') || markets.include?('N2EX1H')
          last_price_forecast_for_epex_n2ex(markets, start_time, end_time, normalizer)
        end
      end

      def last_price_forecast_for_dc(markets, start_time, end_time, query_normalizer)
        products = []
        products.concat(['DCL', 'DCH']) if markets.include?('DYNAMICCONTAINMENT')
        products.concat(['DML', 'DMH']) if markets.include?('DYNAMICMODERATION')
        products.concat(['DRL', 'DRH']) if markets.include?('DYNAMICREGULATION')
        result = get(
          "/v1/eac_price_forecasts",
          query: {
            start_time: start_time.strftime('%Y-%m-%dT%H:%M:%S%z'),
            end_time: end_time.strftime('%Y-%m-%dT%H:%M:%S%z'),
            auction_products: products
          },
          query_string_normalizer: query_normalizer,  
          headers: { 'Content-Type' => 'application/json' }
        )
        most_recent_price_forecast_date(result)
      end

      def last_price_forecast_for_epex_n2ex(markets, start_time, end_time, query_normalizer)
        puts "##### last_price_forecast_for_epex_n2ex: #{markets}"
        result = get(
          "/v1/price_forecasts",
          query: {
            start_time: start_time.strftime('%Y-%m-%dT%H:%M:%S%z'),
            end_time: end_time.strftime('%Y-%m-%dT%H:%M:%S%z'),
            price_type: "forecast",
            markets: markets.collect(&:downcase)
          },
          query_string_normalizer: query_normalizer,  
          headers: { 'Content-Type' => 'application/json' }
        )
        most_recent_price_forecast_date(result)
      end

      def most_recent_price_forecast_date(result)
        resp = nil
        if result.ok?
          most_recent = JSON.parse(result.body).max_by { |entry| Time.parse(entry["created"]) }
          resp = most_recent["created"] if most_recent.present?
        else
          err_msg = "Failed to get eac_price_forecasts for #{markets} between #{start_time} and #{end_time}: #{result.code} and body #{result.body}"
          Rails.logger.error err_msg
        end
        resp
      end

    end
  end
end