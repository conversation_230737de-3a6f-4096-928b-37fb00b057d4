class Services::MarginDataFileImporter

  def parse_file(file_contents, file_name)
    margin_data_lines = []
    errors = []
    begin
      csv = CSV.parse(file_contents, headers: true, col_sep: ',')
      expected_headers = ["SP", "Total Offer Margin", "Total Bid Margin"]
      missing_headers = expected_headers - csv.headers
      if missing_headers.empty?
        margin_data_lines = csv.map do |row|
          {
            settlement_period: row['SP'].try(:to_i),
            total_offer_margin: parse_float(row['Total Offer Margin']),
            total_bid_margin: parse_float(row['Total Bid Margin'])
          }
        end
        if margin_data_lines.empty?
          errors << I18n.t('asset_optimization.err_missing_margin_data', file_name: file_name)
        else
          errors = validate_data(margin_data_lines)
        end
      else
        errors = [I18n.t('errors.messages.margin_data_file_invalid_header', header: missing_headers.join(", "), file_name: file_name)]
      end
    rescue Exception => e
      errors =[e.message]
    end
    {
      margin_data_lines: margin_data_lines,
      errors: errors,
      file_name: file_name
    }
  end

  def validate_data(data_lines)
    errors = []
    invalid_lines = data_lines.
          each_with_index.map{ |l, index| [validate_margin_data(l).empty?, index]}.
          select{|v| v[0] == false }.collect { |v| v[1] + 1 }
    if !invalid_lines.empty?
      errors << I18n.t('errors.messages.err_invalid_margin_data', lines: invalid_lines.join(", "))
    end
    errors
  end

  def validate_margin_data(line)
    errors = {}
    if !(1..50).to_a.include?(line[:settlement_period])
      errors[:sp] = true
    end
    if !line[:total_offer_margin].present?
      errors[:total_offer_margin] = true
    end
    if !line[:total_bid_margin].present?
      errors[:total_bid_margin] = true
    end
    errors
  end

  def parse_float(str_float)
    if str_float.present?
      str_float.gsub!(/\,/,".")
      if (Float(str_float) != nil rescue false)
        str_float.to_f
      else
        nil
      end
    else
      nil
    end
  end

end