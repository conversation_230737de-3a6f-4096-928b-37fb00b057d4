class Services::N2exEpexDsAfrrAuctionResultImporter

  DATE_FORMAT = "%Y-%m-%dT%H:%M:%S%z"

  def parse_buffer(content, with_market)
    wb = RubyXL::Parser.parse_buffer(content)
    parse_workbook(wb, with_market)
  end

  def parse_workbook(workbook, with_market)
    if workbook.worksheets.empty?
      errmsg = I18n.t('bid.err_no_worksheet')
      Rails.logger.error errmsg
      return {error: errmsg}
    end

    parse_bid_worksheet(workbook.worksheets.first, with_market)
  end

  def parse_bid_worksheet(ws, with_market)
    ret = {}
    return error_result I18n.t('bid.err_col_missing', name: 'external_id') unless ws[1].present? && get_cell_str(ws[0][0]) == 'external_id'
    col_offset = with_market ? 1 : 0
    if with_market
      return error_result I18n.t('bid.err_col_missing', name: 'market') unless ws[1].present? && get_cell_str(ws[0][1]) == 'market'
    end
    return error_result I18n.t('bid.err_col_missing', name: 'time_from') unless ws[1].present? && get_cell_str(ws[0][1 + col_offset]) == 'time_from'
    return error_result I18n.t('bid.err_col_missing', name: 'time_to') unless ws[1].present? && get_cell_str(ws[0][2 + col_offset]) == 'time_to'
    return error_result I18n.t('bid.err_col_missing', name: 'direction') unless ws[1].present? && get_cell_str(ws[0][3 + col_offset]) == 'direction'
    return error_result I18n.t('bid.err_col_missing', name: 'volume_kw') unless ws[1].present? && get_cell_str(ws[0][4 + col_offset]) == 'volume_kw'
    return error_result I18n.t('bid.err_col_missing', name: 'price') unless ws[1].present? && get_cell_str(ws[0][5 + col_offset]) == 'price'

    ret[:lines] = []
    ws.each do |row|
      if row.present?
        if row.index_in_collection >= 1 && !empty_row?(row)
          bid_item = parse_bid_row(row, with_market)
          if bid_item[:error]
            return error_result bid_item[:error]
          end
          ret[:lines] << bid_item
        end
      end
    end

    ret
  end

  def empty_row?(row)
    !row[0].try(:value).present? &&
    !row[1].try(:value).present? &&
    !row[2].try(:value).present? &&
    !row[3].try(:value).present? &&
    !row[4].try(:value).present? &&
    !row[5].try(:value).present?
  end

  def parse_bid_row(row, with_market)
    # asset external id must be present
    if !get_cell_str(row[0])
      { error: I18n.t('bid.err_external_id') }
    else
      col_offset = with_market ? 1 : 0
      bid = {
        asset_id: get_cell_str(row[0]),
        time_from: to_iso_time(get_cell_datetime(row[1 + col_offset])),
        time_to: to_iso_time(get_cell_datetime(row[2 + col_offset])),
        direction: get_cell_str(row[3 + col_offset]),
        volume_kw: get_cell_integer(row[4 + col_offset]),
        price: get_cell_float(row[5 + col_offset]),
      }
      if with_market
        market_abbr = get_cell_str(row[1]).try(:strip)
        bid[:market] = Market.abbr_dynamic_services_mapping[market_abbr]
        return error_result I18n.t('bid.err_missing_market', name: 'with_market') unless bid[:market]
      end
      return error_result I18n.t('bid.err_missing_time', name: 'time_from') unless bid[:time_from]
      return error_result I18n.t('bid.err_missing_time', name: 'time_to') unless bid[:time_to]
      return error_result I18n.t('bid.err_missing', name: 'direction') unless bid[:direction]
      return error_result I18n.t('bid.err_missing', name: 'volume_kw') unless bid[:volume_kw]
      return error_result I18n.t('bid.err_missing', name: 'price') unless bid[:price]

      bid
    end
  end

  def error_result(errmsg)
    Rails.logger.error errmsg
    {error: errmsg}
  end

  def get_cell_integer(cell)
    return nil unless cell.present?
    if cell && (Float(cell.value.to_s) != nil rescue false) && cell.value.to_i == cell.value.to_f
      cell.value.to_i
    else
      nil
    end
  end

  def get_cell_float(cell)
    return nil unless cell.present?
    if cell && (Float(cell.value.to_s) != nil rescue false)
      cell.value.to_f
    else
      nil
    end
  end

  def get_cell_str(cell)
    return nil unless cell.present?
    (cell.value || (cell.datatype == RubyXL::DataType::INLINE_STRING && cell.try(:is))).try(:to_s)
  end

  def get_cell_datetime(cell)
    return nil unless cell.present?
    cell_value = nil
    if cell && cell.is_date?
      cell_value = cell.value
    else
      cell_value = parse_time(cell.value.to_s)
    end
    cell_value
  end

  def parse_time(str_time)
    begin
      Time.strptime(str_time, "%Y-%m-%dT%H:%M:%S%z")
    rescue
      nil
    end
  end

  def to_iso_time(time)
    if time.present?
      time.getutc.strftime("%Y-%m-%dT%H:%M:%SZ")
    else
      nil
    end
  end

  def empty_or_integer_cell_value?(cell)
    if cell && cell.value && cell.value.to_i.to_s != cell.value.to_s.strip
      false
    else
      true
    end
  end

end