class Services::OptiResultExporter

  def initialize(opti_result_lines:, asset:, selected_date:)
    @opti_result_lines = opti_result_lines
    @asset = asset
    @selected_date = selected_date
  end

  def content_type
    'application/excel'
  end

  def filename
    "#{@selected_date.strftime("%Y%m%d")}_Optiresult_#{@asset.id}.xlsx"
  end

  def content
    build_excel.to_stream.read
  end

  # XLSX
  #
  private def build_excel
    Axlsx::Package.new.tap do |p|
      wb = p.workbook
      wb.add_worksheet(name: 'Sheet') do |sheet|
        sheet.add_row [
          'datetime',
          'soc',
          'activation_dc_pos',
          'activation_dc_neg',
          'activation_dm_pos',
          'activation_dm_neg',
          'activation_dr_pos',
          'activation_dr_neg',
          'available_char_power',
          'available_dischar_power'
        ]
        @opti_result_lines.each_with_index do |line, index|
          sheet.add_row [
            line.datetime.strftime("%Y-%m-%dT%H:%M:%S%z"),
            line.soc,
            line.activation_dc_pos,
            line.activation_dc_neg,
            line.activation_dm_pos,
            line.activation_dm_neg,
            line.activation_dr_pos,
            line.activation_dr_neg,
            line.available_char_power,
            line.available_dischar_power
          ]
        end
      end
    end
  end


end