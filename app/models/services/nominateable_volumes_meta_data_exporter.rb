class Services::NominateableVolumesMetaDataExporter

  def initialize(nominateable_volume)
    @nominateable_volume = nominateable_volume
    @meta_data = HashCaseConverter.to_underscore(@nominateable_volume.meta_data)

  end

  def export_meta_data_to_string
    build_excel_meta_data.to_stream.read
  end

  def meta_data_filename
    "Meta Data Nomination #{@nominateable_volume.delivery_date.strftime('%d.%m.%Y')}.xlsx"
  end

  # XLSX
  #
  private

  def build_excel_meta_data
    Axlsx::Package.new.tap do |p|
      wb = p.workbook

      style_meta_th = wb.styles.add_style alignment: { horizontal: :center }, bg_color: "8faadc", border: 1
      style_time = wb.styles.add_style :format_code => 'dd-mm-yyyy HH:MM'

      wb.add_worksheet(name: 'Schedule With PQ') do |sheet|

        sheet.add_row [
          "TSO",
          "Market",
          "Energy Direction",
          "Start Time",
          "End Time",
          "Asset ID",
          "PQ",
          "Schedule",
          "Capped Schedule",
          "Price",
          "Contract"
        ], style: ([style_meta_th] * 11)

        tsos = Tso.all
        markets = Market.all

        @meta_data['schedules_with_pq'].each do |swpq_group|
          swpq_group.sort {|a, b| a['asset_id'] <=> b['asset_id']}.each do |swpq|
            sheet.add_row [
              tsos.find{|tso| tso.id == swpq['tso_id']}.try(:name),
              markets.find{|m| m.id == swpq['market_id']}.try(:name),
              swpq['energy_direction'],
              str_to_time(swpq['start_time']),
              str_to_time(swpq['end_time']),
              swpq['asset_id'],
              swpq['pq'],
              swpq['schedule'],
              swpq['pq_capped_schedule'],
              swpq['price'],
              swpq['contract_type']
            ], style: [nil, nil, nil, style_time, style_time, nil, nil, nil, nil, nil, nil]
          end
          sheet.add_row []
        end

      end

      wb.add_worksheet(name: 'Forecast Availability') do |sheet|
        sheet.add_row [
          "Asset ID",
          "Start Time",
          "End Time",
          "Flexibility Positive",
          "Flexibility Negative"
        ], style: ([style_meta_th] * 5)

        @meta_data['forecast_availability'].each do |fa|
          fa = fa.sort {|a, b| a['asset_id'] <=> b['asset_id']}
          fa.each do |fa_intervals|
            sheet.add_row [
              fa_intervals['asset_id'],
              str_to_time(fa_intervals['start_time']),
              str_to_time(fa_intervals['end_time']),
              fa_intervals['flexibility_pos'],
              fa_intervals['flexibility_neg']
            ], style: [nil, style_time, style_time, nil, nil]
          end
        end
      end

      wb.add_worksheet(name: 'Percentage Availability') do |sheet|
        sheet.add_row [
          "Asset ID",
          "Start Time",
          "End Time",
          "Positive %",
          "Negative %",
          "Max Fault Secs %",
          "Max Invalid Rollups%",
        ], style: ([style_meta_th] * 7)

        @meta_data['percentage_availability'].
        sort {|a, b| a['asset_id'] <=> b['asset_id']}.
        each do |pa|

            sheet.add_row [
              pa['asset_id'],
              str_to_time(pa['start_time']),
              str_to_time(pa['end_time']),
              percentage(pa['positive_percentage']),
              percentage(pa['negative_percentage']),
              percentage(pa['max_fault_seconds_percentage']['v']),
              percentage(pa['max_invalid_rollups_percentage']['v'])
            ], style: [nil, style_time, style_time, nil, nil, nil, nil]
        end
      end
    end
  end

  def str_to_time(str)
    Time.parse(str).in_time_zone(Time.zone.name)
  end

  def percentage(n)
    if n
      n * 100
    else
      n
    end
  end

end