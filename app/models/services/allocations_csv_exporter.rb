require 'csv'

module Services
  #
  # turns a list of allocations into CSV format
  #
  class AllocationsCsvExporter
    def self.export(allocations)
      attributes = %w(startTime endTime assetId dgId allocation)

      CSV.generate(headers: true) do |csv|
        csv << attributes
        allocations.each do |allocation|
          csv << [
            allocation['startTime'],
            allocation['endTime'],
            allocation['assetId'],
            allocation['dgId'],
            allocation['allocation'] ? 1 : 0
          ]
        end
      end
    end
  end
end
