class Services::NominateableVolumesExporter

  def initialize(nominateable_volumes:, start_date:, end_date:, market:, tsos:, supplier_bgs:, use_shorthand_payment_direction: false)
    @nominateable_volumes = nominateable_volumes
    @start_date = start_date
    @end_date = end_date
    @market = market
    @tsos = tsos
    @supplier_bgs = supplier_bgs

    @payment_directions = {
      an: use_shorthand_payment_direction ? 'AN' : 'ANBIETER_AN_NETZ',
      na: use_shorthand_payment_direction ? 'NA' : 'NETZ_AN_ANBIETER'
    }
  end

  def export_to_string
    build_excel.to_stream.read
  end

  def filename
    "#{@market.name} Nomination #{@start_date.strftime('%d.%m.%Y')}.xlsx"
  end

  # XLSX
  #
  private def build_excel
    Axlsx::Package.new.tap do |p|
      wb = p.workbook

      style_header     = wb.styles.add_style alignment: { horizontal: :center }, bg_color: "ff0000", border: 1, fg_color: "ffffff", b: true, sz: 25
      style_meata_th   = wb.styles.add_style alignment: { horizontal: :center }, bg_color: "8faadc", border: 1
      style_meata_date = wb.styles.add_style alignment: { horizontal: :center }, bg_color: "d9d9d9", border: 1, :format_code => 'dd/mm/yyyy'
      style_meata_td   = wb.styles.add_style alignment: { horizontal: :center }, bg_color: "d9d9d9", border: 1

      style_th        = wb.styles.add_style alignment: { horizontal: :center }, bg_color: "8faadc", border: 1
      style_td_index  = wb.styles.add_style alignment: { horizontal: :center }, bg_color: "8faadc", border: 1
      style_td_bgs    = wb.styles.add_style alignment: { horizontal: :center }, bg_color: "d9d9d9", border: 1
      style_td_values = wb.styles.add_style alignment: { horizontal: :center }, bg_color: "ffff99", border: 1

      wb.add_worksheet(name: 'Sheet') do |sheet|
        sheet.add_row ["VPP_#{@market.name.upcase}_Nomination"], style: [style_header], height: 30
        sheet.add_row ["Anbieterkürzel", "", "EDG"], style:[style_meata_th, style_meata_th, style_meata_td]
        sheet.add_row ["Zeitraum von", "", @start_date], types: [:string, :string, :date], style:[style_meata_th, style_meata_th, style_meata_date]
        sheet.add_row ["Zeitraum bis", "", @end_date], types: [:string, :string, :date], style:[style_meata_th, style_meata_th, style_meata_date]
        sheet.add_row ["Produktart", "", @market.name.upcase], style:[style_meata_th, style_meata_th, style_meata_td]

        sheet.add_row

        sheet.add_row ["#", "RZ_Erbringung", "Anbieter_BK", "Produktname", "Leistungswert [MW]", "Leistungspreis [¤/MW]", "Arbeitspreis [¤/MWh]", "Zahlungsrichtung", "Anlage ID", "Anlage Name"],
          style: ([style_th] * 10)

        @nominateable_volumes
          .select { |nv| nv['volume'] != 0 }
          .sort_by { |nv| [nv['tso_id'], nv['energy_direction'], nv['start_time']]}
          .each_with_index do |nv, index|

            tso = @tsos.find { |t| t.id == nv['tso_id'] }

            start_hour = Time.parse(nv['start_time']).in_time_zone(Time.zone.name).hour
            end_hour = Time.parse(nv['end_time']).in_time_zone(Time.zone.name).hour
            product_interval = [
              nv['energy_direction'][0..2],
              "%02d" % start_hour,
              "%02d" % (end_hour == 0 ? 24 : end_hour)
            ].join('_')

            if nv['payment_direction_override'].blank?
              payment_direction = nv['energy_price'] >= 0 ? @payment_directions[:na] : @payment_directions[:an]
            else
              payment_direction = nv['payment_direction_override'] == 'NETZ_AN_ANBIETER' ? @payment_directions[:na] : @payment_directions[:an]
            end

            sheet.add_row [
              index + 1, 
              tso.tso_id,
              @supplier_bgs[[nv['tso_id'], nv['market_id']]],
              product_interval,
              nv['volume_mw'],
              nv['capacity_price'],
              nv['energy_price'],
              payment_direction,
              nv['asset_id'],
              prevent_formula_injection(nv['asset_name'])
            ],
            types: [:integer, :string, :string, :string, :integer, :float, :float, :string, :integer, :string],
            style: [style_td_index, style_td_bgs, style_td_bgs, style_td_values, style_td_values, style_td_values, style_td_values, style_td_values, style_td_values, style_td_values]
          end
        sheet.column_widths(8, 18, 18, 12, 19, 19, 19, 19, 19, 19)
        sheet.merge_cells "A1:J1"
        sheet.merge_cells "A2:B2"
        sheet.merge_cells "A3:B3"
        sheet.merge_cells "A4:B4"
        sheet.merge_cells "A5:B5"
      end
    end
  end

  def prevent_formula_injection(value)
    if value && value.kind_of?(String) && value.start_with?("=")
      "'#{value}"
    else
      value
    end
  end

end