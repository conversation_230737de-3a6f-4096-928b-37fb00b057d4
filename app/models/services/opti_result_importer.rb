class Services::OptiResultImporter

  DATE_FORMAT = "%Y-%m-%dT%H:%M:%S%z"

  def parse_buffer(content)
    wb = RubyXL::Parser.parse_buffer(content)
    parse_workbook(wb)
  end

  def parse_workbook(workbook)
    if workbook.worksheets.empty?
      errmsg = I18n.t('bid.err_no_worksheet')
      Rails.logger.error errmsg
      return {error: errmsg}
    end

    parse_worksheet(workbook.worksheets.first)
  end

  def parse_worksheet(ws)
    ret = {}

    return error_result I18n.t('bid.err_col_missing', name: 'datetime') unless ws[1].present? && to_downcase_trim(get_cell_str(ws[0][0])) == 'datetime'
    return error_result I18n.t('bid.err_col_missing', name: 'soc') unless ws[1].present? && to_downcase_trim(get_cell_str(ws[0][1])) == 'soc'
    return error_result I18n.t('bid.err_col_missing', name: 'activation_dc_pos') unless ws[1].present? && to_downcase_trim(get_cell_str(ws[0][2])) == 'activation_dc_pos'
    return error_result I18n.t('bid.err_col_missing', name: 'activation_dc_neg') unless ws[1].present? && to_downcase_trim(get_cell_str(ws[0][3])) == 'activation_dc_neg'
    
    return error_result I18n.t('bid.err_col_missing', name: 'activation_dm_pos') unless ws[1].present? && to_downcase_trim(get_cell_str(ws[0][4])) == 'activation_dm_pos'
    return error_result I18n.t('bid.err_col_missing', name: 'activation_dm_neg') unless ws[1].present? && to_downcase_trim(get_cell_str(ws[0][5])) == 'activation_dm_neg'
    return error_result I18n.t('bid.err_col_missing', name: 'activation_dr_pos') unless ws[1].present? && to_downcase_trim(get_cell_str(ws[0][6])) == 'activation_dr_pos'
    return error_result I18n.t('bid.err_col_missing', name: 'activation_dr_neg') unless ws[1].present? && to_downcase_trim(get_cell_str(ws[0][7])) == 'activation_dr_neg'
    
    return error_result I18n.t('bid.err_col_missing', name: 'available_char_power') unless ws[1].present? && to_downcase_trim(get_cell_str(ws[0][8])) == 'available_char_power'
    return error_result I18n.t('bid.err_col_missing', name: 'available_dischar_power') unless ws[1].present? && to_downcase_trim(get_cell_str(ws[0][9])) == 'available_dischar_power'

    ret[:lines] = []
    ws.each do |row|
      if row.present?
        if row.index_in_collection >= 1
          opti_line_item = parse_opti_line_row(row)
          if opti_line_item[:error]
            return error_result opti_line_item[:error]
          end
          ret[:lines] << opti_line_item
        end
      end
    end

    ret
  end

  def parse_opti_line_row(row)
    {
      datetime: get_cell_datetime(row[0]),
      soc: get_cell_float(row[1]),
      activation_dc_pos: get_cell_float(row[2]),
      activation_dc_neg: get_cell_float(row[3]),
      activation_dm_pos: get_cell_float(row[4]),
      activation_dm_neg: get_cell_float(row[5]),
      activation_dr_pos: get_cell_float(row[6]),
      activation_dr_neg: get_cell_float(row[7]),
      available_char_power: get_cell_float(row[8]),
      available_dischar_power: get_cell_float(row[9])
    }
  end

  def error_result(errmsg)
    Rails.logger.error errmsg
    {error: errmsg}
  end

  def get_cell_float(cell)
    return nil unless cell.present?
    if cell && (Float(cell.value.to_s) != nil rescue false)
      cell.value.to_f
    else
      nil
    end
  end

  def get_cell_datetime(cell)
    return nil unless cell.present?
    cell_value = nil
    if cell && cell.is_date?
      cell_value = cell.value
    else
      cell_value = parse_time(cell.value.to_s)
    end
    cell_value
  end

  def parse_time(str_time)
    begin
      Time.strptime(str_time, "%Y-%m-%dT%H:%M:%S%z")
    rescue
      nil
    end
  end

  def get_cell_str(cell)
    return nil unless cell.present?
    (cell.value || (cell.datatype == RubyXL::DataType::INLINE_STRING && cell.try(:is))).try(:to_s)
  end

  def to_downcase_trim(value)
    if value.present?
      value.downcase.strip
    else
      value
    end
  end

end