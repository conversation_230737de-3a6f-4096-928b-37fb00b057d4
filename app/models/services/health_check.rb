class Services::HealthCheck

  class << self

    #
    # cache is of the form { key => OpenStruct(...) }
    # where the key is the internal name of the health check
    #
    CACHE = Concurrent::Hash.new
    EXPECTED_CHECKS = {
      db_service: "PostgreSQL (vpp_db)".freeze,
      vpp_rollups: "VPP Rollups".freeze,
      vpp_trading: "VPP Trading".freeze,
    }.freeze


    @initialized = false

    def initialize_periodic_checks
      if @initialized
        Rails.logger.info("Periodic health checks already initialized")
        return
      else
        Rails.logger.info("Scheduling periodic health checks")
      end

      tasks = EXPECTED_CHECKS.keys.collect do |check_name|
        task = Concurrent::TimerTask.new(
          execution_interval: 60,
          timeout_interval: 30,
          run_now: true,
        ) {
          new_check = self.send("health_#{check_name}".to_sym)
          update_cache(CACHE, new_check, check_name)
        }

        # monitor timeouts or other exception running the task
        # if any such exceptions occur, create a health_check result based in it, and insert it in the cache
        task.add_observer do |time, result, ex|
          if result
            # all good, nothing to do
          elsif
            Rails.logger.warn("Observed exception in health check #{check_name} execution: #{ex.message}")
            new_check = health_check(check_name){ raise ex }
            update_cache(CACHE, new_check, check_name)
          end
        end

        task.execute
        task
     end

      # make sure the task is shutdown when the ruby process is terminated
      at_exit do
        Rails.logger.info("Shutting down periodic health checks")
        tasks.each(&:shutdown)
      end
    end

    def checks_with_placeholders
      EXPECTED_CHECKS.collect do |k, name|
        CACHE[k] || health_check_placeholder(name)
      end
    end

    private def update_cache(cache, new_check, check_name)
      prev_check = CACHE[check_name]
      if health_check_changed(prev_check, new_check)
        Rails.logger.warn("Health check #{check_name} changed status from #{prev_check.try(:status)} to #{new_check.try(:status)}")
        CACHE[check_name] = new_check
      end
    end

    #
    # Health checks
    # return OpenStruct(name:, status:, error_message:, error_backtrace:)
    # any `status` different than :ok indicates an issue
    #
    private def health_db_service
      Rails.logger.info("Perform health_db_service")
      name = EXPECTED_CHECKS[:db_service]
      health_check(name) do
        ActiveRecord::Base.connection.verify!
        Asset.count
      rescue Exception => e
        raise "Could not connect to postgres #{e.message}"
      end
    end

    private def health_vpp_rollups
      Rails.logger.info("Perform health_vpp_rollups")
      name = EXPECTED_CHECKS[:vpp_rollups]
      health_check(name) { Services::RollupsGeneration.ping }
    end

    private def health_vpp_trading
      Rails.logger.info("Perform health_vpp_trading")
      name = EXPECTED_CHECKS[:vpp_trading]
      health_check(name) { Services::VppTradingService.ping }
    end

    private def health_check_placeholder(name)
      OpenStruct.new(
        name: name,
        created_at: Time.now.utc,
        status: :pending,
        error_message: "Check not executed yet",
        error_backtrace: "")
    end

    private def health_check_changed(prev_check, new_check)
      return true if prev_check.blank?
      prev_check.status != new_check.status || prev_check.error_message != new_check.error_message
    end

    private def health_check(name, &block)
      common_fields = {
        name: name,
        created_at: Time.now.utc
      }
      begin
        yield
        OpenStruct.new(common_fields.merge(
          status: :ok)
        )
      rescue => e
        Rails.logger.error("health check #{name} failed because #{e.message}")
        OpenStruct.new(common_fields.merge(
          status: :error,
          error_message: e.message,
          error_backtrace: e.backtrace)
        )
      end
    end
  end
end
