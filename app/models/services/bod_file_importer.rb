class Services::BodFileImporter

  DATE_FORMAT = "%Y-%m-%dT%H:%M:%S%z"

  def parse_buffer(content)
    wb = RubyXL::Parser.parse_buffer(content)
    parse_workbook(wb)
  end

  def parse_workbook(workbook)
    if workbook.worksheets.empty?
      errmsg = I18n.t('bid.err_no_worksheet')
      Rails.logger.error errmsg
      return {error: errmsg}
    end

    parse_worksheet(workbook.worksheets.first)
  end

  def parse_worksheet(ws)
    ret = {
      data_source: "UI"
    }

    return error_result I18n.t('bid.err_col_missing', name: 'Assetid') unless ws[1].present? && get_cell_str(ws[0][0]) == 'Assetid'
    return error_result I18n.t('bid.err_col_missing', name: 'Datetime') unless ws[1].present? && get_cell_str(ws[0][1]) == 'Datetime'
    return error_result I18n.t('bid.err_col_missing', name: 'Pair') unless ws[1].present? && get_cell_str(ws[0][2]) == 'Pair'
    return error_result I18n.t('bid.err_col_missing', name: 'Offer_Price') unless ws[1].present? && get_cell_str(ws[0][3]) == 'Offer_Price'
    return error_result I18n.t('bid.err_col_missing', name: 'Bid_Price') unless ws[1].present? && get_cell_str(ws[0][4]) == 'Bid_Price'
    return error_result I18n.t('bid.err_col_missing', name: 'Volume') unless ws[1].present? && get_cell_str(ws[0][5]) == 'Volume'

    ret[:bid_offer_data] = []
    ws.each do |row|
      if row.present?
        if row.index_in_collection >= 1
          # skip empty rows
          if (0..5).collect{|i| row[i]}.compact.collect{|cell| cell.value}.compact.present?
            bod_item = parse_bod_row(row)
            if bod_item[:error]
              return error_result bod_item[:error]
            end
            ret[:bid_offer_data] << bod_item
          end
        end
      end
    end

    ret
  end

  def parse_bod_row(row)
    bod = {
      asset_id: get_cell_str(row[0]),
      start_time: get_cell_datetime(row[1]),
      pair: get_cell_integer(row[2]),
      offer_price_gbp_per_mw: get_cell_float(row[3]),
      bid_price_gbp_per_mw: get_cell_float(row[4]),
      power_mw: get_cell_integer(row[5]),
    }
    errors = []
    errors.push(I18n.t('bod.invalid_asset_id', index: row.index_in_collection + 1)) unless valid_asset_id?(bod[:asset_id])
    if valid_datetime?(bod[:start_time])
      bod[:start_time] = bod[:start_time].getutc.strftime("%Y-%m-%dT%H:%M:%S.%LZ") unless bod[:start_time].kind_of?(String)
    else
      errors.push(I18n.t('bod.invalid_datetime', index: row.index_in_collection + 1))
    end
    errors.push(I18n.t('bod.invalid_pair', index: row.index_in_collection + 1)) unless bod[:pair].present?
    if !bod[:pair].present? || !bod[:power_mw].present? || (bod[:pair] > 0 && bod[:power_mw] < 0) || (bod[:pair] < 0 && bod[:power_mw] > 0)
      errors.push(I18n.t('bod.invalid_pair_or_power_mw', index: row.index_in_collection + 1))
    end
    errors.push(I18n.t('bod.invalid_offer_price', index: row.index_in_collection + 1)) unless valid_price?(bod[:offer_price_gbp_per_mw])
    errors.push(I18n.t('bod.invalid_bid_price', index: row.index_in_collection + 1)) unless valid_price?(bod[:bid_price_gbp_per_mw])
    errors.push(I18n.t('bod.invalid_power_mw', index: row.index_in_collection + 1)) unless bod[:power_mw].present?
    unless errors.empty?
      bod[:error] = errors.join(",")
    end
    bod
  end

  def error_result(errmsg)
    Rails.logger.error errmsg
    {error: errmsg}
  end

  def get_cell_str(cell)
    return nil unless cell.present?
    (cell.value || (cell.datatype == RubyXL::DataType::INLINE_STRING && cell.try(:is))).try(:to_s)
  end

  def get_cell_datetime(cell)
    return nil unless cell.present?
    cell_value = nil
    if cell && cell.is_date?
      cell_value = cell.value
    else
      cell_value = parse_time(cell.value.to_s)
    end
    cell_value
  end

  def parse_time(str_time)
    begin
      ActiveSupport::TimeZone['UTC'].parse(str_time).getutc
    rescue
      nil
    end
  end

  def get_cell_integer(cell)
    return nil unless cell.present?
    if cell && (Float(cell.value.to_s) != nil rescue false) && cell.value.to_i == cell.value.to_f
      cell.value.to_i
    else
      nil
    end
  end

  def get_cell_float(cell)
    return nil unless cell.present?
    if cell && (Float(cell.value.to_s) != nil rescue false)
      cell.value.to_f
    else
      nil
    end
  end

  def valid_asset_id?(ext_asset_id)
    ext_asset_id.present? &&
      (asset_id = AssetKeyValueStore.where(key: "shortName", value: ext_asset_id).first.try(:asset_id)) &&
      (AssetKeyValueStore.where('asset_id = ? and key = ? and value IS NOT NULL', asset_id, "balancingMechanismUnitConfig").size > 0)
  end

  def valid_datetime?(datetime)
    puts "### CHECK DATETIME VALIDITY: #{datetime}"
    datetime.present? && multiple_of_30_minutes?(datetime)
  end

  def multiple_of_30_minutes?(timestamp)
    if timestamp.kind_of?(String)
      time = Time.parse(timestamp)
    else
      time = timestamp
    end
    time.min % 30 == 0 && time.sec == 0
  end

  def valid_price?(price)
    price.present? && max_two_decimals?(price)
  end

  def max_two_decimals?(number)
    (number * 100).round == (number * 100).to_i
  end
end