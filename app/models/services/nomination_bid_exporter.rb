class Services::NominationBidExporter

  def initialize(nomination_bids:, auction_config:, start_date:, end_date:)
    @nomination_bids = nomination_bids
    @start_date = start_date
    @start_date = start_date
    @end_date = end_date
    @auction_config = auction_config
    build_bid_volumes
    build_supplier_bgs
  end

  def content_type
    'application/excel'
  end

  def filename
    "#{@auction_config.name} Nomination Bid #{@start_date.strftime('%d.%m.%Y')}.xlsx"
  end

  def content
    if @auction_config.has_internal_bidding_input_format?(["RegelleistungMRL", "RegelleistungSRL", "RegelleistungPRL"])
      content_for_regelleistung
    elsif @auction_config.has_internal_bidding_input_format?(["N2EX1h", "EPEX30min", "DC", "DS", "nlAfrr"])
      content_for_n2ex_epex_dc_afrr
    else
      Rails.logger.error("Bidding input not implemented")
      "Bidding input not implemented"
    end
  end

  def content_for_regelleistung
    market = @auction_config.dispatch_groups.first.market
    @regelleistung_content ||= begin
      Services::NominateableVolumesExporter.new(
        nominateable_volumes: @bid_volumes,
        start_date: @start_date,
        end_date: @end_date,
        market: market,
        tsos: Tso.all,
        supplier_bgs: @supplier_bgs,
        use_shorthand_payment_direction: true).export_to_string
    end
  end

  def content_for_n2ex_epex_dc_afrr
    market = @auction_config.bidding_method_market_name
    market = Market.find_by_name(market) if market.kind_of?(String)
    market = nil if market.kind_of?(Array)
    @n2ex_epex_dc_content ||= begin
      Services::N2exEpexDsAfrrAuctionResultExporter.new(
        bid_volumes: @bid_volumes,
        start_date: @start_date,
        end_date: @end_date,
        market: market,
        tsos: Tso.all).export_to_string
    end
  end

  private

  def build_bid_volumes
    @bid_volumes = @nomination_bids.collect do |b|
      {volume: b.flex_volume,
       volume_mw: b.flex_volume / 1000,
       tso_id: b.tso_id,
       market_id: b.product.market_id,
       market_name: b.product.market.name,
       capacity_price: b.capacity_price,
       energy_price: b.energy_price,
       energy_direction: b.product.energy_direction.upcase,
       payment_direction_override: b.payment_direction,
       start_time: b.start_time.to_s,
       end_time: b.end_time.to_s,
       asset_id: b.asset_id,
       asset_name: b.try(:asset).try(:name),
       asset_external_id: b.try(:asset).try(:short_name).try(:value)
      }.stringify_keys
    end
  end

  def build_supplier_bgs
    @supplier_bgs = @bid_volumes
                      .collect {|nv| [nv['tso_id'], nv['market_id']]}
                      .uniq
                      .inject({}) do |acc, ids|
      tso_id, market_id = ids
      dg = DispatchGroup.not_deleted.where(
        tso_id: tso_id,
        market_id: market_id,
        nomination_tool_enabled: true
      ).first
      if dg.present?
        bgs = dg.balancing_groups.overlap_date_interval(@start_date..@end_date)
        acc[[tso_id, market_id]] = bgs.first.try(:name)
      end
      acc
    end
  end

end