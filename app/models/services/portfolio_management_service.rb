require 'with_instrumentation'

module Services
  #
  # Utility for interacting with PMS
  #
  class PortfolioManagementService
    include HTTParty
    base_uri ENV['PORTFOLIO_MANAGEMENT_SERVICE_URL']
    debug_output $stdout

    UnableToFileUpload = Class.new(StandardError)
    UnableToGetAllocations = Class.new(StandardError)
    UnableToGetAllocatedFlex = Class.new(StandardError)
    UnableToPostAllocations = Class.new(StandardError)
    UnableToPostDUs = Class.new(StandardError)
    UnableToGetNominateableVolumes = Class.new(StandardError)
    UnableToGetBiddingWindows = Class.new(StandardError)
    UnableToDeleteBids = Class.new(StandardError)
    UnableToCreateV2GOptimizationJob = Class.new(StandardError)

    class << self
      #
      # Post a list of allocations to PMS
      #
      # Returns the id of the DbAssetDgAllocationSource returned by PMS
      #
      def post_allocations(asset_dg_allocations:, user_id:, skip_validations:)

        allocations_data = asset_dg_allocations.collect do |a|
          {
            start_time: a.start_time.strftime(Time::DATE_FORMATS[:pms]),
            end_time: a.end_time.strftime(Time::DATE_FORMATS[:pms]),
            asset_id: a.asset_id,
            dg_id: a.dispatch_group_id,
            allocation: a.is_allocated
          }
        end
        payload = {
          allocations: allocations_data,
          user_id: user_id }

        query = {
          skip_validations: skip_validations
        }.reject { |k, v| v.blank? }

        Rails.logger.info(
          "Posting allocations for user_id: #{user_id} #{ allocations_data }")

        result =
          with_instrumentation("event.pms", name: "post_allocations") do
            post(
              '/allocations/create',
              query: HashCaseConverter.to_camel_case(query),
              headers: { 'Content-Type' => 'application/json' },
              body: HashCaseConverter.to_camel_case(payload).to_json)
          end

        ok = result.ok?
        Rails.logger.info("Success: #{ok} #{result.code}")

        unless ok
          Rails.logger.info(
            "Posting allocations failed with status #{result.code}")
          raise UnableToPostAllocations, I18n.t('errors.messages.unable_to_post_allocations', reason: result.body)
        end

        JSON.parse(result.body)['id']

      rescue Exception => e
        raise UnableToPostAllocations, e.to_s
      end

      #
      # Post an allocation file to PMS
      # The file is sent base64 encoded
      #
      # Returns the id of the DbAssetDgAllocationSource returned by PMS
      #
      def allocations_file_upload(file_name:, file_contents:, user_id:, skip_validations:)
        payload = {
          file_name: file_name,
          file_contents: Base64.strict_encode64(file_contents),
          user_id: user_id }

        query = {
          skip_validations: skip_validations
        }.reject { |k, v| v.blank? }

        Rails.logger.info(
          "Posting allocations file upload file_name: #{file_name} user_id: #{user_id}")

        result =
          with_instrumentation("event.pms", name: "allocations_file_upload") do
            post(
              '/allocations/file-upload',
              query: HashCaseConverter.to_camel_case(query),
              headers: { 'Content-Type' => 'application/json' },
              body: HashCaseConverter.to_camel_case(payload).to_json)
          end

        ok = result.ok?
        Rails.logger.info("Success: #{ok} #{result.code}")

        unless ok
          Rails.logger.info(
            "Allocations file upload #{file_name} failed with status #{result.code}")
          raise UnableToFileUpload, I18n.t('errors.messages.unable_to_upload_allocations_file', reason: result.body)
        end

        JSON.parse(result.body)['id']

      rescue Exception => e
        raise UnableToFileUpload, e.to_s
      end

      #
      # loads the DG-level aggregated flex currently allocated
      # on the given interval, to the given DGs
      #
      def dg_aggregations(start_time:, end_time:, dg_ids:)
        Rails.logger.info(
          "Getting dg_aggregations for interval #{start_time}-#{end_time} "\
          "and DGs #{dg_ids}")

        result =
          with_instrumentation("event.pms", name: "dg_aggregations") do
            get(
              '/dg-aggregations',
              query: HashCaseConverter.to_camel_case(
                start_time: start_time.strftime(Time::DATE_FORMATS[:pms]),
                end_time: end_time.strftime(Time::DATE_FORMATS[:pms]),
                dg_ids: dg_ids
              ),
              headers: { 'Content-Type' => 'application/json' })
          end

        ok = result.ok?
        Rails.logger.info("Success: #{ok} #{result.code}")

        unless ok
          Rails.logger.info(
            "Getting dg_aggregations failed with status #{result.code}")
          raise UnableToGetAllocatedFlex, I18n.t('errors.messages.unable_to_get_allocated_flex', reason: result.body)
        end

        JSON.parse(result.body)

      rescue Exception => e
        raise UnableToGetAllocatedFlex, e.to_s
      end

      #
      # loads the Asset-level flex currently allocated
      # for the given asset, on the given interval and optionally the given DG
      #
      def asset_allocated_flex(start_time:, end_time:, asset_id:, dg_ids:)
        Rails.logger.info(
          "Getting asset_allocated_flex for interval #{start_time}-#{end_time} "\
          "and DG #{dg_ids} for asset #{asset_id}")

        query_params = {
          start_time: start_time.strftime(Time::DATE_FORMATS[:pms]),
          end_time: end_time.strftime(Time::DATE_FORMATS[:pms]),
          asset_ids: Array(asset_id),
        }
        query_params[:dg_ids] = Array(dg_ids) if dg_ids.present?

        result =
          with_instrumentation("event.pms", name: "asset_allocated_flex") do
            get(
              '/asset-allocated-flex',
              query: HashCaseConverter.to_camel_case(query_params),
              headers: { 'Content-Type' => 'application/json' })
          end

        ok = result.ok?
        Rails.logger.info("Success: #{ok} #{result.code} #{result.body}")

        unless ok
          Rails.logger.info(
            "Getting aggregated_allocated_flex failed with status #{result.code}")
          raise UnableToGetAllocatedFlex, I18n.t('errors.messages.unable_to_get_allocated_flex', reason: result.body)
        end

        JSON.parse(result.body)

      rescue Exception => e
        raise UnableToGetAllocatedFlex, e.to_s
      end

      #
      # loads the allocations for the given DGs,
      # and on the given interval and optionally the given DG
      #
      def allocations(start_time:, end_time:, dg_ids:)
        Rails.logger.info(
          "Getting allocations for interval #{start_time}-#{end_time} "\
          "and DGs #{dg_ids}")

        query_params = {
          start_time: start_time.strftime(Time::DATE_FORMATS[:pms]),
          end_time: end_time.strftime(Time::DATE_FORMATS[:pms])
        }
        query_params[:dg_ids] = Array(dg_ids) if dg_ids.present?

        result =
          with_instrumentation("event.pms", name: "allocations") do
            get(
              '/allocations',
              query: HashCaseConverter.to_camel_case(query_params),
              headers: { 'Content-Type' => 'application/json' })
          end

        ok = result.ok?
        Rails.logger.info("Success: #{ok} #{result.code} #{result.body}")

        unless ok
          Rails.logger.info(
            "Getting allocations failed with status #{result.code}")
          raise UnableToGetAllocatedFlex, I18n.t('errors.messages.unable_to_get_allocations', reason: result.body)
        end

        JSON.parse(result.body)

      rescue Exception => e
        raise UnableToGetAllocatedFlex
      end

      #
      # loads the allocations for the given DGs,
      # and on the given interval and optionally the given DG
      #
      def nominateable_volumes(intervals:, market_id:, delivery_date:, user_id:)
        Rails.logger.info(
          "Getting nominateable volumes for intervals #{intervals} "\
          "and market #{market_id}")

        query_params = {
          intervals: intervals.collect { |i| 
            i.first.strftime(Time::DATE_FORMATS[:pms]) + "/" + i.last.strftime(Time::DATE_FORMATS[:pms]) 
          },
          market_id: market_id,
          delivery_date: delivery_date.strftime(Time::DATE_FORMATS[:pms]),
          user_id: user_id
        }

        result =
          with_instrumentation("event.pms", name: "allocations") do
            get(
              '/nominations/nominateable-volumes',
              query: HashCaseConverter.to_camel_case(query_params),
              headers: { 'Content-Type' => 'application/json' })
          end

        ok = result.ok?
        Rails.logger.info("Success: #{ok} #{result.code} #{result.body}")

        unless ok
          Rails.logger.info(
            "Getting nominateable volumes failed with status #{result.code}")
          raise UnableToGetAllocatedFlex, I18n.t('errors.messages.unable_to_get_nominateable_volumes', reason: result.body)
        end

        HashCaseConverter.to_underscore(JSON.parse(result.body))

      rescue Exception => e
        raise UnableToGetNominateableVolumes
      end


      #
      # Post a list of DUs to PMS
      #
      # Returns the id of the DbDUSource returned by PMS
      #
      def post_dus(dus:, asset_ids:, user_id:, skip_validations:)

        dus_data = dus.collect do |du|
          {
            start_time: du.start_time.strftime(Time::DATE_FORMATS[:pms]),
            end_time: du.end_time.strftime(Time::DATE_FORMATS[:pms]),
            dg_id: du.dispatch_group_id,
            energy_direction: du.energy_direction,
            flex_volume_megawatt: du.flex_volume_mw,
            energy_price: du.energy_price,
            capacity_price: du.capacity_price,
            used_for_asset_price: du.used_for_asset_price,
            asset_ids: (asset_ids || du.asset_ids || []).select(&:present?).collect(&:to_i),
            id: du.id
          }
        end
        payload = {
          dus: dus_data,
          user_id: user_id }

        query = {
          skip_validations: skip_validations
        }.reject { |k, v| v.blank? }

        Rails.logger.info(
          "Posting DUs for user_id: #{user_id} #{ dus_data }")

        result =
          with_instrumentation("event.pms", name: "post_dus") do
            post(
              '/dus/create',
              query: HashCaseConverter.to_camel_case(query),
              headers: { 'Content-Type' => 'application/json' },
              body: HashCaseConverter.to_camel_case(payload).to_json)
          end

        ok = result.ok?
        Rails.logger.info("Success: #{ok} #{result.code}")

        unless ok
          Rails.logger.info(
            "Posting DUs failed with status #{result.code}")
          raise UnableToPostDUs, I18n.t('errors.messages.unable_to_post_dus', reason: result.body)
        end

        JSON.parse(result.body)['id']

      rescue Exception => e
        raise UnableToPostDUs, e.to_s
      end

      #
      # Post a DUs file to PMS
      # The file is sent base64 encoded
      #
      # Returns the id of the DbDUSource returned by PMS
      #
      def dus_file_upload(file_name:, file_contents:, user_id:, auction_config_id:, skip_validations:, contents_type:)
        payload = {
          file_name: file_name,
          file_contents: Base64.strict_encode64(file_contents),
          user_id: user_id,
          contents_type: contents_type }

        payload[:auction_rule_id] = auction_config_id.try(:to_i) if auction_config_id.present?

        query = {
          skip_validations: skip_validations
        }.reject { |k, v| v.blank? }

        Rails.logger.info(
          "Posting DU file upload file_name: #{file_name} user_id: #{user_id}")

        result =
          with_instrumentation("event.pms", name: "dus_file_upload") do
            post(
              '/dus/file-upload',
              query: HashCaseConverter.to_camel_case(query),
              headers: { 'Content-Type' => 'application/json' },
              body: HashCaseConverter.to_camel_case(payload).to_json)
          end

        ok = result.ok?
        Rails.logger.info("Success: #{ok} #{result.code}")

        unless ok
          Rails.logger.info(
            "DUs file upload #{file_name} failed with status #{result.code}")
          raise UnableToFileUpload, I18n.t('errors.messages.unable_to_upload_dus_file', reason: result.body)
        end

        JSON.parse(result.body)['id']

      rescue Exception => e
        raise UnableToFileUpload, e.to_s
      end


      #
      # Post a DUs file to PMS
      # The file is sent base64 encoded
      #
      # Returns the id of the DbDUSource returned by PMS
      #
      def delete_du(du_id:, user_id:)
        query = {
          user_id: user_id }

        Rails.logger.info(
          "Deleting DU: #{du_id} by user_id: #{user_id}")

        result =
          with_instrumentation("event.pms", name: "delete_du") do
            post(
              "/dus/delete/#{du_id}",
              query: HashCaseConverter.to_camel_case(query))
          end

        ok = result.ok?
        Rails.logger.info("Success: #{ok} #{result.code}")

        unless ok
          Rails.logger.info(
            "Deleting DUs #{du_id} failed with status #{result.code}")
          raise UnableToPostDUs, I18n.t('errors.messages.unable_to_post_dus', reason: result.body)
        end

        JSON.parse(result.body)['id']

      rescue Exception => e
        raise UnableToPostDUs, e.to_s
      end

      #
      # Retrieves the bidding closing times for EON as a
      # hash keyed by market name having time values: {MRL: Time object, SRL: Time object}
      #
      def get_eon_bid_closing_time
        result =
          with_instrumentation("event.pms", name: "get_eon_bid_closing_time") do
            get(
              '/nomination-bids/bidding-windows',
              headers: { 'Content-Type' => 'application/json' })
          end

        ok = result.ok?

        unless ok
          Rails.logger.error "Failed to get bidding windows with status #{result.code} and body #{result.body}"
          raise UnableToGetBiddingWindows, I18n.t('bid.err_pms_bidding_windows', reason: result.body)
        end

        resp = JSON.parse(result.body)

        {
          SRL: Time.parse(resp['auctionTimes']['SRL']['internalEndTime']),
          MRL: Time.parse(resp['auctionTimes']['MRL']['internalEndTime'])
        }
      end

      #
      # Post the bid create request to PMS
      #
      # Returns the id of the nomination_bid_source returned by the PMS
      #
      def post_bids(bid_request:)

        Rails.logger.info "Posting bids for user #{bid_request[:userId]}"

        result =
          with_instrumentation("event.pms", name: "post_bids") do
            post(
               '/nomination-bids/create',
               headers: { 'Content-Type' => 'application/json' },
               body: HashCaseConverter.to_camel_case(bid_request).to_json)
          end

        ok = result.ok?

        Rails.logger.info "Success: #{ok} #{result.code} body: #{result.body}"

        unless ok
          Rails.logger.info "Posting bids failed with status #{result.code} and body #{result.body}"
          raise UnableToFileUpload, I18n.t('bid.err_pms_upload')
        end
        json_result = JSON.parse(result.body)
        ret_val = [json_result['id']]
        if json_result['nominationBidsSourceId']
          ret_val << json_result['nominationBidsSourceId']
        end
        ret_val
      end

      def delete_bids(user_id:, market_id:, delivery_date:)
        Rails.logger.info "Deleting bids for market ##{market_id} scheduled for #{delivery_date} on behalf of user ##{user_id}"

        payload = {
          userId: user_id,
          customerId: nil,
          marketId: market_id,
          deliveryDate: delivery_date.strftime('%0Y-%0m-%0d')
        }

        result =
          with_instrumentation("event.pms", name: "delete_bids") do
            post(
              '/nomination-bids/delete',
              headers: { 'Content-Type' => 'application/json' },
              body: payload.to_json)
          end

        ok = result.ok?
        Rails.logger.info("Success: #{ok} #{result.code}")

        unless ok
          Rails.logger.error "Failed to delete bids with status #{result.code} and body #{result.body}"
          raise UnableToDeleteBids, I18n.t('bid.err_pms_delete', result.body)
        end

        JSON.parse(result.body)['id']
      end

      def create_v2g_optimization_job(user_id:)
        Rails.logger.info "Creating V2G Optimization Job"

        query = {
          user_id: user_id 
        }

        result =
          with_instrumentation("event.pms", name: "create_v2g_optimization_job") do
            post(
              '/v2g/create-optimization-job',
              headers: { 'Content-Type' => 'application/json' },
              query: HashCaseConverter.to_camel_case(query)
            )
          end

        ok = result.ok?
        Rails.logger.info("Success: #{ok} #{result.code}")

        unless ok
          Rails.logger.error "Failed to create V2G optimization job with status #{result.code} and body #{result.body}"
          raise UnableToCreateV2GOptimizationJob, I18n.t('bid.err_pms_v2g_optimization_job', reason: result.body)
        end

        result.body
      end

    end
  end
end
