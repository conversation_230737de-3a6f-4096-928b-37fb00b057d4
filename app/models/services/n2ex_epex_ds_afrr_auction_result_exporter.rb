class Services::N2exEpexDsAfrrAuctionResultExporter
  def initialize(bid_volumes:, start_date:, end_date:, market:, tsos:)
    @bid_volumes = bid_volumes
    @start_date = start_date
    @end_date = end_date
    @market = market
    @tsos = tsos
  end

  def export_to_string
    build_excel.to_stream.read
  end

  def filename
    "#{@market.try(:name) || 'Dynamic Services' } Nomination #{@start_date.strftime('%d.%m.%Y')}.xlsx"
  end

  # XLSX
  #
  private def build_excel
    Axlsx::Package.new.tap do |p|
      wb = p.workbook
      wb.add_worksheet(name: 'Sheet') do |sheet|
        header = ['external_id']
        header << 'market' if @market.nil? # Dynamic Services
        header.concat([
          'time_from',
          'time_to',
          'direction',
          'volume_kw',
          'price'
        ])
        sheet.add_row(header)
        if @market.nil? || @market.name == "DynamicContainment"
          price_field = 'capacity_price'
        else
          price_field = 'energy_price'
        end
        @bid_volumes.each_with_index do |bv, index|
          time_from = Time.parse(bv['start_time']).strftime("%Y-%m-%dT%H:%M:%S%z")
          time_to = Time.parse(bv['end_time']).strftime("%Y-%m-%dT%H:%M:%S%z")
          values = [bv['asset_external_id']]
          values << Market.dynamic_services_mapping_abbr[bv['market_name']] if @market.nil? # Dynamic Services
          values.concat([
            time_from,
            time_to,
            bv['energy_direction'],
            bv['volume'].try(:to_i),
            bv[price_field]
          ])
          sheet.add_row(values)
        end
      end
    end
  end

end