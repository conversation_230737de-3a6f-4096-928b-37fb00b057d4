class Services::DeclarationOfUnavailabilityUploader

  require 'net/http'
  require 'uri'

  def upload(asset_id, file_name, file_path)
    result = {
      success: true,
      errors: []
    }
    result[:errors] = upload_file_contents(asset_id, file_name, file_path)
    result
  end

  def upload_file_contents(asset_id, file_name, file_path)
    errors = []
    begin
      uri = URI.parse(ENV['VPP_TRADING_URL'] + '/dynamic-services/redeclaration')
      http = Net::HTTP.new(uri.host, uri.port)
      http.set_debug_output($stdout)
      request = Net::HTTP::Post.new(uri.path)
      form_data = [
        ['asset_id', asset_id],
        ['redeclaration', File.open(file_path), { filename: file_name }]
      ]
      request.set_form(form_data, 'multipart/form-data')
      response = http.request(request)
      if !response.is_a?(Net::HTTPSuccess)
        Rails.logger.info "Upload declaration o unavailability Error: #{response.code} #{response.message}"
        errors << response.message
      end
    rescue Errno::ENOENT
      errors << "File not found: #{file_path}"
    rescue StandardError => e
      Rails.logger.error "HTTP error for declaration of unavalability file #{file_path} #{e.message} #{e.backtrace}"
      errors << "Error sending file: #{e.message}"
    end
    errors
  end
end
