class Services::SchedulingBalancingGroupsReportExporter

  def initialize(report)
    @report = report
  end

  def export(output_file)
    build_excel.serialize(output_file)
  end

  def sheet_name
    'Sheet'
  end

  def build_excel
    p = Axlsx::Package.new.tap do |p|
      wb = p.workbook

      gray_bg_r = wb.styles.add_style alignment: {horizontal: :center}, bg_color: "C0C0C0", fg_color: "00", border: {style: :thin,  color: "00", edges: [:right]}
      gray_bg_rb = wb.styles.add_style alignment: {horizontal: :center}, bg_color: "C0C0C0", fg_color: "00", border: {style: :thin,  color: "00", edges: [:right, :bottom]}
      blue_bg_r = wb.styles.add_style alignment: {horizontal: :center}, bg_color: "CCFFFF", fg_color: "00", border: {style: :thin,  color: "00", edges: [:right]}
      blue_bg_rb = wb.styles.add_style alignment: {horizontal: :center}, bg_color: "CCFFFF", fg_color: "00", border: {style: :thin,  color: "00", edges: [:right, :bottom]}
      blue_bg_r_highlight = wb.styles.add_style alignment: {horizontal: :center}, bg_color: "CCFFFF", fg_color: "FF0000", border: {style: :thin,  color: "00", edges: [:right]}
      blue_bg_rb_highlight = wb.styles.add_style alignment: {horizontal: :center}, bg_color: "CCFFFF", fg_color: "FF0000", border: {style: :thin,  color: "00", edges: [:right, :bottom]}

      bold_c_rb = wb.styles.add_style b:true, alignment: {horizontal: :center}, bg_color: "FF", fg_color: "00", border: {style: :thin,  color: "00", edges: [:right, :bottom]}
      bold_r_rb = wb.styles.add_style b:true, alignment: {horizontal: :right}, bg_color: "FF", fg_color: "00", border: {style: :thin,  color: "00", edges: [:right, :bottom]}
      bold_c_trb = wb.styles.add_style b:true, alignment: {horizontal: :center}, bg_color: "FF", fg_color: "00", border: {style: :thin,  color: "00", edges: [:top, :right, :bottom]}
      bold_r_trb = wb.styles.add_style b:true, alignment: {horizontal: :right}, bg_color: "FF", fg_color: "00", border: {style: :thin,  color: "00", edges: [:top, :right, :bottom]}
      bold_c_trb_highlight = wb.styles.add_style b:true, alignment: {horizontal: :center}, bg_color: "FF", fg_color: "FF0000", border: {style: :thin,  color: "00", edges: [:top, :right, :bottom]}

      # wb.add_worksheet(name: "#{@report.meta_data.market}-#{@report.meta_data.tso}") do |sheet|
      wb.add_worksheet(name: sheet_name) do |sheet|

        sheet.sheet_view.zoom_scale = 85

        nr_columns = @report.balancing_group_pairs.size
        empty_cells = [''] * nr_columns

        add_header(wb, sheet, empty_cells)

        time_intervals = @report.balancing_group_pairs.first.records
        add_time_intervals(sheet, time_intervals, nr_columns, gray_bg_r, gray_bg_rb)
        sheet.add_row [ "Kontrollsumme", "[MWh]"].concat([''] * nr_columns), style: [bold_c_trb] * (2 + nr_columns)

        current_column = 2
        header_size = 17
        col_width = 20
        empty_columns_width = 8.69 # taken by measuring the size used by excel for empty cells

        #
        # control area - balancing groups
        #
        @report.balancing_group_pairs.each do |bg_pair|
          sheet.rows[0].cells[current_column].value  = @report.meta_data.date
          sheet.rows[1].cells[current_column].value  = bg_pair.tso_from
          sheet.rows[2].cells[current_column].value  = bg_pair.tso_to
          sheet.rows[3].cells[current_column].value  = bg_pair.balancing_group_from
          sheet.rows[4].cells[current_column].value  = bg_pair.balancing_group_to
          sheet.rows[5].cells[current_column].value  = bg_pair.try(:schedule_type)
          sheet.rows[6].cells[current_column].value  = prevent_formula_injection(bg_pair.try(:sender_bg)) # "Absender" row
          sheet.rows[8].cells[current_column].value  = prevent_formula_injection(bg_pair.try(:note))
          sheet.rows[10].cells[current_column].value = prevent_formula_injection(bg_pair.try(:comment))

          # records
          current_row = header_size
          count = 1
          bg_pair.records.each do |record|
            sheet.rows[current_row].cells[current_column].value = record.value
            sheet.rows[current_row].cells[current_column].type = :float
            style = (count == 4 ? (record.try(:highlight) ?  blue_bg_rb_highlight : blue_bg_rb) : (record.try(:highlight) ?  blue_bg_r_highlight : blue_bg_r))
            sheet.rows[current_row].cells[current_column].style = style
            current_row += 1
          end

          sheet.rows[header_size - 3].cells[current_column].value = bg_pair.total
          sheet.rows[header_size - 3].cells[current_column].type = :float
          sheet.rows[header_size - 3].cells[current_column].style = bold_c_trb_highlight if bg_pair.try(:highlight_total)

          sheet.rows[current_row].cells[current_column].value = bg_pair.total
          sheet.rows[current_row].cells[current_column].type = :float
          sheet.rows[current_row].cells[current_column].style = (count != 0 ? bold_c_trb : bold_c_rb)

          current_column += 1
        end

        # enforce column widths both to the relevant columns,
        # and to next columns, otherwise axlsx automatically sets their size
        col_widths = (([col_width] * (nr_columns + 2)) + ([empty_columns_width] * nr_columns))
        sheet.column_widths(*col_widths)
      end
    end
    p
  end

  #
  # Funky @!
  #
  def add_header(wb, sheet, empty_cells)

    y1 = "FFFF99"
    y1_bk     = wb.styles.add_style alignment: {horizontal: :center}, bg_color: y1, fg_color: "00", border: {style: :thin,  color: "00", edges: [:left, :right, :bottom]}
    y1_bk_b_r = wb.styles.add_style b: true, alignment: {horizontal: :right}, bg_color: y1, fg_color: "00", border: {style: :thin,  color: "00", edges: [:left, :right, :bottom]}

    y1_bk_b_l = wb.styles.add_style b: true, alignment: {horizontal: :left}, bg_color: y1, fg_color: "00"
    y1_rd_b_r = wb.styles.add_style b: true, alignment: {horizontal: :right}, bg_color: y1, fg_color: "DD0E05", border: {style: :thin,  color: "00", edges: [:right]}
    y1_rd_c   = wb.styles.add_style alignment: {horizontal: :center}, bg_color: y1, fg_color: "DD0E05", border: {style: :thin,  color: "00", edges: [:right]}
    y1_bl_b_r = wb.styles.add_style b: true, alignment: {horizontal: :right}, bg_color: y1, fg_color: "0001D4", border: {style: :thin,  color: "00", edges: [:right]}
    y1_bl_c   = wb.styles.add_style alignment: {horizontal: :center}, bg_color: y1, fg_color: "0001D4", border: {style: :thin,  color: "00", edges: [:right]}

    sheet.add_row [ '', "Datum"] + empty_cells, style: [y1_bk, y1_bk_b_r].concat([y1_bk] * empty_cells.size)
    sheet.add_row [ "Monatsfahrpläne", "aus Regelzone"] + empty_cells,  style: [y1_bk_b_l, y1_rd_b_r].concat([y1_rd_c] * empty_cells.size)
    sheet.add_row [ "",                "nach Regelzone"] + empty_cells, style: [y1_bk_b_l, y1_rd_b_r].concat([y1_rd_c] * empty_cells.size)

    sheet.add_row [ "",                "von Bilanzkreis"] + empty_cells,  style: [y1_bk_b_l, y1_bl_b_r].concat([y1_bl_c] * empty_cells.size)
    sheet.add_row [ "",                "nach Bilanzkreis"] + empty_cells, style: [y1_bk_b_l, y1_bl_b_r].concat([y1_bl_c] * empty_cells.size)

    sheet.add_row ['', 'Fahrplantyp'] + empty_cells, style: [y1_bk_b_l, y1_bl_b_r].concat([y1_bl_c] * empty_cells.size)
    sheet.add_row ['', "Absender" ] + empty_cells, style: [y1_bk_b_l, y1_bl_b_r].concat([y1_bl_c] * empty_cells.size)
    sheet.add_row ['', "Version"] + ['1'] * empty_cells.size, style: [y1_bk_b_l, y1_bl_b_r].concat([y1_bl_c] * empty_cells.size)

    y2 = "FCF302"
    y3 = "FFFF00"
    y2_bl_b_tr = wb.styles.add_style b: true, alignment: {horizontal: :right}, bg_color: y2, fg_color: "0001D4", border: {style: :thin,  color: "00", edges: [:top, :right]}
    y2_bl_b_r = wb.styles.add_style b: true, alignment: {horizontal: :right}, bg_color: y2, fg_color: "0001D4", border: {style: :thin,  color: "00", edges: [:right]}
    y2_bk_b_r = wb.styles.add_style b: true, alignment: {horizontal: :left}, bg_color: y2, fg_color: "00", border: {style: :thin,  color: "00", edges: [:right]}
    y3_bk_tr = wb.styles.add_style alignment: {horizontal: :center}, bg_color: y3, fg_color: "00", border: {style: :thin,  color: "00", edges: [:top, :right]}
    y3_bk_r = wb.styles.add_style alignment: {horizontal: :center}, bg_color: y3, fg_color: "00", border: {style: :thin,  color: "00", edges: [:right]}

    sheet.add_row [ "Kommentarbereich", ""] + empty_cells, style: [y2_bl_b_tr, y2_bl_b_tr].concat([y3_bk_tr] * empty_cells.size)

    sheet.add_row ['', ''] + empty_cells, style: [y2_bl_b_r, y2_bl_b_r].concat([y3_bk_r] * empty_cells.size)
    sheet.add_row ['', ''] + empty_cells, style: [y2_bl_b_r, y2_bl_b_r].concat([y3_bk_r] * empty_cells.size)
    sheet.add_row ['', ''] + empty_cells, style: [y2_bl_b_r, y2_bl_b_r].concat([y3_bk_r] * empty_cells.size)

    sheet.add_row ['', ''] + empty_cells, style: [y2_bk_b_r, y2_bk_b_r].concat([y3_bk_r] * empty_cells.size)
    sheet.add_row ['', ''] + empty_cells, style: [y2_bk_b_r, y2_bk_b_r].concat([y3_bk_r] * empty_cells.size)

    w_bk = wb.styles.add_style alignment: {horizontal: :center}, bg_color: "FF", fg_color: "00", border: {style: :thin,  color: "00", edges: [:top, :right]}
    w_bk_b_r = wb.styles.add_style b: true, alignment: {horizontal: :center}, bg_color: "FF", fg_color: "00", border: {style: :thin,  color: "00", edges: [:top, :right]}
    g_bk_b_r = wb.styles.add_style b: true, alignment: {horizontal: :right}, bg_color: "C0C0C0", fg_color: "00", border: {style: :thin,  color: "00", edges: [:top, :right]}
    g_bk_b_rb = wb.styles.add_style b: true, alignment: {horizontal: :center}, bg_color: "C0C0C0", fg_color: "00", border: {style: :thin,  color: "00", edges: [:right, :bottom]}

    sheet.add_row [ "Kontrollsumme", "[MWh]"] + empty_cells, style: [w_bk_b_r, w_bk_b_r].concat([w_bk] * empty_cells.size)
    sheet.add_row ['', ''] + empty_cells, style: [g_bk_b_r] * (2 + empty_cells.size)
    sheet.add_row [ "von", "bis"] + ["MW"] * empty_cells.size, style: [g_bk_b_rb] * (3 + empty_cells.size)
  end

  def add_time_intervals(sheet, records, empty_cells, gray_bg_r, gray_bg_rb)
    empty_cells = ['', ''] * empty_cells
    count = 1
    records.each do |record|
      row_style = (count % 4 == 0 ? gray_bg_rb : gray_bg_r)
      sheet.add_row [record.start_time, record.end_time] + empty_cells, style: [row_style, row_style]
      count += 1
    end
  end

  def prevent_formula_injection(value)
    if value && value.kind_of?(String) && value.start_with?("=")
      "'#{value}"
    else
      value
    end
  end

end