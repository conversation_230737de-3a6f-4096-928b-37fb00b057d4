class Services::NominationBidImporter

  DATE_FORMAT = '%d/%m/%Y'

  def parse_date (date_str)
    Date.strptime(date_str, DATE_FORMAT)
  end

  def parse_buffer(content)
    wb = RubyXL::Parser.parse_buffer(content)
    parse_workbook(wb)
  end

  def parse_workbook(workbook)
    if workbook.worksheets.empty?
      errmsg = I18n.t('bid.err_no_worksheet')
      Rails.logger.error errmsg
      return {error: errmsg}
    end

    parse_bid_worksheet(workbook.worksheets.first)
  end

  def parse_bid_worksheet(ws)
    ret = {}

    return error_result I18n.t('bid.err_col_missing', name: '<PERSON>bie<PERSON>kürz<PERSON>') unless ws[1].present? && get_cell_str(ws[1][0]) == 'Anbieterkürzel'
    ret[:providers_shortcut] = get_cell_str(ws[1][2])

    return error_result I18n.t('bid.err_col_missing', name: '<PERSON><PERSON>ra<PERSON> von') unless ws[2].present? && get_cell_str(ws[2][0]) == 'Zeitraum von'
    ret[:start_date] = get_cell_date(ws[2][2])
    return error_result I18n.t('bid.err_missing_date', name: 'Zeitraum von') unless ret[:start_date]

    return error_result I18n.t('bid.err_col_missing', name: 'Zeitraum bis') unless ws[3].present? && get_cell_str(ws[3][0]) == 'Zeitraum bis'
    ret[:end_date] = get_cell_date(ws[3][2])
    return error_result I18n.t('bid.err_missing_date', name: 'Zeitraum bis') unless ret[:end_date]

    return error_result I18n.t('bid.err_col_missing', name: 'Produktart') unless ws[4].present? && get_cell_str(ws[4][0]) == 'Produktart'
    ret[:market_name] = get_cell_str(ws[4][2])
    return error_result I18n.t('bid.err_missing', name: 'Produktart') unless ret[:market_name] && !ret[:market_name].empty?

    ret[:bid_items] = []
    ws.each do |row|
      if row.present?
        if row.index_in_collection >= 7
          bid_item = parse_bid_row(row)
          if bid_item[:error]
            return error_result bid_item[:error]
          end
          ret[:bid_items] << bid_item
        end
      end
    end

    ret
  end

  def parse_bid_row(row)
    # asset id must be integer if present
    if !empty_or_integer_cell_value?(row[8])
      { error: I18n.t('bid.err_asset_id') }
    else
      {
        tso_id: get_cell_str(row[1]),
        supplier_bg: get_cell_str(row[2]),
        product_interval: get_cell_str(row[3]),
        volume_mw: get_cell_float(row[4]),
        capacity_price: get_cell_float(row[5]),
        energy_price: get_cell_float(row[6]),
        payment_direction: get_cell_str(row[7]),
        asset_id: get_cell_integer(row[8]),
        asset: get_cell_str(row[9])
      }
    end
  end

  def error_result(errmsg)
    Rails.logger.error errmsg
    {error: errmsg}
  end

  def get_cell_date(cell)
    return nil unless cell.present?
    cell_value = nil
    if cell && cell.is_date?
      cell_value = cell.value
    else
      cell_value = cell.value.to_s
    end

    if cell_value && cell_value.class == String
      begin
        Time.zone.strptime(cell.value.to_s, DATE_FORMAT)
      rescue
        begin
          Time.zone.strptime(cell.value.to_s, '%d.%m.%Y')
        rescue
          nil
        end
      end
    else
      cell_value
    end
  end

  def get_cell_integer(cell)
    return nil unless cell.present?
    if cell && (Float(cell.value.to_s) != nil rescue false) && cell.value.to_i == cell.value.to_f
      cell.value.to_i
    else
      nil
    end
  end

  def get_cell_float(cell)
    return nil unless cell.present?
    if cell && (Float(cell.value.to_s) != nil rescue false)
      cell.value.to_f
    else
      nil
    end
  end

  def get_cell_str(cell)
    return nil unless cell.present?
    (cell.value || (cell.datatype == RubyXL::DataType::INLINE_STRING && cell.try(:is))).try(:to_s)
  end

  def empty_or_integer_cell_value?(cell)
    if cell && cell.value && cell.value.to_i.to_s != cell.value.to_s.strip
      false
    else
      true
    end
  end

end