class Services::AuctionResultsExporter

  def initialize(bids, start_date, end_date, locale)
    @auction_results = bids
    @start_date = start_date
    @end_date = end_date
    @locale
  end

  def export_to_string
    build_excel.to_stream.read
  end

  def filename
    "auction-results-between-#{@start_date}-and-#{@end_date}.xlsx"
  end

  # XLSX
  #
  private def build_excel
    Axlsx::Package.new.tap do |p|
      wb = p.workbook

      if @locale == 'de'
        header =  [
          "Ausschreibung",
          "ÜNB",
          "Datum",
          "Zeitscheibe (¤/MW)",
          "Leistungspreis (¤/MWh)",
          "Leistung (MW)",
          "Bezuschlagte Leistung (MW)",
          "Anlage",
          "Anlage Name",
          "Anlage External ID",
        ]
      else
        header = [
          "Tender",
          "TSO",
          "Date",
          "Product",
          "Capacity Price (¤/MW)",
          "Energy Price (¤/MWh)",
          "Capacity (MW)",
          "Capacity Accepted (MW)",
          "Asset ID",
          "Asset Name",
          "Asset External ID",
        ]
      end

      wb.add_worksheet(name: 'Sheet') do |sheet|
        sheet.add_row header
        @auction_results.each_with_index do |b, index|
          sheet.add_row [
            b.auction_config_name,
            b.tso_name,
            b.start_time.in_time_zone(Time.zone.name).strftime("%d.%m.%Y"),
            b.product_interval(true),
            b.capacity_price,
            b.energy_price,
            b.flex_volume ? (b.flex_volume / 1000).round(2) : nil,
            b.accepted_flex_volume ? (b.accepted_flex_volume / 1000).round(2) : nil,
            b.asset_id,
            b.asset.try(:name),
            b.asset.try(:short_name).try(:value)
          ]
        end
      end
    end
  end

end
