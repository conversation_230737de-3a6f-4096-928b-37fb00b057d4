module Services
  #
  # Utility for interacting with vpp-trading
  #
  class VppTradingService
    include HTTParty
    base_uri ENV['VPP_TRADING_URL']
    debug_output $stdout

    InvalidTenderConfig = Class.new(StandardError)
    UnableToFileUpload = Class.new(StandardError)
    UnableToPostDUs = Class.new(StandardError)
    UnableToGetBiddingWindows = Class.new(StandardError)
    UnableToDeleteBids = Class.new(StandardError)
    UnableToCreateV2GOptimizationJob = Class.new(StandardError)
    UnableToGetOpenTenders = Class.new(StandardError)
    UnableToGetMarketPositions = Class.new(StandardError)
    UnableToValidateBids = Class.new(StandardError)
    UnableToRejectBids = Class.new(StandardError)
    UnableToRunAssetOptimization = Class.new(StandardError)
    UnableToGetTendersDeliveryIntervals = Class.new(StandardError)

    class << self

      def ping
        result = get('/', timeout: 5)
        raise "Expected 200 but got #{result.code} #{result.body}" unless result.ok?
      end

      def validate_tender_config(tender_config_json)
        result = self.post(
          '/validate-auction-rules',
          headers: { 'Content-Type' => 'application/json' },
          body: tender_config_json)

        ok = result.ok?
        Rails.logger.info("Tender config validated: #{ok} #{result.code}")
        unless ok
          Rails.logger.info("Tender Config validation failed with status #{result.code} and body #{result.body}")
        end
        ok
      rescue Exception => e
        Rails.logger.info("Tender Config validation failed #{e.to_s} #{e.backtrace.join("\n")}")
        false
      end

      #
      # Post a list of DUs
      #
      # Returns the id of the DbDUSource
      #
      def post_dus(dus:, asset_ids:, user_id:, skip_validations:)

        dus_data = dus.collect do |du|
          {
            start_time: du.start_time.strftime(Time::DATE_FORMATS[:pms]),
            end_time: du.end_time.strftime(Time::DATE_FORMATS[:pms]),
            dg_id: du.dispatch_group_id,
            energy_direction: du.energy_direction,
            flex_volume_megawatt: du.flex_volume_mw,
            energy_price: du.energy_price,
            capacity_price: du.capacity_price,
            used_for_asset_price: du.used_for_asset_price,
            asset_ids: (asset_ids || du.asset_ids || []).select(&:present?).collect(&:to_i),
            id: du.id
          }
        end
        payload = {
          dus: dus_data,
          user_id: user_id }

        query = {
          skip_validations: skip_validations
        }.reject { |k, v| v.blank? }

        Rails.logger.info(
          "Posting DUs for user_id: #{user_id} #{ dus_data }")

        result = post(
          '/dus/create',
          query: HashCaseConverter.to_camel_case(query),
          headers: { 'Content-Type' => 'application/json' },
          body: HashCaseConverter.to_camel_case(payload).to_json)

        ok = result.ok?
        Rails.logger.info("Success: #{ok} #{result.code}")

        unless ok
          Rails.logger.info(
            "Posting DUs failed with status #{result.code}")
          raise UnableToPostDUs, I18n.t('errors.messages.unable_to_post_dus', reason: result.body)
        end

        JSON.parse(result.body)['id']

      rescue Exception => e
        raise UnableToPostDUs, e.to_s
      end

      #
      # Post a DUs file
      # The file is sent base64 encoded
      #
      # Returns the id of the DbDUSource
      #
      def dus_file_upload(file_name:, file_contents:, user_id:, auction_config_id:, skip_validations:, contents_type:)
        payload = {
          file_name: file_name,
          file_contents: Base64.strict_encode64(file_contents),
          user_id: user_id,
          contents_type: contents_type }

        payload[:auction_rule_id] = auction_config_id.try(:to_i) if auction_config_id.present?

        query = {
          skip_validations: skip_validations
        }.reject { |k, v| v.blank? }

        Rails.logger.info(
          "Posting DU file upload file_name: #{file_name} user_id: #{user_id}")

        result = post(
          '/dus/file-upload',
          query: HashCaseConverter.to_camel_case(query),
          headers: { 'Content-Type' => 'application/json' },
          body: HashCaseConverter.to_camel_case(payload).to_json)

        ok = result.ok?
        Rails.logger.info("Success: #{ok} #{result.code}")

        unless ok
          Rails.logger.info(
            "DUs file upload #{file_name} failed with status #{result.code}")
          raise UnableToFileUpload, I18n.t('errors.messages.unable_to_upload_dus_file', reason: result.body)
        end

        JSON.parse(result.body)['id']

      rescue Exception => e
        raise UnableToFileUpload, e.to_s
      end


      #
      # Delete a DU
      #
      # Returns the id of the DbDUSource
      #
      def delete_du(du_id:, user_id:)
        query = {
          user_id: user_id }

        Rails.logger.info(
          "Deleting DU: #{du_id} by user_id: #{user_id}")

        result = post(
          "/dus/delete/#{du_id}",
          query: HashCaseConverter.to_camel_case(query))

        ok = result.ok?
        Rails.logger.info("Success: #{ok} #{result.code}")

        unless ok
          Rails.logger.info(
            "Deleting DUs #{du_id} failed with status #{result.code}")
          raise UnableToPostDUs, I18n.t('errors.messages.unable_to_post_dus', reason: result.body)
        end

        JSON.parse(result.body)['id']

      rescue Exception => e
        raise UnableToPostDUs, e.to_s
      end

      #
      # FIXME - this needs to be adapted to the data returned by vpp-trading
      #
      def get_eon_bid_closing_time
        result = get(
          '/nomination-bids/bidding-windows',
          headers: { 'Content-Type' => 'application/json' })

        ok = result.ok?

        unless ok
          Rails.logger.error "Failed to get bidding windows with status #{result.code} and body #{result.body}"
          raise UnableToGetBiddingWindows, I18n.t('bid.err_pms_bidding_windows', reason: result.body)
        end

        resp = JSON.parse(result.body)

        {
          SRL: Time.parse(resp['auctionTimes']['SRL']['internalEndTime']),
          MRL: Time.parse(resp['auctionTimes']['MRL']['internalEndTime'])
        }
      end

      # FIXME - this is a temporary hack, until vpp-trading exposes a correct nomination-bids/bidding-windows endpoint
      def always_open_bidding
        true
      end

      #
      # Post the bid create request to PMS
      #
      # Returns the id of the nomination_bid_source returned by the PMS
      #
      def post_bids(bid_request:)

        Rails.logger.info "Posting bids for user #{bid_request[:userId]}"

        result = post(
                   '/nomination-bids/create',
                   headers: { 'Content-Type' => 'application/json' },
                   body: HashCaseConverter.to_camel_case(bid_request).to_json)

        ok = result.ok?

        Rails.logger.info "Success: #{ok} #{result.code} body: #{result.body}"

        unless ok
          Rails.logger.info "Posting bids failed with status #{result.code} and body #{result.body}"
          raise UnableToFileUpload, I18n.t('bid.err_pms_upload')
        end
        json_result = JSON.parse(result.body)
        ret_val = [json_result['id']]
        if json_result['nominationBidsSourceId']
          ret_val << json_result['nominationBidsSourceId']
        end
        ret_val
      end

      def delete_bids(user_id:, auction_config_id:, asset_ids:, start_date:, end_date:)
        Rails.logger.info "Deleting bids for tednder ##{auction_config_id}, assets #{asset_ids}, scheduled from #{start_date} to #{end_date} on behalf of user ##{user_id}"
        payload = {
          userId: user_id,
          customerId: nil,
          auctionRuleId: auction_config_id.try(:to_i),
          assetIds: asset_ids,
          deliveryDate: start_date.strftime('%0Y-%0m-%0d'),
          deliveryEndDate: end_date.strftime('%0Y-%0m-%0d')
        }

        result = post(
          '/nomination-bids/delete',
          headers: { 'Content-Type' => 'application/json' },
          body: payload.to_json)

        ok = result.ok?
        Rails.logger.info("Success: #{ok} #{result.code}")

        unless ok
          Rails.logger.error "Failed to delete bids with status #{result.code} and body #{result.body}"
          raise UnableToDeleteBids, I18n.t('bid.err_pms_delete', reason: result.body)
        end

        JSON.parse(result.body)['id']
      end

      def post_auction_results(auction_config_id, payload)

        Rails.logger.info "Posting auction results for Tender #{auction_config_id} and user #{payload[:user_id]}"
        query = {
          auction_rule: auction_config_id
        }
        result = post(
                   '/n2ExEpexDc/results',
                   query: HashCaseConverter.to_camel_case(query),
                   headers: { 'Content-Type' => 'application/json'},
                   body: payload.to_json,
                   timeout: 5*30)

        ok = result.success?
        Rails.logger.info "Success: #{ok} #{result.code} body: #{result.body}"
        json_body = JSON.parse(result.body)
        {
          success: ok,
          source_id: json_body['id'],
          error: json_body['error'],
          warning_msgs: json_body['warnings'] || []
        }
      rescue Exception => e
        if result.present?
          raise UnableToFileUpload, I18n.t('errors.messages.unable_to_upload_auction_results_file', reason: result.body)
        else
          raise UnableToFileUpload, e.to_s
        end
      end

      def post_afrr_bids(auction_config_id, payload)
        Rails.logger.info "Posting aFRR bids for Tender #{auction_config_id} and user #{payload[:user_id]}"
        query = {
          auction_rule: auction_config_id
        }
        result = post(
                   '/nl-afrr/bids',
                   query: HashCaseConverter.to_camel_case(query),
                   headers: { 'Content-Type' => 'application/json'},
                   body: payload.to_json)

        ok = result.success?
        Rails.logger.info "Success: #{ok} #{result.code} body: #{result.body}"
        unless ok
          Rails.logger.info(
            "aFRR bids file upload for Tender #{auction_config_id} failed with status #{result.code}")
          raise UnableToFileUpload, I18n.t('errors.messages.unable_to_upload_auction_results_file', reason: result.body)
        end
        json_result = JSON.parse(result.body)
        if json_result[:error.to_s]
          raise UnableToFileUpload, json_result[:error.to_s]
        else
          json_result['nominationBidSourceId'] || json_result['id']
        end
      rescue Exception => e
        raise UnableToFileUpload, e.to_s
      end

      def get_open_tenders
        result = get(
          '/optimizer/battery_uk/open_tenders',
          headers: { 'Content-Type' => 'application/json' })

        ok = result.ok?

        unless ok
          Rails.logger.error "Failed to get open tenders with status #{result.code} and body #{result.body}"
          raise UnableToGetOpenTenders, I18n.t('asset_optimization.err_get_open_tenders', reason: result.body)
        end

        resp = JSON.parse(result.body).deep_symbolize_keys
        if resp[:status] == "success"
          resp[:payload]
        else
          []
        end
      end

      def post_asset_optimizations(payload)
        Rails.logger.info "Posting asset optimization: #{payload.inspect}"

        result = post(
                   '/optimizer/battery_uk',
                   headers: { 'Content-Type' => 'application/json' },
                   body: payload.to_json)

        ok = result.success?

        result_body = result.body
        Rails.logger.info "Success: #{ok} #{result.code} body: #{result_body}"

        unless ok
          Rails.logger.info "Posting asset optimizations failed with status #{result.code} and body #{result_body}"
          raise UnableToRunAssetOptimization, I18n.t('asset_optimization.err_asset_optimization_job', reason: result_body)
        end
        json_result = JSON.parse(result_body)
        json_result
      end

      def get_market_positions(optimization_id)
        result = get(
          "/optimizer/battery_uk/market_positions_xlsx/#{optimization_id}",
          headers: { 'Content-Type' => 'application/json' })

        ok = result.ok?

        unless ok
          Rails.logger.error "Failed to get market positions #{result.code} and body #{result.body}"
          raise UnableToGetMarketPositions, I18n.t('asset_optimization.err_get_market_positions', reason: result.body)
        end

        resp = JSON.parse(result.body).symbolize_keys
        if resp[:status] == "success"
          Base64.decode64(resp[:payload])
        else
          ""
        end
      end

      def validate_bids(user_id:, portfolio_optimization_id:)
        Rails.logger.info "Validating bids for asset optimization ##{portfolio_optimization_id} on behalf of user ##{user_id}"

        payload = {
          userId: user_id,
          id: portfolio_optimization_id
        }

        result = post(
          '/optimizer/battery_uk/validate',
          headers: { 'Content-Type' => 'application/json' },
          body: payload.to_json)

        ok = result.success?
        Rails.logger.info("Success: #{ok} #{result.code}")
        puts "### VALIDATE BIDS RETURNED: #{result.body}"

        unless ok
          Rails.logger.error "Failed to validate bids with status #{result.code} and body #{result.body}"
          raise UnableToValidateBids, I18n.t('asset_optimization.err_validate_bids', reason: result.body)
        end

        JSON.parse(result.body)
      end

      def reject_bids(user_id:, portfolio_optimization_id:)
        Rails.logger.info "Rejecting bids for asset optimization ##{portfolio_optimization_id} on behalf of user ##{user_id}"

        payload = {
          userId: user_id,
          id: portfolio_optimization_id
        }

        result = post(
          '/optimizer/battery_uk/invalidate',
          headers: { 'Content-Type' => 'application/json' },
          body: payload.to_json)

        ok = result.success?
        Rails.logger.info("Success: #{ok} #{result.code}")
        puts "### REJECT BIDS RETURNED: #{result.body}"

        unless ok
          Rails.logger.error "Failed to reject bids with status #{result.code} and body #{result.body}"
          raise UnableToRejectBids, I18n.t('asset_optimization.err_reject_bids', reason: result.body)
        end

        JSON.parse(result.body)
      end

      def delivery_intervals(auction_config_ids, start_date, end_date)
        result = get(
          "/auction-rules/delivery-interval-between",
          query: HashCaseConverter.to_camel_case(
            start_date: start_date.strftime('%0Y-%0m-%0d'),
            end_date: end_date.strftime('%0Y-%0m-%0d'),
            auction_rule_ids: auction_config_ids
          ),
          headers: { 'Content-Type' => 'application/json' })

        ok = result.ok?

        unless ok
          err_msg = "Failed to get tenders #{auction_config_ids} delivery intervals between #{start_date} and #{end_date}: #{result.code} and body #{result.body}"
          Rails.logger.error err_msg
          raise UnableToGetTendersDeliveryIntervals, err_msg
        end
        # Example of response:
        # {
        #   "1" : {
        #     "start" : "2023-08-01T00:00+02:00[Europe/Berlin]",
        #     "end" : "2023-08-03T00:00+02:00[Europe/Berlin]"
        #   },
        #   "2" : {
        #     "start" : "2023-08-01T00:00+02:00[Europe/Berlin]",
        #     "end" : "2023-08-03T00:00+02:00[Europe/Berlin]"
        #   }
        # }
        JSON.parse(result.body)
      end


      def post_opti_result(payload)
        result = post(
          '/optimizer/optiresult',
          headers: { 'Content-Type' => 'application/json' },
          body: payload.to_json)

        ok = result.success?
        Rails.logger.info "Success: #{ok} #{result.code} body: #{result.body}"

        if ok
          nil
        else
          Rails.logger.info "Posting opti results failed with status #{result.code} and body #{result.body}"
          begin
            json_result = JSON.parse(result.body)
            if json_result['status'] == 'failure'
              json_result['errors']
            else
              raise UnableToFileUpload, I18n.t('errors.messages.opti_result_upload')
            end
          rescue
            raise UnableToFileUpload, I18n.t('errors.messages.opti_result_upload')
          end
        end
      end
    end
  end
end
