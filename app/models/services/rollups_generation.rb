require 'with_instrumentation'

class Services::RollupsGeneration
  include HTTParty
  base_uri ENV['VPP_ROLLUPS_SERVICE_URL']
  debug_output $stdout if Rails.env.development?

  UnableToGenerateRollups = Class.new(StandardError)

  class << self
    def ping
      result = get('/', timeout: 5)
      raise "Expected 200 but got #{result.code} #{result.body}" unless result.ok?
    end
  end

  def initialize(rollup_regeneration)
    @rollup_regeneration = rollup_regeneration
  end

  def generate_rollups
    payload = {
      rollup_regeneration: {
          id: @rollup_regeneration.id
      }
    }

    result =
      with_instrumentation("event.vpp_rollups", name: "generate_rollups") do
        self.class.post(
          '/rollups/generate',
          # query: HashCaseConverter.to_camel_case(query),
          headers: { 'Content-Type' => 'application/json' },
          body: HashCaseConverter.to_camel_case(payload).to_json)
      end

    ok = result.ok?
    Rails.logger.info("Generate Rollups Success: #{ok} #{result.code}")
    unless ok
      Rails.logger.info(
        "Generate Rollups failed with status #{result.code}")
      raise UnableToGenerateRollups, I18n.t('errors.messages.unable_to_post_dus', reason: result.body)
    end
    JSON.parse(result.body)

  rescue Exception => e
    raise UnableToGenerateRollups, e.to_s
  end

end
