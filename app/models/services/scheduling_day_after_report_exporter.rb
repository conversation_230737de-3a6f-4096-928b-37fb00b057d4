class Services::SchedulingDayAfterReportExporter

  def initialize(report)
    @report = report
  end

  def export(output_file)
    build_excel.serialize(output_file)
    # build_excel_xls.write(output_file)
  end

  def file_extension
    'xls'
  end

  # XLSX
  #
  def build_excel
    Axlsx::Package.new.tap do |p|
      wb = p.workbook

      time_format = wb.styles.add_style :format_code => 'dd.mm.yy hh:mm'
      mw_format   = wb.styles.add_style :format_code => '#########0.##'

      wb.add_worksheet(name: 'Sheet') do |sheet|
        sheet.sheet_view.zoom_scale = 85

        sheet.add_row ["Zeitschritt", "Saldo", "Preis"]

        @report.records.each do |record|
          sheet.add_row [record.start_time, record.value, record.price], types: [:time, :float], style: [time_format, nil]
        end
      end
    end
  end

  # XLS
  #
  # def build_excel_xls
  #   Spreadsheet::Workbook.new.tap do |book|

  #     time_format = book.add_format Spreadsheet::Format.new number_format: 'DD.MM.YY hh:mm'
  #     mw_format   = book.add_format Spreadsheet::Format.new number_format: '#,##0.00'

  #     sheet = book.create_worksheet
  #     sheet.column(0).default_format = time_format
  #     sheet.column(1).default_format = mw_format

  #     sheet.column(0).width = 20
  #     sheet.column(1).width = 20
  #     sheet.column(2).width = 20

  #     book.worksheet(0).insert_row(0, ["Zeitschritt", "Saldo", "Preis"])
  #     current_row_index = 0

  #     @report.records.each do |record|
  #       current_row_index += 1
  #       row = book.worksheet(0).row(current_row_index)
  #       row.replace([Time.parse(record.start_time), record.value, record.price])
  #     end
  #   end
  # end


end