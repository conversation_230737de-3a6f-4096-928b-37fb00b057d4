class AssetType < ActiveRecord::Base

  BIOMASS           = "Biomass"
  CHP               = "CHP"
  BACKUP_GENERATOR  = "Backup_Generator"
  LOAD              = "Load"
  POWER_TO_GAS      = "Power_To_Gas"
  POWER_TO_HEAT     = "Power_To_Heat"
  WIND_OFF_SHORE    = "Wind_Off_Shore"
  WIND_ON_SHORE     = "Wind_On_Shore"
  SOLAR             = "Solar"
  HYDRO             = "Hydro"
  V2G               = "V2G"
  E_CHARGER         = "e-charger"
  BATTERY           = "Battery"

  EGG_PLANT_TYPES = [BIOMASS, WIND_ON_SHORE, WIND_OFF_SHORE, HYDRO, <PERSON>OL<PERSON>]

  WIND_AND_SOLAR_TYPES = [WIND_ON_SHORE, WIND_OFF_SHORE, SOL<PERSON>]

end