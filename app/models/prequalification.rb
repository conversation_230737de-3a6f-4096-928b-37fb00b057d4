class Prequalification < ApplicationRecord

  belongs_to :asset
  has_many :prequalified_products

  has_one :next_in_chain, class_name: "Prequalification", foreign_key: :parent_id
  belongs_to :parent, class_name: "Prequalification", foreign_key: :parent_id, optional: true

  has_many :prequalified_products_with_product,
      -> { includes(:product).select(:id, :marketing_date, :prequalified_flex, :product_id, :prequalification_id) },
      :class_name => 'PrequalifiedProduct',
      :foreign_key => 'prequalification_id'

  has_many :products,
      -> { includes(:product).select(:id, :marketing_date, :prequalified_flex, :product_id, :prequalification_id) },
      :class_name => 'PrequalifiedProduct',
      :foreign_key => 'prequalification_id'

end
