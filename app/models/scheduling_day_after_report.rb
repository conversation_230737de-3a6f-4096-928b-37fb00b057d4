class SchedulingDayAfterReport < ActiveResource::Base

  self.site = ENV['VPP_REPORTING_URL']
  self.timeout = 5.minutes

  schema do
    attribute 'report', :string
  end

  def self.get(tso, market, energy_direction, date)
    Rails.logger.info "Fetch report /reports/scheduling_day_after/#{tso.id}/#{market.name}/#{energy_direction}/#{date}"
    self.find(:one, from: "/reports/scheduling_day_after/#{tso.id}/#{market.name}/#{energy_direction}/#{date}")
  end

end