#
# Represents DU entities
#
class DistributedUnit < ApplicationRecord
  include Authority::Abilities
  include IntervalFunctions
  include IntervalInput

  belongs_to :product

  belongs_to :dispatch_group

  has_and_belongs_to_many :assets

  validates :start_time, presence: true
  validates :end_time, presence: true
  validates :energy_direction, presence: true
  validates :dispatch_group_id, presence: true
  validates :flex_volume, numericality: true
  validates :energy_price, numericality: true
  validates :capacity_price, numericality: true, allow_nil: true

  attr_writer :energy_direction

  # make sure we never try to update the data in the DB
  def create; end

  def save; end

  def update; end

  def energy_direction
    @energy_direction || product.try(:energy_direction)
  end

  def flex_volume_mw
    self[:flex_volume] / 1_000 if self[:flex_volume]
  end

  def flex_volume_mw=(v)
    self[:flex_volume] = v.to_f * 1_000
  end
end
