class OptiResultLine < ApplicationRecord

  belongs_to :asset

  validates :asset, presence: true
  validates :datetime, presence: true
  validates :soc,
            :activation_dc_pos,
            :activation_dc_neg,
            :activation_dm_pos,
            :activation_dm_neg,
            :activation_dr_pos,
            :activation_dr_neg,
            :available_char_power,
            :available_dischar_power,
            numericality: true, allow_blank: false

  def to_payload_for_upload
    {
      'startDateTime': self.datetime.getutc.strftime("%Y-%m-%dT%H:%M:%SZ"),
      'soc': self.soc,
      'activations': [
        {
            "market": "DYNAMICCONTAINMENT",
            "negKW": self.activation_dc_neg,
            "posKW": self.activation_dc_pos,
        },
        {
            "market": "DYNAMICMODERATION",
            "negKW": self.activation_dm_neg,
            "posKW": self.activation_dm_pos,
        },
        {
            "market": "DYNAMICREGULATION",
            "negKW": self.activation_dr_neg,
            "posKW": self.activation_dr_pos,
        }
      ],
      'availableChgPower': self.available_char_power,
      'availableDischgPower': self.available_dischar_power
    }
  end
end