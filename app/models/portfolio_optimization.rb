class PortfolioOptimization < ApplicationRecord

  has_and_belongs_to_many :nomination_bid_sources,
    join_table: 'portfolio_optimization_nomination_bid_source'

  belongs_to :user_account, -> { select [:id, :name, :email] }, foreign_key: 'user_id'
  belongs_to :asset, -> { select [:id, :name] }, foreign_key: 'asset_id'

  STATUS_NEW = 'NEW'
  STATUS_RESULT_RECEIVED = 'RESULT_RECEIVED'
  STATUS_VALIDATED = 'VALIDATED'
  STATUS_INVALIDATED = 'INVALIDATED'

  TYPE_DAY_AHEAD = 'DAY_AHEAD'

  attr_accessor :auction_configs
  attr_accessor :nbs_auction_configs

  def self.optimizations(bidding_method, created_from, created_to)
    portfolio_pptimizations = PortfolioOptimization.
      left_outer_joins(:nomination_bid_sources).
      joins(:user_account).
      joins(:asset).
      includes(nomination_bid_sources: [:auction_config]).
      includes(:user_account).
      includes(:asset).
      where('portfolio_optimization.created >= :from AND portfolio_optimization.created <= :to', from: created_from, to: created_to).
      where(optimization_type: TYPE_DAY_AHEAD).
      order('portfolio_optimization.created desc')

    #set auction configs if there is no nomination_bid_source
    market_name_mapping = {
      "dc": Market::DYNAMIC_CONTAIMENT_MARKET_NAME,
      "dch": Market::DYNAMIC_CONTAIMENT_MARKET_NAME,
      "dcl": Market::DYNAMIC_CONTAIMENT_MARKET_NAME,
      "intraday": Market::INTRADAY_MARKET_NAME,
      "n2ex1h": Market::N2EX1H,
      "epex30min": Market::EPEX30MIN,
      "imbalance": Market::IMBALANCE,
    }
    portfolio_pptimizations.each do |po|
      po.nbs_auction_configs = po.nomination_bid_sources.collect{|nbs| nbs.auction_config}.compact.uniq
      nbs_auction_config_ids = po.nbs_auction_configs.collect{|nbac| nbac.id}
      po_horizon = po.request.dig("payload", "asset", "strategy_optimization", "horizon")
      horizon_start = Time.parse(po_horizon["start"])
      auctions_config_by_market_name = AuctionConfig.active_with_bidding_method(bidding_method, horizon_start)
      po_markets = po.request.dig("payload", "asset", "strategy_optimization", "markets")
      po_auction_configs = []
      po_markets.each do |po_market|
        po_market_name = market_name_mapping[po_market.to_sym]
        if po_market_name && auctions_config_by_market_name[po_market_name]
          po_auction_configs = po_auction_configs.concat(auctions_config_by_market_name[po_market_name])
        end
      end
      po.auction_configs = po_auction_configs.reject{|acfg| nbs_auction_config_ids.include?(acfg.id) }
    end

    portfolio_pptimizations
  end

end
