class DlmChargingSession < ApplicationRecord
  include Authority::Abilities
  self.table_name = 'dlm_customer_charging_session'

  validates :customer_id, presence: true
  validates  :average_charged_energy_watt_hour, :average_charging_duration_minutes,
            numericality: true, allow_blank: false

  # creates/updates entries inside an sql transaction
  # for customers for which we already have entries, those will be updated
  # for customers for which we don't have entries, new ones will be created
  #
  # sessions_batch is expected to be of the form:
  # [ {customer_id: ?, average_charged_energy_watt_hour: ?, average_charging_duration_minutes: ?}, ... ]
  #
  # returns the updated & created sessions
  def self.batch_insert(sessions_batch:, now:)
    return [] if sessions_batch.blank?

    sessions_batch_by_customer_id = sessions_batch.inject({}) do |acc, element|
      acc[element[:customer_id]] = element
      acc
    end

    # load existing sessions and update the relevant fields - no db save yet
    existing_sessions = where(customer_id: sessions_batch_by_customer_id.keys)
      .to_a
      .map do |existing_session|
        batch_session = sessions_batch_by_customer_id[existing_session.customer_id]
        existing_session.average_charging_duration_minutes = batch_session[:average_charging_duration_minutes]
        existing_session.average_charged_energy_watt_hour = batch_session[:average_charged_energy_watt_hour]
        existing_session.updated = now
        existing_session
      end

    existing_sessions_customer_ids = existing_sessions.map(&:customer_id)

    # instantiate new entities for new customers - no db save yet
    puts sessions_batch_by_customer_id.inspect
    new_sessions = sessions_batch_by_customer_id
      .select { |k, _| !existing_sessions_customer_ids.include? k }
      .values
      .map do |new_session|
        puts new_session.inspect
        new(
          created: now,
          updated: now,
          customer_id: new_session[:customer_id],
          average_charging_duration_minutes: new_session[:average_charging_duration_minutes],
          average_charged_energy_watt_hour: new_session[:average_charged_energy_watt_hour],
        )
      end

    # check validity, so that we have all error messages, because
    # during the below transaction, the first error will
    new_sessions.each(&:valid?)
    existing_sessions.each(&:valid?)

    # create / update inside transaction
    begin
      transaction do
        new_sessions.each(&:save!)
        existing_sessions.each(&:save!)
      end
    rescue ActiveRecord::StatementInvalid
      # ignore this exception
    rescue Exception => e
      Rails.logger.warn("Could not save dlm sessions batch. Failure: #{e.message} #{e.backtrace.join("\n")}")
    end
    new_sessions + existing_sessions
  end
end
