class Contract < ApplicationRecord

  TYPE_CLASSIC = 'classic'
  TYPE_MARKET_ACCESS = 'market_access'
  TYPE_MARKET_ACCESS_LIGHT = 'market_access_light'
  TYPE_SPOT_OPTIMIZATION = 'spot_optimization'
  TYPE_FLEX_OPTIMIZATION = 'flex_optimization'

  belongs_to :rollup_family
  belongs_to :customer
  has_and_belongs_to_many :markets, join_table: 'contract_market', order: :name
  has_and_belongs_to_many :assets, join_table: 'contract_asset', validate: false
  has_many :prices, dependent: :destroy

  def self.last_future_contract_end_date(customer_id)
    Contract.where(customer_id: customer_id).where("end_date > ?", Time.now).
        collect{|c| c.end_date}.max
  end

  def self.active(customer_id)
    Contract.where(customer_id: customer_id).where("start_date < :now AND end_date > :now", now: Time.now)
  end

  def asset_prices_for_market(asset, market)
    prices.where(asset_id: asset.id, market_id: market.id).order(:start_date)
  end

end
