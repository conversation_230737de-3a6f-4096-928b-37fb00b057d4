
*, :before, :after {
	outline: none;
	margin: 0;
	-webkit-font-smoothing: antialiased;
	@include box-sizing(border-box);
}

body {
	font: normal #{$base-font-size}/#{$base-line-height} $base-font-family;
	color: $text-color;
	background: $bg-color;
	text-shadow: 1px 1px 0 rgba(#fff, 1);
}

a {
	color: $text-color;
}

strong, b {font-weight: bold;}
em, i {font-style: italic;}

hr {
	height: 0;
	border: none;
	border-bottom: 1px dotted $border-color;
	clear: both;
	width: 100%;
	margin: 2em 0;
}
img {
	display: block;
	width: 100%;
}

#page {
	overflow: hidden;
	width: 100%;
}

.cursor-hand {cursor:pointer;cursor:hand}

.button-clear {
  border: none !important;
  background: none !important;
  box-shadow:none !important;
}

.pre{
  display: block;
  font-family: monospace;
  white-space: pre;
  margin: 1em 0;
  white-space: pre-wrap;
}