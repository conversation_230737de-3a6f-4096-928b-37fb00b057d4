#app-header {
  background: #fff;
  height: auto;
  z-index: 4;
  position: relative;

  .container-fluid {
    height: 82px;
    position: relative;
    z-index: 1;
  }

  button.hamburger{
    border: none;
    border-radius: 0;
    box-shadow: none;
  }

  .app-name {
    margin: 0;
    font-size: 23px;
    color: #8F9192;
    padding: 0px;
    line-height: 1;
    font-weight: 500;
  }
}

#content h2 {
  font-size: 16px;
}

#sub-header {
  height: 50px;
  position: relative;
  z-index: 2;
  background: #f2f2f2;
}

#tabbed-nav {
  position: relative;
  z-index: 3;
  background: #f2f2f2;
}

.tab-list .tab-item {
  a {
    background: none;
    color: #333;
    font-size: 16px;
    font-weight: 900;
  }

  &.current > a {
    background: #FFF;
    color: #333;
  }
}

button.action, button.action:not(:disabled):not([disabled]):hover, .button.action, .button.action:not(:disabled):not([disabled]):hover, [type='submit'].action, [type='submit'].action:not(:disabled):not([disabled]):hover, [type='button'].action, [type='button'].action:not(:disabled):not([disabled]):hover, [type='reset'].action, [type='reset'].action:not(:disabled):not([disabled]):hover, [type='date'].action, [type='date'].action:not(:disabled):not([disabled]):hover {
  font-size: 16px;
}

[type='text'], [type='text'], [type='email'], [type='password'], [type='number'], button, .button, [type='submit'], [type='button'], [type='reset'], [type='date'], [type='radio'], [type='checkbox'], select, textarea {
  font-weight: 300;
}

table[data-table-for] tbody > tr > td.actions .button {
  font-size: 120%;
}

.form-field [type='checkbox'] {
  margin-bottom: 0px;
}

#app-header .app-header--nav .app-name {
  margin: 0;
  font-size: 23px;
  color: #8F9192;
  padding: 0px 0px;
  line-height: 1;
}

label {
  font-size: 90%;
}
#breadcrumb > li {
  vertical-align: middle;
}

#breadcrumb > li a {
  font-size: 38px;
  font-weight: 500;
  font-size: 41px;
  font-weight: 500;
  line-height: 42px;
}

#app-nav{
  ul.list-nav{
    display: flex;
  }
}

#app-nav ul.list-nav {
  .menu-item .menu {
    a {
      white-space: nowrap;
      padding: 10px 15px;
      color: #333;
      font-size: 12px;
    }

    img {
      padding: 10px 15px;
    }
  }

}
.popup-menubar span.menu span.menu{
  border: 1px solid #eee;
}

.popup-menubar span.menu span.menu a{
  background: none;
}

.popup-menubar span.menu span.menu-item img{
  width: auto;
}

.submenu-block {
  top: 83px;
}
