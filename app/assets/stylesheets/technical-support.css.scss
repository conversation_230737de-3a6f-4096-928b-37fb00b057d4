.technical-support {
  z-index: 100;
  padding: 0;
  position: fixed;
  top: 85px;
  right: 0;
  //width:100px;
  background: $color-dark;
  opacity: 0.85;
  color: #fff !important;
  //@include border-radius(10px 0 0 10px);
  @include box-shadow(0 1px 12px rgba(0, 0, 0, 0.16));
  .expand-btn.open {
    span > i {
      @include transform(rotate(180deg));
    }
  }
  .expand-btn {
    line-height: normal !important;
    height: 48px;
    float: left;
    padding: 3px 12px;
    text-align: center;
    border: none;
    background: none;
    //@include border-radius(10px 0 0 10px);
    &:focus {
      outline: none !important;
      box-shadow:none !important;
    }
    span {
      font-size: 11px;
      padding-bottom: 4px;
      font-weight: bold;
      display: block;
      opacity: 0.75;
      color: #fff !important;
      i {
        @include transition(transform 0.25s ease);
        position: relative;
        left: 4px;
        font-size: 12px;
      }
    }
    > i {
      color: #fff !important;
      display: block;
      float: left;
      font-size: 18px;
      @include transform(scaleX(-1));
    }
  }
  .phone-no {
    display: none;
    float: left;
    height: 48px;
    background: #fff;
    color: $brand-color !important;
    padding: 0 20px 10px 10px;
    a {
      margin-top: 15px;
      display: block;
      font-size: 14px;
      color: $brand-color !important;
    }

  }
}