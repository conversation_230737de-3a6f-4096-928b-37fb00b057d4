// Main configuration file
@import "base";

@import "font-awesome/fontawesome";
@import "font-awesome/fa-regular";
@import "font-awesome/fa-solid";

//@import "hamburgers";

// Compass libraries
@import "compass/css3";
@import "compass/reset";

// Functions
//@import "functions";

// Mixins
@import "mixins";

// Placeholders
@import "placeholders/grid";

// Generic style
@import "generic";

// UI
@import "ui/misc";
@import "ui/forms";
@import "ui/tables";
//@import "ui/icons";
@import "ui/notifications";
@import "ui/pagination";
@import "ui/tooltip";

// Layout
@import "header";
@import "vpp_authentication/popup-menubar";
@import "content";

@import "technical-support";

// Third Party
@import "select2";
@import "jquery-ui";
@import "daterangepicker";
@import "jquery-tablesorter";
@import "daterangepicker-overrides";
@import "daterangepicker-fa-hacks";
@import "autocomplete/jquery.autocomplete";
@import "autocomplete/jquery.autocomplete";
@import "./../../angular/src/app/styles/environment";


// To be moved
body {
  overflow: auto;
}
table[data-table-for] tbody > tr > td.actions .button-group {
  text-align: left;
}
table[data-table-for] {
  th {
    vertical-align: top;
  }
}
table[data-table-for].schedule {
  width: 100%;
}

table[data-table-for] tbody > tr.schedule > td {
  height: 2em;
}
.user-menu .switch_user label {
  display: none;
}

#header #breadcrumb, #sub-header-form {
  //@extend %grid-column-6;
}

#sub-header-form {
  select, input {
    margin-left: 4px;
  }
}

#filter-form {
  select, input {
    margin-right: 4px;
  }
}
select::-ms-expand {
  display: none;
}

#allocations-dg-filter {
  select#dg-ids {
    height: 80px;
  }
}

// select 2 overrides
.select2-container .select2-selection--single {
  height: 2.5em;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 2.5em;
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 2.5em;
}

.code {
  font-family: monospace;
  word-break: break-word;
  overflow-wrap: break-word;
  word-wrap: break-word;
  white-space: pre-wrap;
}

ul.menu {
  display: block;
  li {
    display: block;
    clear: both;
    padding: 5px 0;

    a {
      max-width: 250px;
      line-height: 32px;
      width: 100%;
    }
  }
}
