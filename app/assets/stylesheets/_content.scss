#content {

	margin-top: 2em;
	padding-bottom: 5em;

	@extend %grid-row;

	h2 {
		font-weight: bold;
		color: $brand-color;
		margin: 2em 0;
	}

	& > .notifications {
		margin-bottom: 2em;
	}

	& > .navigation {
		margin-bottom: 2em;
		& > ul {
			white-space: nowrap;
			cursor: default;
			border-bottom: 1px solid $border-color;
			& > li {
				line-height: 2em;
				margin-right: 2em;
				position: relative;

				@include inline-block;

				& > a {
					color: rgba($text-color, .5);
					text-decoration: none;
					position: static;

					&:after {
						content: '';
						position: absolute;
						display: none;
						height: 1px;
						width: 100%;
						bottom: -1px;
						left: 0;
						background: $text-color;
					}

					&:hover {
						color: $text-color;
						&:after {display: block;}
					}
				}

				&.current {
					& > a {
						display: block;
						font-weight: bold;
						color: $brand-color;
						
						&:after {
							background-color: $brand-color;
							display: block;
							height: 2px;
							bottom: -2px;
						}
						
					}	
				}

				&:last-child {margin-right: 0;}
			}
		}
	}

	& > form, .details {
	
		h2 {margin-top: 0;}

		.main-column {
			@extend %grid-column-8;
		}
		.secondary-column {
			@extend %grid-column-4;
			@extend %grid-last-column;
		}

		.use-same-address-option {
			position: absolute;
			top: 0;
			right: 0;
			margin: 0;
		}

		.action-field {
			clear: both;
			margin-top: 1em;
			padding-top: 1.25em;
			border-top: 1px dotted $border-color;

			button {
				margin-right: .5em;
				&:last-of-type {
					margin-right: 0;
				}
			}
		}

        .action-field-no-border {
          clear: both;
          margin-top: 1em;
          padding-top: 1.25em;

          button {
              margin-right: .5em;
              &:last-of-type {
                  margin-right: 0;
              }
          }
        }

	}

	.details {

		.detail-field, .detail-action {
			border-bottom: 1px dotted $border-color;
			padding-bottom: 1em;
			margin-bottom: 1.5em;
		}

		.detail-label {
			font-size: 82%;
			color: rgba($text-color, .5);
			text-transform: uppercase;
			margin-bottom: .33em;
		}

		.detail-value {
			font-size: 115%;
			font-weight: normal;

            .progress-bar {
                padding-top: .25em;
                padding-bottom: .25em;
                height: 1.25em;
            }

            &.status {
                & > span {
                    &.ok {color: $notification-success;}
                    &.error {color: $notification-error;}
                    [class^="icon-"], [class*=" icon-"] {
                        margin-right: .1em;
                    }
                }
            }


        }

        .chart-area {
              height: 515px;
            &.c2 {height: 80px;}
              &.c2 {height: 80px;}
              &.c3, &.c4 {height: 182px;}
        }

        .chart-title {
          text-align: center;
          padding: 5px 5px 10px 5px;
          font-weight: bold;
        }

        .contact-person .detail-value + .button-group {
            margin-top: -.5em;
            text-align: right;
        }

        .contact-person {
            .detail-field:last-of-type {
                border-style: solid;
                border-bottom-width: 2px;
            }
        }

	}

	.table-wrapper + hr,
	.table-wrapper + .action-field {border-width: 0;}
}