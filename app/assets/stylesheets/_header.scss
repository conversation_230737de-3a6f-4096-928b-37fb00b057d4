

#header {
  background: $bg-color-alt;
}

#main-header {

  @include box-shadow(0 1px 0 rgba(#fff, 1));

  border-bottom: 1px solid $border-color;
  padding: 1.5em 0;

  .wrapper {
    @extend %grid-row;
  }
}

#sub-header {
  padding: .5em 0;

  .wrapper {
    @extend %grid-row;
  }
}

#eon-logo {

  white-space: nowrap;

  @extend %grid-column-3;

  a {
    @include inline-block;
    width: 132px;
    height: 38px;

    img {
      width: 100%;
      height: 100%;
      display: block;
    }
  }

  .welcome {
    font-size: .85em;
    margin-left: 1.5em;
    line-height: 1.3;
    position: relative;
    top: -.3em;

    @include inline-block;

    .welcome-info {
      color: rgba($text-color, .5);
    }
  }
}

#navigation {

  text-align: right;
  white-space: nowrap;
  cursor: default;

  @extend %grid-column-9;
  @extend %grid-last-column;

  ul {
    white-space: nowrap;
    line-height: 2.2em;
    margin-top: .4em;

    @include inline-block(middle);

    & > li {
      text-align: left;
      @include inline-block(middle);

      & > a {
        text-decoration: none;
        color: rgba($text-color, .75);
        margin: 0 1em;

        &:hover {
          color: $text-color;
        }
      }

      &:first-child > a {
        margin-left: 0;
      }

      &:last-child > a {
        margin-right: 0;
      }
    }

    &.nav-menu {
      padding-right: 2.5em;
      margin-right: 2.5em;
      border-right: 1px solid $border-color;

      @include box-shadow(1px 0 0 rgba(#fff, .75));

      & > li {
        & > a {
        }

        &.current > a {
          font-weight: bold;
          color: $brand-color;
        }
      }
    }

    &.user-menu {
    }
  }
}

#sub-header {

}

#breadcrumb {
  margin:0;
  padding: 0;
  //cursor: default;
  //line-height: 2.25em;
  //white-space: nowrap;

  //@extend %grid-column-8;

  & > li {
    @include inline-block;

    h1 {
      font-weight: 400;
      max-width: 300px;
      overflow-x: hidden;
      text-overflow: ellipsis;
      font-size: 18px;
      vertical-align: middle;
      display: inline-block;

    }

    a {
      color: #333;
      text-decoration: none;
      font-weight: 600;
      &.button {
        color: rgba($text-color, .5);
        font-size: 70%;
        font-weight: normal;
        margin-left: 2em;

        & > span {
          margin-right: .33em;
        }

        &:hover {
          color: rgba($text-color, .75);
        }
      }
    }

    &:not(:last-child):after {
      content: '›';
      margin: 0 .25em;
      font-weight: lighter;
      font-size: 100%;
      line-height: 0;
      opacity: .5;
    }
  }
}

#sub-header-form {
  @extend %grid-column-4;
  @extend %grid-last-column;

  .form-field {
    margin: 0;
  }

  #search-query {
    width: 100%;
    margin-bottom: 0;
  }

  .button-group {
    text-align: right;
  }

  button {
    margin-bottom: 0;
  }
}

.sub-header-side {
  @extend %grid-column-4;
  @extend %grid-last-column;

  .date-time {
    font-size: 140%;
    font-weight: lighter;
    text-align: right;
    line-height: 1.5em;

    [class^="icon-"], [class*=" icon-"] {
      margin-right: .1em;
    }
  }

  .date {
    margin-left: 2em
  }
}

#tabbed-nav {
  padding: .5em 0;

  .wrapper {
    @extend %grid-row;
  }
}

.tab-list {
  display: inline-block;
  overflow: hidden;
  margin-top: -.75em;
  margin-bottom: -1.33em;

  .tab-item {
    float: left;

    & > a {
      font-size: 18px;
      font-weight: bold;
      display: block;
      padding: 10px;
      border-radius: 3px;
      margin-right: 5px;
      text-decoration: none;

      &:hover {
        background: #fff;

      }

      &:focus {
        background: #fff;

      }

    }

    .tab-item--name {
      margin-right: .5em;
    }

    &.current {
      & > a {
        background: #eee;
        color: #333;
      }
    }
  }
}
