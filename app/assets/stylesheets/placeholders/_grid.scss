
// Configurable Grid SCSS placeholders
// Based on 1140 grid system
// By <PERSON><PERSON> Cristian <<EMAIL>>

// Grid variables
$grid-count: 12;
$grid-max-width: 1040px;
$grid-col-gap-ratio: 1; // column/gap width


	// Widths calculations
	$grid-gap-width: 100% / ($grid-count * $grid-col-gap-ratio + $grid-count - 1);
	$grid-col-width: $grid-gap-width * $grid-col-gap-ratio;


/* Grid rows (max-width: #{$grid-max-width}) */
.grid-row, %grid-row {
	
	max-width: $grid-max-width;
	margin: 0 auto;

	// Clearfix it
	&:after {
		clear: both;
		display: block;
		content: " ";
		height: 0;
		visibility: hidden;
	}
}

/* Grid columns (from 1 to #{$grid-count}) */
@for $i from 1 through $grid-count {
	
	/* Grid column #{$i} */
	.grid-column-#{$i}, %grid-column-#{$i} {
		float: left;
		width: $grid-col-width * $i + $grid-gap-width * ($i - 1) !important;
		min-height: 1px;
		margin-right: $grid-gap-width !important;
	}
}

/* Last grid column elements (no margin-right) */
.grid-column-#{$grid-count}, %grid-column-#{$grid-count},
.grid-last-column, %grid-last-column {margin-right: 0 !important;}
