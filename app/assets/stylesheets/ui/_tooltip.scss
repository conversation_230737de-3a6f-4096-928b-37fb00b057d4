/***
  TOOLTIP Style
  the positions (top|right|bottom|left) of the tooltip
  are relative to the object they represent
  ---
  e.g. right means that the tooltip is on the right
  side of an element, so… the arrow in on the left side
  
***/

.ui-tooltip {
  
  // tooltip configuration variables
  
      $tooltip-arrow-size: 10px;   // technicaly, the edge of a square (that's rotated)
    $tooltip-arrow-offset: 4px;   // can be deduced from the arrow size
  $tooltip-arrow-diagonal: 6px;   // CANNOT be deduced from the arrow size
  
      
  
  
  //@include noisify(transparent, "top, rgba(0,0,0,.85) 0, rgba(0,0,0,1) 100%"); 
  //@include font-smoothing(subpixel-antialiased);
  background: #333;
  font-size: .75em;
  line-height: 1.2;
  color: #FFF;
  
  position: absolute;
  top: 200px;
  left: 200px;
  width: auto;
  padding: 6px 8px;
  text-shadow: -1px -1px 0 rgba(0,0,0,.1);
  
  user-select: none;
  cursor: default;
  
  @include border-radius(5px);
  z-index: 99999999999999;
  
  p {
    margin: 5px 0;
    line-height: 1.2;
  }
  
  // tooltip's arrow
  &:after {
    content: "";
    width: $tooltip-arrow-size;
    height: $tooltip-arrow-size;
    background: #333;
    position: absolute;
    bottom: -1*$tooltip-arrow-offset;
    left:50%;
    margin-left: -1*$tooltip-arrow-diagonal;
    @include transform(rotate(45deg));
    @include transform-origin(50% 50%);
    @include border-radius(0px);
    z-index: -1;
  }
  
  // arrow position, relative to the tooltip position,
  // by default, the relative position is TOP
  // (the arrow position will be on the bottom of the tooltip)
  &[data-tooltip-position="right"] {
    &:after {
      top: 50%;
      right: auto;
      bottom: auto;
      left: $tooltip-arrow-offset - 1;
      margin-top: -1*$tooltip-arrow-diagonal/1.2;
    }
  }
  &[data-tooltip-position="bottom"] {
    &:after {
      top: -1*$tooltip-arrow-offset;
      bottom: auto;
    }
  }
  &[data-tooltip-position="left"] {
    &:after {
      top: 50%;
      left: auto;
      bottom: auto;
      right: -1*$tooltip-arrow-offset + 1;
      margin-top: -1*$tooltip-arrow-diagonal/1.2;
    }
  }
  
  // arrow visibility, by default is TRUE
  &[data-tooltip-arrow="false"] {
    &:after {display:none !important;}
  }
}

// IE fixes
.ie-lte8 .ui_tooltip:after {display:none !important;}

