
// Main data table

$dotted-border-image: "table-dotted-border.svg";
$solid-border-image: "table-solid-border.svg";

table[data-table-for] {
	width: 100%;
	margin-top: 2em;
	background: image-url($dotted-border-image) repeat-x 0 100%;
	font-size: .85em;
	position: relative;

	th, td {
		padding: 1em;
		position: relative;

		&.checkbox {
			width: 15px;
			input {top: -1px; margin: 0;}
		}
	}

	th {font-weight: bold; padding-top: 0;}
	
	td {}

	tbody > tr {

		position: relative;
		
		& > td {
			position: relative;

			&, &:first-child:before {
				background: image-url($dotted-border-image) repeat-x 0 0;
			}
			
			
			&:first-child:before {
				content: 'xxx';
				position: absolute;
				height: 100%;
				width: 9999px;
				top: 0;
				left: -4999px;
				background: rgba(#313141, .05) repeat-x 0 0;
				background-image: image-url($solid-border-image), image-url($solid-border-image);
				background-position: 0 0, 0 100%;
				display: none;
			}

			&.actions {
				position: absolute;
				right: 100%;
				background: none;
				visibility: hidden;

				button {
					color: #9797a7;
					font-size: 1.2em;
					& > span {display: none;}

					&:hover {
						color: #575767;
					}

					&.edit-button {float: right;}
					&.delete-button {float: left;}
				} 
			}
		}

		&:hover {
			& > td, & + tr > td {background-image: none;}
			& > td:first-child:before {display: block;}

			& > .actions {visibility: visible;}
		}

	}
}