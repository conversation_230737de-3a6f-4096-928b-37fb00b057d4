.pagination {
	cursor: default;
	white-space: nowrap;
	& > li {
		@include inline-block(top);
		
		& > a {
			display: block;
			color: $text-color;
			text-decoration: none;
			padding: 0 .5em;
			text-align: center;
			line-height: 2em;
			height: 2em;
			min-width: 2em;
			position: relative;


			@include border-radius(3px);

			&:hover {
				background-color: rgba($text-color, .15);
			}


		}

		&.current {
			& > a {
				background: $text-color;
				color: $bg-color;
				text-shadow: none;
				font-weight: bold;
			}
		}

		&.first, &.last, &.next, &.previous {
			& > a {
				&:before {
                  @include fa-icon();
                  font-family: "Font Awesome 5 Free";
                  font-weight: 900;
					position: absolute;
					top: 0;
					left: 0;
					width: 100%;
					height: 100%;
					text-align: center;
					line-height: 2em;
				}

				& > span {display: none;}
			}

			&.disabled {
				opacity: .5;
				a {cursor: default;}
				a:hover {background: none;}
			}

			
		}

        &.first > a:before {content: fa-content($fa-var-angle-double-left);}
        &.previous > a:before {content: fa-content($fa-var-angle-left);}
        &.next > a:before {content: fa-content($fa-var-angle-right);}
        &.last > a:before {content: fa-content($fa-var-angle-double-right);}
	}
}