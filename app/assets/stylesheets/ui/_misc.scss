.ui-center {text-align: center;}
.ui-left {float: left;}
.ui-right {float: right;}
.ui-hidden, [hidden] {display: none;}
.ui-clear {clear: both;}
.ui-expand {@extend .ui-clear; width: 100%;}
.ui-inline {@include inline-block;}
.ui-block {display: block;}

.ui-half, .ui-half-last {@extend %grid-column-6;}
.ui-half-last {@extend %grid-last-column;}


.progress-bar {
	height: .5em;
	position: relative;

	.bar {
		float: left;
		height: 100%;
		border-right: 1px solid #fff;
		background-color: #aaa;

		&.ok {background-color: $notification-success;}
		&.problem {background-color: $notification-error;}
	}

	.value {
		height: 200%;
		width: 1px;
		background: #000;
		position: absolute;
		top: -50%;
		margin-left: -1px;
	}
}

.progress-bar-large {
  height: 2em;
  .value {
    height: 150%;
    top: -25%;
  }
}