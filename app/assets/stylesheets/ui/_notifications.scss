.notification {
	width: 100%;
	color: #fff;
	background: $notification-info;
	text-shadow: 1px 1px 1px rgba(#000, .1);
	padding: 1em;
	margin: 1em 0;

	@include border-radius(3px);

	&.success {background-color: $notification-success;}
	&.warning {background-color: $notification-warning;}
	&.error {background-color: $notification-error;}

	&:before {
		float: left;
		width: 1em;
		text-align: center;
		margin-right: .5em;
		font-size: 120%;
		line-height: 1.1em;
	}

    &:before {
      @include fa-icon();
      font-family: "Font Awesome 5 Free";
      font-weight: 900;
    }
    &.success:before {content: fa-content($fa-var-check);}
    &.warning:before {content: fa-content($fa-var-exclamation-triangle);}
    &.error:before {content: fa-content($fa-var-exclamation-circle);}


}