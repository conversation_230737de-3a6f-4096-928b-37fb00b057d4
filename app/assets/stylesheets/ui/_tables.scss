// Main data table

$dotted-border-image: "/assets/table-dotted-border.svg";
$solid-border-image: "/asets/table-solid-border.svg";

table[data-table-for] {
  width: 100%;
  margin-top: 2em;
  font-size: 85%;
  position: relative;

  a {
    color: $brand-color;
    text-decoration: none;
  }

  small {
    color: rgba($text-color, .5);
  }

  th, td {
    padding: .5em .75em;
    position: relative;

    &.checkbox {
      width: 15px;
      padding-right: .5em;
      input {
        top: -1px;
        margin: 0;
      }
    }

    &.centered, &.actions {
      text-align: center;
    }
  }

  th {
    font-weight: bold;
    padding-top: 0;
    padding-bottom: 1em;
    border-top-color: transparent;
    white-space: nowrap;

    &.sortable {
      span {
        cursor: pointer;
      }
      &:after {
        @include fa-icon();
        font-family: "Font Awesome 5 Free";
        font-weight: 900;
        color: rgba($text-color, .5);
        margin-left: .5em;
      }

      &.sorted-asc:after {
        content: fa-content($fa-var-caret-down);
      }
      &.sorted-desc:after {
        content: fa-content($fa-var-caret-left);
      }
    }

    &.actions {
      font-weight: normal;
      width: 1px;
    }

    &.status {
      width: 1px;
    }
  }

  td {

    height: 4em;

    .status, .state {
      white-space: nowrap;
      &.success, &.ok, & > .success, & > .ok {
        &, & + * {
          color: $notification-success;
        }
      }
      &.info, & > .info {
        &, & + * {
          color: $notification-info;
        }
      }
      &.warning, & > .warning {
        &, & + * {
          color: $notification-warning;
        }
      }
      &.error, & > .error {
        &, & + * {
          color: $notification-error;
        }
      }

      [class^="fa-"], [class*=" fa-"] {
        font-size: 130%;
      }
    }

    .state {
      display: block;
    }

    .state.ok > span,
    .state.error > span {
      font-weight: bold;
    }

    [class^="fa-"], [class*=" fa-"] {
      //font-weight: normal !important;
      margin: 0 .25em;
      //font-size: 125%;

      &:first-child {
        margin-left: 0;
      }
    }

    .compact-view & {
      height: auto;
    }

  }

  tbody > tr {

    & > td {

      border: 1px dotted $border-color;
      border-left-color: transparent;
      border-right-color: transparent;

      &.name {
        font-size: 115%;
        white-space: nowrap;
      }

      &.actions {
        padding: 0;

        .button-group {
          text-align: center;
          border-left: 1px dotted $border-color;
          padding: 0 1em;

          [class^="fa-"], [class*=" fa-"] {
            margin: 0;
          }
        }

        &:last-child {
          //          .button-group {padding-right: 0;}
        }

        .button {
          color: #9797a7;
          font-size: 130%;
          line-height: 1em;
          height: auto;
          padding: .5em;

          .label {
            display: none;
          }

          &:hover {
            color: #575767;
          }
        }
      }
    }

    &:hover {
      & > td {
        border-style: solid;
        border-left-color: rgba(#313141, .0125);
        border-right-color: rgba(#313141, .0125);
        background-color: rgba(#313141, .025);

        &:first-child {
          border-left-color: transparent;
          @include border-radius(3px);
        }

        &:last-child {
          border-right-color: transparent;
        }
      }
    }

  }
}

.table-actions {
  margin: 1em 0;
  form {
    float: left;
    select {
      margin-right: .5em;
    }
    .form-field {
      margin: 0;
    }
  }
  .pagination {
    float: right;
  }
}

[data-table-for="status-board"], [data-table-for="alerts"] {
  .state {
    text-align: center;
    span {
      display: none;
    }
    [class^="fa-"], [class*=" fa-"] {
      display: block;
      font-size: 150% !important;
    }
  }
}

[data-table-for="alerts"] {
  .state {
    [class^="fa-"], [class*=" fa-"] {
    }
  }
}

table[data-table-for="allocation-validations-entries"] {
  margin-top: 0;
  tbody > tr > td {
    border: 0;
  }
}

table[data-table-for="allocation-validations"], table[data-table-for="distributed-units-validations"] {
  td.validation-name {
    width: 30%;
  }
}

table[data-table-for="signal_lists"] {
  td,
  li {
    word-break: break-all;
  }
}

table[data-table-for="bids"] {
  thead tr th {
    color: #fff;
    background: #1EA2B1;

    font-size: 14px;

    text-shadow: none;

    padding: 1.5em .75em;
    position: relative;


  }
}