$button-gradient: linear-gradient(rgba(#fff, .1), rgba(#9797a7, .15));
$button-hover-gradient: linear-gradient(rgba(#9797a7, .05), rgba(#9797a7, .25));
$button-active-gradient: linear-gradient(rgba(#9797a7, .15), rgba(#9797a7, .05));

$inputs-box-shadow: 0 4px 3px rgba(#000, .05) inset, 1px 1px 0 rgba(#fff, .5);

#{$text-input-selectors},
#{$button-selectors},
#{$option-input-selectors},
select,
textarea {
  font-weight: lighter;
  font-size: 1em;
  font-family: $base-font-family;

  height: 2.5em;
  max-width: 100%;
  color: $text-color;
  padding: 0 .75em;
  margin: 0;

  html:not(.lt-ie9) & {
    border: 1px solid #b9b9c9;
  }

  @include inline-block;
  @include border-radius(3px);

  &:disabled, &[disabled] {
    background-color: $bg-color-alt;
    opacity: .66;

    @include box-shadow(none);
  }

  &[readonly] {
    background-color: $bg-color-alt;
  }

  &:not(:disabled):not([disabled]):not([readonly]):hover {
    border-color: #9999a9;
  }

  &:not([readonly]):focus {
    border-color: transparent !important;
    @include box-shadow(0 0 0 2px #73b9ff);
  }

  .form-field & {
    margin-bottom: .75em;
  }

  @include input-placeholder {
    color: rgba($text-color, .66);
    font-style: italic;
  }

}

#{$option-input-selectors} {
  width: 15px;
  height: 15px;
  padding: 0;
  background-repeat: no-repeat;
  background-position: 50% 50%;

  html:not(.lt-ie9) {
    background-color: #fff;
  }

  position: relative;
  top: -1px;
  margin: 0 .25em 0 0;

  @include inline-block(middle);
  @include appearance(none);
  @include border-radius(2px);
  @include box-shadow($inputs-box-shadow);

  & + label {
    font-weight: normal;
  }

  &:not(:first-child) {
    margin-left: 1em;
  }
  &:not(:disabled):not([disabled]) {
    cursor: pointer;
  }
}

[type="checkbox"] {
  &:checked {
    background-image: image-url("input-checkbox.svg");
  }
}

[type="radio"] {
  @include border-radius(100%);
  &:checked {
    background-image: image-url("input-radio.svg");
  }
}

.form-option {
  margin: .5em 0;
  #{$option-input-selectors} {
    margin: 0;
    margin-right: .25em
  }

  &.ui-inline {
    margin-right: 1em;
  }
}

textarea {
  height: auto;
}

#{$text-input-selectors} {
  @include box-shadow($inputs-box-shadow);
}

#{$button-selectors}, select {
  text-shadow: 0 1px 0 #fff;
  background-repeat: no-repeat;

  @include background-image($button-gradient);
  @include box-shadow(0 1px 1px rgba(#000, .1), 0 1px 0 rgba(#fff, .75) inset);
  @include border-radius(3px);
  @include inline-block;

  &:not(:disabled):not([disabled]):hover {
    cursor: pointer;
    @include background-image($button-hover-gradient);
  }
}

select {
  padding-right: 2.25em;
  background-position: 100% 50%;

  @include background-image($button-gradient, image-url("select-arrows.svg"));
  @include appearance(none);

  &:not(:disabled):not([disabled]):hover {
    @include background-image($button-hover-gradient, image-url("select-arrows.svg"));
    background-size: auto;
  }

  &:disabled, &[disabled] {
    @include background-image(image-url("select-arrows.svg"));
    background-size: auto;
  }
  background-size: auto;
}

[type="date"] {
  padding-right: 0;
}

[type="date"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
}

[type="date"]::-webkit-calendar-picker-indicator {
  color: transparent;
  background: transparent image-url("select-arrows.svg") no-repeat 50% 50%;
  -webkit-appearance: none;
}

#{$button-selectors} {

  line-height: 2.5em;
  text-decoration: none;
  font-weight: normal;
  background-color: transparent;

  @include user-select(none);

  html.lt-ie9 & {
    border: none;
  }

  &:not(:disabled):not([disabled]):active {
    @include background-image($button-active-gradient);
    @include box-shadow(0 1px 3px 0 rgba(#000, .2) inset, 1px 1px 0 rgba(#fff, .5));
  }

  &.action {

    &, &:not(:disabled):not([disabled]):hover {
      background-color: $brand-color;
      border-color: mix(#000, $brand-color, 33%) !important;
    }

    &:not(:disabled):not([disabled]):active {
      border-top-color: mix(#000, $brand-color, 50%) !important;
    }

    &:focus {
      border-color: mix(#000, $brand-color, 50%) !important;
      @include box-shadow(0 0 0 2px rgba(#000, .2));
    }

    color: #fff;
    font-weight: bold;
    text-shadow: 1px 1px 0 rgba(#000, .1);
    padding-left: 1em;
    padding-right: 1em;

    @include background-image(linear-gradient(mix(#fff, $brand-color, 10%), mix(#000, $brand-color, 15%)));
    @include box-shadow(0 1px 0 rgba(#fff, .3) inset);
  }

  &.silent {

    &, &:hover {
      border: none !important;
      background: none !important;
      @include box-shadow(none !important);
    }

    &:hover {
    }
  }

  &.small {
    font-size: 80%;
  }

  [class^="icon-"], [class*=" icon-"] {
    margin: 0 .33em;
    &:first-of-type {
      margin-left: 0;
    }
    &:last-of-type {
      margin-right: 0;
    }
  }
}

label {
  display: inline;
  font-weight: bold;
  font-size: 85%;
  margin-bottom: 2px;
  &[for] {
    cursor: pointer;
  }
  &:after {
    content: '';
    display: block;
    height: .33em;
  }
}

.button-group {
  white-space: nowrap;
  cursor: default;
  overflow: hidden;
}

.button-group-wrap {
  cursor: default;
  overflow: hidden;
}

.form-field-group {
  @extend %grid-row;
}

.form-field {

  @extend %grid-row;

  margin-bottom: 1em;
  position: relative;

  &.search-field {
    &:before {
      @include fa-icon();
      content: fa-content($fa-var-search);
      font-family: "Font Awesome 5 Free";
      font-weight: 900;

      position: absolute;
      line-height: 2em;
      font-size: 120%;
      width: 2.2em;
      text-align: center;
      left: 0;
      bottom: 0;
      color: rgba($text-color, .5);
    }
    & > input {
      padding-left: 2.4em;
    }
  }
}

form.edit_dispatch_group,
form.new_dispatch_group {
  .form-field {
    label {
      white-space: nowrap;
    }
  }
}