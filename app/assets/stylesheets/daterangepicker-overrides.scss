.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    list-style: none;
    font-size: 14px;
    text-align: left;
    background-color: white;
    border: 1px solid #cccccc;
    border: 1px solid rgba(0,0,0,0.15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0,0,0,0.175);
    box-shadow: 0 6px 12px rgba(0,0,0,0.175);
    background-clip: padding-box;
}
.daterangepicker {
    select.hourselect, select.minuteselect, select.secondselect, select.ampmselect {
    width: 50px;
    margin-bottom: 0;
    padding-right: 0px;
    height: 30px;
  }

  .col-xs-6 {
    width: 50%;
    float: left;
  }

  .btn.btn-primary, a.btn.btn-primary {
    color: #fff;
    background: #e52717;
    border-color: #e52717 !important;
    margin: 2px 5px;
    width: 95%;
  }
  .btn.btn-clear, a.btn.btn-clear {
    background: none;
    color: #f21c0a;
    border-color: #f21c0a;
    margin: 2px 5px;
    width: 95%;
  }
}
[type='text'].hasDatepicker {
  background: white;
  &:hover {
    border-color: #9999a9;
  }
}