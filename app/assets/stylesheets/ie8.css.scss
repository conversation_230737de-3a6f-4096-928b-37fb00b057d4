@import "compass/css3";

.lt-ie9 {
	input[type="text"] {
		line-height: 2.5em;
	}
	input[type="checkbox"], input[type="radio"] {
		padding: 0;
		margin: 0;
		width: auto;
		height: auto;
	}
	select {
		padding-right: 0;
		padding-left: 0;
	}

	#content > form .use-same-address-option {
		width: 50%;
		text-align:right;
	}

	table[data-table-for] {
		small {color: #aaa;}
	}


	#breadcrumb li {
		&:before {
			content: '›';
			margin: 0 .5em;
			font-weight: lighter;
			line-height: 0;
			opacity: .5;
			position: relative;
			top: -2px;
		}
		&:first-child:before {
			display: none;
		}
	}

	[type="button"], [type="submit"] {
		background: #ddd;
		&.action {background: #f21c0a;}
	}

	#eon-logo {
		& > a {
			background: image-url("eon-logo.png") no-repeat 50% 50%;
			& > img {display: none;}
		}
	}
}