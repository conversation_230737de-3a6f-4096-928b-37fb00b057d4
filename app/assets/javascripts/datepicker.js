$(function() {
  $('input[data-behaviour=datepicker]').each(function(i) {
    var data = $(this).data();
    // console.log("DATA:", data, data.mindate, data.maxdate)
    var options = {
      altFormat: "yy-mm-dd",
      altField: $(this).next(),
      timePicker: true
    }
    if (data.mindate) {
      options['minDate'] = new Date(data.mindate);
    }
    if (data.maxdate) {
      options['maxDate'] = new Date(data.maxdate);
    }
    // console.log("Options:", options)
    $(this).datepicker(
      $.extend( $.datepicker.regional[ railsExports.locale ], options)
    );
  });

  $('input[data-behaviour=weekpicker]').each(function(i) {
    var data = $(this).data();
    $(this).weekpicker(
      {
        firstDay: data.weekstart || 1,
        startField: $(this)
      }
    );
  });

$.each( $('.daterange-picker'), function() {
    var options = {
        locale: {
            format: 'DD/MM/YYYY',
            firstDay: 1
        },
        timePicker: false,
        timePicker24Hour: true,
        timePickerSeconds: false
    }
    localizeDaterangepicker(options, railsExports.locale);
    var timePicker = $(this).data('time-picker');
    if (timePicker) {
      options.timePicker = timePicker;
      options.locale.format = 'DD/MM/YYYY HH:mm';
    }
    var stepMinute = $(this).data('step-minute');
    if (stepMinute) {
      options.timePickerIncrement = stepMinute;
    }
    var minDate = $(this).data('min-date');
    if (minDate) {
      options.minDate = minDate;
    }
    var maxDate = $(this).data('max-date');
    if (maxDate) {
      options.maxDate = maxDate;
    }
    var dateLimit = $(this).data('date-limit');
    if (dateLimit) {
      options.dateLimit = dateLimit;
    }
    var singleDatePicker = $(this).data('single-date-picker');
    if (singleDatePicker) {
      options.singleDatePicker = singleDatePicker;
    }
    var autoUpdateInput = $(this).data('auto-update-input');
    if (autoUpdateInput) {
      options.autoUpdateInput = autoUpdateInput;
    }
    $(this).daterangepicker(options);
  });
});

function localizeDaterangepicker(options, language) {
  if (language == 'de') {
    options.locale.applyLabel = "Hinzufügen"
    options.locale.cancelLabel = "Abbrechen"
    options.locale.fromLabel = "Von"
    options.locale.toLabel = "Bis"
    options.locale.daysOfWeek = ['So', 'Mo', 'Di', 'Mi', 'Do', 'Fr', 'Sa']
    options.locale.monthNames = ['Jan', 'Feb', 'Mär', 'Apr', 'Mai', 'Jun', 'Jul', 'Aug', 'Sep', 'Okt', 'Nov', 'Dez']
  }
}

function setupDaterangepicker(elementId) {
  moment.locale(railsExports.locale)
  var options = {
      locale: {
          format: 'DD/MM/YYYY HH:mm'
      },
      timePicker: true,
      timePicker24Hour: true,
      timePickerSeconds: false
  }
  localizeDaterangepicker(options, railsExports.locale);
  var stepMinute = $('#' + elementId).data('step-minute')
  if (stepMinute) {
    options.timePickerIncrement = stepMinute;
  }
  $('#' + elementId).daterangepicker(options);

}