function dgAggregationsChart(containerId){
  console.log("Setting up dg aggregations chart on ", containerId)

  var container = $('#' + containerId);
  var dgName = container.data('dgName');
  var startTime = container.data('startTime');
  var endTime = container.data('endTime');
  var allocatedFlexSeries = container.data('allocatedFlexSeries') || [];
  var nominatedFlexSeries = container.data('nominatedFlexSeries') || [];

  console.log("read data for asset flex chart", dgName, startTime, endTime, allocatedFlexSeries, nominatedFlexSeries);

  container.highcharts({
    chart: {
      type: 'arearange'
    },

    plotOptions: {
      series: {
        step: 'left',
      },
      arearange: {
        fillOpacity: 0.2,
        lineColor: "rgba(0, 0, 0, 0)"
      }
    },

    rangeSelector: {
      selected: 2
    },

    title: {
      text: dgName,
    },

    tooltip: {
      formatter: function() {
        var points = this.points;
        if (points && points.constructor === Array){
          var res = "";
          for (var i = 0; i < points.length; i++) {
            var p = points[i].point;

            // this is for allocated flex
            if (p.description) {
              res += "-" + Math.abs(p.low) + "/ +" + p.high + " (MW)" +
                     " | " + p.description + "</span>" +
                     "<br/>";
            }
            // this is for nominated flex
            else {
              res += p.y + " MW" + " Nomination<br/>"
            }
          }

          return res;
        }
        else {
          return false
        }
      },
      shared: true,
      crosshairs: true
    },

    xAxis: {
      type: 'datetime',
      min: startTime,
      max: endTime
    },

    series: [
      {
        name: 'Allocated Flex',
        data: allocatedFlexSeries
      },
      {
        name: 'Pos. Nominated Flex',
        data: nominatedFlexSeries['positive'],
        type: 'line',
        step: true
      },
      {
        name: 'Neg. Nominated Flex',
        data: nominatedFlexSeries['negative'],
        type: 'line',
        step: true
      },
    ]
  });
};
