function assetFlexChart(containerId){
  console.log("Setting up asset flex chart on ", containerId)

  var container = $('#' + containerId);
  var assetName = container.data('assetName');
  var startTime = container.data('startTime');
  var endTime = container.data('endTime');
  var allocatedFlexSeries = container.data('allocatedFlexSeries');

  console.log("read data for asset flex chart", assetName, startTime, endTime, allocatedFlexSeries);

  // show only the border lines for non-exclussive DGs
  allocatedFlexSeries.forEach(function(data){
    if (!data['exclussive']) {
      data['fillOpacity'] = 0;
      data['dashStyle'] = 'longdash';
      data['zIndex'] = 10;
      data['width'] = 2;
    }
    else {
      data['fillOpacity'] = 0.2;
      data['lineColor'] = "rgba(0, 0, 0, 0)";
    }
  });

  container.highcharts({
    chart: {
      type: 'arearange'
    },

    plotOptions: {
      series: {
        step: 'left'
      },
    },

    rangeSelector: {
      selected: 2
    },

    title: {
      text: assetName
    },

    tooltip: {
      formatter: function() {
        var points = this.points;
        if (points && points.constructor === Array){
          var res = "";
          for (var i = 0; i < points.length; i++) {
            var p = points[i].point;

            res += "-" + Math.abs(p.low) + "/ +" + p.high + " (MW)" +
                   " | " + p.description + "</span>" +
                   "<br/>"
          }
          return res;
        }
        else {
          return false
        }
      },
      shared: true,
      crosshairs: true
    },

    xAxis: {
      type: 'datetime',
      min: startTime,
      max: endTime
    },

    series: allocatedFlexSeries
  });
}

