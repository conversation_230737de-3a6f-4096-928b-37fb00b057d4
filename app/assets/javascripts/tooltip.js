var tooltip = $("<div id='tooltip' class='ui-tooltip'></div>");
var resetTooltip = function() {
  $('#tooltip').stop().animate({top: "+=5px", opacity: 0}, function(e) {$(this).hide()});
  //tooltip.hide();
}
// mouseenter event
$(document).on('mouseenter', '[data-tooltip-content]', function(e) {
  
  // get the data object
  // (e.g. data = {tooltipContent: "<b>HTML</b> Content", tooltipPosition: "top"})
  
  var data = $(this).data();
  if(!data.tooltipPosition) data.tooltipPosition  = "top";
  if(!data.tooltipTheme)    data.tooltipTheme     = "black";
  // if(!data.tooltipArrow)    data.tooltipArraw     = true;
  // else                      data.tooltipArrow     = false;
  
  tooltip.html(data.tooltipContent || $(this).attr('title'))
    .attr("data-tooltip-position",  data.tooltipPosition)
    .attr("data-tooltip-theme",     data.tooltipTheme);
  
  // get some variable for calculating the position
  
  _th = tooltip.outerHeight();  // tooltips's height
  _tw = tooltip.outerWidth();   // tooltip's width
  
  _eh = $(this).outerHeight();  // rel element's height
  _ew = $(this).outerWidth();   // rel element's width
  _et = $(this).offset().top;   // rel element's offset top
  _el = $(this).offset().left;  // rel element's offset left
  
  // [TODO] Add dynamic arrow offset
  
  // calculate the position of the tooltip
  // according to the relative element and
  // the declared position for the tooltip
  
  _tp = {top: -100, left: -100};  // tooltip's position (by default the tooltip's position is outside the viewport, just in case (something goes wrong))
  _to = 10; // tooltip's arrow offset
  
  switch(data.tooltipPosition) {
    case "top":
    default:
      _tp.top   = _et - (_th + _to);
      _tp.left  = (_el + _ew) - (_ew + _tw) / 2;
      break;
    case "right":
      _tp.top   = (_et + _eh) - (_eh + _th) / 2;
      _tp.left  = _el + _ew + _to;
      break;
    case "bottom":
       _tp.top   = _et + _eh + _to;
       _tp.left  = (_el + _ew) - (_ew + _tw) / 2;
      break;
    case "left":
       _tp.top   = (_et + _eh) - (_eh + _th) / 2;
       _tp.left  = _el - (_tw + _to);
      break;
  }
  
  // position our sweet tooltip
  tooltip.css({display:'block', top: _tp.top - 10, left: _tp.left}).stop().animate({opacity: 1, top: _tp.top}, 200);
  
  
  
});
// mouseleave event
$(document).on('mouseleave', '[data-tooltip-content]', function(e) {
  resetTooltip();
});
$(document).ready(function() {
  
  $('body').prepend(tooltip);
  tooltip.css({opacity:0});
  
});