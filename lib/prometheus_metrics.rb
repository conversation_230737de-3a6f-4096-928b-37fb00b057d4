require 'prometheus/client'

class PrometheusMetrics
  include Singleton

  def client
    @client ||= Prometheus::Client.registry
  end

  def self.counters(*args)
    instance.counters(*args)
  end

  def self.gauges(*args)
    instance.gauges(*args)
  end

  def self.histograms(*args)
    instance.histograms(*args)
  end
        
  def counters(args)
    name = args[:name]
    docstring = args[:docstring]
    labels = args[:labels]
    @counters ||= {}
    unless @counters[name]
      @counters[name] = Prometheus::Client::Counter.new(name, docstring: docstring, labels: labels)
      client.register(@counters[name]) unless client.get(name)
    end
    @counters[name]
  end

  def gauges(args)
    name = args[:name]
    docstring = args[:docstring]
    labels = args[:labels]
    @gauges ||= {}
    unless @gauges[name]
      @gauges[name] = Prometheus::Client::Gauge.new(name, docstring: docstring, labels: labels)
      client.register(@gauges[name]) unless client.get(name)
    end
    @gauges[name]
  end

  def histograms(args)
    name = args[:name]
    docstring = args[:docstring]
    labels = args[:labels]
    @histograms ||= {}
    unless @histograms[name]
      @histograms[name] = Prometheus::Client::Histogram.new(name, docstring: docstring, labels: labels)
      client.register(@histograms[name]) unless client.get(name)
    end
    @histograms[name]
  end
  
end