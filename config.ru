# This file is used by Rack-based servers to start the application.

require ::File.expand_path('../config/environment',  __FILE__)

require 'prometheus/middleware/collector'
require 'prometheus/middleware/exporter'
use Rack::Deflater
use Prometheus::Middleware::Collector
use Prometheus::Middleware::Exporter

run Rails.application

# enable mapping if not already handled by passenger
# map (ENV['ENABLE_RACK_RELATIVE_URL'] && ActionController::Base.config.relative_url_root) || "/" do
#   run Rails.application
# end
