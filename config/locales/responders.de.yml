de:
  flash:
    actions:
      create:
        notice: '%{resource_name} wurde erstellt.'
        # alert: '%{resource_name} konnte nicht erstellt werden.'
      update:
        notice: '%{resource_name} wurde aktualisiert.'
        # alert: '%{resource_name} konnte nicht aktualisiert werden.'
      destroy:
        notice: '%{resource_name} wurde gelöscht.'
        alert: '%{resource_name} konnte nicht gelöscht werden.'
      search:
        no_matches: Keine Ergebnisse
    allocations:
      filter:
        no_asset_selected: Anlage muss ausgewählt werden.
        no_data: Keine Ergebnisse.
        no_dg_selected: Abrufgruppe muss ausgewählt werden.
    scheduling_reports:
      third_party_balancing_group:
        required: Bitte Drittbilanzkreis eingeben!
      regenerate_scheduling_balancing_groups_third_party_reports:
        failure: 'Verschicken Scheduling an dritte BKV für %{date} ist fehlgeschlagen!'
        success: 'Verschicken Scheduling an dritte BKV für %{date} wurde gestarted!'
      regenerate_bg_asset_activations_reports:
        failure: 'Verschicken BK6-17-046 report für %{date} ist fehlgeschlagen!'
        success: 'Verschicken BK6-17-046 report für %{date} wurde gestarted!'
    send_files_externally:
      send_allocations_files:
        failure: 'Sending Allocation File to TenneT for %{date} has failed!'
        success: 'Sending Allocation File to TenneT for %{date} has started.'
      send_measurements_files:
        failure: 'Sending Measurement File to TenneT for %{date} has failed!'
        success: 'Sending Measurement File to TenneT for %{date} has started.'
      send_afrr_measurements_files:
        failure: 'Sending aFRR Measurement File to TenneT for %{date} has failed!'
        success: 'Sending aFRR Measurement File to TenneT for %{date} has started.'
      send_afrr_activated_energy_documents:
        failure: 'Sending aFRR Activated Energy Document to TenneT for %{date} has failed!'
        success: 'Sending aFRR Activated Energy Document to TenneT for %{date} has started.'
