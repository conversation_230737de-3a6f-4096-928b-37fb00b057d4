en-GB:
  flash:
    actions:
      create:
        notice: '%{resource_name} was successfully created.'
        # alert: '%{resource_name} could not be created.'
      update:
        notice: '%{resource_name} was successfully updated.'
        # alert: '%{resource_name} could not be updated.'
      destroy:
        notice: '%{resource_name} was successfully destroyed.'
        alert: '%{resource_name} could not be destroyed.'
      search:
        no_matches: No Matches found
    allocations:
      filter:
        no_asset_selected: Asset must be selected.
        no_data: No data found.
        no_dg_selected: DG must be selected.
    scheduling_reports:
      third_party_balancing_group:
        required: Please provide 3rd Party BG!
      regenerate_scheduling_balancing_groups_third_party_reports:
        failure: 'Sending Scheduling Third Parties for %{date} has failed!'
        success: 'Sending Scheduling Third Parties for %{date} has started.'
      regenerate_bg_asset_activations_reports:
        failure: 'Sending BK6-17-046 report for %{date} has failed!'
        success: 'Sending BK6-17-046 report for %{date} has started.'
    send_files_externally:
      send_allocations_files:
        failure: 'Sending Allocation File to TenneT for %{date} has failed!'
        success: 'Sending Allocation File to TenneT for %{date} has started.'
      send_measurements_files:
        failure: 'Sending Measurement File to TenneT for %{date} has failed!'
        success: 'Sending Measurement File to TenneT for %{date} has started.'
      send_afrr_measurements_files:
        failure: 'Sending aFRR Measurement File to TenneT for %{date} has failed!'
        success: 'Sending aFRR Measurement File to TenneT for %{date} has started.'
      send_afrr_activated_energy_documents:
        failure: 'Sending aFRR Activated Energy Document to TenneT for %{date} has failed!'
        success: 'Sending aFRR Measurement File to TenneT for %{date} has started.'
