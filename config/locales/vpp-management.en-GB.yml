en-GB:
  activemodel:
    attributes:
      report_email_sendings_configuration:
        scheduling_balancing_groups_active: EDG Reports Active
        scheduling_balancing_groups_email_recipients: EDG Reports Recipients
        scheduling_balancing_groups_third_party_active: Third Party Reports Active
        scheduling_balancing_groups_third_party_email_recipients: Third Party Reports Recipients
        scheduling_balancing_groups_third_party_email_recipients_email_only_when_activation: Only in case of activation
        scheduling_day_after_active: UGC Reports Active
        scheduling_day_after_email_recipients: UGC Reports Recipients
        scheduling_control_active: Control Reports Active
        scheduling_control_email_recipients: Control Reports Recipients
        tso_schedule_alarm_email_recipients: TSO Scheduling Upload Alarm Recipients
        tso_schedule_alarm_active: TSO Scheduling Upload Alarm Active
        main_address: Send all emails to
      imported_report:
        contents: Contents
    errors:
      models:
        report_email_regeneration:
          attributes:
            base:
              date_not_in_the_past: Date must be in the past.
    models:
      report_email_sendings_configuration: Automatic Report Emailings Configuration
      imported_report: Imported Report
  activerecord:
    attributes:
      asset_dg_allocation:
        asset: Asset
        dispatch_group: Dispatch Group
        end_time: End
        is_allocated: Allocated
        interval: Interval
        skip_start_time_validations: Allow modifications with immediate effect
        start_time: Start
      asset_dg_allocation_source:
        asset_dg_allocations: Allocations
        created: Performed on
        user: User
        content_type: Type
        content: Content
        success: Successfully imported
        warnings: Warnings
        errors: Errors
        validation_result: Validation
        skip_start_time_validations: Allow modifications with immediate effect
      asset_dg_allocation_source/success:
        "true": Successfully imported
        "false": Failure
      asset_dg_allocation_source/content_type:
        file_import: Allocations File
        ui_update: Form
      dispatch_group:
        activation_factor: Activation Buffer (1=100%)
        alarm_assets_with_not_recoverable_faults_enabled: Signal 1 (AssetAlertLevelAlarm)
        alarm_assets_with_recoverable_faults_enabled: Signal 2 (AssetAlertLevelWarning)
        alarm_setpoint_reachable_enabled: Signal 3 (DUAlertLevelWarning)
        alarm_available_flex_too_low_enabled: Signal 4 (VPPMissingFlexibilitySRL)
        alarm_assets_faults_pq_threshold: Assets relevant if PQ exceeds (KW)
        alarm_flexibility_threshold_factor: FlexTooLow Threshold Factor (1=100%)
        alarm_flexibility_threshold_buffer: FlexTooLow Duration (s)
        availability_deviation_threshold_factor: Change in Availability (1=100%)
        echo_signal_heartbeat: Echo Heartbeat Signal
        echo_signal_active_power: Echo ActivePower Signal
        individual_alarm_echo_signal_active_power_enabled: RCC EchoActivePower Alarm enabled
        individual_alarms_enabled: RCC FlexTooLow Alarm enabled
        individual_alarm_merlin_enabled: RCC MerLin Alarm enabled
        individual_alarm_overdelivery_neg_enabled: Overdelivery by neg. setpoint alarm enabled
        individual_alarm_overdelivery_neg_threshold: Overdelivery by neg. setpoint alarm threshold (MW)
        individual_alarm_overdelivery_neg_delay: Overdelivery by neg. setpoint alarm duration (s)
        individual_alarm_overdelivery_pos_enabled: Overdelivery by pos. setpoint alarm enabled
        individual_alarm_overdelivery_pos_threshold: Overdelivery by pos. setpoint alarm threshold (MW)
        individual_alarm_overdelivery_pos_delay: Overdelivery by pos. setpoint alarm duration (s)
        nomination_tool_additional_buffer_pos_mw: Additional Buffer POS MW (default 0)
        nomination_tool_additional_buffer_neg_mw: Additional Buffer NEG MW (default 0)
        nomination_tool_enabled: Trading Nomination Enabled
        nomination_tool_energy_price_target_margin: "Energy Price Margin (1=100%; default 0)"
        nomination_tool_max_fault_seconds_percentage: "Rollup Asset Fault Threshold (1=100%; default 0.9)"
        nomination_tool_max_invalid_rollups_percentage: "Aggregrated Asset Fault Threshold (1=100%; default 0.9)"
        nomination_tool_past_availability_hours: Time window for rollups (h) (default 24)
        nomination_tool_target_bid_volume_mw: Minimum Size Bid MW (default 5)

        automatic_allocation_enabled: Automatic Allocation Enabled
        automatic_allocation_weight: Weight
        automatic_allocation_nomination_buffer_percentage: "Nomination buffer (%)"
        max_asset_reallocation: Max Asset Reallocation (Default No Constraint)

        subpools_value_on: Marketed Poolindex
        subpools_value_off: Non-Marketed Poolindex

        name: Name
        tso: TSO
        market: Market
        dispatch_mode: Dispatch Mode
        dsos: Subsignals for DSOs
        event_types_acknowledge_alarm: Events to monitor with silent alarm
        event_types_acknowledge_audio_alarm: Events to monitor with audio alarm
        minimum_duration_of_dispatch: Min. duration of dispatch (s)
        reaction_time: Reaction time (s)
        signals: Relevant Signals

        setpoint_reached_detection_upper_tolerance_factor: Upper Tolerance (1=100%)
        setpoint_reached_detection_lower_tolerance_factor: Lower Tolerance (1=100%)
        setpoint_reached_detection_upper_tolerance_minimum: Upper Tolerance Min. (KW)
        setpoint_reached_detection_lower_tolerance_minimum: Lower Tolerance Min. (KW)
        setpoint_reached_detection_rolling_average_duration: Average Window (s)

        is_at_setpoint_detection_upper_tolerance_factor: Upper Tolerance (1=100%)
        is_at_setpoint_detection_lower_tolerance_factor: Lower Tolerance (1=100%)
        is_at_setpoint_detection_upper_tolerance_minimum: Upper Tolerance Min. (KW)
        is_at_setpoint_detection_lower_tolerance_minimum: Lower Tolerance Min. (KW)

        compensation_check_interval: Check Window (s)
        compensation_resolution: Resolution (KW)
        over_delivery_excess_compensation_factor: Over-Delivery Compensation (1=100%)
        over_delivery_compensation_limit_factor:  Over-Delivery Limit (1=100%)
        under_delivery_excess_compensation_factor: Under-Delivery Compensation (1=100%)
        under_delivery_compensation_limit_factor:  Under-Delivery Limit (1=100%)
        compensation_is_at_setpoint_detection_upper_tolerance_factor: Upper Tolerance (1=100%)
        compensation_is_at_setpoint_detection_lower_tolerance_factor: Lower Tolerance (1=100%)
        compensation_is_at_setpoint_detection_upper_tolerance_minimum: Upper Tolerance Min. (KW)
        compensation_is_at_setpoint_detection_lower_tolerance_minimum: Lower Tolerance Min. (KW)

        sally_setpoint_quantization_resolution: Quantization Resolution (KW)
        sally_setpoint_quantization_timeout: Quantization Timeout (s)
        sally_setpoint_read_enabled: Enabled

        merlin_dispatch_enabled: Enabled
        capacity_price_grouping_steps: Capacity Price Grouping Steps

        portfolio_id: Porfolio ID
        rollups_enabled: Enabled

        ext_backup_active: Active
        ext_backup_buffer: Buffer (s)
        ext_backup_threshold_pos: Positive Threshold MW
        ext_backup_threshold_neg: Negative Threshold MW
        ext_backup_nomination_threshold_pos: Positive Threshold % of Nomination
        ext_backup_nomination_threshold_neg: Negative Threshold % of Nomination
        ext_backup_increment_pos: Rounding steps positive (MW)
        ext_backup_increment_neg: Rounding steps negative (MW)

        dead_band_pos_mhz: Dead band positive (mHz)
        dead_band_neg_mhz: Dead band negative (mHz)
        max_frequency_deviation_pos_mhz: Max frequency deviation positive (mHz)
        max_frequency_deviation_neg_mhz: Max frequency deviation negative (mHz)

        # generic dg attributes
        export_entrader: Export Entrader
        entrader_treshold_update: Treshold Update (MW)
        entrader_lead_time_update: Lead Time Update (min)
        entrader_upload_folder: Upload Folder

        has_exclusive_behaviour: Exclusive Behaviour
        has_nomination: Nomination
        has_preceding_basepoint: Preceding Basepoint

        asset_activation_type: Asset Activation Type
        aat_ar_flsp_dead_band_pos: Dead Band Pos (Hz)
        aat_ar_flsp_dead_band_neg: Dead Band Neg (Hz)
        aat_ar_flsp_max_frequency_deviation_pos: Frequency Deviation Pos (Hz)
        aat_ar_flsp_max_frequency_deviation_neg: Frequency Deviation Neg (Hz)
        aat_ar_flsp_high_knee_joint: High Knee Joint (Hz)
        aat_ar_flsp_low_knee_joint: High Knee Joint (Hz)

        scm_dc_target_state_of_charge_low: Target State of Charge Low (%)
        scm_dc_target_state_of_charge_high: Target State of Charge High (%)
        scm_dc_target_state_of_charge_both: Target State of Charge Both (%)
        scm_dc_foot_room: Foot Room (%)
        scm_dc_head_room: Head Room (%)
        scm_dc_ramp_rate_limit: Ramp Rate Limit (%)
        scm_dc_energy_reserve: Energy Reserve (seconds)
        scm_dc_min_energy_recovery: Min. Energy Recovery (seconds)
        scm_dc_dead_band_factor: Dead Band Factor (%)
        scm_vb_dc_delivery_duration: Delivery Duration (sec)
        scm_vb_dc_delivery_duration_buffer: Delivery Duration Buffer (%)
        scm_vb_dc_min_energy_recovery: Min Energy Recovery (%)
        scm_vb_dm_delivery_duration: Delivery Duration (sec)
        scm_vb_dm_delivery_duration_buffer: Delivery Duration Buffer (%)
        scm_vb_dm_min_energy_recovery: Min Energy Recovery (%)
        scm_vb_dr_delivery_duration: Delivery Duration (sec)
        scm_vb_dr_delivery_duration_buffer: Delivery Duration Buffer (%)
        scm_vb_dr_min_energy_recovery: Min Energy Recovery (%)
        scm_fcr_lower_soc_limit: Lower SoC Limit (%)
        scm_fcr_upper_soc_limit: Upper SoC Limit (%)
        scm_opt_sp_duration_minutes: Settlement Period Duration (minutes)

        ads_start_using_assets_only_when_at_basepoint: Start using assets only when at basepoint
        ads_strategy: Strategy
        ads_strategy_by_price_preserve_current_dispatches: Preserve current dispatches
        ads_strategy_by_price_and_soc_limit_battery_power_window: Battery Dispatch Window (seconds)
        ads_strategy_pro_rata_symmetric: Symmetric
        ads_strategy_on_off_signal": "Signal used for on/off"

        cross_dg_links: Cross DG links
        cdl_cross_plan_propagation_frequency: Cross plan propagation frequency

        dc_check_interval_seconds: Check Interval (seconds)
        dc_over_delivery_excess_compensation_factor: Over delivery excess compensation factor
        dc_over_delivery_compensation_limit_factor: Over delivery compensation limit factor
        dc_under_delivery_excess_compensation_factor: Under delivery excess compensation factor
        dc_under_delivery_compensation_limit_factor: Under delivery compensation limit factor
        dc_compensation_resolution_kw: Compensation resolution (kW)
        dc_is_at_setpoint_upper_tolerance_factor: "At setpoint: upper tolerance factor"
        dc_is_at_setpoint_upper_tolerance_minimum_kw: "At setpoint: upper tolerance min (kW)"
        dc_is_at_setpoint_lower_tolerance_factor: "At setpoint: lower tolerance factor"
        dc_is_at_setpoint_lower_tolerance_minimum_kw: "At setpoint: lower tolerance min (kW)"

        dg_activation_type: DG Activation Type
        dispatch_commands: Dispatch commands
        dispatch_source: Dispatch Source
        ds_nominated_volume_activation_factor: Nominated Volume Activation Factor
        ds_nominated_volume_symmetric_activation: Nominated Volume Symetric Activation
        ds_ui_edg_schedule: UI EDG Schedule
        ds_residual_shape_positive_threshold: Positive Threshold
        ds_residual_shape_negative_threshold: Negative Threshold
        ds_residual_shape_window: Window
        ds_price_trigger_window: Window (seconds)
        ds_price_trigger_price_type: Price Type
        ds_price_trigger_settlement_period: Settlement Period
        ds_price_trigger_price_threshold_neg: Negative Price Threshold
        ds_price_trigger_price_threshold_pos: Positive Price Threshold
        ds_price_trigger_price_expiration_seconds: Price Expiration Time (sec)
        ds_nlafrr_bleeding_time_seconds: Bleeding Time (seconds)

        execution_plan_window_seconds: Execution Plan Window (seconds)
        execution_plan_frequency_seconds: Execution Plan Frequency (seconds)

        net_extend_before_nomination_seconds: Extend before nomination (seconds)
        net_overlap_nominations_seconds: Overlap nomination (seconds)
        net_stop_before_end_of_nomination_seconds: Stop before end of nomination (seconds)

        notifications: Notifications
        ntf_flex_too_low_threshold_factor: Flex too low Threshold factor
        ntf_flex_too_low_threshold_buffer_seconds: Flex too low Threshold buffer (seconds)
        ntf_over_delivery_negative_threshold_kw: Over Delivery Negative threshold (kW)
        ntf_over_delivery_negative_delay_seconds: Over Delivery Negative delay (seconds)
        ntf_over_delivery_positive_threshold_kw: Over Delivery Positive threshold (kW)
        ntf_over_delivery_positive_delay_seconds: Over Delivery Positive delay (seconds)
        ntf_setpoint_not_reached_reach_setpoint_in_seconds: Setpoint Not Reached Reach setpoint (seconds)
        ntf_setpoint_not_reached_is_at_setpoint_detection_upper_tolerance_factor: Setpoint Not Reached Upper tolerance factor
        ntf_setpoint_not_reached_is_at_setpoint_detection_upper_tolerance_minimum_kw: Setpoint Not Reached Upper Tolerance Minimum (kW)
        ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_factor: Setpoint Not Reached Lower tolerance factor
        ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_minimum_kw: Setpoint Not Reached Lower tolerance minimum (kW)
        ntf_setpoint_reachable_is_at_setpoint_detection_upper_tolerance_factor: Setpoint Reachable Upper tolerance factor
        ntf_setpoint_reachable_is_at_setpoint_detection_upper_tolerance_minimum_kw: Setpoint Reachable Upper Tolerance Minimum (kW)
        ntf_setpoint_reachable_is_at_setpoint_detection_lower_tolerance_factor: Setpoint Reachable Lower tolerance factor
        ntf_setpoint_reachable_is_at_setpoint_detection_lower_tolerance_minimum_kw: Setpoint Reachable Lower tolerance minimum (kW)

        periodicity_dg_aggregations_seconds: Periodicity DG aggregations (seconds)
        periodicity_output_signal_write_seconds: Periodicity Output signal write (seconds)

        ramp_adjustment_strategy: Ramp adjustment strategy
        redispatch_triggers: Redispatch triggers
        rt_asset_available_flex_change_threshold_kw: "Av. flex change: Threshold (kW)"
        rt_asset_available_flex_change_since_activation_factor: "Av. flex change since activation: Factor"
        rt_dg_total_deviation_buffer_kw: "Total dev. Buffer (kW)"
        rt_dg_total_deviation_suppress_redispatch_seconds: "Total dev. Suppress redispatch (seconds)"

        setpoint_validation_reaction_time_seconds: Setpoint Validation Reaction time (seconds)
        setpoint_validation_cap_by_nomination: Setpoint Validation Cap by nomination
        setpoint_validation_minimum_duration_of_dispatch_seconds: Setpoint Validation Minimum duration of dispatch (seconds)
        setpoint_validation_nomination_interval_validation: Setpoint Validation Nomination interval validation
        setpoint_validation_quantization_filter_resolution_kw: Setpoint Validation Quantization resolution (kW)
        setpoint_validation_quantization_filter_quantization_duration_seconds: Setpoint Validation Quantization duration (seconds)

        signals_output_signals: Output Signals
        signals_sub_pools_values_on: Subpool Values On
        signals_sub_pools_values_off: Subpool Values Off
        signals_dso_for_signals: DSO for signals
        signals_heart_beat_mirror: Heart beat mirror

        dpb_min_power_for_dispatch_discharge: Min Power For Dispatch Discharge
        dpb_min_power_for_dispatch_charge: Min Power For Dispatch Charge

        #dlm params
        grouping_rule: Load Management Scheme
        nominal_site_current: Import Restrictions - Nominal Site Current (A)
        reduction_factor: Import Restrictions - Reduction Factor (1=100%)
        dlm_group_reduced_id: DLM Group ID's - Reduced
        dlm_group_blocked_id: DLM Group ID's - Blocked
        control_windows: Control Time Windows
        session_duration_threshold_minutes: Minimum Charging Time (minutes)
        session_energy_threshold_watt_hour: Minimum Energy (Wh)
        grouping_rule_both_thresholds: Conjunction between left and right parameter for assigning chargers to dlm groups
        min_average_charged_energy_factor: Minimum Proportion of Average Energy (1=100%)
        min_average_charging_duration_factor: Minimum Proportion of Average Charging Time (1=100%)

      distributed_unit:
        created: Created at
        deleted: Deleted
        end_time: End
        energy_direction: Energy Direction
        energy_price: Energy Price
        capacity_price: Capacity Price
        flex_volume: Flex Capacity
        interval: Interval
        skip_start_time_validations: Allow modifications with immediate effect and/or adding a new nomination in the past
        start_time: Start
        used_for_asset_price: Use for Asset Price
      distributed_unit_source:
        asset_dg_allocations: Allocations
        created: Performed on
        user: User
        content_type: Type
        content: Content
        success: Successfully imported
        warnings: Warnings
        errors: Errors
        validation_result: Validation
        skip_start_time_validations: Allow modifications with immediate effect and/or adding a new nomination in the past
        nothing_imported: No nomination has been uploaded as the file only contains a header or is empty.
      distributed_unit_source/success:
        "true": Successfully imported
        "false": Failure
      distributed_unit_source/content_type:
        file_import: Nomination File
        ui_update: Form
      subpool:
        assets: Assets
        dispatch_groups: Dispatch Groups
        implicit: Implicit
        name: Name
        signals: Signals
      rollup_family:
        name: Rollup Grouping Name
      rollup_regeneration:
        user: Triggered by
        rollup_types: Rollup Metrics
        interval: Interval
        status: Status
        assets: Assets
        rollup_families: Rollup Groupings
        dispatch_groups: Dispatch Groups
        retries: Retries
        error_details: Errors
      rollup_email_sendings_configuration:
        rollup_notifications_email_recipients: Rollup Notifications Recipients
        rollup_notifications_active: Rollup Notifications Active
      rollup_error:
        created: 'On'
        description: Description
      nominateable_volume:
        created: Performed on
        user: User
        delivery_date: Delivery Date

      generic_steering_config:
        model_name: 'Steering Type'
        configuration_name: 'Steering Type Configuration'
        name: 'Adapter Name'
        basepoint: 'Basepoint'
        schedule: 'Schedule'
        flex: 'Flex'
        setpoint: 'Setpoint'
        setpoint_relativity: 'Setpoint'
        asset_sp: 'Default Setpoint'
        has_setpoint_feedback: 'Setpoint Feedback'
        setpoint_frequency_seconds: 'Setpoint Frequency (s)'
        setpoint_unit: '  '
        has_setpoint_lock: '   '
        setpoint_interval: '   '
        has_dispatched_deviated: 'Dispatch Deviated'
        has_heartbeat_vpp: 'Heartbeat VPP Comm Alive'
        heartbeat_vpp_frequency_seconds: 'Frequency (s)'
        heartbeat_vpp_authorization: 'Read / Write Authorization'
        has_heartbeat_asset: 'Heartbeat Asset Comm Alive'
        heartbeat_asset_frequency_seconds: 'Frequency (s)'
        heartbeat_asset_authorization: 'Read / Write Authorization'
        allow_steering_by_schedule: 'Allow steering according to schedule'
        e_unknown_error: 'Error'
        e_setpoint_interval_on_off_requires_boolean: 'ON/OFF Setpoint requires Boolean'
        e_name_can_not_be_empty: 'Name is required'
        e_name_already_exists: 'A configuration with that name already exists'
        e_boolean_setpoint_requires_no_both_flex: 'Boolean Setpoint requires either Pos or Neg Only Flex'
        e_boolean_setpoint_requires_onoff: 'Boolean Setpoint requires ON/OFF Setpoint'
        e_absolute_setpoint_with_both_flex_requires_lock: 'Absolute setpoint with Flex in Both direction is only possible if Lock is activated'
        e_steering_type_is_in_use: 'Steering type is in use'
        basepoints:
          HasBasepointAndPbp: 'Preceding Basepoint from asset'
          HasBasepoint: 'Basepoint from asset'
          NoBasepoint: 'No Basepoint(-> Avg of Pist)'
        schedules:
          Production: 'Prod'
          Flexibility: 'Flex'
        flexes:
          Both: 'Both'
          Positive: 'Pos Only'
          Negative: 'Neg Only'
        setpoint_relativities:
          Relative: 'Relative'
          Absolute: 'Absolute'
        setpoint_units:
          Kilowatts: 'kW'
          Boolean: 'Boolean'
        setpoint_intervals:
          Continuous: 'Continuous'
          OnOff: 'ON/OFF'
          Steps: 'Steps (2 or more)'
        heartbeat_authorizations:
          Read: 'Read Only'
          Write: 'Write Only'
          ReadWrite: 'R/W'
        has_setpoint_locks:
          'true': 'Setpoint activation bit'
          'false': 'No Setpoint activation bit'
        yes_nos:
          'true': 'Yes'
          'false': 'No'
        y_ns:
          'true': 'Y'
          'false': 'N'
        feedback: 'Feedback'
        frequency_s: 'Frequency (s)'
        confirm_delete: 'Are you sure?'
        default_absolute_setpoints:
          ImposedPMax: 'PMax'
          ImposedPMin: 'PMin'
          PNorm: 'PNorm'

      auction_config:
        name: Name
        auction_definition_interval: Validity Interval
        time_zone: Time Zone
        dispatch_group_ids: Dispatch Groups
        bidding_method: Bidding Method
        bidding_method_market: Market
        assets: Assets
        frequency_time_minutes: Frequency Time (minutes)
        delivery_interval: Delivery Interval
        offers_time_block: Offer block
        bidding_direction: Direction
        rounding_digits: Volume rounding
        minimum_mw_volume_for1_bid: Minimum MW volume for 1 bid
        minimum_mw_volume: Minimum MW volume
        price_type: Price required
        energy_price_rounding_digits: Energy price rounding
        capacity_price_rounding_digits: Capacity price rounding
        swing_limit: Swing Limit
        swing_limit_value: Swing Limit Value
        optimization_horizon_change: Optimization Horizon Change
        optimization_horizon_change_days_before_delivery: Days before delivery interval
        optimization_horizon_change_time_hours: Time
        optimization_horizon_change_time_minutes: Time
        market_auction_start: Auction Start
        market_auction_start_days_before_delivery: Days before delivery interval
        market_auction_start_time_hours: Time
        market_auction_start_time_minutes: Time
        market_auction_start_minutes: Minutes
        market_auction_end: Auction End
        market_auction_end_days_before_delivery: Days before delivery interval
        market_auction_end_time_hours: Time
        market_auction_end_time_minutes: Time
        market_auction_end_minutes: Minutes
        internal_bidding_input_format: Bidding format
        internal_auction_start: Auction Start
        internal_auction_start_days_before_delivery: Days before delivery interval
        internal_auction_start_time_hours: Time
        internal_auction_start_time_minutes: Time
        internal_auction_start_minutes: Minutes
        internal_auction_end: Auction End
        internal_auction_end_days_before_delivery: Days before delivery interval
        internal_auction_end_time_hours: Time
        internal_auction_end_time_minutes: Time
        internal_auction_end_minutes: Minutes
        customer_bidding_input_format: Bidding format
        customer_auction_start: Auction Start
        customer_auction_start_days_before_delivery: Days before delivery interval
        customer_auction_start_local_time_hours: Time
        customer_auction_start_local_time_minutes: Time
        customer_auction_start_minutes: Minutes
        customer_auction_minimum_mw_volume_for1_bid: Minimum MW volume for 1 bid
        customer_auction_end: Auction End
        customer_auction_end_days_before_delivery: Days before delivery interval
        customer_auction_end_local_time_hours: Time
        customer_auction_end_local_time_minutes: Time
        customer_auction_end_minutes: Minutes
        optimizer_bod_frequency_minutes: "Frequency (Minuten)"
        optimizer_bod_computed_sp: "Computed SPs"
        optimizer_bod_delta_for_submission: "Delta for submission (£)"
        optimizer_bod_delta_for_undo: "Undo delta (£)"
        api_auction_bidding: Bidding API
        auction_system: Auction System
        auction_system_upload_behaviour: Behaviour
        auction_system_upload_market_time: Minutes before market tender end
        auction_system_upload_number_of_retries: Number of retries
        auction_system_upload_seconds_between_retries: Seconds between retries
        auction_system_download_behaviour: Market results download
        auction_system_download_market_time: Minutes after market tender end
        auction_system_download_number_of_retries: Number of retries
        auction_system_download_seconds_between_retries: Seconds between retries
        auction_system_ui_trader_look_ahead_hours: Trader Look Ahead Time (hours)
        auction_system_ui_price_grouping_step: Price Grouping Step Size (£/MWh)
        auction_system_ui_bid_expiry_warning_minutes: Bid Expiry Warning (minutes)
        auction_system_ui_currency: Currency
        auction_system_ui_site_id: Site ID
        auction_system_ui_for_battery_uk_intraday_asset_id: Asset ID
        auction_system_ui_for_battery_uk_intraday_portfolio_id: Portfolio ID
        auction_system_ui_for_battery_uk_intraday_export_entrader: Export Entrader
        auction_system_ui_for_battery_uk_intraday_upload_folder: Upload Folder
        auction_system_uk_vatp_smart_volume_strategy_template_id: Smart Volume Strategy Template ID
        auction_system_uk_vatp_hidden_iceberg_strategy_template_id: Hidden Iceberg Strategy Template ID
        auction_system_uk_vatp_smart_iceberg_strategy_template_id: Smart Iceberg Strategy Template ID
        auction_system_de_vatp_smart_volume_strategy_template_id: Smart Volume Strategy Template ID
        auction_system_de_vatp_hidden_iceberg_strategy_template_id: Hidden Iceberg Strategy Template ID
        auction_system_de_vatp_smart_iceberg_strategy_template_id: Smart Iceberg Strategy Template ID
        create_nominations: Create nomination out of result?
        create_allocations: Create allocation out of result?
        allocation_start_extension_seconds: Allocation Start Extension (seconds)
        allocation_end_extension_seconds: Allocation End Extension (seconds)
        allocation_end_extension_from_ramp_rate: Allocation End Extension From Ramp Rate

    errors:
      messages:
        start_date_after_end_date: The start date must be before the end date.
        start_date_is_in_the_past: The start date can not be in the past. Please correct the start date.
        end_date_is_in_the_past: The end date can not be in the past.
        cannot_change_historic_data: Cannot change historic data.
        cannot_delete_historic_data: Cannot delete historic data.
        cannot_change_start_date: Cannot change start date.
        overlaps_existing_balancing_group: Overlaps an existing balancing group.
        unique_implicit_subpool_per_dg: Dispatch groups %{dg_names} already have an implicit Subpool.
        session_duration_threshold_minutes: Minimum Charging Time cannot cover more than 24 hours.
        control_windows: "The control time windows must be given in format HH:MM."
        marketdata_api_error: "Marketdata API returned the following error: %{error}"
      models:
        dispatch_group:
          attributes:
            nomination_tool_enabled:
              taken: "DG #%{dg_id} %{dg_name} is already used for nomination for this market and TSO."
    models:
      asset:
        one: Asset
        other: Assets
      asset_dg_allocation_source: Allocation Source
      balancing_group_dg: Balancing Group
      db/db_bg_name_supplier: Supplier Balancing Group
      db/db_bg_name_collecting: Collecting Balancing Group
      db/db_bg_name_internal: E.ON Internal
      db/db_bg_name_third_party: Third Party
      dispatch_group:
        one: Dispatch Group
        other: Dispatch Groups
      distributed_unit: Nomination
      market: Market
      product: Product
      subpool: Subpool
      tso: TSO
      rollup_type: Rollup Metric
      rollup_family: Rollup Grouping
      rollup_email_sendings_configuration: Automatic Rollup Emailings Configuration
      signal_list:
        one: Signal List
        other: Signal Lists

  application:
    meta:
      title: VPP Management
    chromeframe:
      outdated_browser_html: You are using an <strong>outdated</strong> browser. Please <a href="http://browsehappy.com/">upgrade your browser</a> or <a href="http://www.google.com/chromeframe/?redirect=true">activate Google Chrome Frame</a> to improve your experience.
    header:
      welcome_message: "%{name}"
      welcome_info: Your last activity was %{time} ago
      nav_customers: Customers
      nav_notifications: Notifications
      nav_user_menu: User Menu
    tech_support:
      help: Help
  navigation:
    breadcrumb:
      add: Add new %{model_name}
      edit: Edit
      upload: Upload new %{model_name}
      report_email_sendings:
        index:
          title: Automatic Report Emailings
      asset_dg_allocation:
        assets:
          title: Assets
        index:
          title: Dispatch Groups
      asset_dg_allocation_sources:
        index:
          title: Track History
      distributed_units:
        index:
          title: "DUs (%{count})"
      distributed_unit_sources:
        index:
          title: Track History
      reports:
        index:
          title: Reports
    main:
      dashboard: Dashboard
      allocations: Allocations
      asset_dg_allocation: DG Allocations
      asset_dg_allocation_assets: Asset Allocations
      asset_dg_allocation_sources: Allocation Track History
      dispatch_groups: DGs
      distributed_units: DU List
      nominations: Nominations
      reports: Reports
      report_email_sendings: Automatic Emails
      scheduling_reports: Scheduling Reports
      subpools: Subpools
      tso_schedule: "TSO & Control Reports"
      asset_reports: Asset Reports
      configurations: Configuration
      asset_configurations: Asset Configuration
      balancing_group_names: Balancing Groups
      rollup_management: Rollup Management
      rollup_management_asset: Asset Level
      rollup_management_dg: Dispatch Group Level
      rollup_errors: Rollup Errors
      generic_steering_configs: 'Steering Type'
      event_notifications: Notifications
    nominations:
      auction_result_upload: Upload Auction Results
      distributed_units: Manual Nominations
      distributed_unit_sources: Track History
      nominateable_volumes: Prepare Auctions
      track_history_nominations: Nomination
      track_history_auctions: Auction
    subheader:
      edit: Edit %{model_name}
  forms:
    actions:
      allocate: Allocate
      cancel: Cancel
      deallocate: Deallocate
      download: Download
      show: Show
      upload: Upload
      add: Add
      edit: Edit
      save: Save
    select:
      prompt: Choose ...
      prompt_all: 'All %{model_name}'
      prompt_model_name: 'Please select %{model_name}'
    csv_format: Format
    from: From
    to: To
    values_for: Values for
    wait: Please Wait ...

  lists:
    actions:
      copy: Copy
      delete: Delete
      confirm_delete: Are you sure ?
      edit: Edit
      report: Report
      reject: Reject
      submit: Submit
      view: View
      ok: OK
    bulk_actions:
      choose: Choose an action ...
      delete: Delete
      submit: Submit to Trader
      selected_items_count_html: selected items (<span class='count'>%{count}</span>)

  allocations:
    manual_allocation_title: Manual Allocation
    visualisation_title: Allocation Visualisation
    automatic_allocation_title: Automatic Allocation

  balancing_group_names:
    index:
      manage_configuration: Edit
    manage:
      list_configuration: Back to list
    scheduling_title: Scheduling Balancing Group

  automatic_allocations:
    list:
      no_automatic_allocation: No Automatic Allocation
      start: Start
      fallback_start: Fallback Start
      end: End
      type: Type
      status: Status
      import_status: Import Result
      dispatch_groups: Dispatch Groups
      triggers: Triggers
    show:
      import_result: Import Result
      no_import_result: Nothing found
    types:
      fallback: Fallback
      standard: Standard
    status:
      types:
        started: Started
        success: Success
        fallback: Fallback
        data_preparation_failure: Data Preparation Failure
        failure: Failure
        allocation_not_accepted: Allocation Not Accepted
        writing_allocations_failure: Allocations could not be stored
        unknown_failure: Unknown Failure
    triggers:
      types:
        allocation_type: A new allocation has been entered manually
        computation_failed_type: Previous allocation run has failed
        nomination_type: A new nomination has been entered
        flex_type: Not enough flex available compared to the nominated
        time_type: Quarterly Allocation
        asset_back_from_fault_type: Asset comes back from fault

  dispatch_groups:
    alarms_title: Alarm Signals
    balancing_groups_title: Balancing Groups
    compensation_title: Compensation
    general_data_title: Dispatch Groups Details
    is_at_setpoint_detection_title: Is-At-Setpoint detection
    merlin_dispatch_title: MeRLin Dispatch
    nomination_tool_title: Trading Nomination Interface
    prl_title: Pro rata activation (PRL/FFRD)
    rcc_title: RCC (German Control Room) Alarms
    setpoint_reached_title: Setpoint-Reached detection
    sally_setpoint_title: Automatic (Sally) Setpoint
    signals_title: Generated Signals
    spot_optimization_title: Spot Optimisation
    ui_alerts_title: VPP Ops Alerts
    rollups_enabled: Enable Rollups
    automatic_allocation: Automatic Allocation
    ext_backup: External Backup
    ext_backup_info_html: "<b> External backup will be activated if the functionality is marked as active and if during more than the buffer time the following assertion is true: <br/> Available flex without external backup < Total nominated capacity - MAX(Threshold MW; Threshold % of nomination * Total nominated capacity) </b>"
    individual_alarms_enabled_info_html: An event will be created if the flex available is lower than the threshold percentage of the traded volume over the duration defined in seconds.
    individual_alarm_overdelivery_pos_enabled_info_html: During a positive dispatch, an event will be created if the dispatch group overdelivers longer than the accepted time in seconds by more than the entered MW.
    individual_alarm_overdelivery_neg_enabled_info_html: During a negative dispatch, an event will be created if the dispatch group overdelivers longer than the accepted time in seconds by more than the entered MW.
    parameters_title: Local steering parameters
    parameters_info_html: <b>If an asset can be configured remotely and is allocated to the given dispatch group then the parameters will be sent automatically to the local box.</b>

  distributed_unit:
    warning_no_matching_bid_for_auction_result: "There was no bids uploaded in the system matching these results"
    warning_no_matching_auction_result_for_bid: "There is no result for the following bids"

  distributed_units:
    manual_nomination_title: Manual Nomination
    visualisation_title: Nomination Visualisation

  nominateable_volumes:
    form:
      date: Delivery Date
      market: Product
      product_intervals: Time Slices
      product_intervals_all: All Time Slices
      submit: Prepare Auctions
    actions:
      download_meta_data: Meta-Data
      download_volumes: Auction

  report_email_sendings:
    id: '#'
    report_type: Report
    report_date: Date
    active: Active
    success: Success
    email_to: To
    error: Error
    report_file_name: Name
    has_attachment: Att.
    report_parameters: Params
    title: Automatic Emails for last days
    created: At
    configuration:
      title: Configuration
    index:
      edit_configuration: Configuration

  reports:
    messages:
      invalid_collecting_bgs: "Invalid Collecting BG: %{bgs}. Exactly one must be defined."
      invalid_supplier_bgs: "Invalid Supplier BG: %{bgs}. Exactly one must be defined."
      missing_bgs: "Some assets that delivered flexibility are not assigned to any balancing group: %{asset_ids}."
      multiple_bgs: "Some assets that delivered flexibility were assigned to multiple balancing groups: %{asset_ids}."
      no_asset_with_balancing_group: "No assets for the selected balancing group %{balancing_group_id}."
      no_flex_delivered: "No %{energy_direction} flexiblity delivered in %{tso} for %{market} on %{date}."
      no_flex_delivered_balancing: "No flexiblity delivered in %{tso} for %{market} on %{date}."
      no_flex_delivered_balancing_third_party: "No flexiblity delivered in %{tso} on %{date} between %{scheduling_balancing_group_id} and %{collecting_balancing_group_id}"
      no_flex_delivered_no_schedule_balancing: "No flexiblity delivered and no schedule in %{tso} for %{market} on %{date}."
      no_setpoint_dispatched: "No %{energy_direction} setpoints dispatched in %{tso} for %{market} on %{date}."
      no_schedule: "Missing schedule at %{time} for %{tso} for %{market}."

  report_email_sendings_configurations:
    form:
      one_per_line: One per row

  scheduling_reports:
    form:
      date: Date
      energy_direction: Energy Direction
      action:
        regenerate_scheduling_balancing_groups_third_party_reports:  Send Scheduling Third Parties
        scheduling_control: Scheduling Control
        scheduling_edg: Scheduling to EDG
        scheduling_egc: Scheduling to UGC
        scheduling_third_party: Scheduling Third Parties
        regenerate_bg_asset_activations_reports: Send BK6-17-046 Reports

  asset_rollups:
    index:
      title_new_rollup_family: Define new Rollup Grouping
      title_rollup_regeneration: Rollup regeneration
      title_rollup_management: Rollup Management Visualisation
    form_rollup_family:
      rollup_family_created: Rollup Grouping Created
      rollup_family_disabled: Rollup Grouping Disabled
      rollup_family_disable_error: The data aggregation per asset based on %{rollup_family} is still used in live contracts from customers %{customers}. Change the data aggregation entered in their contract before deleting it from the configuration.
    form_generate_rollups:
      select_rollup_type: Select Rollup Metric
      select_item_type: Select Type
      select_asset: Select Asset
      select_rollup_family: Select Rollup Group
      select_interval: Select Interval
      btn_regenerate: Regenerate
      please_select_rollup: Please Select Rollup Metric
      please_select_asset: Please Select Asset
      please_select_rollup_family: Please Select Rollup Group
      rollup_regeneration_ok: Rollups regeneration started
      rollup_regeneration_error: Error while generating rollups

  dispatch_group_rollups:
    index:
      title_rollup_regeneration: Rollup regeneration
      title_rollup_management: Rollup Management Visualisation
    form_generate_rollups:
      select_rollup_type: Select Rollup Metric
      select_dispatch_group: Select Dispatch Group
      select_interval: Select Interval
      btn_regenerate: Regenerate
      please_select_rollup: Please Select Rollup Metric
      please_select_dispatch_group: Please Select Dispatch Group
      rollup_regeneration_ok: Rollups regeneration started
      rollup_regeneration_error: Error while generating rollups

  rollup_regenerations:
    track_history: Track History
    index:
      no_rollup_generations: No rollup renegeneration
    show:
      title_details: Rollup Regeneration Details
  rollup_errors:
    index:
      no_rollup_errors: No rollup error
    show:
      title_details: Rollup Error Details

  event_notifications:
    index:
      dg_heartbeat_mirror_failure: TSO line Heartbeat failure
      dg_heartbeat_mirror_failure_info_html: "A notification is sent if a communication issue on at least one TSO line <br/>between the VPP and the TSO is detected."
      bidding_api_failure: Issues by bids upload on Regelleistung.net
      bidding_api_failure_info_html: In case of an issue during the upload of the bids to the Regelleistung.net website, a message with the bids attached as an xml is sent.
      bidding_api_success: Successful bids upload to Regelleistung.net
      bidding_api_success_info_html: A message is sent to inform about the successful upload of bids to the market.
      auction_download_failure: Issues by bids download
      auction_download_failure_info_html: If no result is provided for all the bids stored in the system then a message is sent.
      auction_download_success: Successful download of the bids
      auction_download_success_info_html: If the auction results are downloaded successfully then a message, with the auction results attached, is sent to the recipients.
      auction_download_results_exceed_bids: Auction results with higher volumes than bids
      auction_download_results_exceed_bids_info_html: If the auction results are downloaded successfully but their volumes exceed the already bid volumes, then a message is sent to the recipients.
      dg_over_delivery: Pool overdelivery
      dg_over_delivery_info_html: A notification is sent to the recipients when a dispatch group overdelivery event is created. The overdelivery event is defined in the dispatch group configuration page.
      dg_available_flex_too_low: VPP pool issues
      dg_available_flex_too_low_info_html: Event triggered when the per-direction available flex of a DG is below the configured threshold of the nominated flex.
      dg_merlin_issue: MeRLin issue
      dg_merlin_issue_info_html: In case of an issue with the Merlin Client a notification will be sent to the recipients.
      v2g_optimization_submission_failure: V2G-EE Optimisation Failures
      v2g_optimization_submission_failure_info_html: A notification is sent to the recipients when the V2G optimisation process fails.
      dlm_asset_fault: A notification is generated when a DLM asset enters in fault while it is allocated to a DG.
      dlm_execution_failure: A notification is generated when a DLM related error occurs.
      virta_dlm_api_failure: A notification is generated when communication with the Virta DLM API cannot be established or is corrupted.
      virta_admin_api_failure: A notification is generated when communication with the Virta Admin API cannot be established or is corrupted.
    form:
      email: E-Mail
      call: Call
      sms: SMS

  errors:
    format:  "%{attribute}: %{message}"
    messages:
      unable_to_get_allocated_flex: "Unable to get flex data: %{reason}"
      unable_to_get_allocations: "Unable to get allocations: %{reason}"
      unable_to_get_nominateable_volumes: "Nominations could not be prepared: %{reason}"
      unable_to_post_allocations: "Unable to post allocations: %{reason}"
      unable_to_upload_allocations_file: "File can not be uploaded.\nPlease make sure that you are uploading a valid CSV file, in the expected format.\n \n%{reason}"
      unable_to_post_dus: "Unable to post DUs: %{reason}"
      unable_to_upload_dus_file: "File can not be uploaded.\nPlease make sure that you are uploading a valid CSV file, in the expected format.\n \n%{reason}"
      unable_to_upload_auction_results_file: "File can not be uploaded.\nPlease make sure that you are uploading a valid Excel file, in the expected format.\n \n%{reason}"
      unable_to_delete_dg: "The %{dg_name} can't be deleted because it is used in Subpools %{subpools}. It must be removed from the Subpools first."
      unable_to_delete_dg_in_use: "The %{dg_name} can't be deleted because it is in use."
      phone_number_invalid: "The phone number must start by + and the country indicator and it must contain only digits"
      invalid_tender_config: "Invalid tender config."
      dispatch_groups_same_market: "All dispatch groups of a tender need to belong to the same market."
      cannot_create_nomination_for_spot_optimization: "Cannot create nominations for Spot Optimization"
      duplicate_tender_with_same_bidding_method_and_market: There is already a tender with the same bidding method and market and overlapping the Validity Interval.
      duplicate_tender_with_same_bidding_method_and_market_site: There is already a tender with the same bidding method, market and site ID and overlapping the Validity Interval.
      opti_result_file_invalid: "Invalid data at line(s) %{lines}"
      opti_result_file_invalid_header: "Invalid/Missing CSV header(s) %{header}"
      opti_result_file_invalid_not_enough_intervals: The file must contain at least two lines.
      opti_result_file_invalid_timezone: The file must have the datimes records in the same timezone.
      opti_result_file_invalid_settlement_interval: Invalid settlement interval. The settlement interval must be 30 minutes.
      opti_result_file_missing_allocations: Missing allocations for the date interval %{from_time} to %{to_time}
      opti_result_file_invalid_asset: Asset %{asset_id} not found
      opti_result_file_invalid_soc: Invalid soc at lines %{lines}. The value must be between %{low_val} and %{high_val}
      opti_result_file_invalid_activation_dc_pos: Invalid activation_dc_pos at lines %{lines}. The value must be between %{low_val} and %{high_val}
      opti_result_file_invalid_activation_dc_neg: Invalid activation_dc_neg at lines %{lines}. The value must be between %{low_val} and %{high_val}
      opti_result_file_invalid_activation_dm_pos: Invalid activation_dm_pos at lines %{lines}. The value must be between %{low_val} and %{high_val}
      opti_result_file_invalid_activation_dm_neg: Invalid activation_dm_neg at lines %{lines}. The value must be between %{low_val} and %{high_val}
      opti_result_file_invalid_activation_dr_pos: Invalid activation_dr_pos at lines %{lines}. The value must be between %{low_val} and %{high_val}
      opti_result_file_invalid_activation_dr_neg: Invalid activation_dr_neg at lines %{lines}. The value must be between %{low_val} and %{high_val}
      opti_result_file_invalid_available_char_power: Invalid available_char_power at lines %{lines}. The value must be between %{low_val} and %{high_val}
      opti_result_file_invalid_available_dischar_power: Invalid available_dischar_power at lines %{lines}. The value must be between %{low_val} and %{high_val}
      opti_result_upload: "The format of the file isn't recognized. Please check and upload the file again."
      perf_data_asset_not_supported: The asset is not supported.
      perf_data_invalid_file_extension: Invalid File extension. Supported files are %{supported_extension}.
      perf_data_invalid_file_name: Invalid File name. Supported file name format is %{name_format}.
      perf_data_invalid_times: Invalid timestamps. Start time needs to be before the end time and the difference between must be multiple of 1h.
      bod_file_upload: "The format of the file isn't recognized. Please check and upload the file again."
      margin_data_file_upload: "The format of the file isn't recognized. Please check and upload the file again."
      invalid_prices_for_tender: "Invalid prices file for Tender %{tender_name} in file %{file_name}: %{errors}"
      price_forecast_file_invalid_header: "Invalid/Missing CSV header(s) %{header} in file %{file_name}"
      err_missing_price_forecast: "Missing prices in prices forecast file %{file_name}"
      margin_data_file_invalid_header: "Invalid/Missing CSV header(s) %{header} in file %{file_name}"
      err_missing_margin_data: "Missing margin data in file %{file_name}"
      err_invalid_margin_data: "Invalid margin data at line(s) %{lines}"
      invalid_uuid: "Invalid UUID"

  time:
    formats:
      du_start_time: ! '%d %b %H:%M %Z'

  asset_configuration:
    type: Type
    title: "Asset Configuration"
    box_type_configuration: "Box Type Configuration"
    signal_type_configuration: "Signal Type Configuration"
    box_type: "Box Type / Steering Type"
    protocol: "Protocol"
    port: "Port"
    vpn_type: "VPN Type"
    contact_person_email: "Contact Person Email"
    contact_person_name: "Contact Person Name"
    contact_person_phone: "Contact Person Phone"
    contact_person_first_name: "Contact Person First Name"
    contact_person_last_name: "Contact Person Last Name"
    signal_type: "Signal Type"
    direction: "Direction"
    name: "Name"
    register_address: "Register Address"
    type_register: "Type Register"
    start_address: "Start Address"
    signal_scaling: "Signal Scaling"
    min_kw: "Min KW"
    min_adc: "Min ADC"
    max_kw: "Max KW"
    max_adc: "Max ADC"
    gateway: "Gateway"
    sally_name: "Sally Name"
    scada_name: "Scada Name"
    submit_action: "Add"
    edit_action: "Edit"

  signal_list:
    signal_direction:
      read: Read (Customer to VPP)
      write: Write (VPP to Customer)
    vpp_name: VPP Name
    direction: Direction
    msg_list_created: Signal list created.
    msg_list_create_failed: "Failed to create the signal list: %{detail}"
    msg_list_updated: Signal list name updated.
    msg_list_in_use: Cannot delete signal list that is in use.
    msg_list_deleted: Signal list deleted.
    msg_signal_added: Signal added.
    msg_signal_add_failed: "Failed to add the new signal: %{detail}"
    msg_signal_updated: Signal updated.
    msg_signal_update_failed: "Failed to update the signal: %{detail}"
    msg_signal_removed: The signal was removed from the list.
    back_to_list: Back to list

  bid:
    upload_bids: 'Upload Bids'
    delivery_date: 'Delivery date'
    product: 'Product'
    bids_overview: 'Bids Overview'
    download_all_bids: 'Download all bids'
    last_uploaded: 'Last File uploaded on: %{date} by %{username}'
    not_uploaded: 'No bids uploaded for selected delivery date and tender.'
    auction_closed: 'Auction closed'
    delete_bids: 'Delete Bids'
    th_market: 'Market'
    th_product: 'Product'
    th_tso: 'TSO'
    th_capacity_price: 'Capacity Price (¤/MW)'
    th_energy_price: 'Energy Price (¤/MWh)'
    th_payment_direction: 'Payment Direction'
    th_capacity: 'Capacity (MW)'
    success_deleted: 'Successfully deleted the bids.'
    success_uploaded: 'Successfully uploaded the bids. '
    success_uploaded_automatic: 'Nomination bids were automatically created for the bids that were not previously auctioned with a nomination bid.'
    err_no_file: 'Please select a file to upload.'
    err_col_missing: 'Missing column %{name}. Please check and upload the file again.'
    err_missing: 'Missing %{name}. Please check and upload the file again.'
    err_missing_date: 'Missing %{name} or there is an issue with the date format. It should be dd/mm/yyyy or dd.mm.yyyyy. Please check and upload the file again.'
    err_missing_time: 'Missing %{name} or there is an issue with the date format. It should be YYYY-MM-DDTHH:MM:SSZ. Please check and upload the file again.'
    err_missing_market: 'Market is mandatory and must be one of DC, DM, DR.'
    err_asset_id: 'Asset ID expected in column I not recognized.'
    err_external_id: 'External Asset ID expected in column I not recognized'
    err_validation: 'Validation error(s)'
    err_no_worksheet: 'Could not find any worksheet. Please check and upload the file again.'
    err_ErrorAuctionClosed: "The auction isn't open for this delivery date and market."
    err_ErrorAuctionMrlClosedButSrlOpen: "The auction isn't open for this delivery date and market."
    err_ErrorAuctionSrlClosedButMrlOpen: "The auction isn't open for this delivery date and market."
    err_ErrorTSONotFound: 'At least one TSO zone isn’t recognized.'
    err_ErrorInvalidEnergyDirection: 'At least one energy direction isn’t recognized.'
    err_ErrorInvalidTSO: "At least one TSO zone isn’t recognized."
    err_ErrorInvalidMarket: 'At least one market name isn’t recognized.'
    err_ErrorInvalidProductName: 'At least one product name isn’t recognized.'
    err_ErrorInvalidPaymentDirection: 'At least one payment direction isn’t recognized.'
    err_ErrorFlexVolumeNotWholeValue: 'At least one volume not rounded to MW.'
    err_ErrorDGEnabledForBidsMissing: 'No dispatch group in %{tsoName} and %{marketName} are configured in the system to accept bids. Please enable the trading functionality in the dispatch group configuration.'
    err_ErrorBGNotFoundForBid: 'No balancing group are defined at dispatch group level in %{tsoName} and %{marketName} for the marketed date. Please enter a balancing group in the dispatch group configuration.'
    err_ErrorMinFlexBidRule: 'The minimum size of a bid is 5 MW. A bid of less than 5 MW can be uploaded only if it is the unique bid for that day, product and market. Please check the bids in %{tsoName}'
    err_ErrorInvalidFloatingEnergyPrice: 'Only three numbers after the comma for the energy price are accepted.'
    err_ErrorInvalidFloatingCapacityPrice: 'Only two numbers after the comma for the capacity price are accepted.'
    err_ErrorInvalidAssetID: 'Asset not recognized.'
    err_ErrorInvalidContractType: "No contract of type 'Market Access Light' attached to customer."
    err_ErrorFlexExceedsPrequalification: 'Prequalification limits exceeded.'
    err_ErrorWrongDeletionCriteria: 'Already existing bids for that time interval and product are left unchanged.'
    err_row: 'Row %{row_num}: '
    err_pms_bidding_windows: 'Unable to get bidding windows: %{reason}.'
    err_pms_delete: 'Failed to delete bids: %{reason}.'
    err_pms_upload: "The format of the file isn't recognized. Please check and upload the file again."
    err_pms_v2g_optimization_job: "Optimisation could not be initialized: %{reason}."
    please_check_and_try_again: "Please check and upload the file again."
    warn_auction_results_exceed_bids: Auction results have been uploaded without optimization results from a previous optimization run or with volumes higher than the previous bids (%{description}).
  bod:
    invalid_asset_id: "Invalid Assetid at row %{index}"
    invalid_datetime: "Invalid Datetime at row %{index}"
    invalid_pair: "Invalid Pair at row %{index}"
    invalid_power_mw: "Invalid Volume at row %{index}"
    invalid_pair_or_power_mw: "Pair and Volume don't have the same sign at row %{index}"
    invalid_offer_price: "Invalid Offer_Price at row %{index}"
    invalid_bid_price: "Invalid Bid_Price at row %{index}"
  asset_optimization:
    err_asset_optimization_job: "Optimisation could not be initialized: %{reason}."
    err_invalid_price: "Invalid prices at line(s) %{lines}"
    err_get_open_tenders: 'Cannot get open tenders: %{reason}.'
    err_get_market_positions: 'Cannot get market positions: %{reason}.'
    err_validate_bids: 'Cannot validate bids: %{reason}.'
    err_reject_bids: 'Cannot reject bids: %{reason}.'
  angular:
    locale:
      de: Deutsche
      en-GB: English
