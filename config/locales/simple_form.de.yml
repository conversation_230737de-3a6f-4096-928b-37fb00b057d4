de:
  simple_form:
    "yes": 'Ja'
    "no": 'Nein'
    required:
      text: '<PERSON>rforderlich'
      mark: '*'
    error_notification:
      default_message: "Bitte das untenstehende Problem überprüfen:"

    labels:
      vpp:
        name: DU Name
        start_time: Zeitraum
      dispatch_group:
        name: Name
        tso: ÜNB
        market: Market
        dispatch_mode: Abruf-Modus
        event_types_acknowledge_alarm: Stummen Alarm bei Eintritt folgender Ereignesse
        event_types_acknowledge_audio_alarm: Alarm mit Ton bei Eintritt folgender Ereignesse
      rollup_family:
        name: Rollup Gruppierungsname
    placeholders:
      vpp:
        name: automatisch generiert falls nicht eingegeben
        energy_price: überschreibe den gewichteten Arbeitspreis
        assets_weighted_average_price: gewichteten Arbeitspreis der ausgewählten Anlagen
        weighted_average_price: z.B. 100
        filter_price: preis niedriger als (¤/MWh)
        filter_flex: flex niedriger als (kW)
        assets_count: <PERSON><PERSON><PERSON> der ausgewählten Anlagen
      dispatch_group:
        name: Name der Abrufgruppe eingeben
    hints:
      dispatch_group:
        individual_alarms_enabled_html: "Bitte klicken Sie diese Kästchen an, wenn diese Abrufgruppe Alarme an das RCC in Hamburg übermitteln soll. <br />Bitte kontaktieren Sie das VPP Operations Team falls Sie Zweifel haben."
      subpool:
        implicit: "Ein implizites Subpool enthält alle Anlagen, welche keiner anderen Subpool zugeordnet wurden."
