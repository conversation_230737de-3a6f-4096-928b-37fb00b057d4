de:
  activemodel:
    attributes:
      report_email_sendings_configuration:
        scheduling_balancing_groups_active: EDG Berichte Aktiv
        scheduling_balancing_groups_email_recipients: EDG Berichte Empfänger
        scheduling_balancing_groups_third_party_active: Dritte BK Berichte Aktiv
        scheduling_balancing_groups_third_party_email_recipients: Dritte BK Berichte Empfänger
        scheduling_balancing_groups_third_party_email_recipients_email_only_when_activation: nur Berichte mit Abrufe
        scheduling_day_after_active: UGC Berichte Aktiv
        scheduling_day_after_email_recipients: UGC Berichte Empfänger
        scheduling_control_active: Kontrolldatei Aktiv
        scheduling_control_email_recipients: Kontrolldatei Empfänger
        tso_schedule_alarm_email_recipients: ÜNB Fahrplan Alarm Empfänger
        tso_schedule_alarm_active: ÜNB Fahrplan Alarm Aktiv
        main_address: Alle emails schicken an
      imported_report:
        contents: Inhalt
    errors:
      models:
        report_email_regeneration:
          attributes:
            base:
              date_not_in_the_past: Das Datum muss in der Vergangenheit liegen.
    models:
      report_email_sendings_configuration: Konfiguration der Automatischen Berichte-Emails
      imported_report: Importierter Bericht
  activerecord:
    attributes:
      asset_dg_allocation:
        asset: Anlage
        dispatch_group: Abrufgruppe
        end_time: Bis
        is_allocated: Zuordnen
        interval: Interval
        skip_start_time_validations: Änderungen mit soforter Gültigkeit zulassen.
        start_time: Von
      asset_dg_allocation_source:
        asset_dg_allocations: Zuordnungen
        created: Ausgeführt am
        user: Benutzer
        content_type: Typ
        content: Inhalt
        success: Erfolgreich importiert
        warnings: Warnungen
        errors: Fehlermeldungen
        skip_start_time_validations: Änderungen mit soforter Gültigkeit zulassen.
        validation_result: Validierung
      asset_dg_allocation_source/success:
        "true": Erfolgreich importiert
        "false": Fehlschlag
      asset_dg_allocation_source/content_type:
        file_import: Zuordnungen-Datei
        ui_update: Form
      dispatch_group:
        activation_factor: Aktivierungspuffer (1=100%)
        alarm_assets_with_not_recoverable_faults_enabled: Signal 1 (AssetAlertLevelAlarm)
        alarm_assets_with_recoverable_faults_enabled: Signal 2 (AssetAlertLevelWarning)
        alarm_setpoint_reachable_enabled: Signal 3 (DUAlertLevelWarning)
        alarm_available_flex_too_low_enabled: Signal 4 (VPPMissingFlexibilitySRL)
        alarm_assets_faults_pq_threshold: Anlagen relevant wenn PQ grösser als (KW)
        alarm_flexibility_threshold_factor: FlexTooLow Schwellenwert-Faktor (1=100%)
        alarm_flexibility_threshold_buffer: FlexTooLow Dauer (s)
        availability_deviation_threshold_factor: Verfügbarkeitsänderung (1=100%)
        echo_signal_heartbeat: Echo für Heartbeat Signal
        echo_signal_active_power: Echo für ActivePower Signal
        individual_alarm_echo_signal_active_power_enabled: RCC Alarm aktiv für Echo für ActivePower Signal
        individual_alarms_enabled: RCC Alarm aktiv für Flex Too Low
        individual_alarm_merlin_enabled: RCC Alarm aktiv für MerLin
        individual_alarm_overdelivery_neg_enabled: Alarm aktiv bei neg. Überlieferung
        individual_alarm_overdelivery_neg_threshold: Alarm aktiv bei neg. Überlieferung Schwellenwert (MW)
        individual_alarm_overdelivery_neg_delay: Alarm aktiv bei neg. Überlieferung Dauer (s)
        individual_alarm_overdelivery_pos_enabled: Alarm aktiv bei pos. Überlieferung
        individual_alarm_overdelivery_pos_threshold: Alarm aktiv bei pos. Überlieferung Schwellenwert (MW)
        individual_alarm_overdelivery_pos_delay: Alarm aktiv bei pos. Überlieferung Dauer (s)
        nomination_tool_additional_buffer_pos_mw: Zusätzlicher Puffer POS MW (Defaultwert 0)
        nomination_tool_additional_buffer_neg_mw: Zusätzlicher Puffer NEG MW (Defaultwert 0)
        nomination_tool_enabled: Nominierung Aktiviert
        nomination_tool_energy_price_target_margin: "Energiepreis Gewinnmarge (1=100%; Defaultwert 0)"
        nomination_tool_max_fault_seconds_percentage: "Rollup Anlage Fehlergrenze (1=100%; Defaultwert 0.9)"
        nomination_tool_max_invalid_rollups_percentage: "Aggregierte Anlage Fehlergrenze (1=100%; Defaultwert 0.9)"
        nomination_tool_past_availability_hours: Zeitfenster für Rollups (h) (Defaultwert 24)
        nomination_tool_target_bid_volume_mw: Minimale Angebotsgröße MW (Defaultwert 5)

        automatic_allocation_enabled: Automatisch Zuordnung Aktiv
        automatic_allocation_weight: Gewicht
        automatic_allocation_nomination_buffer_percentage: "Nominierungspuffer (%)"
        max_asset_reallocation: Max Anlage Zuordnungswechsel (Default keine Beschränkung)

        subpools_value_on: Poolindex (zugeordnet)
        subpools_value_off: Poolindex (nicht zugeordnet)

        name: Name
        tso: ÜNB
        market: Market
        dispatch_mode: Abruf-Modus
        dsos: VNB spezifische Datenpunkte
        event_types_acknowledge_alarm: Stummen Alarm bei Eintritt folgender Ereignesse
        event_types_acknowledge_audio_alarm: Alarm mit Ton bei Eintritt folgender Ereignesse
        minimum_duration_of_dispatch: Min. Dauer eines Abrufes (s)
        reaction_time: Reaktionszeit (s)
        signals: Relevante Datenpunkte

        setpoint_reached_detection_upper_tolerance_factor: Upper Tolerance (1=100%)
        setpoint_reached_detection_lower_tolerance_factor: Lower Tolerance (1=100%)
        setpoint_reached_detection_upper_tolerance_minimum: Upper Tolerance Min. (KW)
        setpoint_reached_detection_lower_tolerance_minimum: Lower Tolerance Min. (KW)
        setpoint_reached_detection_rolling_average_duration: Average Window (s)

        is_at_setpoint_detection_upper_tolerance_factor: Upper Tolerance (1=100%)
        is_at_setpoint_detection_lower_tolerance_factor: Lower Tolerance (1=100%)
        is_at_setpoint_detection_upper_tolerance_minimum: Upper Tolerance Min. (KW)
        is_at_setpoint_detection_lower_tolerance_minimum: Lower Tolerance Min. (KW)

        compensation_check_interval: Check Window (s)
        compensation_resolution: Resolution (KW)
        over_delivery_excess_compensation_factor: Over-Delivery Compensation (1=100%)
        over_delivery_compensation_limit_factor:  Over-Delivery Limit (1=100%)
        under_delivery_excess_compensation_factor: Under-Delivery Compensation (1=100%)
        under_delivery_compensation_limit_factor:  Under-Delivery Limit (1=100%)
        compensation_is_at_setpoint_detection_upper_tolerance_factor: Upper Tolerance (1=100%)
        compensation_is_at_setpoint_detection_lower_tolerance_factor: Lower Tolerance (1=100%)
        compensation_is_at_setpoint_detection_upper_tolerance_minimum: Upper Tolerance Min. (KW)
        compensation_is_at_setpoint_detection_lower_tolerance_minimum: Lower Tolerance Min. (KW)

        sally_setpoint_quantization_resolution: Quantization Resolution (KW)
        sally_setpoint_quantization_timeout: Quantization Timeout (s)
        sally_setpoint_read_enabled: Aktiv

        merlin_dispatch_enabled: Aktiv
        capacity_price_grouping_steps: Kapazität Preisgruppen Stufen

        portfolio_id: Porfolio ID
        rollups_enabled: Aktiv

        ext_backup_active: Aktiv
        ext_backup_buffer: Puffer (s)
        ext_backup_threshold_pos: Positives Grenzwert MW
        ext_backup_threshold_neg: Negatives Grenzwert MW
        ext_backup_nomination_threshold_pos: Positives Grenzwert % von Nominierung
        ext_backup_nomination_threshold_neg: Negatives Grenzwert % von Nominierung
        ext_backup_increment_pos: Rundungsschritt Positiv MW
        ext_backup_increment_neg: Rundungsschritt Negativ MW

        dead_band_pos_mhz: Positive tote Zone (mHz)
        dead_band_neg_mhz: Negative tote Zone (mHz)
        max_frequency_deviation_pos_mhz: Max. positive Frequenzabweichung (mHz)
        max_frequency_deviation_neg_mhz: Max. negative Frequenzabweichung (mHz)

        # generic dg attributes
        export_entrader: Export Entrader
        entrader_treshold_update: Schwellenwert für Aktualisierungen (MW)
        entrader_lead_time_update: Vorlaufzeit für Aktualisierungen (min)
        entrader_upload_folder: Upload Folder

        has_exclusive_behaviour: Exklusives Verhalten
        has_nomination: Nominierung
        has_preceding_basepoint: Voreilender Arbeitspunkt

        asset_activation_type: Anlagenaktivierungstyp
        aat_ar_flsp_dead_band_pos: positives Totband (Hz)
        aat_ar_flsp_dead_band_neg: negatives Totband (Hz)
        aat_ar_flsp_max_frequency_deviation_pos: Frequenzabweichnung Pos (Hz)
        aat_ar_flsp_max_frequency_deviation_neg: Frequenzabweichnung Neg (Hz)
        aat_ar_flsp_high_knee_joint: Oberer Knickpunkt (Hz)
        aat_ar_flsp_low_knee_joint: Unterer Knickpunkt (Hz)

        scm_dc_target_state_of_charge_low: Zielladezustand Low (%)
        scm_dc_target_state_of_charge_high: Zielladezustand High (%)
        scm_dc_target_state_of_charge_both: Zielladezustand Both (%)
        scm_dc_foot_room: Fußraum (%)
        scm_dc_head_room: Kopfraum (%)
        scm_dc_ramp_rate_limit: Ramp Rate Limit (%)
        scm_dc_energy_reserve: Energiereserve (Sekunden)
        scm_dc_min_energy_recovery: Mindestenergiewiederherstellung (Sekunden)
        scm_dc_dead_band_factor: Faktor Totband (%)
        scm_vb_dc_delivery_duration: Delivery Duration (sec)
        scm_vb_dc_delivery_duration_buffer: Delivery Duration Buffer (%)
        scm_vb_dc_min_energy_recovery: Min Energy Recovery (%)
        scm_vb_dm_delivery_duration: Delivery Duration (sec)
        scm_vb_dm_delivery_duration_buffer: Delivery Duration Buffer (%)
        scm_vb_dm_min_energy_recovery: Min Energy Recovery (%)
        scm_vb_dr_delivery_duration: Delivery Duration (sec)
        scm_vb_dr_delivery_duration_buffer: Delivery Duration Buffer (%)
        scm_vb_dr_min_energy_recovery: Min Energy Recovery (%)
        scm_fcr_lower_soc_limit: Unteres SoC-Limit (%)
        scm_fcr_upper_soc_limit: Oberes SoC-Limit (%)
        scm_opt_sp_duration_minutes: Dauer der Abrechnungsperioden (Minuten)

        ads_start_using_assets_only_when_at_basepoint: Asset nur nutzen wenn am Basepoint
        ads_strategy: Strategy
        ads_strategy_by_price_preserve_current_dispatches: ktuelle Einsatzplanung beibehalten
        ads_strategy_by_price_and_soc_limit_battery_power_window: Batterie Dispatch-Zeitfenster (Sekunden)
        ads_strategy_pro_rata_symmetric: Symmetrisch
        ads_strategy_on_off_signal: "Signal für Ein- und Ausschalten"

        cross_dg_links: Cross DG links
        cdl_cross_plan_propagation_frequency: Cross plan propagation frequency

        dc_check_interval_seconds: Prüfintervall (Sekunden)
        dc_over_delivery_excess_compensation_factor: Überbelieferungskompensationsfaktor
        dc_over_delivery_compensation_limit_factor: Überbelieferungsbegrenzungsfaktor
        dc_under_delivery_excess_compensation_factor: Unterbeliferungskompensationsfaktor
        dc_under_delivery_compensation_limit_factor: Unterbelieferungsbegrenzungsfaktor
        dc_compensation_resolution_kw: Auflösung Kompensation (kW)
        dc_is_at_setpoint_upper_tolerance_factor: "Am Sollwert: oberer Toleranzfaktor"
        dc_is_at_setpoint_upper_tolerance_minimum_kw: "Am Sollwert: obere Toleranz min (kW)"
        dc_is_at_setpoint_lower_tolerance_factor: "Am Sollwert: unterer Toleranzfaktor"
        dc_is_at_setpoint_lower_tolerance_minimum_kw: "Am Sollwert: untere Toleranz min (kW)"

        dg_activation_type: DG Aktivierungstyp
        dispatch_commands: Einsatzplanungsbefehle
        dispatch_source: Herkunft der Einsatzplanung
        ds_nominated_volume_activation_factor: Nominiertes Volumen Aktivierungsfaktor
        ds_nominated_volume_symmetric_activation: Nominiertes Volumen symetrische Aktivierung
        ds_ui_edg_schedule: UI EDG Fahrplan
        ds_residual_shape_positive_threshold: positiver Schwellenwert (kW)
        ds_residual_shape_negative_threshold: negativer Schwellenwert (kW)
        ds_residual_shape_window: Zeitfenster (Sekunden)
        ds_price_trigger_window: Zeitfenster (Sekunden)
        ds_price_trigger_price_type: Preisart
        ds_price_trigger_settlement_period: Abrechnungsperioden
        ds_price_trigger_price_threshold_neg: Negative Preisgrenze
        ds_price_trigger_price_threshold_pos: Positive Preisgrenze
        ds_price_trigger_price_expiration_seconds: Preisgültigkeitsdauer (sec)
        ds_nlafrr_bleeding_time_seconds: Bleeding Time (seconds)

        execution_plan_window_seconds: Berechnungszeitfenster für Ausführungsplan (Sekunden)
        execution_plan_frequency_seconds: Berechnungsfrequenz für Ausführungsplan (Sekunden)

        net_extend_before_nomination_seconds: Extend before nomination (Sekunden)
        net_overlap_nominations_seconds: Overlap nomination (Sekunden)
        net_stop_before_end_of_nomination_seconds: Stop before end of nomination (Sekunden)

        notifications: Notifications
        ntf_flex_too_low_threshold_factor: Schwellenwertfaktor
        ntf_flex_too_low_threshold_buffer_seconds: Schwellenwertpuffer (Sekunden)
        ntf_over_delivery_negative_threshold_kw: negativer Schwellenwert (kW)
        ntf_over_delivery_negative_delay_seconds: negative Verzögerung (Sekunden)
        ntf_over_delivery_positive_threshold_kw: positiver Schwellenwert (kW)
        ntf_over_delivery_positive_delay_seconds: positive Verzögerung (Sekunden)
        ntf_setpoint_not_reached_reach_setpoint_in_seconds: Sollwert zu erreichen  (Sekunden)
        ntf_setpoint_not_reached_is_at_setpoint_detection_upper_tolerance_factor: Oberer Toleranzfaktor
        ntf_setpoint_not_reached_is_at_setpoint_detection_upper_tolerance_minimum_kw: Oberes Toleranzminimum (kW)
        ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_factor: Unterer Toleranzfaktor
        ntf_setpoint_not_reached_is_at_setpoint_detection_lower_tolerance_minimum_kw: Unteres Toleranzminimum (kW)
        ntf_setpoint_reachable_is_at_setpoint_detection_upper_tolerance_factor: Setpoint Reachable Upper tolerance factor
        ntf_setpoint_reachable_is_at_setpoint_detection_upper_tolerance_minimum_kw: Setpoint Reachable Upper Tolerance Minimum (kW)
        ntf_setpoint_reachable_is_at_setpoint_detection_lower_tolerance_factor: Setpoint Reachable Lower tolerance factor
        ntf_setpoint_reachable_is_at_setpoint_detection_lower_tolerance_minimum_kw: Setpoint Reachable Lower tolerance minimum (kW)

        periodicity_dg_aggregations_seconds: Periodicity DG aggregations (Sekunden)
        periodicity_output_signal_write_seconds: Periodicity Output signal write (Sekunden)

        ramp_adjustment_strategy: Ramp adjustment strategy
        redispatch_triggers: Redispatch triggers
        rt_asset_available_flex_change_threshold_kw: "Av. flex change: Threshold (kW)"
        rt_asset_available_flex_change_since_activation_factor: "Av. flex change since activation: Factor"
        rt_dg_total_deviation_buffer_kw: "Total dev. Buffer (kW)"
        rt_dg_total_deviation_suppress_redispatch_seconds: "Total dev. Suppress redispatch (Sekunden)"

        setpoint_validation_reaction_time_seconds: Setpoint Validation Reaction time (Sekunden)
        setpoint_validation_cap_by_nomination: Setpoint Validation Cap by nomination
        setpoint_validation_minimum_duration_of_dispatch_seconds: Setpoint Validation Minimum duration of dispatch (Sekunden)
        setpoint_validation_nomination_interval_validation: Setpoint Validation Nomination interval validation
        setpoint_validation_quantization_filter_resolution_kw: Setpoint Validation Quantization resolution (kW)
        setpoint_validation_quantization_filter_quantization_duration_seconds: Setpoint Validation Quantization duration (Sekunden)

        signals_output_signals: Output Signals
        signals_sub_pools_values_on: Subpool Values On
        signals_sub_pools_values_off: Subpool Values Off
        signals_dso_for_signals: DSO for signals
        signals_heart_beat_mirror: Heart beat mirror

        dpb_min_power_for_dispatch_discharge: Min Power For Dispatch Discharge
        dpb_min_power_for_dispatch_charge: Min Power For Dispatch Charge

        #dlm params
        grouping_rule: Lastmanagementmodus
        nominal_site_current: Bezugseinschränkungen - Nominaler Strom (A)
        reduction_factor: Bezugseinschränkungen - Reduktionsfaktor (1=100%)
        dlm_group_reduced_id: DLM-Gruppen-IDs - Reduziert
        dlm_group_blocked_id: DLM-Gruppen-IDs - Geblockt
        control_windows: Regelzeitfenster
        session_duration_threshold_minutes: Mindestladezeit (Minuten)
        session_energy_threshold_watt_hour: Mindestladeenergie (Wh)
        grouping_rule_both_thresholds: Verknüpfung zwischen linkem und rechtem Parameter für die Zuordnung von Ladern zu DLM-Gruppen
        min_average_charged_energy_factor: Minimaler Anteil von durchschnittlicher Ladeenergie (1=100%)
        min_average_charging_duration_factor: Minimaler Anteil von durchschnittlicher Ladezeit (1=100%)

      distributed_unit:
        created: Hinzugefügt am
        deleted: Entfernt
        end_time: Bis
        energy_direction: Energie Richtung
        energy_price: Energie Preis
        capacity_price: Leistungspreis
        flex_volume: Flex Kapazität
        interval: Intervall
        skip_start_time_validations: Änderungen mit sofortiger Gültigkeit und/oder Nominierungen für die Vergangenheit zulassen.
        start_time: Von
        used_for_asset_price: Relevant für APDU
      distributed_unit_source:
        asset_dg_allocations: Zuordnungen
        created: Ausgeführt am
        user: Benutzer
        content_type: Typ
        content: Inhalt
        success: Erfolgreich importiert
        warnings: Warnungen
        errors: Fehlermeldungen
        validation_result: Validierung
        skip_start_time_validations: Änderungen mit sofortiger Gültigkeit und/oder Nominierungen für die Vergangenheit zulassen.
        nothing_imported: Es wurde keine Nominierung hochgeladen, da die Datei nur einen Header enthält oder leer ist.
      distributed_unit_source/success:
        "true": Erfolgreich importiert
        "false": Fehlschlag
      distributed_unit_source/content_type:
        file_import: Nominierung-Datei
        ui_update: Form
      subpool:
        assets: Anlagen
        dispatch_groups: Abrufgruppen
        implicit: Implizit
        name: Name
        signals: Signale
      rollup_family:
        name: Rollup Gruppierungsname
      rollup_regeneration:
        user: Triggered by
        rollup_types: Rollup metriken
        interval: Intervall
        status: Status
        assets: Anlagen
        rollup_families: Rollup Gruppierungs
        dispatch_groups: Abrufgruppen
        retries: Retries
        error_details: Errors
      rollup_email_sendings_configuration:
        rollup_notifications_email_recipients: Rollup Benachrichtigungen Empfänger
        rollup_notifications_active: Rollup Benachrichtigungen Aktiv
      rollup_error:
        created: 'Am'
        description: Beschreibung
      nominateable_volume:
        created: Ausgeführt am
        user: Benutzer
        delivery_date: Lieferdatum

      generic_steering_config:
        model_name: Steuerungstyp
        configuration_name: Steuerungstyp Konfiguration
        name: Adaptername
        basepoint: Arbeitspunkt
        schedule: Fahrplan
        flex: Flexibilität
        setpoint: Sollwert
        setpoint_relativity: Sollwert
        asset_sp: Standard Sollwert
        has_setpoint_feedback: Sollwert Rückmeldung
        setpoint_frequency_seconds: Sollwert Frequenz (s)
        setpoint_unit: Sollwert Einheit
        has_setpoint_lock: '   '
        setpoint_interval: '   '
        has_dispatched_deviated: Abruf Abweichung
        has_heartbeat_vpp: Kontrollsignal VPP Kommunikation OK
        heartbeat_vpp_frequency_seconds: Frequenz (s)
        heartbeat_vpp_authorization: Lese/Schreibe Rechte
        has_heartbeat_asset: Kontrollsignal Anlagenkommunikation OK
        heartbeat_asset_frequency_seconds: Frequenz (s)
        heartbeat_asset_authorization: Lese/Schreibe Rechte
        allow_steering_by_schedule: 'Lenkung nach Zeitplan zulassen'
        e_unknown_error: Fehler
        e_setpoint_interval_on_off_requires_boolean: An/Aus Sollwert benötigt Boolean
        e_name_can_not_be_empty: Name benötigt
        e_name_already_exists: Eine Konfiguration mit diesem Namen existiert bereits
        e_boolean_setpoint_requires_no_both_flex: Boolean Sollwert benötigt nur entweder positive oder negative Flexibilität
        e_boolean_setpoint_requires_onoff: Boolean Sollwert benötigt An/Aus Sollwert
        e_absolute_setpoint_with_both_flex_requires_lock: Absoluter Sollwert mit Flexibilität in beide Richtungen ist nur möglich wenn "Lock" ist aktiviert
        e_steering_type_is_in_use: Steuerungstyp wird aktiviert
        basepoints:
          HasBasepointAndPbp: Vorauseilender Arbeitspunkt der Anlage
          HasBasepoint: Arbeitspunkt von Anlage
          NoBasepoint: Kein Arbeitspunkt (-> Pist Mittelwert)
        schedules:
          Production: Prod
          Flexibility: Flex
        flexes:
          Both: Beide
          Positive: Nur Positive
          Negative: Nur Negative
        setpoint_relativities:
          Relative: Relativ
          Absolute: Absolut
        setpoint_units:
          Kilowatts: kW
          Boolean: Boolean
        setpoint_intervals:
          Continuous: Kontinuierlich
          OnOff: ON/OFF
          Steps: Stufen (2 oder mehr)
        heartbeat_authorizations:
          Read: Nur lesend
          Write: Nur schreibend
          ReadWrite: L/S
        has_setpoint_locks:
          'true': Sollwert Aktivierungsbit
          'false': Kein Sollwert Aktivierungsbit
        yes_nos:
          'true': Ja
          'false': Nein
        y_ns:
          'true': Ja
          'false': Nein
        feedback: Rückmeldung
        frequency_s: Frequenz (s)
        confirm_delete: Sind Sie sicher?
        default_absolute_setpoints:
          ImposedPMax: 'Maximale Leistung'
          ImposedPMin: 'Minimale Leistung'
          PNorm: 'Normalleistung'

      auction_config:
        name: Name
        auction_definition_interval: Gültigkeitszeitraum
        time_zone: Zeitzone
        dispatch_group_ids: Steuerungsgruppe
        bidding_method: Gebotsmethode
        bidding_method_market: Market
        assets: Assets
        frequency_time_minutes: Frequency Time (minutes)
        delivery_interval_: Lieferintervall
        offers_time_block: Angebotsblock
        bidding_direction: Richtung
        rounding_digits: Volumen-Rundung
        minimum_mw_volume_for1_bid: Minimales MW für 1 Gebot
        minimum_mw_volume: Minimales MW
        price_type: Geforderter Preis
        energy_price_rounding_digits: Energiepreis-Rundung
        capacity_price_rounding_digits:  Kapazitätspreis-Rundung
        swing_limit: Swing Limit
        swing_limit_value: Swing Limit Value
        optimization_horizon_change: Optimization Horizon Change
        optimization_horizon_change_days_before_delivery: Tage vor Lieferintervall
        optimization_horizon_change_time_hours: Zeit
        optimization_horizon_change_time_minutes: Zeit
        market_auction_start: Ausschreibungsbeginn
        market_auction_start_days_before_delivery: Tage vor Lieferintervall
        market_auction_start_time_hours: Zeit
        market_auction_start_time_minutes: Zeit
        market_auction_start_minutes: Minuten
        market_auction_end: Ausschreibungsend
        market_auction_end_days_before_delivery: Tage vor Lieferintervall
        market_auction_end_time_hours: Zeit
        market_auction_end_time_minutes: Zeit
        market_auction_end_minutes: Minutes
        internal_bidding_input_format: Gebotsformat
        internal_auction_start: Ausschreibungsbeginn
        internal_auction_start_days_before_delivery: Tage vor Lieferintervall
        internal_auction_start_time_hours: Zeit
        internal_auction_start_time_minutes: Zeit
        internal_auction_start_minutes: Minuten
        internal_auction_end: Ausschreibungsend
        internal_auction_end_days_before_delivery: Tage vor Lieferintervall
        internal_auction_end_time_hours: Zeit
        internal_auction_end_time_minutes: Zeit
        internal_auction_end_minutes: Minuten
        customer_bidding_input_format: Gebotsformat
        customer_auction_start: Ausschreibungsbeginn
        customer_auction_start_days_before_delivery: Tage vor Lieferintervall
        customer_auction_start_local_time_hours: Zeit
        customer_auction_start_local_time_minutes: Zeit
        customer_auction_start_minutes: Minuten
        customer_auction_minimum_mw_volume_for1_bid: Minimales MW für 1 Gebot
        customer_auction_end: Ausschreibungsend
        customer_auction_end_days_before_delivery: Tage vor Lieferintervall
        customer_auction_end_local_time_hours: Zeit
        customer_auction_end_local_time_minutes: Zeit
        customer_auction_end_minutes: Minuten
        optimizer_bod_frequency_minutes: "Frequency (Minuten)"
        optimizer_bod_computed_sp: "Computed SPs"
        optimizer_bod_delta_for_submission: "Delta for submission (£)"
        optimizer_bod_delta_for_undo: "Undo delta (£)"
        api_auction_bidding: Bidding API
        auction_system: Auction System
        auction_system_upload_behaviour: Behaviour
        auction_system_upload_market_time: Minuten vor Ende der Marktausschreibung
        auction_system_upload_number_of_retries: Anzahl der Wiederholungsversuche
        auction_system_upload_seconds_between_retries: Sekunde zwischen Wiederholungsversuchen
        auction_system_download_behaviour: Marktergebnisse herunterladen
        auction_system_download_market_time: Minuten nach Ende der Marktausschreibung
        auction_system_download_number_of_retries: Anzahl der Wiederholungsversuche
        auction_system_download_seconds_between_retries: Sekunde zwischen Wiederholungsversuchen
        auction_system_ui_trader_look_ahead_hours: Vermarktungshorizont (Stunden)
        auction_system_ui_price_grouping_step: Preisgruppengröße (£/MWh)
        auction_system_ui_bid_expiry_warning_minutes: Gebotsablaufwarnung (Minutes)
        auction_system_ui_currency: Währung
        auction_system_ui_site_id: Site ID
        auction_system_ui_for_battery_uk_intraday_asset_id: Asset ID
        auction_system_ui_for_battery_uk_intraday_portfolio_id: Portfolio ID
        auction_system_ui_for_battery_uk_intraday_export_entrader: Export Entrader
        auction_system_ui_for_battery_uk_intraday_upload_folder: Upload Folder
        auction_system_uk_vatp_smart_volume_strategy_template_id: Smart Volume Strategy Template ID
        auction_system_uk_vatp_hidden_iceberg_strategy_template_id: Hidden Iceberg Strategy Template ID
        auction_system_uk_vatp_smart_iceberg_strategy_template_id: Smart Iceberg Strategy Template ID
        auction_system_de_vatp_smart_volume_strategy_template_id: Smart Volume Strategy Template ID
        auction_system_de_vatp_hidden_iceberg_strategy_template_id: Hidden Iceberg Strategy Template ID
        auction_system_de_vatp_smart_iceberg_strategy_template_id: Smart Iceberg Strategy Template ID
        create_nominations: Nominierung aus dem Ergebnis erstellen?
        create_allocations: Zuordnung aus dem Ergebnis erstellen?
        allocation_start_extension_seconds: Allocation Start Extension (seconds)
        allocation_end_extension_seconds: Allocation End Extension (seconds)
        allocation_end_extension_from_ramp_rate: Allocation End Extension From Ramp Rate

    errors:
      messages:
        start_date_after_end_date: Das Startdatum muss vor dem Enddatum liegen.
        start_date_is_in_the_past: Das Startdatum kann nicht in der Vergangenheit liegen. Bitte korrigieren Sie das Startdatum.
        end_date_is_in_the_past: Das Bilanzkreissenddatum kann nicht in der Vergangenheit liegen.
        cannot_change_historic_data: Historische Daten können nicht verändert werden.
        cannot_delete_historic_data: Historische Daten können nicht entfernt werden.
        cannot_change_start_date: Das Startdatum kann nicht verändert werden.
        overlaps_existing_balancing_group: Überschneidung mit einem bestehenden Bilanzkreis.
        unique_implicit_subpool_per_dg: "Einige Abrufgruppen haben bereits ein implizites Subpool: %{dg_names}"
        session_duration_threshold_minutes: Minimale Ladezeit kann maximal 24 Stunden betragen.
        control_windows: "Die Regelzeitfenster müssen im Format HH:MM eingegeben werden."
        marketdata_api_error: "Marketdata API returned the following error: %{error}"
      models:
        dispatch_group:
          attributes:
            nomination_tool_enabled:
              taken: "Abrufgruppe #%{dg_id} %{dg_name} mit gleichen ÜNB und Market ist bereits benutzt für Nominierung."
    models:
      asset:
        one: Anlage
        other: Anlagen
      asset_dg_allocation_source: Zuordnungsänderung
      balancing_group_dg: Bilanzkreis
      db/db_bg_name_supplier: Anbieterbilanzkreis
      db/db_bg_name_collecting: Sammelbilanzkreis
      db/db_bg_name_internal: E.ON Intern
      db/db_bg_name_third_party: Drittbilanzkreis
      dispatch_group:
        one: Abrufgruppe
        other: Abrufgruppen
      distributed_unit: Nominierung
      market: Market
      product: Produkt
      subpool: Subpool
      tso: ÜNB
      rollup_type: Rollup Metrik
      rollup_family: Rollup Gruppierung
      rollup_email_sendings_configuration: Konfiguration der Automatischen Rollup-Emails
      signal_list:
        one: Signalliste
        other: Signallisten

  application:
    meta:
      title: VPP Management
    chromeframe:
      outdated_browser_html: Sie benutzen einen <strong>überalterten</strong> Browser. Bitte <a href="http://browsehappy.com/"> aktualisieren Sie Ihren Browser </a> oder <a href="http://www.google.com/chromeframe/?redirect=true">aktivieren Sie Google Chrome Frame</a> um Ihr Surferlebnis zu verbessern.
    header:
      welcome_message: "%{name}"
      welcome_info: Deine letzte Aktivität war vor %{time}
      nav_customers: Kunden
      nav_notifications: Benachrichtigungen
      nav_user_menu: Benutzermenu
    tech_support:
      help: Hilfe
  navigation:
    breadcrumb:
      edit: Bearbeiten
      add: '%{model_name} hinzufügen'
      upload: '%{model_name} hochladen'
      asset_dg_allocation:
        assets:
          title: Anlagen
        index:
          title: Abrufgruppen
      asset_dg_allocation_sources:
        index:
          title: Änderungsgeschichte
      distributed_unit_sources:
        index:
          title: Änderungsgeschichte
      distributed_units:
        index:
          title: "DUs (%{count})"
      reports:
        index:
          title: Berichte
      report_email_sendings:
        index:
          title: Automatische Berichte-Emails
    main:
      dashboard: Dashboard
      allocations: Zuordnungen
      asset_dg_allocation: Abrufgruppen-Zuordnungen
      asset_dg_allocation_assets: Anlagen-Zuordnungen
      asset_dg_allocation_sources: Zuordnungen-Änderungsgeschichte
      distributed_units: DU Liste
      dispatch_groups: DGs
      nominations: Nominierungen
      reports: Berichte
      report_email_sendings: Automatische Emails
      scheduling_reports: Fahrplan Berichte
      subpools: Subpools
      tso_schedule: "TSO & Kontroll Berichte"
      asset_reports: Anlagen Berichte
      configurations: Konfiguration
      asset_configurations: Asset Konfiguration
      balancing_group_names: Bilanzkreise
      rollup_management: Rollup Management
      rollup_management_asset: Asset Ebene
      rollup_management_dg: Abrufgruppe Ebene
      rollup_errors: Rollup Fehler
      generic_steering_configs: Steuerungstyp
      event_notifications: Benachrichtigungen
    nominations:
      auction_result_upload: Auktionsergebnisse ins VPP hochladen
      distributed_units: Manualle Nominierungen
      distributed_unit_sources: Änderungsgeschichte
      nominateable_volumes: Auktion vorbereiten
      track_history_nominations: Nominierung
      track_history_auctions: Auktion
    subheader:
      edit: '%{model_name} bearbeiten'
  forms:
    actions:
      allocate: Zuteilen
      cancel: Abbrechen
      deallocate: Freigeben
      download: Herunterladen
      show: Anzeigen
      upload: Hochladen
      add: "Neu hinzufügen"
      edit: "Bearbeiten"
      save: Sichern
    select:
      prompt: Auswählen ...
      prompt_all: 'Alle %{model_name}'
      prompt_model_name: 'Bitte %{model_name} wählen'
    csv_format: Format
    from: Von
    to: Zu
    values_for: Werte für
    wait: Bitte warten ...

  lists:
    actions:
      copy: Kopieren
      delete: Entfernen
      confirm_delete: Sind Sie sicher ?
      edit: Bearbeiten
      report: Berichten
      reject: Ablehnen
      submit: Einreichen
      view: Ansehen
      ok: OK
    bulk_actions:
      choose: Aktion auswählen ...
      delete: Entfernen
      submit: dem Händler einreichen
      selected_items_count_html: Ausgewählte Objekte (<span class='count'>%{count}</span>)

  allocations:
    manual_allocation_title: Manuelle Zuordnungen
    visualisation_title: Zuordnungen Visualisierung
    automatic_allocation_title: Automatische Zuordnung

  balancing_group_names:
    index:
      manage_configuration: Bearbeiten
    manage:
      list_configuration: Zurück zur Liste
    scheduling_title: Schedulingbilanzkreis

  automatic_allocations:
    list:
      no_automatic_allocation: Keine Automatisch Zuordnung
      start: Von
      fallback_start: Fallback Von
      end: Bis
      type: Typ
      status: Status
      import_status: Import Ereignesse
      dispatch_groups: Abrufgruppen
      triggers: Auslöser
    show:
      import_result: Import Erbebnis
      no_import_result: Nichts gefunden
    types:
      fallback: Rückfall
      standard: Standard
    status:
      types:
        started: Gestartet
        success: Erfolgreich
        fallback: Rückfall
        data_preparation_failure: Fehler bei Datenaufbereitung
        failure: Fehler
        allocation_not_accepted: Zuordnung nicht akzeptiert
        writing_allocations_failure: Zuordnungen konnten nicht gespeichert werden
        unknown_failure: Unbekannter Fehler ist aufgetreten
    triggers:
      types:
        allocation_type: Eine neue Zuordnung wurde manuell eingegeben
        computation_failed_type: Vorherige Automatische Zuordnung ist fehlgeschlagen
        nomination_type: Eine neue Zuordnung wurde eingegeben
        flex_type: Nicht genügend Flex im Vergleich zum Nominierten
        time_type: Viertelstündliche Zuordnung
        asset_back_from_fault_type: Anlage kommt aus einem Fehler zurück

  dispatch_groups:
    alarms_title: Alarmsignale
    balancing_groups_title: Bilanzkreise
    compensation_title: Kompensation
    general_data_title: Abrufgruppe Details
    is_at_setpoint_detection_title: Is-At-Setpoint Detektion
    merlin_dispatch_title: MeRLin Abruf
    nomination_tool_title: Schnittstelle für Nominierung Trading
    prl_title: Anteilsmäßige Aktivierung (PRL/FFRD)
    rcc_title: RCC Hamburg Alarme
    setpoint_reached_title: Rampenende Erkennung
    sally_setpoint_title: Auto-Sollwert (Sally)
    signals_title: Erzeugte Signals
    spot_optimization_title: Spot Optimierung
    ui_alerts_title: VPP Ops Warnungen
    rollups_enabled: Rollups aktivieren
    automatic_allocation: Automatisch Zuordnung
    ext_backup: Externe Besicherung
    ext_backup_info_html: "<b>Die externe Besicherung wird aktiviert, wenn die Funktionalität als aktiv markiert ist und wenn während der Pufferzeit die folgende Behauptung wahr ist: <br/> verfügbare Flexibilität ohne externe Besicherung < Nominierte Gesamtkapazität - MAX (Schwellenwert MW; Schwellenwert% der Nominierung * Nominierte Gesamtkapazität)</b>"
    individual_alarms_enabled_info_html: Ein Ereignis wird erzeugt, wenn der verfügbare Flex niedriger ist als der prozentuale Schwellenwert des gehandelten Volumens über die in Sekunden definierte Dauer.
    individual_alarm_overdelivery_pos_enabled_info_html: Bei einem positiven Sollwert, wird ein Ereignis erzeugt, wenn die Abrufsgruppe länger als die angenommene Zeit in Sekunden und um mehr als den eingegebenen MW Volumen überliefert.
    individual_alarm_overdelivery_neg_enabled_info_html: Bei einem negativen Sollwert, wird ein Ereignis erzeugt, wenn die Abrufsgruppe länger als die angenommene Zeit in Sekunden und um mehr als den eingegebenen MW Volumen überliefert.
    parameters_title: Lokale Steuerungsparameter
    parameters_info_html: <b>Wenn eine Anlage aus der Ferne konfiguriert werden kann und der angegebenen Abrufsgruppe zugeordnet ist, werden die Parameter automatisch an die lokale Box gesendet.</b>

  distributed_unit:
    warning_no_matching_bid_for_auction_result: "Im System wurden keine Gebote hochgeladen, die mit diesen Ergebnissen übereinstimmen"
    warning_no_matching_auction_result_for_bid: "Für die folgende Angebote ist kein Ergebnis vorhanden"

  distributed_units:
    manual_nomination_title: Manuelle Nominierung
    visualisation_title: Nominierung Visualisierung

  nominateable_volumes:
    form:
      date: Lieferdatum
      market: Produkt
      product_intervals: Zeitscheiben
      product_intervals_all: Alle Zeitscheiben
      submit: Auktion vorbereiten
    actions:
      download_meta_data: Meta-Data
      download_volumes: Auktion

  report_email_sendings:
    id: '#'
    report_type: Bericht
    report_date: Datum
    active: Aktiv
    success: Erfolg
    email_to: An
    error: Fehler
    report_file_name: Name
    has_attachment: Att.
    report_parameters: Parameter
    title: Automatische Emails der letzten Tagen
    created: Um
    configuration:
      title: Optionen
    index:
      edit_configuration: Konfiguration

  reports:
    messages:
      invalid_collecting_bgs: "Ungültige Collecting BK: %{bgs}. Genau ein BG muss definiert sein."
      invalid_supplier_bgs: "Ungültige Supplier BK: %{bgs}. Genau ein BG muss definiert sein."
      missing_bgs: "Es wurden Anlagen gefunden, welche Flexibilität geliefert haben, aber keinem Bilanzkreis zugeordnet sind: %{asset_ids}."
      multiple_bgs: "Es wurden Anlagen gefunden, welche an mehreren Bilanzkreise zugeorden sind: %{asset_ids}."
      no_asset_with_balancing_group: "Keine Anlagen für das angegebenen Bilanzkreis %{balancing_group_id}."
      no_flex_delivered: "Keine %{energy_direction} Flexibilität wurde geliefert in %{tso} für %{market} am %{date}."
      no_flex_delivered_balancing: "Keine Flexibilität wurde geliefert in %{tso} für %{market} am %{date}."
      no_flex_delivered_balancing_third_party: "Keine Flexibilität wurde geliefert in %{tso} am %{date} zwischen %{scheduling_balancing_group_id} und %{collecting_balancing_group_id}."
      no_flex_delivered_no_schedule_balancing: "Keine gelieferte Flexibilität und kein Fahrplan in %{tso} für %{market} am %{date}."
      no_setpoint_dispatched: "Keine %{energy_direction} Setpoints in %{tso} für %{market} am %{date}."
      no_schedule: "Kein Fahrplan für %{time} in %{tso} für %{market}."

  report_email_sendings_configurations:
    form:
      one_per_line: Eine per Zeile

  scheduling_reports:
    form:
      date: Datum
      energy_direction: Energie Richtung
      action:
        regenerate_scheduling_balancing_groups_third_party_reports: Verschicke Scheduling an dritte BKV
        scheduling_control: Scheduling Control
        scheduling_edg: Scheduling an EDG
        scheduling_egc: Scheduling an UGC
        scheduling_third_party: Scheduling an Dritte BKs
        regenerate_bg_asset_activations_reports: Verschicke BK6-17-046 Berichte

  asset_rollups:
    index:
      title_new_rollup_family: Definiere neue Rollup Gruppierung
      title_rollup_regeneration: Rollup neu erstellen
      title_rollup_management: Rollup Management Visualisierung
    form_rollup_family:
      rollup_family_created: Rollup Gruppierung wurde erstellt
      rollup_family_disabled: Rollup Gruppierung disabled
      rollup_family_disable_error: Die Datenaggregation pro Anlage auf Basis von %{rollup_family} wird weiterhin in Live-Verträgen mit Kunden verwendet %{customers}. Ändern Sie die in ihrem Vertrag eingetragene Datenaggregation, bevor Sie sie aus der Konfiguration löschen.
    form_generate_rollups:
      select_rollup_type: Rollup Metrik auswählen
      select_item_type: Typ auswählen
      select_asset: Anlage auswählen
      select_rollup_family: Rollup Gruppierungsname auswählen
      select_interval: Intervall auswählen
      btn_regenerate: Neu erstellen
      please_select_rollup: Bitte Rollup Metrik auswählen
      please_select_asset: Bitte Anlage auswählen
      please_select_rollup_family: Bitte Rollup Gruppierungsname auswählen
      rollup_regeneration_ok: Rollup Erstellung wurde gestartet
      rollup_regeneration_error: Fehler bei der neuen Erstellung von Rollups

  dispatch_group_rollups:
    index:
      title_rollup_regeneration: Rollup neu erstellen
      title_rollup_management: Rollup Management Visualisierung
    form_generate_rollups:
      select_rollup_type: Rollup Metrik auswählen
      select_dispatch_group: Abrufgruppe auswählen
      select_interval: Intervall auswählen
      btn_regenerate: Neu erstellen
      please_select_rollup: Bitte Rollup Metrik auswählen
      please_select_dispatch_group: Bitte Abrufgruppe auswählen
      rollup_regeneration_ok: Rollup Erstellung wurde gestartet
      rollup_regeneration_error: Fehler bei der neuen Erstellung von Rollups

  rollup_regenerations:
    track_history: Historie
    index:
      no_rollup_generations: Keine neue Rollup Erstellung
    show:
      title_details: Details der Rollup Erstellung
  rollup_errors:
    index:
      no_rollup_errors: Keine Rollup Fehler
    show:
      title_details: Details der Rollup Fehler

  event_notifications:
    index:
      dg_heartbeat_mirror_failure: ÜNB-Signalstreckenfehler
      dg_heartbeat_mirror_failure_info_html: "Eine Benachrichtigung wird gesendet, wenn ein Kommunikationsproblem auf mindestens <br/>einer TSO-Leitung zwischen dem VPP und dem TSO erkannt wird"
      bidding_api_failure: Fehler beim Hochladen auf Regelleistung.net
      bidding_api_failure_info_html: Im Falle eines Problems während des Hochladens der Gebote auf die Website Regelleistung.net wird eine Nachricht mit den Geboten als XML gesendet
      bidding_api_success: Erfolgreiches Gebots-Uploads auf Regelleistung.net
      bidding_api_success_info_html: Es wird eine Nachricht gesendet, die über den erfolgreichen Upload von Geboten auf den Markt informiert.
      auction_download_failure: Fehler beim Runterladen der Geboten
      auction_download_failure_info_html: Wenn kein Ergebnis für alle im System gespeicherten Gebote vorliegt, wird eine Nachricht gesendet.
      auction_download_success: Erfolgreiches Runterladen der Gebote
      auction_download_success_info_html: Wenn die Auktionsergebnisse erfolgreich heruntergeladen wurden, wird eine Nachricht mit den Auktionsergebnissen an die Empfänger gesendet.
      auction_download_results_exceed_bids: Auktionsergebnisse mit höheren Volumen als die vorherigen Gebote
      auction_download_results_exceed_bids_info_html: Wenn Auktionsergebnisse heruntergeladen wurden und diese haben einen höheren Volumen als die vorherigen Gebote, dann wird eine Nachricht mit den Auktionsergebnissen an die Empfänger gesendet.
      dg_over_delivery: Pool overdelivery
      dg_over_delivery_info_html: Eine Benachrichtigung an die Empfänger wird gesendet, wenn ein Überlieferungsereignis der Abrufsgruppe angelegt wird. Das Überlieferungsereignis wird auf der Konfigurationsseite der Abrufsgruppe definiert.
      dg_available_flex_too_low: VPP Pool Probleme
      dg_available_flex_too_low_info_html: Event triggered when the per-direction available flex of a DG is below the configured threshold of the nominated flex.
      dg_merlin_issue: MeRLin Probleme
      dg_merlin_issue_info_html: In case of an issue with the Merlin Client a notification will be sent to the recipients.
      v2g_optimization_submission_failure: V2G-EE Optimisierung Probleme
      v2g_optimization_submission_failure_info_html: Eine Benachrichtigung an die Empfänger wird gesendet, wenn Probleme beim EE V2G Optimisierungs Prozess auftreten.
      dlm_asset_fault: Eine Benachrichtigung wird gesendet, wenn ein DLM-Asset während der Zuweisung an eine DG in einen Fehlerzustand gerät.
      dlm_execution_failure: Eine Benachrichtigung wird generiert, wenn ein allgemeiner Fehler bzgl. DLM auftritt.
      virta_dlm_api_failure: Eine Benachrichtigung wird generiert, wenn die Kommunikation mit der Virta-DLM-API nicht hergestellt werden kann oder fehlerhaft ist.
      virta_admin_api_failure: Eine Benachrichtigung wird generiert, wenn die Kommunikation mit der Virta Admin API nicht hergestellt werden kann oder fehlerhaft ist.
    form:
      email: E-Mail
      call: Anruf
      sms: SMS

  errors:
    format:  "%{attribute}: %{message}"
    messages:
      unable_to_get_allocated_flex: "Kann Flex nicht lesen: %{reason}"
      unable_to_get_allocations: "Kann Zuordnungen nicht lesen: %{reason}"
      unable_to_get_nominateable_volumes: "Auktionen konnten nicht vorbereitet weden: %{reason}"
      unable_to_post_allocations: "Zuordnungen konnten nicht hochgeladen werden: %{reason}"
      unable_to_upload_allocations_file: "Datei kann nicht hochgeladen werden.\nBitte eine CSV Datei mit dem erwarteten Format hochladen.\n \n%{reason}"
      unable_to_post_dus: "DUs konnten nicht hochgeladen werden: %{reason}"
      unable_to_upload_dus_file: "Datei kann nicht hochgeladen werden.\nBitte eine CSV Datei mit dem erwarteten Format hochladen.\n \n%{reason}"
      unable_to_upload_auction_results_file: "Datei kann nicht hochgeladen werden.\nBitte eine Excel Datei mit dem erwarteten Format hochladen.\n \n%{reason}"
      unable_to_delete_dg: "Der %{dg_name} kann nicht gelöscht werden da für diese SubPools %{subpools} angelegt sind. Zuerst müssen die SubPools gelöscht werden."
      unable_to_delete_dg_in_use: "Der %{dg_name} kann nicht gelöscht werden, weil es verwendet wird."
      phone_number_invalid: "Die Telefonnummer muss mit + und die Länderkennzahl beginnen. Nur Ziffern erlaubt"
      invalid_tender_config: "Invalid tender config."
      dispatch_groups_same_market: "Alle Abrufgruppen einer Ausschreibung müssen zum gleichen Martk gehören."
      duplicate_tender_with_same_bidding_method_and_market: Es existiert bereits eine Ausschreibung mit der gleichen Angebotsmethode und Markt, das sich mit dem Gültigkeitsintervall überschneidet.
      duplicate_tender_with_same_bidding_method_and_market_site: Es existiert bereits eine Ausschreibung mit der gleichen Angebotsmethode, Markt und Site ID, das sich mit dem Gültigkeitsintervall überschneidet.
      cannot_create_nomination_for_spot_optimization: "Cannot create nominations for Spot Optimization"
      opti_result_file_invalid: "Ungültige Daten in Zeile(n) %{lines}."
      opti_result_file_invalid_header: "Ungültige/fehlender CSV-Kopfzeile(n) %{header}"
      opti_result_file_invalid_not_enough_intervals: Die Datei muss mindestens 2 Zeilen enhalte.
      opti_result_file_invalid_timezone: Die Datei muss die Datensätze für die gleiche Zeitzone erhalten.
      opti_result_file_invalid_settlement_interval: Ungültiges Abrechnungsintervall. Das Abrechnungsintervall muss 30 Minuten betragen.
      opti_result_file_missing_allocations: Fehlende Zuordnung für das Zeitintervall %{from_time} bis %{to_time}
      opti_result_file_invalid_asset: Anlage %{asset_id} nicht gefunden.
      opti_result_file_invalid_soc: Ungültiger SoC in Zeile(n) %{lines}. Der Wert muss zwischen %{low_val} und %{high_val} sein.
      opti_result_file_invalid_activation_dc_pos: Ungültiger positiver activation_dc_pos in Zeile(n) %{lines}. Der Wert muss zwischen %{low_val} und %{high_val} sein.
      opti_result_file_invalid_activation_dc_neg: Ungültiger negativer activation_dc_neg in Zeile(n) %{lines}. Der Wert muss zwischen %{low_val} und %{high_val} sein.
      opti_result_file_invalid_activation_dm_pos: Ungültiger positiver activation_dm_pos in Zeile(n) %{lines}. Der Wert muss zwischen %{low_val} und %{high_val} sein.
      opti_result_file_invalid_activation_dm_neg: Ungültiger negativer activation_dm_neg in Zeile(n) %{lines}. Der Wert muss zwischen %{low_val} und %{high_val} sein.
      opti_result_file_invalid_activation_dr_pos: Ungültiger positiver activation_dr_pos in Zeile(n) %{lines}. Der Wert muss zwischen %{low_val} und %{high_val} sein.
      opti_result_file_invalid_activation_dr_neg: Ungültiger negativer activation_dr_neg in Zeile(n) %{lines}. Der Wert muss zwischen %{low_val} und %{high_val} sein.
      opti_result_file_invalid_available_char_power: Ungültiger verfügbare Ladeleistung in Zeile(n) %{lines}. Der Wert muss zwischen %{low_val} und %{high_val} sein.
      opti_result_file_invalid_available_dischar_power: Ungültige verfügbare Entladeleistung in Zeile(n) %{lines}. Der Wert muss zwischen %{low_val} und %{high_val} sein.
      opti_result_upload: "Das Dateiformat wird nicht erkannt. Bitte überprüfen Sie und laden Sie die Datei erneut hoch."
      perf_data_asset_not_supported: Anlage nicht unterstützt.
      perf_data_invalid_file_extension: Ungültige Dateiendung. Unterstützte Dateien sind %{supported_extension}.
      perf_data_invalid_file_name: Ungültiger Dateiname. Das unterstützte Dateinamenformat ist %{name_format}.
      perf_data_invalid_times: Ungültige Zeitstempel. Die Startzeit muss vor der Endzeit liegen und die Differenz zwischen ihnen muss ein Vielfaches von 1 Stunde sein.
      bod_file_upload: "Das Dateiformat wird nicht erkannt. Bitte überprüfen Sie und laden Sie die Datei erneut hoch."
      margin_data_file_upload: "Das Dateiformat wird nicht erkannt. Bitte überprüfen Sie und laden Sie die Datei erneut hoch."
      invalid_prices_for_tender: "Invalid prices file for Tender %{tender_name} in file %{file_name}: %{errors}"
      price_forecast_file_invalid_header: "Ungültige/fehlender CSV-Kopfzeile(n) %{header} in file %{file_name}"
      err_missing_price_forecast: "Missing prices in prices forecast file %{file_name}"
      margin_data_file_invalid_header: "Ungültige/fehlender CSV-Kopfzeile(n) %{header} in file %{file_name}"
      err_missing_margin_data: "Missing margin data in file %{file_name}"
      err_invalid_margin_data: "Invalid margin data at line(s) %{lines}"
      invalid_uuid: "Ungültige UUID"


  time:
    formats:
      du_start_time: ! '%d. %B, %H:%M Uhr %Z'

  asset_configuration:
    type: Type
    title: "Anlagenkonfiguration"
    box_type_configuration: "Boxvariante Konfiguration"
    signal_type_configuration: "Singalvariante Konfiguration"
    box_type: "Box Variante"
    protocol: "Protokoll"
    port: "Port"
    vpn_type: "VPN Variante"
    contact_person_email: "Kontaktperson E-Mail"
    contact_person_name: "Kontaktperson Name"
    contact_person_phone: "Kontaktperson Telefonnummer"
    contact_person_first_name: "Kontaktperson Vorname"
    contact_person_last_name: "Kontaktperson Familienname"
    signal_type: "Signalvariante"
    direction: "Richtung"
    name: "Name"
    register_address: "Register Adresse"
    type_register: "Registervariante"
    start_address: "Startadresse"
    signal_scaling: "Signalskalierung"
    min_kw: "Min KW"
    min_adc: "Min ADC"
    max_kw: "Max KW"
    max_adc: "Max ADC"
    gateway: "Gateway"
    sally_name: "Sally Name"
    scada_name: "Scada Name"
    submit_action: "Add"
    edit_action: "Edit"

  signal_list:
    signal_direction:
      read: "Lesen (Kunde zu VPP)"
      write: "Schreiben (VPP zu Kunde)"
    vpp_name: "VPP Name"
    direction: "Richtung"
    msg_list_created: "Signalliste erzeugt."
    msg_list_create_failed: "Signalliste erzeugen fehlgeschlagen: %{detail}"
    msg_list_updated: "Bezeichnung der Signalliste geändert."
    msg_list_in_use: "Die Signalliste kann nicht gelöscht werden, da sie momentan in Gebrauch ist."
    msg_list_deleted: "Signalliste gelöscht."
    msg_signal_added: "Signal hinzugefügt."
    msg_signal_add_failed: "Neues Signal erzeugen fehlgeschlagen: %{detail}"
    msg_signal_updated: "Signal geändert."
    msg_signal_update_failed: "Änderung des Signals fehlgeschlagen: %{detail}"
    msg_signal_removed: "Signal wurde von der Liste entfernt."
    back_to_list: "Zurück zur Liste"
  bid:
    upload_bids: 'Angebote hochladen'
    delivery_date: 'Lieferdatum'
    product: 'Produkt'
    bids_overview: 'Angebotsübersicht'
    download_all_bids: 'Alle Angebote runterladen'
    last_uploaded: 'Letzte hochgeladene Datei wurde am: %{date} von %{username}'
    not_uploaded: 'Für das ausgewählte Lieferdatum und den ausgewählten Markt wurden keine Gebote hochgeladen'
    auction_closed: 'Geschlossene Auktion'
    delete_bids: 'Angebote löschen'
    th_market: 'Markt'
    th_product: 'Produkt'
    th_tso: 'ÜNB'
    th_capacity_price: 'Leistungspreis (¤/MW)'
    th_energy_price: 'Arbeitspreis (¤/MWh)'
    th_payment_direction: 'Zahlungsrichtung'
    th_capacity: 'Angebotsleistung (MW)'
    success_deleted: 'Die Angebote wurden erfolgreich gelöscht. '
    success_uploaded: 'Die Angebote wurden erfolgreich hochgeladen'
    success_uploaded_automatic: 'Nomination bids were automatically created for the bids that were not previously auctioned with a nomination bid.'
    err_no_file: 'Bitte wählen Sie eine Datei zum Hochladen aus.'
    err_col_missing: 'Fehlende Spalte %{name}. Bitte überprüfen Sie und laden Sie die Datei erneut hoch.'
    err_missing: 'Fehlender Name %{name}. Bitte überprüfen Sie und laden Sie die Datei erneut hoch.'
    err_missing_date: 'Fehlender Name %{name} oder das Datumsformat sollte tt/mm/yyyy oder tt.mm.yyyy sein. Bitte überprüfen Sie und laden Sie die Datei erneut hoch.'
    err_missing_time: 'Fehlender Name %{name} oder das Datumsformat sollte YYYY-MM-DDTHH:MM:SSZ sein. Bitte überprüfen Sie und laden Sie die Datei erneut hoch.'
    err_missing_market: 'Der Markt ist obligatorisch und muss einer der folgenden sein: DC, DM, DR.'
    err_asset_id: 'Anlage ID, in Spalte I erwartet, nicht vorhanden.'
    err_external_id: 'External Anlage ID, in Spalte I erwartet, nicht vorhanden.'
    err_validation: 'Validierungsfehler'
    err_no_worksheet: 'Es konnte kein Arbeitsblatt gefunden werden. Bitte überprüfen Sie und laden Sie die Datei erneut hoch.'
    err_ErrorAuctionClosed: 'Die Auktion ist für diesen Zeitraum und für diesen Markt nicht offen.'
    err_ErrorAuctionMrlClosedButSrlOpen: 'Die Auktion ist für diesen Zeitraum und für diesen Markt nicht offen.'
    err_ErrorAuctionSrlClosedButMrlOpen: 'Die Auktion ist für diesen Zeitraum und für diesen Markt nicht offen.'
    err_ErrorTSONotFound: 'Mindestens eine Erbringungszone wird nicht erkannt.'
    err_ErrorInvalidEnergyDirection: 'Mindestens eine Energierichtung wird nicht erkannt.'
    err_ErrorInvalidTSO: "Mindestens eine Erbringungszone wird nicht erkannt."
    err_ErrorInvalidMarket: 'Mindestens ein Markt wird nicht erkannt.'
    err_ErrorInvalidProductName: 'Mindestens ein Produktname wird nicht erkannt.'
    err_ErrorInvalidPaymentDirection: 'Mindestens eine AP-Zahlungsrichtung wird nicht erkannt.'
    err_ErrorFlexVolumeNotWholeValue: 'Mindestens ein volumen ist nicht auf MW gerundet.'
    err_ErrorDGEnabledForBidsMissing: 'Im System sind keine Abrufsgruppen in %{tsoName} und %{marketName} für die Annahme von Angeboten konfiguriert. Bitte aktivieren Sie die Trading-Funktionalität in der Konfiguration der Abrufsgruppe.'
    err_ErrorBGNotFoundForBid: 'Auf Abrufsgruppenebene sind in %{tsoName} und %{marketName} zum vermarkteten Datum keine Bilanzkreise definiert. Bitte tragen Sie in der Abrufsgruppenkonfiguration einen Bilanzkreis ein.'
    err_ErrorMinFlexBidRule: 'Die Mindestleistung eines Angebots beträgt 5 MW. Ein Angebot von weniger als 5 MW kann nur hochgeladen werden, wenn es sich um das einmalige Angebot für diesen Tag, dieses Produkt und diesen Markt handelt. Bitte überprüfen Sie die Gebote in %{tsoName}.'
    err_ErrorInvalidFloatingEnergyPrice: 'Nur drei Zahlen nach dem Komma für den Energiepreis werden akzeptiert.'
    err_ErrorInvalidFloatingCapacityPrice: 'Nur zwei Zahlen nach dem Komma für den Kapazitätspreis werden akzeptiert.'
    err_ErrorInvalidAssetID: 'Anlage nicht vorhanden.'
    err_ErrorInvalidContractType: "Kein Vertrag vom Typ 'Externe Besicherung' vorhanden für diesen Kunden."
    err_ErrorFlexExceedsPrequalification: 'Präqualifikationslimit überschritten.'
    err_ErrorWrongDeletionCriteria: 'Bereits bestehende Gebote für dieses Zeitintervall und Produkt werden unverändert gelassen.'
    err_row: 'Zeile %{row_num}: '
    err_pms_bidding_windows: 'Es konnten keine Gebotsfenster angezeigt werden: %{reason}.'
    err_pms_delete: 'Auktionen konnten nicht gelöscht werden: %{reason}.'
    err_pms_upload: "Das Dateiformat wird nicht erkannt. Bitte überprüfen Sie und laden Sie die Datei erneut hoch."
    err_pms_v2g_optimization_job: "Optimisierung konnte nichte gestartet werden: %{reason}."
    please_check_and_try_again: "Bitte überprüfen Sie und laden Sie die Datei erneut hoch."
    warn_auction_results_exceed_bids: "Es wurden Auktionsergebnisse ohne einen vorherigen valdierten Optimierungslauf oder mit einem höheren Volumen als die vorherigen Gebote hochgeladen (%{description})."
  bod:
    invalid_asset_id: "Invalid Assetid at row %{index}"
    invalid_datetime: "Invalid Datetime at row %{index}"
    invalid_pair: "Invalid Pair at row %{index}"
    invalid_power_mw: "Invalid Volume at row %{index}"
    invalid_pair_or_power_mw: "Pair and Volume don't have the same sign at row %{index}"
    invalid_offer_price: "Invalid Offer_Price at row %{index}"
    invalid_bid_price: "Invalid Bid_Price at row %{index}"
  asset_optimization:
    err_asset_optimization_job: "Optimisierung konnte nichte gestartet werden: %{reason}."
    err_invalid_price: "Invalid prices at line(s) %{lines}"
    err_get_open_tenders: 'Cannot get open tenders: %{reason}.'
    err_get_market_positions: 'Cannot get market positions: %{reason}.'
  angular:
    locale:
      de: Deutsche
      en-GB: English
