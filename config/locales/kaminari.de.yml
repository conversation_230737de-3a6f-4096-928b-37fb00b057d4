de:
  helpers:
    page_entries_info:
      more_pages:
        display_entries: "<PERSON>ez<PERSON><PERSON> werden %{entry_name} <b>%{first}&nbsp;-&nbsp;%{last}</b> von insgesamt <b>%{total}</b>"
      one_page:
        display_entries:
          zero: "%{entry_name} wurde nicht gefunden"
          one: "Angezeigt wird <b>1</b> %{entry_name}"
          other: "<PERSON>ez<PERSON><PERSON> werden <b>alle %{count}</b> %{entry_name}"
  views:
    pagination:
      first: "&laquo; Erste"
      last: "Letzte &raquo;"
      previous: "&lsaquo; Vorherige"
      next: "Nächste &rsaquo;"
      truncate: "&hellip;"
