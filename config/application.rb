require File.expand_path('../boot', __FILE__)

# Pick the frameworks you want:
require "active_record/railtie"
require "action_controller/railtie"
require "action_mailer/railtie"
require "sprockets/railtie"
require "rails/test_unit/railtie"

require 'action_cable'
require 'action_cable/engine'
require 'action_cable/server'

require 'prometheus/client'
require 'prometheus/client/data_stores/direct_file_store'

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(:default, Rails.env)

module VppManagement
  class Application < Rails::Application
    # Settings in config/environments/* take precedence over those specified here.
    # Application configuration should go into files in config/initializers
    # -- all .rb files in that directory are automatically loaded.

    # Set Time.zone default to the specified zone and make Active Record auto-convert to this zone.
    # Run "rake -D time" for a list of tasks for finding time zone names. Default is UTC.
    TIME_ZONE = ENV['VPP_TIME_ZONE'] || 'Europe/Berlin'
    # config.time_zone = 'Central Time (US & Canada)'
    config.time_zone = ENV['TZ'] = TIME_ZONE

    config.i18n.load_path += Dir[Rails.root.join('config', 'locales', 'vpp-db-i18n', 'locales', '*.{rb,yml}').to_s]
    config.i18n.default_locale = ENV['DEFAULT_LOCALE'] || :'en-GB'

    config.autoload_paths += [
      Rails.root.join("lib"),
      Rails.root.join("app", "assets", "dist"),
      Rails.root.join('app', 'subscribers'),
      Rails.root.join('app', 'decorators'),
    ]
    config.assets.paths += [
      Rails.root.join("app", "assets", "graphics"),
      Rails.root.join("app", "assets", "fonts"),
      Rails.root.join("app", "assets", "dist")
    ]
    # IE fixes
    config.assets.precompile += [
      'ie8.css',
      'iefixes.js',
      'html5shiv.js'
    ]
    # fonts
    config.assets.precompile += %w( .svg .eot .woff .ttf .otf )

    # all image files
    config.assets.precompile += %w(*.png *.jpg *.jpeg *.gif)

    config.active_record.pluralize_table_names = false

    Prometheus::Client.config.data_store = Prometheus::Client::DataStores::DirectFileStore.new(dir: '/tmp/prometheus/')
  end
end