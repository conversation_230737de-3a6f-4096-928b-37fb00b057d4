VppManagement::Application.configure do

  config.cache_classes = true

  config.eager_load = true

  config.consider_all_requests_local       = false
  config.action_controller.perform_caching = true

  # Force all access to the app over SSL, use Strict-Transport-Security, and use secure cookies.
  if ENV['DISABLE_SSL'] != 'true'
    config.force_ssl = true
    config.ssl_options = {
      hsts: { subdomains: true },
      redirect: { exclude: -> request { request.path =~ /health/  || request.path =~ /metrics/ } }
    }
  end

  config.action_mailer.delivery_method = :smtp
  config.action_mailer.smtp_settings = {
      :address => ENV['SMTP_HOST'],
      :port => ENV['SMTP_PORT'],
      :enable_starttls_auto  => ENV['SMTP_TLS'] || false,
      :user_name => ENV['SMTP_USER'],
      :password => ENV['SMTP_PASS']
  }
  config.action_mailer.perform_deliveries     = true
  config.action_mailer.raise_delivery_errors  = true

  config.assets.compile = false
  config.assets.digest = true
  config.assets.version = '1.0'

  # disable syslogger
  #config.logger = ActiveSupport::TaggedLogging.new(Logger::Syslog.new(Rails.application.class.module_parent_name, Syslog::LOG_LOCAL6))
  config.logger = ActiveSupport::Logger.new(STDOUT)
  config.lograge.enabled = true
  config.lograge.formatter = Lograge::Formatters::Logstash.new
  config.lograge.custom_options = lambda do |event|
    if event.payload[:exception_object]
      {
        error_trace: event.payload[:exception_object].backtrace.join("\n")
      }
    end
  end
  config.log_level = :info

  config.i18n.fallbacks = true
  config.i18n.default_locale = ENV['DEFAULT_LOCALE'].try(:to_sym) || :de

  config.active_support.deprecation = :notify
end
#LocalizedDecorator.raise_missing_translation = false
