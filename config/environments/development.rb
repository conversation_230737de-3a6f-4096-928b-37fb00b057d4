VppManagement::Application.configure do
  config.cache_classes = false
  config.eager_load = false

  config.consider_all_requests_local       = true
  config.action_controller.perform_caching = false
  config.action_mailer.raise_delivery_errors = false
  config.active_support.deprecation = :log

  config.assets.debug = true

  config.action_mailer.delivery_method = :smtp
  config.action_mailer.smtp_settings = {
      :address => ENV['SMTP_HOST'],
      :port => ENV['SMTP_PORT'],
      :enable_starttls_auto  => ENV['SMTP_TLS'] || false,
      :user_name => ENV['SMTP_USER'],
      :password => ENV['SMTP_PASS']
  }
  config.action_mailer.perform_deliveries     = true
  config.action_mailer.raise_delivery_errors  = true

end

# set to true to raise missing translation errors as soon as possible
# LocalizedDecorator.raise_missing_translation = false
