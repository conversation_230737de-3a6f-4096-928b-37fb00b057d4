default: &default
  adapter: postgresql
  encoding: unicode
  pool: 50
  host:     <%= ENV['VPP_MANAGEMENT_DATABASE_HOST'] %>
  database: <%= ENV['VPP_MANAGEMENT_DATABASE_NAME'] %>
  username: <%= <PERSON>NV['VPP_MANAGEMENT_DATABASE_USERNAME'] %>
  password: "<%= ENV['VPP_MANAGEMENT_DATABASE_PASSWORD'] %>"
  sslmode: <%= ENV.fetch('VPP_MANAGEMENT_DATABASE_SSLMODE') { 'verify-full' } %>
  sslrootcert: 'config/certs/root.pem'

development:
  <<: *default

integration:
  <<: *default

staging:
  <<: *default

test:
  adapter: sqlite3
  database: db/test.sqlite3
  pool: 5
  timeout: 5000

production:
  <<: *default
