VppManagement::Application.routes.draw do
  mount VppAuthentication::Engine => '/vpp_authentication'

  root :to => 'angular#index'
  get 'health' => 'health#index'

  resources :v2g_optimization_jobs, only: [] do
    get :download, on: :member
  end

  resources :steering_optimizations, only: [] do
    get :download, on: :member
  end

  resources :bidding_optimizations, only: [] do
    get :download, on: :member
  end

  resources :asset_dg_allocation_sources, only: [] do
    get :download, on: :member
    get :download_auto_allocation, on: :member
  end

  get 'configurations' => 'content#configurations', as: 'configurations'

  get 'locale/:locale' => 'application#switch_locale', as: 'switch_locale'
  get 'timezone'  => 'application#switch_timezone'

  post 'current_user' => 'application#set_current_user', as: 'set_current_user'
  get  'sudo'         => 'application#set_current_user_super_admin'
  get 'remove_rollup_errors', to: 'rollup_errors_remove#remove'
  get 'remove_ws_tokens', to: 'ws_tokens_remove#remove'

  mount ActionCable.server => '/cable'

  # Angular
  get 'dashboard' => 'angular#index'
  get 'history' => 'angular#index'
  get 'history/bid_detail/:id' => 'angular#index'
  get 'history(/:tab)' => 'angular#index'
  get 'history/dlm-charging-sessions' => 'angular#index'
  get 'nominations' => 'angular#index'
  get 'nominations(/:tab)' => 'angular#index'
  get 'nominations/nomination_detail/:id' => 'angular#index'
  get 'nominations(/:tab)' => 'angular#index'
  get 'rollups' => 'angular#index'
  get 'rollups/rollup-error-detail/:id' => 'angular#index'
  get 'rollups(/:tab)' => 'angular#index'
  get 'auctions' => 'angular#index'
  get 'auction_configs' => 'angular#index'
  get 'allocations' => 'angular#index'
  get 'allocations/manual-allocation/:id' => 'angular#index'
  get 'allocations/automatic-allocation/:id' => 'angular#index'
  get 'allocations(/:tab)' => 'angular#index'
  get 'event_notifications_ribbon' => 'angular#index'
  get 'event_notifications_ribbon(/:tab)' => 'angular#index'
  get 'asset_configuration_ribbon(/:tab)' => 'angular#index'
  get 'market_configuration_ribbon(/:tab)' => 'angular#index'
  get 'market_configuration_ribbon/dispatch-groups/:id' => 'angular#index'
  get 'rollup_configuration_ribbon(/:tab)' => 'angular#index'
  get 'reporting' => 'angular#index'
  get 'reporting(/:tab)' => 'angular#index'
  get 'scheduling_reports_ribbon' => 'angular#index'
  get 'scheduling_reports_ribbon(/:tab)' => 'angular#index'
  get 'automatic-report-emailings' => 'angular#index'
  get 'automatic-report-emailings(/:tab)' => 'angular#index'
  get 'bidding' => 'angular#index'
  get 'bidding(/:tab)' => 'angular#index'
  get 'reporting-emails/reporting_email_detail/:id' => 'angular#index'

  namespace :api, defaults: { format: 'json' } do
    namespace :v1 do
      post 'charts/allocations/download' => 'charts#download_allocations_csv'
      get 'charts/allocations' => 'charts#allocations'
      get 'assets' => 'asset#list'
      get 'assets_for_opti_results_upload' => 'asset#for_opti_results_upload'
      get 'assets_for_optimization' => 'asset#for_optimization'
      get 'assets_for_perf_data_upload' => 'asset#for_perf_data_upload'
      get 'assets_from_nomination_bids' => 'asset#from_nomination_bids'
      get 'asset_opti_results_count' => 'asset#opti_results_count'
      get 'assets_for_bmu' => 'asset#for_bmu'
      get 'tsos' => 'tso#list'
      get 'tso_names' => 'tso#tso_names'
      get 'markets' => 'market#list'
      get 'auction/results' => 'auction#results'
      get 'auction/download_results' => 'auction#download_results'
      get 'auction/:id/meta_data' => 'auction#meta_data'
      get 'auction/:id/download' => 'auction#download'
      get 'auction_configs' => 'auction_config#list'
      get 'auction_configs_with_names' => 'auction_config#list_with_names'
      get 'auction_configs_with_names_for_asset_optimization' => 'auction_config#list_with_names_for_asset_optimization'
      post 'auction_configs/create' => 'auction_config#create'
      post 'auction_configs/:id/update' => 'auction_config#update'
      delete 'auction_configs/:id' => 'auction_config#destroy'
      get 'dispatch_groups' => 'dispatch_group#list'
      get 'dispatch_groups_list' => 'dispatch_group#dispatch_groups_list'
      get 'dispatch_group/dispatch_group_details' => 'dispatch_group#dispatch_group_details'
      get 'dispatch_group/form_data' => 'dispatch_group#form_data'
      post 'dispatch_group/create_dispatch_group' => 'dispatch_group#create_dispatch_group'
      post 'dispatch_group/update_dispatch_group' => 'dispatch_group#update_dispatch_group'
      post 'dispatch_group/delete_dispatch_group' => 'dispatch_group#delete_dispatch_group'
      post 'dispatch_group/create_balancing_group' => 'dispatch_group#create_balancing_group'
      post 'dispatch_group/update_balancing_group' => 'dispatch_group#update_balancing_group'
      post 'dispatch_group/delete_balancing_group' => 'dispatch_group#delete_balancing_group'
      get 'history/asset_manual_allocations' => 'history#asset_manual_allocations'
      get 'history/asset_automatic_allocations' => 'history#asset_automatic_allocations'
      get 'history/nominations' => 'history#nominations'
      get 'history/auctions' => 'history#auctions'
      get 'history/report_email_recipients' => 'history#report_email_recipients'
      get 'history/reporting-emails' => 'history#report_emails'
      get 'history/report_email/:id' => 'history#report_email'
      get 'history/report_email/:id/download' => 'history#report_email_download'
      get 'history/bids' => 'history#bids'
      get 'history/bid_source/:id' => 'history#bid_source'
      get 'history/dlm_charging_sessions' => 'dlm_charging_sessions#list'
      post 'dlm_charging_sessions' => 'dlm_charging_sessions#new'
      get 'history/v2g_optimization_jobs' => 'history#v2g_optimization_jobs'
      post 'history/v2g_optimization_job_create' => 'history#v2g_optimization_job_create'
      get 'rollups/rollups' => 'rollups#rollups'
      get 'rollups/rollup_types' => 'rollups#rollup_types'
      get 'rollups/rollup_errors' => 'rollups#rollup_errors'
      get 'rollups/rollup_error/:id' => 'rollups#rollup_error'
      get 'users/users_as_id_name_email' => 'users#users_as_id_name_email'
      delete 'nominations/distributed_unit/:id' => 'nomination#delete_distributed_unit'
      get 'nominations/distributed_unit/:id' => 'nomination#nomination'
      get 'nominations/distributed_units' => 'nomination#distributed_units'
      get 'allocations/manual-allocation/:id' => 'allocation#asset_dg_allocation_source'
      get 'allocations/automatic-allocation/:id' => 'allocation#automatic_allocation'
      get 'allocation/allocation_sample' => 'allocation#download_allocation_sample'
      post 'allocations' => 'allocation#new'
      post 'nominations' => 'nomination#new'
      post 'nomination/:id' => 'nomination#update'
      get 'nomination/download/:id' => 'nomination#download'
      post 'markets' => 'market#upload'
      post 'opti_results' => 'opti_results#upload'
      get 'opti_results' => 'opti_results#download'
      post 'perf_data_files' => 'perf_data_files#upload'
      post 'bod_files' => 'bod_files#upload'
      post 'margin_data_files' => 'margin_data_files#upload'
      post 'declaration_of_unavailability' => 'declaration_of_unavailability#upload'
      post 'asset_optimization' => 'asset_optimizations#upload'
      get 'portfolio_optimizations' => 'asset_optimizations#portfolio_optimizations'
      post 'portfolio_optimizations/validate_bids' => 'asset_optimizations#validate_bids'
      post 'portfolio_optimizations/reject_bids' => 'asset_optimizations#reject_bids'
      get 'portfolio_optimizations/download_market_positions/:id' => 'asset_optimizations#download_market_positions'
      post 'bids' => 'bid#upload'
      post 'bids/upload_bids' => 'bid#upload_bids'
      get '/bids/upload_bids_sample' => 'bid#upload_bids_sample'
      get '/bids/prepare_auctions_download' => 'bid#prepare_auctions_download'
      get '/bids/auctions_bids_download' => 'bid#download_all_bids'
      get '/bids/time_slices' => 'bid#time_slices'
      get '/bids/products' => 'bid#products'
      get '/bids/eon_nomination_bids' => 'bid#eon_nomination_bids'
      get '/bids/delete_bids' => 'bid#delete_bids'
      get 'reports/tso' => 'reports#tso_report'
      post 'reports/tso' => 'reports#import_tso_report'
      get 'reports/tso/download/:id' => 'reports#tso_download'
      get 'reports/tso/control/download/:date/:tso/:market' => 'reports#tso_control'
      get 'email_configuration/scheduling_report' => 'email_configuration#scheduling_report'
      post 'email_configuration/scheduling_report' => 'email_configuration#save_scheduling_report'
      get 'email_configuration/rollup' => 'email_configuration#rollup'
      post 'email_configuration/rollup' => 'email_configuration#save_rollup'
      get 'email_configuration/allocation' => 'email_configuration#allocation'
      post 'email_configuration/allocation' => 'email_configuration#save_allocation'
      get  'event_notification/event_notification_recipients'         => 'event_notification#event_notification_recipients'
      post 'event_notification/update_event_notification_recipients'  => 'event_notification#update_event_notification_recipients'
      get  'event_notification/event_notification_auction_config_recipients' => 'event_notification#event_notification_auction_config_recipients'
      post 'event_notification/update_event_notification_auction_config_recipients' => 'event_notification#update_event_notification_auction_config_recipients'
      get  'event_notification/event_notification_dispatch_group_recipients' => 'event_notification#event_notification_dispatch_group_recipients'
      post 'event_notification/update_event_notification_dispatch_group_recipients' => 'event_notification#update_event_notification_dispatch_group_recipients'
      get 'configuration/asset/box_type_configurations' => 'asset_configuration#box_type_configurations'
      post 'configuration/asset/create_box_type_configuration' => 'asset_configuration#create_box_type_configuration'
      post 'configuration/asset/update_box_type_configuration' => 'asset_configuration#update_box_type_configuration'
      post 'configuration/asset/delete_box_type_configuration' => 'asset_configuration#delete_box_type_configuration'
      get 'configuration/asset/generic_steering_configs' => 'generic_steering_config#generic_steering_configs'
      post 'configuration/asset/create_generic_steering_config' => 'generic_steering_config#create_generic_steering_config'
      post 'configuration/asset/update_generic_steering_config' => 'generic_steering_config#update_generic_steering_config'
      post 'configuration/asset/delete_generic_steering_config' => 'generic_steering_config#delete_generic_steering_config'
      get 'configuration/asset/signal_lists' => 'asset_configuration#signal_lists'
      post 'configuration/asset/create_signal_list' => 'asset_configuration#create_signal_list'
      post 'configuration/asset/update_signal_list' => 'asset_configuration#update_signal_list'
      post 'configuration/asset/delete_signal_list' => 'asset_configuration#delete_signal_list'
      get 'configuration/market/subpools' => 'subpool#subpools'
      get 'configuration/market/subpool_signals' => 'subpool#subpool_signals'
      post 'configuration/market/create_subpool' => 'subpool#create_subpool'
      post 'configuration/market/update_subpool' => 'subpool#update_subpool'
      post 'configuration/market/delete_subpool' => 'subpool#delete_subpool'
      get 'configuration/market/bgs' => 'balancing_group#bgs'
      post 'configuration/market/update_bgs' => 'balancing_group#update_bgs'
      get 'configuration/rollups/asset_rollups_data' => 'rollup_config#asset_rollups_data'
      post 'configuration/rollups/save_asset_rollup_interval' => 'rollup_config#save_asset_rollup_interval'
      post 'configuration/rollups/save_asset_rollup_products' => 'rollup_config#save_asset_rollup_products'
      post 'configuration/rollups/update_asset_rollup_families' => 'rollup_config#update_asset_rollup_families'
      post 'configuration/rollups/generate_rollup' => 'rollup_config#generate_rollup'
      get 'configuration/rollups/dg_rollups_data' => 'rollup_config#dg_rollups_data'
      post 'configuration/rollups/save_dg_rollup_interval' => 'rollup_config#save_dg_rollup_interval'
      get 'scheduling_report/download_reports_form_data' => 'scheduling_report#download_reports_form_data'
      get 'scheduling_report/generate_report' => 'scheduling_report#generate_report'
      get 'scheduling_report/regenerate_scheduling_balancing_groups_third_party_reports' => 'scheduling_report#regenerate_scheduling_balancing_groups_third_party_reports'
      get 'scheduling_report/regenerate_bg_asset_activations_reports' => 'scheduling_report#regenerate_bg_asset_activations_reports'
      get 'send_files_externally/send_allocations_files' => 'send_files_externally#send_allocations_files'
      get 'send_files_externally/send_measurements_files' => 'send_files_externally#send_measurements_files'
      get 'send_files_externally/send_afrr_measurements_files' => 'send_files_externally#send_afrr_measurements_files'
      get 'send_files_externally/send_afrr_activated_energy_documents' => 'send_files_externally#send_afrr_activated_energy_documents'
    end
  end
end
