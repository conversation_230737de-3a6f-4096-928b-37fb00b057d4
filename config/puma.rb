# Use clustered workers only in production or Linux environments
if ENV['RAILS_ENV'] == 'production'
  workers_count = Integer(ENV['RAILS_WORKERS'] || 3)
  workers workers_count
end

threads_count = Integer(ENV['RAILS_MAX_THREADS'] || 16)
threads 0, threads_count

bind "tcp://0.0.0.0:#{ENV['PORT'] || 80}"

on_worker_boot do
  ActiveSupport.on_load(:active_record) do
    ActiveRecord::Base.establish_connection
  end

  # start health checks
  enabled = ENV['HEALTH_CHECKS_ENABLED'] != "false"
  if enabled
    Rails.logger.info("Enabling health checks.")
    Services::HealthCheck.initialize_periodic_checks
  else
    Rails.logger.warn("Health checks are disabled.")
  end
end

before_fork do
  Dir["/tmp/prometheus/*.bin"].each do |file_path|
    File.unlink(file_path)
  end
end

preload_app!
