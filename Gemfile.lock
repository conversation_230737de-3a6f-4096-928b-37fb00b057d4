GIT
  remote: https://eon-seed.visualstudio.com/Flexibility%20Trading%20Technologies/_git/activeresource_extensions
  revision: 25365009a2e4ff5cae315bac7271ef4db4bb819c
  specs:
    activeresource_extensions (0.0.1)
      kaminari
      rails (>= 5.0)

GIT
  remote: https://eon-seed.visualstudio.com/Flexibility%20Trading%20Technologies/_git/axlsx
  revision: 3defb17c09eebf58b54903c0e0562fe3359df4ab
  specs:
    axlsx (2.1.0.pre)
      htmlentities (~> 4.3.1)
      mimemagic (~> 0.3)
      nokogiri (>= 1.4.1)
      rubyzip (>= 1.3.0)

GIT
  remote: https://eon-seed.visualstudio.com/Flexibility%20Trading%20Technologies/_git/event_trail
  revision: de4b71f429a400de1d978a6e9d6393cdfede8522
  specs:
    event_trail (0.0.1)
      rails (>= 5.0.0)

GIT
  remote: https://eon-seed.visualstudio.com/Flexibility%20Trading%20Technologies/_git/vpp_authentication
  revision: ec4df46559c3ea44b9d93e5f1697aab6dee588da
  specs:
    vpp_authentication (0.0.1)
      bootstrap-sass (= 3.3.7)
      compass-rails (= 4.0.0)
      jquery-rails (~> 4.6.0)
      rails (~> 6.1.0)
      sass-rails (>= 5.0.0)

GIT
  remote: https://github.com/composite-primary-keys/composite_primary_keys.git
  revision: b28b0f04e6284dccb06fb4114e2487913fc362e9
  specs:
    composite_primary_keys (13.0.0)
      activerecord (~> 6.1.0)

GIT
  remote: https://github.com/drapergem/draper.git
  revision: 9cb42f6e714764c1b196f49754328132bcc7ed32
  specs:
    draper (4.0.1)
      actionpack (>= 5.0)
      activemodel (>= 5.0)
      activemodel-serializers-xml (>= 1.0)
      activesupport (>= 5.0)
      request_store (>= 1.0)
      ruby2_keywords

GEM
  remote: https://rubygems.org/
  specs:
    actioncable (6.1.3)
      actionpack (= 6.1.3)
      activesupport (= 6.1.3)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (6.1.3)
      actionpack (= 6.1.3)
      activejob (= 6.1.3)
      activerecord (= 6.1.3)
      activestorage (= 6.1.3)
      activesupport (= 6.1.3)
      mail (>= 2.7.1)
    actionmailer (6.1.3)
      actionpack (= 6.1.3)
      actionview (= 6.1.3)
      activejob (= 6.1.3)
      activesupport (= 6.1.3)
      mail (~> 2.5, >= 2.5.4)
      rails-dom-testing (~> 2.0)
    actionpack (6.1.3)
      actionview (= 6.1.3)
      activesupport (= 6.1.3)
      rack (~> 2.0, >= 2.0.9)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (6.1.3)
      actionpack (= 6.1.3)
      activerecord (= 6.1.3)
      activestorage (= 6.1.3)
      activesupport (= 6.1.3)
      nokogiri (>= 1.8.5)
    actionview (6.1.3)
      activesupport (= 6.1.3)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (6.1.3)
      activesupport (= 6.1.3)
      globalid (>= 0.3.6)
    activemodel (6.1.3)
      activesupport (= 6.1.3)
    activemodel-serializers-xml (1.0.2)
      activemodel (> 5.x)
      activesupport (> 5.x)
      builder (~> 3.1)
    activerecord (6.1.3)
      activemodel (= 6.1.3)
      activesupport (= 6.1.3)
    activeresource (5.1.0)
      activemodel (>= 5.0, < 7)
      activemodel-serializers-xml (~> 1.0)
      activesupport (>= 5.0, < 7)
    activestorage (6.1.3)
      actionpack (= 6.1.3)
      activejob (= 6.1.3)
      activerecord (= 6.1.3)
      activesupport (= 6.1.3)
      marcel (~> 0.3.1)
      mimemagic (~> 0.3.2)
    activesupport (6.1.3)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
      zeitwerk (~> 2.3)
    authority (3.3.0)
      activesupport (>= 3.0.0)
    authy (3.0.0)
      httpclient (>= 2.5.3.3)
    autoprefixer-rails (10.2.4.0)
      execjs
    bcrypt (3.1.16)
    better_errors (2.9.1)
      coderay (>= 1.0.0)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
    binding_of_caller (1.0.0)
      debug_inspector (>= 0.0.1)
    bootstrap-sass (3.3.7)
      autoprefixer-rails (>= 5.2.1)
      sass (>= 3.3.4)
    browser (5.3.1)
    builder (3.2.4)
    byebug (11.1.3)
    chunky_png (1.4.0)
    coderay (1.1.3)
    compass (1.0.3)
      chunky_png (~> 1.2)
      compass-core (~> 1.0.2)
      compass-import-once (~> 1.0.5)
      rb-fsevent (>= 0.9.3)
      rb-inotify (>= 0.9)
      sass (>= 3.3.13, < 3.5)
    compass-core (1.0.3)
      multi_json (~> 1.0)
      sass (>= 3.3.0, < 3.5)
    compass-import-once (1.0.5)
      sass (>= 3.2, < 3.5)
    compass-rails (4.0.0)
      compass (~> 1.0.0)
      sass-rails (< 5.1)
      sprockets (< 4.0)
    concurrent-ruby (1.3.5)
    crass (1.0.6)
    date (3.4.1)
    debug_inspector (1.0.0)
    digest (3.2.0)
    domain_name (0.6.20240107)
    dotenv (2.7.6)
    dotenv-rails (2.7.6)
      dotenv (= 2.7.6)
      railties (>= 3.2)
    erubi (1.10.0)
    erubis (2.7.0)
    execjs (2.7.0)
    faraday (1.4.2)
      faraday-em_http (~> 1.0)
      faraday-em_synchrony (~> 1.0)
      faraday-excon (~> 1.1)
      faraday-net_http (~> 1.0)
      faraday-net_http_persistent (~> 1.1)
      multipart-post (>= 1.2, < 3)
      ruby2_keywords (>= 0.0.4)
    faraday-em_http (1.0.0)
    faraday-em_synchrony (1.0.0)
    faraday-excon (1.1.0)
    faraday-net_http (1.0.1)
    faraday-net_http_persistent (1.1.0)
    ffi (1.15.0)
    globalid (1.2.1)
      activesupport (>= 6.1)
    gon (6.4.0)
      actionpack (>= 3.0.20)
      i18n (>= 0.7)
      multi_json
      request_store (>= 1.0)
    haml (5.2.1)
      temple (>= 0.8.0)
      tilt
    haml-rails (2.0.1)
      actionpack (>= 5.1)
      activesupport (>= 5.1)
      haml (>= 4.0.6, < 6.0)
      html2haml (>= 1.0.1)
      railties (>= 5.1)
    has_scope (0.8.0)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
    highcharts-rails (6.0.3)
      railties (>= 3.1)
    html2haml (2.2.0)
      erubis (~> 2.7.0)
      haml (>= 4.0, < 6)
      nokogiri (>= 1.6.0)
      ruby_parser (~> 3.5)
    htmlentities (4.3.4)
    http-accept (1.7.0)
    http-cookie (1.0.8)
      domain_name (~> 0.5)
    httparty (0.18.1)
      mime-types (~> 3.0)
      multi_xml (>= 0.5.2)
    httpclient (2.8.3)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    inherited_resources (1.12.0)
      actionpack (>= 5.2, < 6.2)
      has_scope (~> 0.6)
      railties (>= 5.2, < 6.2)
      responders (>= 2, < 4)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    jwt (2.2.3)
    kaminari (1.2.1)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.1)
      kaminari-activerecord (= 1.2.1)
      kaminari-core (= 1.2.1)
    kaminari-actionview (1.2.1)
      actionview
      kaminari-core (= 1.2.1)
    kaminari-activerecord (1.2.1)
      activerecord
      kaminari-core (= 1.2.1)
    kaminari-core (1.2.1)
    logger (1.7.0)
    lograge (0.9.0)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    logstash-event (1.2.02)
    loofah (2.9.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.5.9)
    mail (2.7.1)
      mini_mime (>= 0.1.1)
    mailjet (1.7.11)
      activesupport (>= 5.0.0)
      rack (>= 1.4.0)
      rest-client (>= 2.1.0)
      yajl-ruby
    marcel (0.3.3)
      mimemagic (~> 0.3.2)
    method_source (1.0.0)
    mime-types (3.3.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2021.0225)
    mimemagic (0.3.9)
      nokogiri (~> 1)
      rake
    mini_mime (1.0.2)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    multi_json (1.15.0)
    multi_xml (0.6.0)
    multipart-post (2.1.1)
    net-ftp (0.2.1)
      net-protocol
      time
    net-http (0.3.2)
      uri
    net-imap (0.2.5)
      digest
      net-protocol
      strscan
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-sftp (4.0.0)
      net-ssh (>= 5.0.0, < 8.0.0)
    net-smtp (0.3.4)
      net-protocol
    net-ssh (7.2.1)
    netrc (0.11.0)
    nio4r (2.7.4)
    nobspw (0.6.2)
    nokogiri (1.18.9)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    oj (3.11.2)
    pg (1.5.9)
    prometheus-client (2.1.0)
    psych (3.3.4)
    puma (3.12.6)
    racc (1.8.1)
    rack (2.2.3)
    rack-test (1.1.0)
      rack (>= 1.0, < 3)
    rails (6.1.3)
      actioncable (= 6.1.3)
      actionmailbox (= 6.1.3)
      actionmailer (= 6.1.3)
      actionpack (= 6.1.3)
      actiontext (= 6.1.3)
      actionview (= 6.1.3)
      activejob (= 6.1.3)
      activemodel (= 6.1.3)
      activerecord (= 6.1.3)
      activestorage (= 6.1.3)
      activesupport (= 6.1.3)
      bundler (>= 1.15.0)
      railties (= 6.1.3)
      sprockets-rails (>= 2.0.0)
    rails-dom-testing (2.0.3)
      activesupport (>= 4.2.0)
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.3.0)
      loofah (~> 2.3)
    railties (6.1.3)
      actionpack (= 6.1.3)
      activesupport (= 6.1.3)
      method_source
      rake (>= 0.8.7)
      thor (~> 1.0)
    rake (13.0.3)
    rb-fsevent (0.10.4)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    request_store (1.5.0)
      rack (>= 1.4)
    responders (3.0.1)
      actionpack (>= 5.0)
      railties (>= 5.0)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    ruby-ole (********)
    ruby2_keywords (0.0.4)
    rubyXL (3.4.17)
      nokogiri (>= 1.10.8)
      rubyzip (>= 1.3.0)
    ruby_parser (3.15.1)
      sexp_processor (~> 4.9)
    rubyzip (2.3.0)
    sass (3.4.25)
    sass-rails (5.0.8)
      railties (>= 5.2.0)
      sass (~> 3.1)
      sprockets (>= 2.8, < 4.0)
      sprockets-rails (>= 2.0, < 4.0)
      tilt (>= 1.1, < 3)
    select2-rails (4.0.13)
    settingslogic (2.0.9)
    sexp_processor (4.15.2)
    simple_form (5.1.0)
      actionpack (>= 5.2)
      activemodel (>= 5.2)
    spreadsheet (1.2.8)
      ruby-ole
    sprockets (3.7.2)
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.2.2)
      actionpack (>= 4.0)
      activesupport (>= 4.0)
      sprockets (>= 3.0.0)
    strscan (3.1.5)
    temple (0.8.2)
    thor (1.1.0)
    tilt (2.0.10)
    time (0.4.1)
      date
    timeout (0.4.3)
    twilio-ruby (5.55.0)
      faraday (>= 0.9, < 2.0)
      jwt (>= 1.5, <= 2.5)
      nokogiri (>= 1.6, < 2.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uglifier (4.2.0)
      execjs (>= 0.3.0, < 3)
    uri (1.0.3)
    websocket-driver (0.7.3)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    yajl-ruby (1.4.3)
    zeitwerk (2.6.18)

PLATFORMS
  arm64-darwin-24
  x86_64-darwin-19
  x86_64-darwin-20
  x86_64-linux

DEPENDENCIES
  activeresource (= 5.1.0)
  activeresource_extensions!
  authority
  authy
  axlsx!
  bcrypt (~> 3.1.7)
  better_errors
  binding_of_caller
  bootstrap-sass (~> 3.3.7)
  browser
  byebug
  compass-rails (= 4.0.0)
  composite_primary_keys!
  dotenv-rails
  draper!
  event_trail!
  globalid (~> 1.0)
  gon
  haml-rails
  highcharts-rails
  httparty
  inherited_resources
  jquery-rails (~> 4.6.0)
  kaminari
  logger (~> 1.4)
  lograge (~> 0.9.0)
  logstash-event
  mailjet
  net-ftp (~> 0.2.0)
  net-http (~> 0.3.1)
  net-imap (~> 0.2.3)
  net-pop (~> 0.1.1)
  net-protocol (~> 0.2.1)
  net-sftp
  net-smtp (~> 0.3.1)
  nobspw
  nokogiri (>= 1.13)
  oj
  pg (~> 1.5)
  prometheus-client
  psych (~> 3.3.2)
  puma (~> 3.0)
  rails (~> 6.1.0)
  rubyXL
  sass-rails (~> 5.0)
  select2-rails
  settingslogic
  simple_form
  spreadsheet
  twilio-ruby
  uglifier
  vpp_authentication!

BUNDLED WITH
   2.4.19
